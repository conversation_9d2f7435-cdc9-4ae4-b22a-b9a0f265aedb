import React, { useState, useEffect } from 'react';
import { StyleSheet, View, Text } from 'react-native';
import { sightSeeingCities } from '../../../../PhoenixDetail/Utils/PhoenixDetailUtils';
import AtomicCss from '@mmt/legacy-commons/Styles/AtomicCss';
import {getGoogleAPIKeyForAllPlarforms, getStaticMapUriForCoordinatesList} from '../../../../utils/HolidayUtils';

import {isNumber, isEmpty} from 'lodash';
import PlaceholderImageView from '@mmt/legacy-commons/Common/Components/PlaceholderImageView';
import { fontStyles } from '../../../../Styles/holidayFonts';
import { holidayColors } from '../../../../Styles/holidayColors';
import { SightSeeingDetailContainer, SightSeeingLocation } from '../../../../DetailMimaComponents/SightSeeing/SightSeeingComponents';
import { marginStyles, paddingStyles } from '../../../../Styles/Spacing';

export const SightSeeingViewDetails = ({ data, closeModal }) => {
  const [googleAPIKey, setGoogleAPIKey] = useState('');
  const fetchGoogleKey = async () => {
    const googleAPIKey = await getGoogleAPIKeyForAllPlarforms();
    setGoogleAPIKey(googleAPIKey);
  };
  useEffect(() => {
    fetchGoogleKey();
  }, []);

  let duration = 0;
  let coordinateList = [];
  const citySet = new Set();
  let googleMapImage = '';

  data.forEach(item => {
    duration += item.duration;
    if (isNumber(item.lat) && isNumber(item.long)) {
      coordinateList.push({ lat: item.lat, lon: item.long });
    }
    else if (isEmpty(googleMapImage) && item?.images?.length > 0) {
      googleMapImage = item?.images[0].path;
    }
    citySet.add(item.cityName);
  });

  if (coordinateList && coordinateList.length > 0){
      googleMapImage = getStaticMapUriForCoordinatesList(coordinateList, false, googleAPIKey);
  }

  return (
      <View style={styles.sightSeeingCardWrap}>
        <View style={styles.cardHdrWrap}>
          <Text style={styles.heading}>Sightseeing in {sightSeeingCities(citySet)}</Text>
        </View>
        {!!googleMapImage && <PlaceholderImageView url={googleMapImage} style={styles.picImage} />}
        <View style={[AtomicCss.flexRow, AtomicCss.alignCenter, marginStyles.mv10]}>
          <SightSeeingDetailContainer
            details={[
              { title: 'Duration: ', value: duration },
              { title: 'Places Covered: ', value: data.length },
            ]}
          />
        </View>
        <SightSeeingLocation locations={data} />
      </View>
  );
};

const styles = StyleSheet.create({
  sightSeeingCardWrap: {
    ...paddingStyles.pa16,
    backgroundColor: holidayColors.white,
    height:'100%',
  },
  picImage: {
    width: '100%',
    height: 200,
    resizeMode: 'cover',
    backgroundColor: '#e5e5e5',
    borderRadius: 4,
  },
  picImageWrap: {
    backgroundColor: '#e5e5e5',
    width: '100%',
    height: 200,
    borderRadius: 4,
  },
  cardHdrWrap: {
    width: '70%',
    flexWrap: 'wrap',
    flexDirection: 'row',
    ...marginStyles.mb10,
  },
  heading: {
    ...fontStyles.labelMediumBlack,
    color: holidayColors.black,
  },
});

export default SightSeeingViewDetails;
