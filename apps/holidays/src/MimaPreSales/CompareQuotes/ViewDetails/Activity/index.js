import React from 'react';
import {ScrollView} from 'react-native';
import ActivityDetailsCommonCard from 'mobile-holidays-react-native/src/PhoenixDetail/Components/ActivityOverlay/ActivityDetail/ActivityDetailsCommonCard';

const ActivityViewDetails = ({ data, trackClickEvent }) => {
  return (

      <ScrollView showsVerticalScrollIndicator={false}>
        <ActivityDetailsCommonCard
          restriction={true} // to hide change and remove for PSM
          activity={data}
          trackClick={trackClickEvent}
        />
      </ScrollView>
  );
};


export default ActivityViewDetails;
