import React from 'react';
import { ScrollView, StyleSheet, Text, View} from 'react-native';
import AtomicCss from '@mmt/legacy-commons/Styles/AtomicCss';
import {has, isEmpty} from 'lodash';
import BasePage from '@mmt/legacy-commons/Common/navigation/BasePage';
import CovidTag from '../../../../PhoenixDetail/Components/TransferDetailPage/Common/CovidTag';
import TransferCarDetails from 'mobile-holidays-react-native/src/DetailMimaComponents/Transfer/TransferDetail/TransferCarDetails';
import { DriverSpokenLanguages, Exclusion, ImportantRead, Inclusion, TransferFacilities } from 'mobile-holidays-react-native/src/DetailMimaComponents/Transfer/TransferDetail/TransferFacilites';
import { marginStyles } from 'mobile-holidays-react-native/src/Styles/Spacing';
import { largeHeightSeperator } from 'mobile-holidays-react-native/src/Styles/holidaySpacing';
import { paddingStyles } from '../../../../Styles/Spacing';
import { fontStyles } from '../../../../Styles/holidayFonts';
import { holidayColors } from '../../../../Styles/holidayColors';

export default class TransferViewDetails extends BasePage {
  constructor(props) {
    super(props);
    this.handleBackButtonClick = this.onBackPressed.bind(this);

    this.state = {
      item: props.data.transfer,
    };
  }

  onBackPressed() {
    const {fromPage} = this.props || {};
    if (fromPage === 'TRANSFERS_LISTING') {
      this.updateBlackStrip();
    }
  }

  render() {
    const {item} = this.state || {};
    let vehicleInfo = {};
    let additionalInfo = {};
    let mySafe = false;
    let inclusionText;
    let imageUrl = '';
    let privateText = '';



    if (item && has(item, 'commuteDetails')) {
      // Handle car itinerary case.
      const {inclusionText : inclText} =  item || {};
      const {vehicleInfo : vi, additionalInfo: ai, safe} =  item.commuteDetails[0] || {};
      vehicleInfo = vi;
      const {imageUrl : vehicleImageUrl, privateOrShared} = vehicleInfo || {};
      imageUrl = vehicleImageUrl;
      additionalInfo = ai;
      mySafe = safe;
      inclusionText = inclText;
      privateText = privateOrShared;
    } else {
      // Handle Airport transfer case.
      const {privateTransfer, defaultSelection} = item || {};
      const {vehicleInfo: vi, additionalData} = privateTransfer || {};
      const {inclusionText : inclText, safe} = additionalData || {};
      vehicleInfo = vi;
      const {imageUrl : vehicleImageUrl} = vehicleInfo || {};
      imageUrl = vehicleImageUrl;
      inclusionText = inclText;
      mySafe = safe;
      privateText = defaultSelection;
    }

    if (isEmpty(privateText) && !isEmpty(item.type)) {
      privateText = item.type;
    }

    return (
      <ScrollView>
        <View style={styles.container}>
          <TransferCarDetails
            vehicleInfo={vehicleInfo}
            imageUrl={imageUrl}
            privateText={privateText}
          />
          {mySafe && (
            <View style={AtomicCss.marginBottom10}>
              <CovidTag subTitle={'Safe & Sanitized Transfer'} />
            </View>
          )}

          {!isEmpty(inclusionText) && (
            <View>
              <Text style={styles.inclusionText}>{inclusionText}</Text>
            </View>
          )}
          <TransferFacilities vehicleInfo={vehicleInfo} />
          <DriverSpokenLanguages vehicleInfo={vehicleInfo} />
        </View>
        {(has(additionalInfo, 'exclusions') ||
          has(additionalInfo, 'inclusions') ||
          has(additionalInfo, 'importantInfos')) && (
          <View>
            <View style={styles.separator2} />
            <View style={marginStyles.ml16}>
              <Inclusion additionalInfo={additionalInfo} />
              <Exclusion additionalInfo={additionalInfo} />
              <ImportantRead additionalInfo={additionalInfo} />
            </View>
          </View>
        )}
      </ScrollView>
    );
  }
}

const styles = StyleSheet.create({
  container: {
   ...paddingStyles.pa16,
  },
  iconClose: {
    width: 20,
    height: 20,
  },
  closeWrap: {
    padding: 15,
  },
  cabStyle: {
    width: 120,
    height: 74,
    marginRight: 15,
    resizeMode: 'contain',
  },
  inclusionText: {
    ...fontStyles.labelSmallBold,
    color: holidayColors.black,
    ...marginStyles.mv10,
  },
  separator2: {
    ...largeHeightSeperator,
    marginBottom: 9,
  },
});
