import React from 'react';
import {Image, StyleSheet, Text, View, ScrollView} from 'react-native';
import {parseFlightDate} from '../../../../PhoenixDetail/Utils/FlightUtils';
import arrow from '@mmt/legacy-assets/src/ic_arrow_blue.webp';
import fecha from 'fecha';
import FlightDetailPage from '../../../Components/FlightDetailCard/PsmFlightDetail';
import { fontStyles } from '../../../../Styles/holidayFonts';
import { holidayColors } from '../../../../Styles/holidayColors';
import { marginStyles, paddingStyles } from '../../../../Styles/Spacing';

export const TechnicalStops = (props) => {
    const DATE_FORMAT = 'ddd MMM DD';
    const {flightCardData: data, baggageInfo} = props || {};
    const {fromAirport, toAirport, departure} = data || {};
    const flightDate = fecha.format(parseFlightDate(departure), DATE_FORMAT);

    return (
        <View style={styles.flightCard}>
            <View style={styles.cardHeader}>
                <Text style={styles.flightCardHeader}>{fromAirport.airportCity}</Text>
                <Image source={arrow} style={styles.arrow}/>
                <Text style={styles.flightCardHeader}>{toAirport.airportCity} | {flightDate}</Text>
            </View>
            <FlightDetailPage data={data} baggageInfoMap={baggageInfo}/>
        </View>
    );
};

const FlightsViewDetails = ({ data, baggageInfo, closeModal }) => {
    const { isLastCard = false, fromAirport, toAirport } = data || {};
    const hasValidAirports = !!fromAirport?.airportCity && !!toAirport?.airportCity;
    const flightTypeHeaderText = isLastCard ? 'Return' : 'Departure';
    return (
        <ScrollView>
          {data && (
            <View style={styles.flightDetailView}>
              <View>
                {hasValidAirports && (
                  <Text style={styles.flightDetailHeader}>
                    Flight from{' '}
                    <Text style={styles.flightDetailHeaderBold}>
                      {data.fromAirport.airportCity} to {data.toAirport.airportCity}
                    </Text>
                  </Text>
                )}
              </View>
              <View style={marginStyles.mt10}>
                <Text style={styles.flightTypeHeader}>{flightTypeHeaderText} Flight</Text>
              </View>
              <View style={styles.flightDetailViewInner}>
                <TechnicalStops flightCardData={data} baggageInfo={baggageInfo} />
              </View>
            </View>
          )}
        </ScrollView>
    );
};
    const styles = StyleSheet.create({
    flightDetailView: {
        ...paddingStyles.ph16,
    },
    flightDetailHeader: {
        ...fontStyles.labelSmallRegular,
        color: holidayColors.black,
    },
    flightDetailHeaderBold: {
        ...fontStyles.labelSmallBold,
    },
    flightDetailViewInner: {
        borderWidth: 1,
        borderColor: holidayColors.grayBorder,
        borderRadius: 4,
        marginTop: 10,
    },
    flightTypeHeader: {
        ...fontStyles.labelMediumBlack,
        color: holidayColors.black,
    },
    flLayoverTag: {
        backgroundColor: '#e4f3ff',
        alignItems: 'center',
        justifyContent: 'center',
        padding: 5,
        width: '100%',
    },
    cardHeader: {
        backgroundColor: holidayColors.lightGray2,
        padding: 10,
        flexDirection: 'row',
        alignItems: 'center',
    },
    flightCardHeader: {
        ...fontStyles.labelSmallBold,
        color: holidayColors.black,
    },
    arrow: {
        marginLeft: 5,
        marginRight: 5,
        width: 12,
        height: 9,
        resizeMode: 'contain',
        tintColor: holidayColors.primaryBlue,
    },
});

export default FlightsViewDetails;
