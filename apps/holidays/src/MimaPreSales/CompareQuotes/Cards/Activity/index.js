import React from 'react';
import { StyleSheet, View, TouchableOpacity, Text } from 'react-native';
import PlaceholderImageView from '@mmt/legacy-commons/Common/Components/PlaceholderImageView';
import AtomicCss from '@mmt/legacy-commons/Styles/AtomicCss';
import getPlatformElevation from '@mmt/legacy-commons/Styles/getPlatformElevation';
import UserRating from '../../../Components/UserRating';
import { PresalesImages } from '../../../utils/PresalesImages';

const ItineraryActivityCard = (props) => {
  const { handlePopup, item } = props;

  return (
    <View style={[styles.card]}>
      <View>
        {!!item.cityName && (
          <Text
            numberOfLines={1}
            style={[AtomicCss.font10, AtomicCss.blackText, AtomicCss.regularFont]}
          >
            <Text style={AtomicCss.boldFont}>Activity </Text>
            <Text style={{color:'#7a7a7a'}}>in{' '}</Text>
            <Text style={AtomicCss.boldFont}> {item.cityName}</Text>
          </Text>
        )}
      </View>
      <View style={styles.cardContent}>
        <View style={styles.activityImg}>
          <PlaceholderImageView url={item.activityImage} style={styles.iconActivity} />
          {!!item.userRating && (
            <View style={styles.userRatingWrap}>
              <UserRating rating={item.userRating} />
            </View>
          )}
        </View>
        <View style={styles.activityDetails}>
          {!!item.activityName && (
            <Text
              numberOfLines={3}
              style={[AtomicCss.font10, AtomicCss.boldFont, AtomicCss.blackText]}
            >
              {item.activityName}
            </Text>
          )}
          <View>
            {!!item.duration && (
              <Text style={[AtomicCss.font10, AtomicCss.regularFont, AtomicCss.blackText]}>
                Duration: <Text style={AtomicCss.boldFont}>{item.duration}</Text>
              </Text>
            )}
          </View>
        </View>
      </View>
      <TouchableOpacity
        style={styles.cardFooter}
        hitSlop={{ top: 20, bottom: 20, left: 50, right: 50 }}
        onPress={handlePopup}
      >
        <Text
          style={[AtomicCss.font8, AtomicCss.azure, AtomicCss.boldFont, AtomicCss.textUppercase]}
        >
          View Details
        </Text>
      </TouchableOpacity>
    </View>
  );
};

const styles = StyleSheet.create({
  card: {
    backgroundColor: 'white',
    borderRadius: 4,
    padding: 10,
    width: 200,
    height: 95,
    ...getPlatformElevation(3),
    position: 'relative',
    marginBottom: 10,
  },
  cardContent: {
    flexDirection: 'row',
    paddingTop: 5,
    paddingBottom: 5,
    alignItems: 'center',
    flexWrap: 'wrap',
  },
  cardFooter: {
    position: 'absolute',
    right: 10,
    bottom: 7,
  },
  activityImg: {
    width: '40%',
    marginRight: '5%',
    alignItems: 'center',
    position: 'relative',
  },
  activityDetails: {
    width: '55%',
    justifyContent: 'space-between',
    height: 50,
  },
  vehichleImg: {
    width: 77,
    height: 42,
    resizeMode: 'contain',
  },
  iconActivity: {
    width: 77,
    height: 50,
    resizeMode: 'cover',
  },
  userRatingWrap: {
    position: 'absolute',
    left: 2,
    bottom: 5,
  },
});

export default ItineraryActivityCard;
