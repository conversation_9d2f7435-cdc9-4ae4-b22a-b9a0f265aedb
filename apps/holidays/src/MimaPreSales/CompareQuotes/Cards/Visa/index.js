import React from 'react';
import AtomicCss from '@mmt/legacy-commons/Styles/AtomicCss';
import { StyleSheet, View, Text, Image } from 'react-native';
import iconVisa from '../../../images/ic_visa.png';
import getPlatformElevation from '@mmt/legacy-commons/Styles/getPlatformElevation';

const VISA_TYPES = {
  INCLUDED: 'Included',
  ASSISTANCE: 'Assistance',
  ON_ARRIVAL: 'On Arrival',
};

const ItineraryVisaCard = (props) => {
  const { item } = props;
  const fetchVisaType = (visaType) => {
    let typeOfVisa = '';
    if (visaType === VISA_TYPES.INCLUDED.toUpperCase()) {typeOfVisa = VISA_TYPES.INCLUDED;}
    else if (visaType === VISA_TYPES.ASSISTANCE.toUpperCase()) {typeOfVisa = VISA_TYPES.ASSISTANCE;}
    else if (visaType === VISA_TYPES.ON_ARRIVAL.toUpperCase()) {typeOfVisa = VISA_TYPES.ON_ARRIVAL;}
    return typeOfVisa;
  };

  return (
    <View style={[styles.card]}>
      <View style={styles.cardContent}>
        <View style={styles.visaImg}>
          <Image source={iconVisa} style={styles.iconVisa} />
        </View>
        {!!item.visaType && (
          <View style={styles.visaType}>
            <Text
              numberOfLines={1}
              style={[AtomicCss.font12, AtomicCss.boldFont, AtomicCss.blackText]}
            >
              Visa {fetchVisaType(item.visaType)}
            </Text>
          </View>
        )}
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  card: {
    backgroundColor: 'white',
    borderRadius: 4,
    padding: 10,
    width: 200,
    height: 55,
    ...getPlatformElevation(3),
    position: 'relative',
    marginBottom: 10,
  },
  cardContent: {
    flexDirection: 'row',
    paddingTop: 10,
    paddingBottom: 5,
    alignItems: 'center',
  },
  cardFooter: {
    position: 'absolute',
    right: 10,
    bottom: 7,
  },
  visaImg: {
    width: '20%',
    position: 'relative',
  },
  visaType: {
    width: '55%',
    justifyContent: 'center',
  },
  iconVisa: {
    width: 25,
    height: 25,
    resizeMode: 'cover',
  },
});

export default ItineraryVisaCard;
