import React from 'react';
import AtomicCss from '@mmt/legacy-commons/Styles/AtomicCss';
import {StyleSheet, View, TouchableOpacity, Text, Image} from 'react-native';
import iconFlexiDate from '../../../../PhoenixReview/images/ic_flexiDate.png';
import iconZC from '../../../../PhoenixReview/images/ic_zcSmall.png';
import getPlatformElevation from '@mmt/legacy-commons/Styles/getPlatformElevation';

const ItineraryAddOnsCard = (props) => {
    const {item, handlePopup} = props;
    const {selectedType} = item || {};
    const icon = selectedType === 'ZC' ? iconZC : iconFlexiDate;
    return (
        <View style={[styles.card]}>
            <View style={styles.cardContent}>
                <View style={styles.cardBody}>
                    <Image source={icon} style={styles.addonImageNew}/>
                    <TouchableOpacity style={{marginLeft: 30}} onPress={handlePopup}>
                        <View>
                            <Text
                                style={[
                                    AtomicCss.font8,
                                    AtomicCss.azure,
                                    AtomicCss.boldFont,
                                    AtomicCss.textUppercase,
                                ]}>
                                View Details
                            </Text>
                        </View>
                    </TouchableOpacity>
                </View>
            </View>
        </View>
    );
};

const styles = StyleSheet.create({
    card: {
        backgroundColor: 'white',
        borderRadius: 4,
        width: 200,
        ...getPlatformElevation(3),
    },
    cardContent: {
        height: 40,
        justifyContent: 'center',
    },
    cardFooter: {
        right: 10,
    },
    addOnType: {
        justifyContent: 'center',
    },
    addonImg: {},
    addonImageNew: {width: 93, height: 26, resizeMode: 'contain'},
    cardBody: {
        justifyContent: 'flex-start', marginLeft: 10, flexDirection: 'row', alignItems: 'center',
    },
    iconAddOn: {
        width: '100%',
        height: 25,
        resizeMode: 'contain',
    },
});

export default ItineraryAddOnsCard;
