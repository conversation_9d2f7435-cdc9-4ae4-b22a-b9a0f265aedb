import React from 'react';
import AtomicCss from '@mmt/legacy-commons/Styles/AtomicCss';
import {StyleSheet, Text, View} from 'react-native';
import getPlatformElevation from '@mmt/legacy-commons/Styles/getPlatformElevation';
import PlaceholderImageView from '@mmt/legacy-commons/Common/Components/PlaceholderImageView';

const OtherAddonsCard = (props) => {
    const {item} = props || {};
    const {imageUrl = '', title = ''} =  item || {};
    return (
        <View style={[styles.card]}>
            <View style={styles.cardContent}>
                <View style={styles.cardBody}>
                    <PlaceholderImageView url={imageUrl} style={styles.addonImageNew}/>
                    <View style={styles.addOnType}>
                        <Text
                            numberOfLines={1}
                            style={[AtomicCss.font12, AtomicCss.boldFont, AtomicCss.blackText, {marginLeft:20}]}>
                            {title}
                        </Text>
                    </View>
                </View>
            </View>
        </View>
    );
};

const styles = StyleSheet.create({
    card: {
        backgroundColor: 'white',
        borderRadius: 4,
        width: 200,
        ...getPlatformElevation(3),
    },
    cardContent: {
        height: 40,
        justifyContent: 'center',
    },
    addOnType: {
        justifyContent: 'center',
    },
    addonImageNew: {width: 25, height: 25, resizeMode: 'contain'},
    cardBody: {
        justifyContent: 'flex-start', marginLeft: 10, flexDirection: 'row', alignItems: 'center',
    },
});

export default OtherAddonsCard;
