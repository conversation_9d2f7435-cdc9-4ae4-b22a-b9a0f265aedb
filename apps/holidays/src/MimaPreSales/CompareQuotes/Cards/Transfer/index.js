import React from 'react';
import { StyleSheet, View, TouchableOpacity, Text } from 'react-native';
import PlaceholderImageView from '@mmt/legacy-commons/Common/Components/PlaceholderImageView';
import AtomicCss from '@mmt/legacy-commons/Styles/AtomicCss';
import getPlatformElevation from '@mmt/legacy-commons/Styles/getPlatformElevation';
import {isEmpty, has} from 'lodash';
import {getImageUrlFromImageDetailObject} from '../../../../PhoenixDetail/Utils/PhoenixDetailUtils';
import {fonts} from '@mmt/legacy-commons/Common/Components/MIMABottomOverlay/OverlayMessage/globalStyles';

const transferTypes = {
  AIRPORT_TO_HOTEL: 'AIRPORT_TO_HOTEL',
  HOTEL_TO_AIRPORT: 'HOTEL_TO_AIRPORT',
  AIRPORT_TO_HOTEL_TEXT: 'Transfer from Airport to Hotel',
  HOTEL_TO_AIRPORT_TEXT: 'Transfer from Hotel to Airport',
};

const ItineraryTransferCard = (props) => {
  const { handlePopup, item } = props || {};
  const {transfer} = item || {};
  const {privateTransfer} = transfer || {};

  let imageUrl = '';
  let vchlModel = '';
  let privateText = '';
  let vchlName = '';
  let seatCpcty = 0;
  let category = '';
  let facilities = [];
  let sellableId = '';

  const onlyTransfer = !isEmpty(privateTransfer);

  const populateDataFromVehicleInfo = vehicleInfo => {
    const { model, privateOrShared, vehicleName, maxPaxCapacity, vehicleCategory, facilities: fcltes, imageUrl: imgUrl } = vehicleInfo || {};
    vchlModel = model;
    privateText = privateOrShared;
    vchlName = vehicleName;
    seatCpcty = maxPaxCapacity;
    category = vehicleCategory;
    facilities = fcltes;
    if (imgUrl) { imageUrl = imgUrl; }
  };

  if (onlyTransfer) {
    const { privateTransfer, defaultSelection, groupTransfer } = transfer || {};
    if (defaultSelection === 'SHARED' && groupTransfer) {
      const { vehicleInfo, sellableId: slbleId } = groupTransfer || {};
      sellableId = slbleId;
      populateDataFromVehicleInfo(vehicleInfo);
    } else if (privateTransfer) {
      const { vehicleInfo, sellableId: slbleId } = privateTransfer || {};
      sellableId = slbleId;
      populateDataFromVehicleInfo(vehicleInfo);
    }
    privateText = defaultSelection;
  } else {
    const { commuteDetails } = transfer || {};
    if (commuteDetails && commuteDetails.length > 0) {
      const {vehicleInfo} = commuteDetails[0] || {};
      populateDataFromVehicleInfo(vehicleInfo);
    }
  }

  const Facilities = ({ facilities }) => {
    const MAX_FACILITY_COUNT = 3;
    if (!facilities || facilities.length === 0) {
      return [];
    }

    const facilityView = [];
    facilities.forEach((facility, index) => {
      const { title } = facility || {};  // type will ve used to fetch ICON
      if (facilityView.length < MAX_FACILITY_COUNT && title) {
        facilityView.push(<Text key={index} style={styles.detailText}>{title}{(facilityView.length === MAX_FACILITY_COUNT - 1 || facilities.length - 1 === index) ? '' : <Text style={{ color: '#b3b2b2' }}>  | </Text>} </Text>);
      }
    });

    return (<View style={styles.transferMajorDetails}>
      <View>
        <View style={[AtomicCss.flexRow, AtomicCss.alignCenter]}>
          {facilityView}
        </View>
      </View>
    </View>);
  };

  return (
    <View style={[styles.card]}>
      <View>
        {!!item.transferType && item.transferType === transferTypes.AIRPORT_TO_HOTEL && (
          <Text
            numberOfLines={1}
            style={[AtomicCss.font10, AtomicCss.blackText, AtomicCss.regularFont]}
          >
            {transferTypes.AIRPORT_TO_HOTEL_TEXT}
          </Text>
        )}
        {!!item.transferType && item.transferType === transferTypes.HOTEL_TO_AIRPORT && (
          <Text
            numberOfLines={1}
            style={[AtomicCss.font10, AtomicCss.blackText, AtomicCss.regularFont]}
          >
            {transferTypes.HOTEL_TO_AIRPORT_TEXT}
          </Text>
        )}

        {!!privateText && !privateTransfer && (
            <Text
                numberOfLines={1}
                style={[AtomicCss.font10, AtomicCss.blackText, AtomicCss.regularFont]}>
              {privateText} Transfer | {vchlName} or similar
            </Text>
        )}
      </View>
      <View style={styles.cardContent}>
        <View style={styles.transferImg}>
            <PlaceholderImageView url={imageUrl} style={styles.vehicleImg} />
        </View>
        <View style={styles.transferDetails}>
          {!!item.defaultSelection && (
            <Text style={[AtomicCss.font10, AtomicCss.boldFont, AtomicCss.blackText]}>
              {item.defaultSelection} Transfer
            </Text>
          )}
            <Text numberOfLines={2} style={[AtomicCss.font10, AtomicCss.regularFont, AtomicCss.blackText, AtomicCss.paddingTop2]}>
              {vchlName} {!isEmpty(vchlModel) ? ` | ${vchlModel}` : ''}
            </Text>
        </View>
        <Facilities facilities={facilities} />
      </View>
      <TouchableOpacity
        style={styles.cardFooter}
        hitSlop={{ top: 20, bottom: 20, left: 50, right: 50 }}
        onPress={handlePopup}>
        <Text style={[AtomicCss.font8, AtomicCss.azure, AtomicCss.boldFont, AtomicCss.textUppercase]}>
          View Details
        </Text>
      </TouchableOpacity>
    </View>
  );
};

const styles = StyleSheet.create({
  transferMajorDetails: {
    flex: 1,
    flexDirection: 'row',
  },
  detailText: {
    fontSize: 12,
    fontFamily: fonts.bold,
    color: '#4a4a4a',
    marginBottom: 4,
  },
  card: {
    backgroundColor: 'white',
    borderRadius: 4,
    padding: 10,
    width: 200,
    height: 95,
    ...getPlatformElevation(3),
    position: 'relative',
    marginBottom: 10,
  },
  cardContent: {
    flexDirection: 'row',
    paddingTop: 10,
    paddingBottom: 5,
  },
  cardFooter: {
    position: 'absolute',
    right: 10,
    bottom: 7,
  },
  transferImg: {
    width: '40%',
    marginRight: '5%',
    alignItems: 'center',
  },
  transferDetails: {
    width: '60%',
  },
  vehicleImg: {
    width: 77,
    height: 42,
    resizeMode: 'contain',
  },
});

export default ItineraryTransferCard;
