import React from 'react';
import { StyleSheet, View, TouchableOpacity, Image, Text } from 'react-native';
import AtomicCss from '@mmt/legacy-commons/Styles/AtomicCss';
import getPlatformElevation from '@mmt/legacy-commons/Styles/getPlatformElevation';
import UserRating from '../../../Components/UserRating';
import { PresalesImages } from '../../../utils/PresalesImages';
import {has, isArray, isEmpty} from 'lodash';
import PlaceholderImageView from '@mmt/legacy-commons/Common/Components/PlaceholderImageView';
import {findDaysBetweenDates} from '@mmt/legacy-commons/Common/utils/DateUtils';
import { holidayColors } from '../../../../Styles/holidayColors';

const StarRating = ({ rating }) => {
  let stars = [];
  let i = 1;
  for (i = 1; i <= 5; i++) {
    let path = PresalesImages.StarFilled;
    if (i > rating) {
      path = PresalesImages.StarUnfilled;
    }
    stars.push(<Image key={i} style={styles.starImg} source={{ uri: path }} />);
  }
  return <View style={{flexDirection: 'row'}}>{stars}</View>;
};

function getMainImage(imageDataList) {
  if (!imageDataList) {
    return '';
  }

  const image = imageDataList.filter((item) => {
    if (has(item, 'mainImage') && item.mainImage) {
      return item.mainImage.path;
    }
  });

  if (image) {
    return image[0]?.mainImage.path;
  } else {
    return '';
  }
}

const ItineraryHotelCard = (props) => {
  const { handlePopup, item } = props;
  const { hotel } = item || {};
  const {checkInDate, checkOutDate, roomTypes, locationInfo} = hotel || {};
  const { roomInformation } = item  || {};
  const { imageDataList } = roomInformation || {};
  const mainImageUrl = getMainImage(imageDataList);
  const numberOfNights = findDaysBetweenDates(checkInDate, checkOutDate);

  const roomType = (!isEmpty(roomTypes) && isArray(roomTypes) && roomTypes.length > 0 && has(roomTypes[0], 'ratePlan')) ? roomTypes[0] : {};
  const { ratePlan = {} } = roomType;
  const { mealName } = ratePlan || '';
  const {areaName, cityName} = locationInfo || '';
  const HOTEL_LOCATION = (!!areaName && !!cityName) ? areaName + ' , ' + cityName : cityName;

  return (
    <View style={[styles.card]}>
      {!!item.city && (
        <View>
          <Text numberOfLines={1} style={styles.hotelsMeta}>
            <Text style={AtomicCss.boldFont}>Hotel </Text>
            <Text style={{color:holidayColors.gray}}>in{' '}</Text>
            <Text style={AtomicCss.boldFont}>{item.city} </Text>
            <Text style={AtomicCss.boldFont}> |  {numberOfNights} {numberOfNights > 1 ? 'Nights' : 'Night'} </Text>
          </Text>
        </View>
      )}
      <View style={[AtomicCss.marginTop5, {marginLeft: -4}]}>
        {!!item.hotelName && (
          <Text numberOfLines={2} style={[AtomicCss.font11, AtomicCss.blackText, AtomicCss.boldFont]}>
            {item.hotelName.length > 30 ? item.hotelName.slice(0, 30).concat('...') : item.hotelName}
          </Text>
        )}
        {!!item.starRating && (
        <Text style={AtomicCss.marginTop5}>
          <StarRating rating={item.starRating} />
        </Text>
      )}
      </View>
      <View style={styles.cardContent}>
        <View style={styles.transferImg}>
          <PlaceholderImageView url={ mainImageUrl } style={styles.iconHotel} />
          {!!item?.mmtRatingInfo?.userRating && (
            <View style={styles.userRatingWrap}>
              <UserRating rating={item?.mmtRatingInfo?.userRating} />
            </View>
          )}
        </View>
        <View style={styles.transferDetails}>
          {!!item.city && <Text numberOfLines={1} style={styles.address}>{HOTEL_LOCATION}</Text>}
          <View>
            {!!item.roomType && <Text numberOfLines={1} style={styles.roomType}>{item.roomType}</Text>}
            {!!mealName && <Text numberOfLines={2} style={{...styles.roomType, marginTop: 2}}>{mealName} Included</Text>}
          </View>
        </View>
      </View>
      <TouchableOpacity
        style={styles.cardFooter}
        hitSlop={{ top: 20, bottom: 20, left: 50, right: 50 }}
        onPress={handlePopup}>
        <Text style={[AtomicCss.font8, AtomicCss.azure, AtomicCss.boldFont, AtomicCss.textUppercase]}>
          View Details
        </Text>
      </TouchableOpacity>
    </View>
  );
};

const styles = StyleSheet.create({
  card: {
    backgroundColor: 'white',
    borderRadius: 4,
    padding: 10,
    width: 200,
    height: 135,
    ...getPlatformElevation(3),
    position: 'relative',
    marginBottom: 10,
  },
  hotelsMeta:{
    ...AtomicCss.font10,
    ...AtomicCss.blackText,
    ...AtomicCss.regularFont,
    marginLeft: -3,
  },
  cardContent: {
    flexDirection: 'row',
    paddingTop: 10,
    paddingBottom: 5,
    flexWrap: 'wrap',
  },
  cardFooter: {
    position: 'absolute',
    right: 10,
    bottom: 7,
  },
  roomType:{
    ...AtomicCss.font8,
    ...AtomicCss.regularFont,
    ...AtomicCss.blackText,
    marginTop:10,
    marginLeft:5,
  },
  address:{
    ...AtomicCss.font10,
    ...AtomicCss.boldFont,
    ...AtomicCss.blackText,
    marginLeft:5,
  },
  transferImg: {
    width: '40%',
    marginRight: '5%',
    alignItems: 'center',
    position: 'relative',
  },
  transferDetails: {
    width: '55%',
  },
  iconHotel: {
    width: 82,
    height: 55,
    resizeMode: 'cover',
    borderRadius: 4,
  },
  userRatingWrap: {
    position: 'absolute',
    left: 2,
    bottom: 5,
  },
  starImg: {
    width: 8,
    height: 8,
    resizeMode: 'contain',
    marginHorizontal: 2,
  },
});

export default ItineraryHotelCard;
