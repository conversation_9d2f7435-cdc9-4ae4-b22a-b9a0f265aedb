import React from 'react';
import { StyleSheet, View, Text } from 'react-native';
import AtomicCss from '@mmt/legacy-commons/Styles/AtomicCss';
import getPlatformElevation from '@mmt/legacy-commons/Styles/getPlatformElevation';

const ItineraryHeadFloatingCard = ({ data }) => {
  return (
    <View style={[AtomicCss.flexRow, AtomicCss.alignCenter, { backgroundColor: 'red' }]}>
      {data.map((item, i) => (
        <View key={i} style={[styles.headCard]}>
          <View style={i === 0 ? [styles.cardContent, styles.cardSeparator] : styles.cardContent}>
            <View style={[AtomicCss.flexRow, AtomicCss.alignCenter, AtomicCss.flexWrap]}>
              <View style={[AtomicCss.flexRow, AtomicCss.alignCenter, AtomicCss.marginBottom3]} />
              <View style={[AtomicCss.marginLeft12, AtomicCss.marginRight10]}>
                <Text />
              </View>
              <View style={[AtomicCss.flexRow, AtomicCss.alignCenter, AtomicCss.marginBottom3]}>
                {!!item.duration && (
                  <Text
                    style={[
                      AtomicCss.red2Text,
                      AtomicCss.boldFont,
                      AtomicCss.font12,
                      AtomicCss.marginRight5,
                    ]}
                  >
                    {}
                  </Text>
                )}
                <Text style={[AtomicCss.blackText, AtomicCss.boldFont, AtomicCss.font12]}>
                  {}
                </Text>
              </View>
            </View>
            <View
              style={[
                AtomicCss.flexRow,
                AtomicCss.alignCenter,
                AtomicCss.spaceBetween,
                AtomicCss.paddingTop3,
              ]}
            >
              <View style={[AtomicCss.flexRow, AtomicCss.alignCenter]}>
                <Text style={[AtomicCss.font12, AtomicCss.boldFont, AtomicCss.blackText]}>
                  {}
                </Text>
              </View>
              <View style={[AtomicCss.flexRow, AtomicCss.alignCenter]}>
                <Text style={[AtomicCss.font16, AtomicCss.blackFont, AtomicCss.blackText]}>
                  {' '}
                </Text>

              </View>
            </View>
          </View>
        </View>
      ))}
    </View>
  );
};

const styles = StyleSheet.create({
  headCard: {
    backgroundColor: 'white',
    width: 220,
    ...getPlatformElevation(3),
  },
  cardContent: {
    padding: 10,
  },
  cardSeparator: {
    borderRightWidth: 1,
  },
});

export default ItineraryHeadFloatingCard;
