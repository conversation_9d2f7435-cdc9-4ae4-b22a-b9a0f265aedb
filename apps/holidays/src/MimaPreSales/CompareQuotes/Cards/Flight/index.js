import React from 'react';
import { StyleSheet, View, TouchableOpacity, Text } from 'react-native';
import { flightDuration } from '../../../../ChangeFlight/HolidayFlightUtils';
import HolidayImageHolder from '../../../../Common/Components/HolidayImageHolder';
import { getFlightTime, parseFlightDate } from '../../../../PhoenixDetail/Utils/FlightUtils';
import { getAirlineIconUrl } from '../../../../PhoenixDetail/Components/FlightDetailPage/FlightListing/FlightsUtils';
import AtomicCss from '@mmt/legacy-commons/Styles/AtomicCss';
import getPlatformElevation from '@mmt/legacy-commons/Styles/getPlatformElevation';

const ItineraryFlightCard = (props) => {
  const { handlePopup, item } = props;
  return (
    <View style={[styles.card]}>
      {!!item?.fromAirport?.airportCity && (
        <View>
          <Text
            numberOfLines={1}
            style={[AtomicCss.font10, AtomicCss.blackText, AtomicCss.regularFont]}
          >
            <Text style={AtomicCss.boldFont}>Flight</Text>
            <Text style={{color:'#7a7a7a'}}> from{' '}</Text>
            <Text style={AtomicCss.boldFont}>
              {item.fromAirport.airportCity} to {item.toAirport.airportCity}{' '}
            </Text>
          </Text>
        </View>
      )}
      <View style={styles.cardContent}>
        <View style={styles.flightName}>
          {!!item.airlineCode && (
            <View style={styles.logoWrap}>
              <HolidayImageHolder
                imageUrl={getAirlineIconUrl(item.airlineCode)}
                style={styles.iconAirlineLogo}
              />
            </View>
          )}
          {!!item.airlineName && (
            <Text
              style={[
                AtomicCss.font8,
                AtomicCss.defaultText,
                AtomicCss.regularFont,
                AtomicCss.paddingTop3,
              ]}
            >
              {item.airlineName}
            </Text>
          )}
        </View>
        <View style={styles.flightDetails}>
          <View>
            {!!item.flightLegs[0]?.departure && (
              <Text style={[AtomicCss.font12, AtomicCss.blackFont, AtomicCss.blackText]}>
                {getFlightTime(parseFlightDate(item.flightLegs[0].departure))}
              </Text>
            )}
            {!!item.fromAirport.airportCode && (
              <Text
                style={[
                  AtomicCss.font10,
                  AtomicCss.regularFont,
                  AtomicCss.defaultText,
                  AtomicCss.paddingTop3,
                ]}
              >
                {item.fromAirport.airportCode}
              </Text>
            )}
          </View>
          <View style={AtomicCss.alignCenter}>
            {!!item.flightLegs[0]?.departure && (
              <Text style={[AtomicCss.font10, AtomicCss.regularFont, AtomicCss.blackText]}>
                {flightDuration(item.flightLegs[0].duration)}
              </Text>
            )}
            <View style={styles.layoverWrap}>
              <View style={styles.layoverLine} />
              <View style={styles.layoverCircle} />
              {item.stops == 1 ? <View style={styles.layoverCircle} /> : null}
              <View style={styles.layoverCircle} />
            </View>
            {!!item.stops && (
              <Text style={[AtomicCss.font9, AtomicCss.boldFont, AtomicCss.defaultText]}>
                {item.stops} stop
              </Text>
            )}
          </View>
          <View>
            {!!item.flightLegs[0].arrival && (
              <Text style={[AtomicCss.font12, AtomicCss.blackFont, AtomicCss.blackText]}>
                {getFlightTime(parseFlightDate(item.flightLegs[0].arrival))}
              </Text>
            )}
            {!!item.toAirport?.airportCode && (
              <Text
                style={[
                  AtomicCss.font10,
                  AtomicCss.regularFont,
                  AtomicCss.defaultText,
                  AtomicCss.paddingTop3,
                ]}
              >
                {item.toAirport.airportCode}
              </Text>
            )}
          </View>
        </View>
      </View>
      <TouchableOpacity
        style={styles.cardFooter}
        hitSlop={{ top: 20, bottom: 20, left: 50, right: 50 }}
        onPress={handlePopup}
      >
        <View>
          <Text style={[AtomicCss.font8, AtomicCss.azure, AtomicCss.boldFont, AtomicCss.textUppercase]}>
            View Details
          </Text>
        </View>
      </TouchableOpacity>
    </View>
  );
};

const styles = StyleSheet.create({
  card: {
    backgroundColor: 'white',
    borderRadius: 4,
    padding: 10,
    width: 200,
    height: 95,
    ...getPlatformElevation(3),
    position: 'relative',
    marginBottom: 10,
  },

  iconAirlineLogo: {
    width: 25,
    height: 25,
    resizeMode: 'cover',
  },
  imgMultiFlightLogo: {
    width: 16,
    height: 16,
    resizeMode: 'cover',
    borderWidth: 1,
  },
  layoverWrap: {
    width: 50,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    position: 'relative',
    marginVertical: 3,
  },
  layoverCircle: {
    width: 4,
    height: 4,
    borderRadius: 4,
    borderWidth: 1,
    borderColor: '#4a4a4a',
  },

  layoverLine: {
    width: 40,
    height: 1,
    backgroundColor: '#e7e7e7',
    position: 'absolute',
    left: 5,
  },
  flightName: {
    width: '20%',
    marginRight: '5%',
    alignItems: 'center',
  },
  flightDetails: {
    width: '75%',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  logoWrap: {
    flexDirection: 'row',
    height: 28,
  },
  cardContent: {
    flexDirection: 'row',
    paddingTop: 10,
    paddingBottom: 5,
    alignItems: 'flex-start',
  },
  cardFooter: {
    position: 'absolute',
    right: 10,
    bottom: 7,
  },
  secLogo: {
    position: 'relative',
    top: 5,
    left: -5,
  },
});

export default ItineraryFlightCard;
