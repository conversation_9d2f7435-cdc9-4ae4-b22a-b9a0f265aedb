import React from 'react';
import {Image, StyleSheet, Text, View} from 'react-native';
import AtomicCss from '@mmt/legacy-commons/Styles/AtomicCss';
import rightArrow from '@mmt/legacy-assets/src/Images/right_arrow.webp';
import {fonts} from '@mmt/legacy-commons/Styles/globalStyles';
import { holidayColors } from '../../../../Styles/holidayColors';

const ItineraryHeadCard = ({ data}) => {
  return (
    <View style={[AtomicCss.flexRow, {backgroundColor:'#f6f5f5'}]}>
      {data.map((item, i) => (
        <View key={i} style={[styles.headCard, styles.headCardInnerWrapRight]}>
            <View style={[styles.cardContent,AtomicCss.flexRow, AtomicCss.alignCenter, AtomicCss.flexWrap, AtomicCss.flexRow, AtomicCss.alignCenter, AtomicCss.marginBottom3]}>
                <View style={styles.headingContainer}>
                  {item.destinations && item.destinations.map((destination, index) => {
                    const { duration, name } = destination || {};
                    return (
                        <View key={index} flexDirection={'row'}>
                          <Text style={styles.duration}>{duration}N </Text>
                          <Text style={styles.destination}>{name}</Text>
                          {item.destinations.length - 1 === index ? [] : <Image source={rightArrow} style={styles.rightArrow}/>}
                        </View>
                    );
                  })}
            </View>
            <View>
              <Text numberOfLines={2} style={[AtomicCss.font9, AtomicCss.regularFont, AtomicCss.defaultText]}>
                {item.roomInfo} • From {item.departureCity} • {item.startDate} - {item.endDate}
              </Text>
            </View>
          </View>
        </View>
      ))}
    </View>
  );
};

const styles = StyleSheet.create({
  headCard: {
    backgroundColor: holidayColors.white,
    borderTopEndRadius: 4,
    borderTopStartRadius:4,
    width: 213,
    // height: 75,
    position: 'relative',
    justifyContent: 'space-between',
  },
  cardContent: {
    padding: 10,
    // height: 67,
    justifyContent: 'space-between',
  },
  cardFooter: {
    backgroundColor: holidayColors.lightBlueBg,
    borderBottomLeftRadius: 4,
    borderBottomRightRadius: 4,
    padding: 10,
  },
  headingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
    flexWrap: 'wrap',
  },
  rightArrow: {
    width: 7,
    height: 7,
    marginTop: 5,
    marginLeft:8,
    marginRight: 8,
  },
  duration: {
    fontSize: 14,
    color: holidayColors.red,
    fontFamily: fonts.bold,
  },
  destination: {
    fontSize: 14,
    color: '#000000',
    fontFamily: fonts.bold,
  },
  arrowRight: {
    width: 0,
    height: 0,
    backgroundColor: 'transparent',
    borderStyle: 'solid',
    borderTopWidth: 4,
    borderRightWidth: 0,
    borderBottomWidth: 4,
    borderLeftWidth: 6,
    borderTopColor: 'transparent',
    borderRightColor: 'transparent',
    borderBottomColor: 'transparent',
    borderLeftColor: '#9b9b9b',
  },
  headCardInnerWrapLeft: {
    margin: 5,
    marginRight: 2.5,
  },
  headCardInnerWrapRight: {
    marginTop: 5,
    marginRight:5,
    marginLeft: 2.5,
  },
  iconDownArrow: {
    width: 6.25,
    height: 6.25,
    resizeMode: 'contain',
    marginLeft: 5,
  },
});

export default ItineraryHeadCard;
