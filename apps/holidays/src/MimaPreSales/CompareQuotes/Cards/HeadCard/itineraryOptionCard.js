import React from 'react';
import { StyleSheet, View, TouchableOpacity, Text, Image } from 'react-native';
import AtomicCss from '@mmt/legacy-commons/Styles/AtomicCss';
import getPlatformElevation from '@mmt/legacy-commons/Styles/getPlatformElevation';
import { PresalesImages } from '../../../utils/PresalesImages';

const ItineraryOptionCard = ({ data, openSheet, showCompareOptions }) => {
  const formatPrice = (price) => price.split('.')[0];

  return (
    <View style={[AtomicCss.flexRow, {backgroundColor:'#f6f5f5'}]}>
      {data.map((item, i) => (
        <View key={i} style={[styles.headCard, styles.headCardInnerWrapRight]}>
          <View style={styles.cardFooter}>
            <View style={[AtomicCss.flexRow, AtomicCss.alignCenter, AtomicCss.spaceBetween]}>
              {showCompareOptions && <TouchableOpacity onPress={openSheet}>
                <View style={[AtomicCss.flexRow, AtomicCss.alignCenter]}>
                  {!!item.version && (
                    <Text style={[AtomicCss.font11, AtomicCss.boldFont, AtomicCss.blackText]}>
                      Option {item.version}
                    </Text>
                  )}
                  <Image
                    source={{ uri: PresalesImages.iconDownArrow }}
                    style={styles.iconDownArrow}
                  />
                </View>
              </TouchableOpacity>}
              <View style={[AtomicCss.flexRow, AtomicCss.alignCenter]}>
                {!!item.price && (
                  <Text style={[AtomicCss.font12, AtomicCss.blackFont, AtomicCss.blackText]}>
                    {formatPrice(item.price)}{' '}
                  </Text>
                )}
                <Text style={[AtomicCss.font8, AtomicCss.blackText, AtomicCss.regularFont]}>
                  per person
                </Text>
              </View>
            </View>
            <View
              style={[
                AtomicCss.flexRow,
                AtomicCss.spaceBetween,
                AtomicCss.alignCenter,
                AtomicCss.paddingTop3,
              ]}
            >
              <View style={[AtomicCss.flexWrap]}>
                {!!item.agentName && !!item.createdAt && (
                  <Text style={[AtomicCss.font8, AtomicCss.blackFont, AtomicCss.regularFont]}>
                    Curated by {item.agentName} {''} {item.createdAt}
                  </Text>
                )}
              </View>
            </View>
          </View>
        </View>
      ))}
    </View>
  );
};

const styles = StyleSheet.create({
  headCard: {
    backgroundColor: 'white',
    borderRadius: 4,
    width: 213,
    height: 47,
    ...getPlatformElevation(3),
    position: 'relative',
    justifyContent: 'space-between',
    marginTop:6,
  },
  cardContent: {
    padding: 10,
    height: 67,
    justifyContent: 'space-between',
  },
  cardFooter: {
    backgroundColor: '#E3F2FE',
    borderBottomLeftRadius: 4,
    borderBottomRightRadius: 4,
    padding: 10,
  },
  arrowRight: {
    width: 0,
    height: 0,
    backgroundColor: 'transparent',
    borderStyle: 'solid',
    borderTopWidth: 4,
    borderRightWidth: 0,
    borderBottomWidth: 4,
    borderLeftWidth: 6,
    borderTopColor: 'transparent',
    borderRightColor: 'transparent',
    borderBottomColor: 'transparent',
    borderLeftColor: '#9b9b9b',
  },
  headCardInnerWrapLeft: {
    margin: 5,
    marginRight: 2.5,
  },
  headCardInnerWrapRight: {
    margin: 5,
    marginLeft: 2.5,
  },
  iconDownArrow: {
    width: 6.25,
    height: 6.25,
    resizeMode: 'contain',
    marginLeft: 5,
  },
});

export default ItineraryOptionCard;
