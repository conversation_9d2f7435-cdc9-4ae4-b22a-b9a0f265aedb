import React, {useEffect, useState} from 'react';
import AtomicCss from '@mmt/legacy-commons/Styles/AtomicCss';
import { StyleSheet, View, Text, TouchableOpacity } from 'react-native';
import getPlatformElevation from '@mmt/legacy-commons/Styles/getPlatformElevation';
import {isEmpty} from 'lodash';
import {getGoogleAPIKeyForAllPlarforms, getStaticMapUriForCoordinatesList} from '../../../../utils/HolidayUtils';
import isNumber from 'lodash/isNumber';
import PlaceholderImageView from '@mmt/legacy-commons/Common/Components/PlaceholderImageView';

const ItinerarySightSeeingCard = (props) => {
  const { panelData, handlePopup } = props;
  const [googleAPIKey, setGoogleAPIKey] = useState('');

  const fetchGoogleKey = async () => {
    const googleAPIKey = await getGoogleAPIKeyForAllPlarforms();
    setGoogleAPIKey(googleAPIKey);
  };

  useEffect(() => {
    fetchGoogleKey();
  }, []);


  let coordinateList = [];
  let googleMapImage = '';


  panelData.forEach(item => {
    if (isNumber(item.lat) && isNumber(item.long)) {
      coordinateList.push({ lat: item.lat, lon: item.long });
    }
    else if (isEmpty(googleMapImage) && item?.images?.length > 0) {
      googleMapImage = item?.images[0].path;
    }
  });

  if (coordinateList && coordinateList.length > 0){
    googleMapImage = getStaticMapUriForCoordinatesList(coordinateList, false, googleAPIKey);
  }

  const getCityName = () => {
    if (isEmpty(panelData)){
      return '';
    }
    const firstItem = panelData[0];
    const {cityName = ''} = firstItem || {};
    return cityName;
  };


  const fetchSightSeeingPoints = () => {
    if (panelData?.length > 3) {
      const sightNames = panelData.map(item => item.sightName);
      const sightName = sightNames.slice(0, 2).join(', ');
      const total = sightNames.length - 2;
      return sightName + `, +${total} more`;
    } else {
       return panelData
          .map((item) => item.sightName)
          .slice(0, panelData.length)
          .join(', ');
    }
  };

  return (
    <View style={[styles.card]}>
      <View>
        <Text
          numberOfLines={1}
          style={[AtomicCss.font10, AtomicCss.blackText, AtomicCss.regularFont]}>
          <Text style={AtomicCss.boldFont}>Sightseeing </Text>
          <Text style={{color:'#7a7a7a'}}>in{' '}</Text>
          <Text style={AtomicCss.boldFont}> {getCityName()}</Text>
        </Text>
      </View>
      <View style={styles.cardContent}>
        <View style={styles.cssImg}>
          {!!googleMapImage &&
          <PlaceholderImageView url={googleMapImage } style={styles.iconMap}/>
          }
        </View>
        <View style={styles.cssDetails}>
          <Text
            numberOfLines={3}
            style={[AtomicCss.font10, AtomicCss.boldFont, AtomicCss.blackText, AtomicCss.marginLeft5]}>
            {fetchSightSeeingPoints()}
          </Text>
        </View>
      </View>
      <TouchableOpacity style={styles.cardFooter} onPress={handlePopup}>
        <Text style={[AtomicCss.font8, AtomicCss.azure, AtomicCss.boldFont, AtomicCss.textUppercase]}>View Details</Text>
      </TouchableOpacity>
    </View>
  );
};

const styles = StyleSheet.create({
  card: {
    backgroundColor: 'white',
    borderRadius: 4,
    padding: 10,
    width: 200,
    height: 95,
    ...getPlatformElevation(3),
    position: 'relative',
    marginBottom: 10,
  },
  cardContent: {
    flexDirection: 'row',
    paddingTop: 10,
    paddingBottom: 5,
    alignItems: 'center',
    flexWrap: 'wrap',
  },
  cardFooter: {
    position: 'absolute',
    right: 10,
    bottom: 7,
  },
  cssImg: {
    width: '40%',
    marginRight: '5%',
    alignItems: 'center',
    position: 'relative',
  },
  cssDetails: {
    width: '55%',
    justifyContent: 'space-between',
    height: 50,
  },
  iconMap: {
    width: 82,
    height: 55,
    resizeMode: 'cover',
    borderRadius: 4,
  },
});

export default ItinerarySightSeeingCard;
