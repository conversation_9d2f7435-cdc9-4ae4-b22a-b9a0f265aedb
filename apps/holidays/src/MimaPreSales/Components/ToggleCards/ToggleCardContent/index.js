import React, { useState } from 'react';
import { View, StyleSheet, Text, TouchableOpacity, Image } from 'react-native';
import AtomicCss from '@mmt/legacy-commons/Styles/AtomicCss';
import { fonts } from '@mmt/legacy-commons/Styles/globalStyles';

const ToggleCardContent = (props) => {
  const { data, listType, bulletList } = props;

  const [textShown, setTextShown] = useState(false);
  const toggleTextShown = () => {
    setTextShown(!textShown);
  };
  let dataNew = textShown ? data : data.slice(0, 3);
  return (
    <View style={[AtomicCss.marginTop10]}>
      <View style={[AtomicCss.marginTop10]}>
        {dataNew.map((item, index) => {
          return (
            <View
              key={index}
              style={[AtomicCss.flexRow, AtomicCss.alignCenter, AtomicCss.marginBottom10]}
            >
              <View style={[AtomicCss.marginRight10]}>
                {bulletList == 'tickmark' ? (
                  <Text style={styles.bulletListTick}>{listType}</Text>
                ) : (
                  <Text style={styles.bulletListDot}>{listType}</Text>
                )}
              </View>
              <View>
                <Text style={[AtomicCss.defaultText, AtomicCss.font12, AtomicCss.regularFont]}>
                  {item}
                </Text>
              </View>
            </View>
          );
        })}
      </View>
      <TouchableOpacity onPress={() => toggleTextShown()}>
        <View style={[AtomicCss.marginTop10]}>
          <Text
            style={[AtomicCss.azure, AtomicCss.boldFont, AtomicCss.font10, AtomicCss.textUpper]}
          >
            {textShown ? 'Read Less' : 'Read More'}
          </Text>
        </View>
      </TouchableOpacity>
    </View>
  );
};

const styles = StyleSheet.create({
  bulletListTick: {
    color: '#33d18f',
    fontFamily: fonts.bold,
    fontSize: 9,
  },
  bulletListDot: {
    color: '#4a4a4a',
    fontSize: 9,
    fontFamily: fonts.bold,
  },
});

export default ToggleCardContent;
