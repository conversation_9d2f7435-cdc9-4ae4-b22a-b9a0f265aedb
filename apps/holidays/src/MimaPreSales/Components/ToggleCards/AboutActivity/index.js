import React from 'react';
import {Text, View} from 'react-native';
import AtomicCss from '@mmt/legacy-commons/Styles/AtomicCss';

const AboutActivity = ({ data }) => {

    const {activityDescription} =  data || {};

  return (
    <View>
      <View style={[AtomicCss.marginTop10]}>
        {!!activityDescription && (
          <Text style={[AtomicCss.font12, AtomicCss.defaultText, AtomicCss.regularFont]}>
            {activityDescription}
          </Text>
        )}
      </View>
    </View>
  );
};

export default AboutActivity;
