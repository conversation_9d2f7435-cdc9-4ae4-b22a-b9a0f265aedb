import React from 'react';
import {View, TouchableOpacity,Text} from 'react-native';
import PropTypes from 'prop-types';
import HolidayImageHolder from '../../../../Common/Components/HolidayImageHolder';
import {CapsuleCardCss} from '../../phoenixCss';

const CapsuleCard = ({card, style = CapsuleCardCss, onPress}) => {
  if (card && card.deeplink) {
    return (
      <TouchableOpacity
        onPress={() => onPress(card)}
        activeOpacity={0.8}
      >
        <View style={style.container}>
          <HolidayImageHolder
            style={[style.logoImg, card.name ? null : {marginRight: 0}]}
            imageUrl={card.image}
          />
          {!!card.name && <Text numberOfLines={1} style={style.title}>{card.name}</Text>}
        </View>
      </TouchableOpacity>
    );
  }
  return [];
};

CapsuleCard.propTypes = {
  card: PropTypes.object.isRequired,
  onPress: PropTypes.func.isRequired,
  style: PropTypes.object,
};

export default CapsuleCard;
