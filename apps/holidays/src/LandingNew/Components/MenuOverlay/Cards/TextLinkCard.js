import React from 'react';
import {TouchableOpacity, Text} from 'react-native';
import PropTypes from 'prop-types';
import {TextLinkCardCss} from '../../phoenixCss';

const TextLinkCard = ({card, onPress, style = TextLinkCardCss}) => {
  if (card && card.name) {
    const link = !!card.deeplink;
    return (
      <TouchableOpacity
        activeOpacity={0.8}
        onPress={() => onPress(card)}
        disabled={!link}
        style={style.container}
      >
        <Text numberOfLines={3} style={style.title}>{card.name}</Text>
      </TouchableOpacity>
    );
  }
 return [];
};

TextLinkCard.propTypes = {
  card: PropTypes.object.isRequired,
  onPress: PropTypes.func.isRequired,
  style: PropTypes.object,
};

export default TextLinkCard;
