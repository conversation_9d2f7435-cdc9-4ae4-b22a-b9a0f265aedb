import React from 'react';
import {TouchableOpacity, Text} from 'react-native';
import PropTypes from 'prop-types';
import HolidayImageHolder from '../../../../Common/Components/HolidayImageHolder';
import {ImageOfferCard2} from '../../phoenixCss';

const ImageOfferCard = ({card, onPress, style = ImageOfferCard2}) => {
  if (card && card.deeplink) {
    return (
      <TouchableOpacity
        onPress={() => onPress(card)}
        activeOpacity={0.7}
        style={style.container}
      >
        <HolidayImageHolder imageUrl={card.image} style={style.image} />
        {!!card.name && <Text style={style.text} numberOfLines={2}>{card.name}</Text>}
      </TouchableOpacity>
    );
  }
  return [];
};

ImageOfferCard.propTypes = {
  card: PropTypes.object.isRequired,
  onPress: PropTypes.func.isRequired,
  style: PropTypes.object,
};

export default ImageOfferCard;
