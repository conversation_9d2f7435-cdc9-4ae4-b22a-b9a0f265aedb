import React from 'react';
import {View, Text, TouchableOpacity} from 'react-native';

const TabBtn = ({item, active, onPress, style, index}) => {
  const containerStyle = [style.tabContainer];
  const textStyle = [style.text];
  if (active) {
    containerStyle.push(style.activeContainer);
    textStyle.push(style.activeText);
  }
  if (item.name) {
    return (
      <TouchableOpacity
        onPress={() => onPress(item, index)}
        activeOpacity={0.8}
        style={style.cardContainer}
        disabled={!!active}
      >
      <View style={containerStyle}>
          <Text style={textStyle}>{item.name}</Text>
        </View>
      </TouchableOpacity>
    );
  }
  return [];
};


export default TabBtn;
