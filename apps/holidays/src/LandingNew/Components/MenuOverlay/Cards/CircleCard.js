import React from 'react';
import {View, Text, TouchableOpacity} from 'react-native';
import PropTypes from 'prop-types';
import HolidayImageHolder from '../../../../Common/Components/HolidayImageHolder';
import {CircleCardCss} from '../../phoenixCss';

const CircleCard = ({card, onPress, style = CircleCardCss}) => {
  if (card && card.deeplink) {
    return (
      <View style={style.cardContainer}>
        <TouchableOpacity
          onPress={() => onPress(card)}
          activeOpacity={0.8}
        >
          <View style={style.cardContainer2}>
            <HolidayImageHolder imageUrl={card.image} style={style.image} />
            {!!card.name && <Text numberOfLines={2} style={style.heading}>{card.name}</Text>}
          </View>
        </TouchableOpacity>
      </View>
    );
  }
  return null;
};

CircleCard.propTypes = {
  card: PropTypes.object.isRequired,
  onPress: PropTypes.func.isRequired,
  style: PropTypes.object.isRequired,
};

export default CircleCard;
