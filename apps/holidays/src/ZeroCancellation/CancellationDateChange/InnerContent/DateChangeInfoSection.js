import React from 'react';
import {Image, StyleSheet, Text, View} from 'react-native';
import DateCancellationPrice from './DateCancellationPrice';
import fdIcon from '../images/fdIcon.webp';
import ZCIcon from '../images/ZCIcon.webp';
import AtomicCss from '@mmt/legacy-commons/Styles/AtomicCss';
import {DETAIL_TRACKING_PAGE_NAME} from '../../../PhoenixDetail/DetailConstants';

export const WITH_ZC = 'with_zc';
export const WITHOUT_ZC = 'without_zc';

const DateChangeInfoSection = ({activePlan, withoutPlan, flexiDate, ZC, penalties, ZCData, activeIndex, travellerCount, type, pageName}) => {
    return (
        <View style={activePlan ? [AtomicCss.flex1, styles.activePlanSection] : [AtomicCss.flex1]}>
            <View style={activePlan ? [styles.activeWrapper] : ''}>
                <View style={styles.plansHeading}>
                    {withoutPlan ? <Text style={[AtomicCss.font12, AtomicCss.boldFont, AtomicCss.blackText]}>Without
                        plan </Text> : null}
                    {flexiDate ? <Image style={styles.fdIcon} source={fdIcon}/> : null}
                    {ZC ? <Image style={styles.zcIcon} source={ZCIcon}/> : null}
                </View>
                <PriceView penalties={penalties} activePlan={activePlan} ZCData={ZCData} activeIndex={activeIndex}
                           travellerCount={travellerCount} type={type} pageName={pageName}/>
            </View>
        </View>

    );
};

export const PriceView = ({penalties, activePlan, ZCData, activeIndex, travellerCount, type, pageName}) => {
    let viewList = [];
    const bcColor = ['#eaeef2', '#F8FCFF', '#eaeef2', '#F8FCFF', '#eaeef2'];
    const tc = travellerCount ? travellerCount : 1;

    if (penalties) {
      penalties.forEach((penalty, index) => {
        const { nonRefundable } = penalty;
        var penaltyAmount = geCancellationPenaltyAmount(penalty, type);
        let finalPenalty = getFinalPenaltyAmount(pageName, tc, penaltyAmount);
        let node = <DateCancellationPrice bgColorGrey={activePlan ? '#ffffff' : bcColor[index]}
                                          penalty={finalPenalty}
                                          nonRefundable={nonRefundable}
                                          activePlan={false}
                                          ZCData={ZCData}
                                          key={index}
                                          activeIndex={activeIndex}
                                          pageName={pageName}/>;
        viewList.push(node);
      });
    }
    return (<View>{viewList}</View>);
};

export const geCancellationPenaltyAmount = (p, type) => {
    const {nonRefundable, penalty, withZCPenalty} = p || {};
    if (nonRefundable) {
        return 0;
    } else if (type === WITHOUT_ZC) {
        return penalty;
    } else if (type === WITH_ZC) {
        return withZCPenalty;
    }
    return penalty;
};

const getFinalPenaltyAmount = (pageName, tc, penaltyAmount) => {
    if (pageName && pageName === DETAIL_TRACKING_PAGE_NAME) {
        return Math.ceil(penaltyAmount / tc);
    }
    return penaltyAmount;
};

const styles = StyleSheet.create({
    fdIcon: {width: 82, height: 21},
    zcIcon: {alignSelf: 'center', resizeMode: 'stretch', width: 90, height: 19, marginLeft: -10},
    ZCContainer: {marginHorizontal: 28},
    activePlanSection: {
        backgroundColor: '#fff', shadowColor: '#330000',
        shadowOpacity: 0.3,
        shadowRadius: 2,
        shadowOffset: {
            width: 0,
            height: 2,
        },
        elevation: 6,
        zIndex: 4, borderTopLeftRadius: 18, borderTopRightRadius: 18, marginTop: 6,
    },
    plansHeading: {height: 54, paddingTop: 28, paddingLeft: 20},
    ZCSection: {backgroundColor: '#f9fcff', marginBottom: 24},
    height54: {height: 54},
    activeWrapper: {marginTop: -6},

});

export default DateChangeInfoSection;
