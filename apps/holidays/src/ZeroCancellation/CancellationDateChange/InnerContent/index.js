import React, { useEffect } from 'react';
import { StyleSheet, Text, View } from 'react-native';
import TabButtons from './TabButtons';
import DateCancellationInfo from './DateCancellationInfo';
import BulletInfo from './BulletInfo';
import DateCancellationPrice from './DateCancellationPrice';
import DateChangeInfoSection, {WITH_ZC, WITHOUT_ZC} from './DateChangeInfoSection';
import AtomicCss from '@mmt/legacy-commons/Styles/AtomicCss';
import {addDays, getFormattedDate, getDateObjectFromDate, getFormattedDateFromDate} from '@mmt/legacy-commons/Helpers/dateHelpers';
import {fetchDayPostFix, getStarConditionText} from '../../../PhoenixDetail/Utils/HolidayDetailUtils';
import { isZCAvailable } from '../../../utils/HolidayUtils';
import {isEmpty} from 'lodash';
import {DETAIL_TRACKING_PAGE_NAME} from '../../../PhoenixDetail/DetailConstants';
import { PDTConstants } from '../../../Review/HolidayReviewConstants';

export const CANCELLATION = 0;
export const DATE_CHANGE = 1;
const FLEXI_DATE = 'FlexiDate';
const ZERO_CANCELLATION = 'ZC';

const InnerContent = ({ cancellationPolicyData, onHandleTabChange, activeTabIndex, zcResponse, travellerCount, pageName }) => {
  const starConditionText = getStarConditionText(cancellationPolicyData, activeTabIndex, pageName);

  return (
    <View style={styles.ZCContainer}>
      <Text style={[AtomicCss.blackFont, AtomicCss.font12, AtomicCss.blackText, AtomicCss.marginBottom20, AtomicCss.marginTop15]}>CANCELLATION
        POLICIES</Text>
      <View style={[AtomicCss.flexRow, AtomicCss.alignCenter, AtomicCss.justifyCenter]}><TabButtons onHandleTabChange={onHandleTabChange} activeTabIndex={activeTabIndex}/></View>
      {/* Normal date section */}
      <NormalCancellation cancellationPolicyData={cancellationPolicyData} activeTabIndex={activeTabIndex} travellerCount={travellerCount} pageName={pageName}/>

      {/* flexi date section */}
      <ZeroCancellation cancellationPolicyData={cancellationPolicyData} activeTabIndex={activeTabIndex} zcResponse={zcResponse} travellerCount={travellerCount} pageName={pageName}/>

      {!isEmpty(starConditionText) &&
      <Text style={[AtomicCss.font12, AtomicCss.lightGrey, AtomicCss.regularFont, AtomicCss.marginBottom16, AtomicCss.marginLeft12]}>{starConditionText}</Text>}

      <View style={[AtomicCss.marginBottom15]}>
        <PolicyList cancellationPolicyData={cancellationPolicyData} activeTabIndex={activeTabIndex}/>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  ZCContainer: { marginHorizontal: 28 },
  activePlan: {
    backgroundColor: '#fff', shadowColor: '#330000',
    shadowOpacity: 0.3,
    shadowRadius: 2,
    shadowOffset: {
      width: 0,
      height: 2,
    },
    elevation: 6,
    zIndex: 4, borderTopLeftRadius: 18, borderTopRightRadius: 18, marginTop: 6,
  },
  plansHeading: { height: 54, paddingTop: 28, paddingLeft: 20 },
  fdIcon: { width: 74, height: 19 },
  ZCSection: { backgroundColor: '#f9fcff', marginBottom: 24 },
  height54: { height: 54 },
  activeWrapper: { marginTop: -6 },

});

export const NormalCancellation = ({ cancellationPolicyData, activeTabIndex, travellerCount, pageName }) => {
  const { penaltyDetail } = cancellationPolicyData;
  const { zcOptions } = penaltyDetail;

  // Hide normal cancellation view if ZCOptions are available.
  if ((activeTabIndex === CANCELLATION && isZCAvailable(zcOptions))
    || (activeTabIndex === DATE_CHANGE && isFlexiDateAvailable(zcOptions))
  ) {
    return null;
  }

  return (<View style={[AtomicCss.flexRow, styles.ZCSection]}>
    <View style={AtomicCss.flex1}>
      <LeftRow cancellationPolicyData={cancellationPolicyData} activeIndex={activeTabIndex} zeroCancellation={false}/>
    </View>
    <View style={AtomicCss.flex1}>
      <RightRow data={cancellationPolicyData} activeIndex={activeTabIndex} travellerCount={travellerCount} pageName={pageName}/>
    </View>
  </View>);
};

export const ZeroCancellation = ({ cancellationPolicyData, activeTabIndex, zcResponse, travellerCount, pageName }) => {

  const { penaltyDetail } = cancellationPolicyData;
  const { zcOptions } = penaltyDetail;

  // Hide zero cancellation view if ZC Options are not available.
  if ((activeTabIndex === CANCELLATION && !isZCAvailable(zcOptions))
    || (activeTabIndex === DATE_CHANGE && !isFlexiDateAvailable(zcOptions))
  ) {
    return null;
  }

  const ZCData = getZCData(zcOptions, activeTabIndex);

  const penalties = getCancellationPenalties(cancellationPolicyData, activeTabIndex);

  return (<View style={[AtomicCss.flexRow, styles.ZCSection]}>
    <View style={AtomicCss.flex1}>
      <View style={styles.height54}/>
      <LeftRow cancellationPolicyData={cancellationPolicyData} activeIndex={activeTabIndex}/>
    </View>
    <MiddleRowZC data={cancellationPolicyData} activeIndex={activeTabIndex} penalties={penalties} zcResponse={zcResponse} ZCData={ZCData} travellerCount={travellerCount} pageName={pageName}/>
    <LastRowZC data={cancellationPolicyData} activeIndex={activeTabIndex} penalties={penalties} ZCData={ZCData} zcResponse={zcResponse} ZCData={ZCData} travellerCount={travellerCount} pageName={pageName}/>
  </View>);
};

export const MiddleRowZC = ({ activeIndex, penalties, zcResponse, travellerCount, pageName, ZCData }) => {
  const activePlan = !isPlanActive(activeIndex, zcResponse, ZCData);
  return (<DateChangeInfoSection activePlan={activePlan} withoutPlan={true} penalties={penalties} activeIndex={activeIndex} travellerCount={travellerCount} type={WITHOUT_ZC} pageName={pageName}/>);
};

export const LastRowZC = ({ activeIndex, penalties, ZCData, zcResponse, travellerCount, pageName }) => {
  const activePlan = isPlanActive(activeIndex, zcResponse, ZCData);
  return (<DateChangeInfoSection
    activePlan={activePlan}
    flexiDate={activeIndex === DATE_CHANGE}
    ZC={activeIndex === CANCELLATION}
    penalties={penalties}
    activeIndex={activeIndex}
    travellerCount={travellerCount}
    type={WITH_ZC}
    ZCData={ZCData}
    pageName={pageName}/>);
};

export const LeftRow = ({ cancellationPolicyData, activeIndex }) => {
  const penalties = getCancellationPenalties(cancellationPolicyData, activeIndex);

  if (penalties) {
    return (<PNP penalties={penalties}/>);
  }
  return null;
};

export const RightRow = ({ data, activeIndex, travellerCount, pageName }) => {
  const { penaltyDetail } = data || {};
  const { cancellationPenalty, dateChangePenalty } = penaltyDetail || {};
  if (activeIndex === CANCELLATION) {
    const { penalties } = cancellationPenalty || {};
    return (<PriceView penalties={penalties} activeIndex={activeIndex} travellerCount={travellerCount} pageName={pageName}/>);
  }

  if (activeIndex === DATE_CHANGE) {
    const { penalties } = dateChangePenalty || {};
    return (<PriceView penalties={penalties} activeIndex={activeIndex} travellerCount={travellerCount} pageName={pageName}/>);
  } else {return null;}
};

export const PriceView = ({ penalties, activeIndex, travellerCount, pageName   }) => {
  let viewList = [];
  const bcColor = ['#eaeef2', '#F8FCFF', '#eaeef2'];

  const tc = travellerCount ? travellerCount : 1;

  for (let i = 0; i < penalties.length; i++) {
    const { nonRefundable, penalty } = penalties[i];
    let finalPenalty = DETAIL_TRACKING_PAGE_NAME === pageName ? Math.ceil(penalty / tc) : penalty;
    let node = <DateCancellationPrice bgColorGrey={bcColor[i]} penalty={finalPenalty} nonRefundable={nonRefundable} activePlan={false} activeIndex={activeIndex} key={i} pageName={pageName}/>;
    viewList.push(node);
  }
  return (<View>{viewList}</View>);
};

export const PNP = ({ penalties }) => {
  let lv = [];
  const bcColor = ['#eaeef2', '#F8FCFF', '#eaeef2'];
  let dateToShow = '';
  for (let i = 0; i < penalties.length; i++) {
    const { fromDate, nonRefundable } = penalties[i];
    const textColorStyle = nonRefundable ? '#eb2026' : ( i === 0 && !nonRefundable) ? '#1a7971' : '#cf8100';
    let dateObj = getDateObjectFromDate(fromDate, 'YYYY-MM-DD');
    let newDate = addDays(dateObj, -1);
    let fromDateObj = getFormattedDateFromDate(newDate, 'YYYY-MM-DD');


    if (isEmpty(dateToShow)) {
      dateToShow = fromDateObj;
    } else if (!nonRefundable) {
      dateToShow = fromDateObj;
    }

    let completeDate = '';
    const monthYear = getFormattedDate(dateToShow, 'YYYY-MM-DD', 'MMM YY');
    const onlyDate = getFormattedDate(dateToShow, 'YYYY-MM-DD', 'DD');
    const postfixDate = fetchDayPostFix(Number(onlyDate));
    completeDate = onlyDate + postfixDate + ' ' + monthYear;

    if (penalties.length == 1 && nonRefundable) {
      completeDate = 'Booking';
    }


    var node = <DateCancellationInfo
      status={i === penalties.length - 1 ? 'After' : 'Till'}
      date={completeDate}
      time={''}
      TextColor={textColorStyle}
      bgColorGrey={bcColor[i]}
      key={i}
    />;
    lv.push(node);
  }
  return (<View>{lv}</View>);
};

export const isFlexiDateAvailable = (zcOptions) => {
  for (let i = 0; i < zcOptions.length; i++) {
    const { type, available } = zcOptions[i];
    if (type === FLEXI_DATE && available) {
      return true;
    }
  }
  return false;
};

export const getZCData = (zcOptions, activeTabIndex) => {
  for (let i = 0; i < zcOptions.length; i++) {
    const { type } = zcOptions[i];
    if (activeTabIndex === DATE_CHANGE && type === FLEXI_DATE) {
      return zcOptions[i];
    } else if (activeTabIndex === CANCELLATION && type === ZERO_CANCELLATION) {
      return zcOptions[i];
    }
  }
  return null;
};

const PolicyList = ({ cancellationPolicyData, activeTabIndex }) => {
  let policies = getCancellationPolicies(cancellationPolicyData, activeTabIndex);

  if (!policies || policies.size < 1) {
    return null;
  }

  let view = [];

  policies.forEach((policy, index) => {
    const { text } = policy;
    view.push(<BulletInfo title={text} key={index}/>);
  });

  return view;
};

const getCancellationPenalties = (cancellationPolicyData, activeTabIndex) => {
  const { penaltyDetail } = cancellationPolicyData;
  const { cancellationPenalty, dateChangePenalty } = penaltyDetail;

  if (activeTabIndex === CANCELLATION) {
    return cancellationPenalty.penalties;
  }

  if (activeTabIndex === DATE_CHANGE) {
    return dateChangePenalty.penalties;
  }
  return null;
};

const getCancellationPolicies = (cancellationPolicyData, activeTabIndex) => {
  const { penaltyDetail } = cancellationPolicyData;
  const { cancellationPenalty, dateChangePenalty } = penaltyDetail;

  if (activeTabIndex === CANCELLATION) {
    return cancellationPenalty.policies;
  }

  if (activeTabIndex === DATE_CHANGE) {
    return dateChangePenalty.policies;
  }
  return null;
};

const isPlanActive = (activeIndex, zcResponse, ZCData) => {
  if (zcResponse && zcResponse.zcDetail) {
    const { zcDetail } = zcResponse || {};
    const { selected, selectedType } = zcDetail || {};
    if (selected && activeIndex === CANCELLATION && selectedType === ZERO_CANCELLATION) {
      return true;
    }
    return selected && activeIndex === DATE_CHANGE && selectedType === FLEXI_DATE;
  } else if (ZCData && ZCData.selected) {
    const { type } = ZCData || {};
    if (activeIndex === CANCELLATION && type === ZERO_CANCELLATION) {
      return true;
    }
    return activeIndex === DATE_CHANGE && type === FLEXI_DATE;
  } else {
    return false;
  }
};

export default InnerContent;
