import React from 'react';
import { StyleSheet, Text, View } from 'react-native';
import AtomicCss from '@mmt/legacy-commons/Styles/AtomicCss';

const DateCancellationInfo = ({ status, date, TextColor, time, bgColorGrey }) => {
  return (
    <View style={bgColorGrey ? [styles.cancellationSection, { backgroundColor: bgColorGrey }] : [styles.cancellationSection]}>
      <Text style={[AtomicCss.font14, AtomicCss.regularFont, AtomicCss.marginBottom3, { color: TextColor }]}>{status}</Text>
      <Text style={[AtomicCss.font14, AtomicCss.blackFont, AtomicCss.marginBottom3, { color: TextColor }]}>{date}</Text>
      <Text style={[AtomicCss.font10, AtomicCss.regularFont, AtomicCss.defaultText]}>{time}</Text>
    </View>
  );
};

const styles = StyleSheet.create({
  cancellationSection: { height: 87, paddingLeft: 15, justifyContent: 'center' },
  bgGrey: { backgroundColor: '#f3f3f3' },
  font8: { fontSize: 8 },
  width107: { width: 107 },

});

export default DateCancellationInfo;
