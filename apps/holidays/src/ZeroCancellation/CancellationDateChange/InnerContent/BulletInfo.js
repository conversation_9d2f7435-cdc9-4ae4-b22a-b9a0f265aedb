import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
import AtomicCss from '@mmt/legacy-commons/Styles/AtomicCss';

const BulletInfo = ({ title }) => {
  return (
    <View style={[AtomicCss.flexRow]}>
      <View style={styles.bulletPoints}/>
      <Text style={[AtomicCss.font12, AtomicCss.flex1, styles.lineHeight23, AtomicCss.blackText, AtomicCss.regularFont]}>{title}</Text>
    </View>
  );
};

const styles = StyleSheet.create({
  bulletPoints: { width: 5, height: 5, borderRadius: 15, backgroundColor: '#000', marginTop: 10, marginRight: 20 },
  lineHeight23: { lineHeight: 22 },
});

export default BulletInfo;
