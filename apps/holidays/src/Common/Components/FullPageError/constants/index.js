import pageNotFoundIcon from '@mmt/legacy-assets/src/Error/pageNotFound.webp';
import pageDidNotLoadIcon from '@mmt/legacy-assets/src/Error/pageDidNotLoad.webp';
import noInternetIcon from '@mmt/legacy-assets/src/Error/noInternet.webp';
import serverErrorIcon from '@mmt/legacy-assets/src/Error/serverError.webp';

export const ERROR_TYPES = {
  PAGE_NOT_FOUND: 'pageNotFound',
  PAGE_DID_NOT_LOAD: 'pageDidNotLoad',
  NO_INTERNET: 'noInternet',
  SERVER_ERROR: 'serverError',
  LOGIN_NONE: 'nonlogin',
};

export const DEFAULT_ERROR_PROPS = {
  [ERROR_TYPES.PAGE_NOT_FOUND]: {
    title: 'Oops! Page not found',
    subTitle: 'We can’t seem to find the page you’re looking for',
    image: pageNotFoundIcon,
  },
  [ERROR_TYPES.PAGE_DID_NOT_LOAD]: {
    title: 'Sorry, the page didn’t load',
    subTitle:
      'Due to some technical issue the page could not be loaded. Please refresh the page or check later',
    image: pageDidNotLoadIcon,
  },
  [ERROR_TYPES.LOGIN_NONE]: {
    title: 'Sorry, the page didn’t load',
    subTitle:
      'You are not logged in. Please login.',
    image: pageDidNotLoadIcon,
  },
  [ERROR_TYPES.SERVER_ERROR]: {
    title: 'Uh oh!',
    subTitle:
      'It looks like our servers took too long. Why don’t you write to us and well get back to you.',
    image: serverErrorIcon,
  },
  [ERROR_TYPES.NO_INTERNET]: {
    title: 'Oops! No Internet!',
    subTitle: 'Please check the internet or wi-fi settings to restore back the connectivity',
    image: noInternetIcon,
  },
};
