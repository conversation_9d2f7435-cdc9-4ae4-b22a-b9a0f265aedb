import React from 'react';
import Carousel from 'react-native-snap-carousel';
import { Dimensions, View } from 'react-native';
import PropTypes from 'prop-types';
import BasePage from '@mmt/legacy-commons/Common/navigation/BasePage';

const ITEM_WIDTH = Dimensions.get('window').width;

export default class WebCarousel extends BasePage {

  constructor(props) {
    super(props);
    this._carouselRef = React.createRef();
  }

  onSnapToIndexCallback = (index) => {
    if (this.props.onSnapToItem) {
      this.props.onSnapToItem(index);
    }
  }

  snapToItem = (index) => {
    if (this._carouselRef?.current) {
      this._carouselRef.current.goToSlide(index);
    }
  }


  render() {
    const {
      data,
      renderItem,
      loop,
      autoplay,
      itemWidth,
      sliderWidth,
      carousalContainerStyle,
      withoutControls,
      showBullets,
      firstItem,
      scrollEnabled,
      autoplayDelay,
    } = this.props;
    const hMargin = (sliderWidth - itemWidth) / 2;
    return (
      <Carousel
        {...this.props}
        ref={this._carouselRef}
        slideIndex={firstItem}
        afterSlide={slideIndex => this.onSnapToIndexCallback(slideIndex)}
        wrapAround={loop}
        autoplay={autoplay}
        width={sliderWidth}
        withoutControls={withoutControls}
        renderCenterLeftControls={null}
        renderCenterRightControls={null}
        renderBottomCenterControls={showBullets ? undefined : null}
        defaultControlsConfig={{
          pagingDotsStyle: {
            fill: 'white',
          },
        }}
        swiping={scrollEnabled}
        autoplayInterval={autoplayDelay}
        style={carousalContainerStyle}
      >
        {data.map((item, index) => (
          <View key={index} style={hMargin ? {marginHorizontal: hMargin} : {}}>
            {renderItem(item, index)}
          </View>
          ))}
      </Carousel>
    );
  }
}

WebCarousel.defaultProps = {
  onSnapToItem: () => {},
  loop: false,
  autoplay: false,
  withoutControls: false,
  itemWidth: ITEM_WIDTH,
  sliderWidth: ITEM_WIDTH,
  carousalContainerStyle: {},
  showBullets: false,
  firstItem: 0,
  scrollEnabled: true,
  autoplayDelay: 3000,
};

WebCarousel.propTypes = {
  data: PropTypes.array.isRequired,
  renderItem: PropTypes.func.isRequired,
  carousalContainerStyle: PropTypes.object,
  onSnapToItem: PropTypes.func,
  loop: PropTypes.bool,
  autoplay: PropTypes.bool,
  withoutControls: PropTypes.bool,
  itemWidth: PropTypes.number,
  showBullets: PropTypes.bool,
  firstItem: PropTypes.number,
  scrollEnabled: PropTypes.bool,
  autoplayDelay: PropTypes.number,
};

