import React, { forwardRef, useState } from 'react';
import { Dimensions, View } from 'react-native';
import Carousel, {Pagination} from 'react-native-snap-carousel';
import PropTypes from 'prop-types';

const ITEM_WIDTH = Dimensions.get('window').width;

const AppCarousel = forwardRef((props, ref) => {
  const [active, setActive] = useState(0);
  const {
    data,
    onSnapToItem,
    showBullets,
    carousalContainerStyle,
  } = props;

  const getPagination = () => (
    <Pagination
      {...props}
      dotsLength={data.length}
      activeDotIndex={active}
    />
  );

  const onSnapToIndexCallback = (index) => {
    if (onSnapToItem) {
      onSnapToItem(index);
    }
    setActive(index);
  };
  return (
    <View style={carousalContainerStyle}>
      <Carousel
        {...props}
        ref={ref}
        data={data}
        onSnapToItem={(index) => onSnapToIndexCallback(index)}
      />
      {showBullets && getPagination()}
    </View>
  );
});

AppCarousel.defaultProps = {
  onSnapToItem: () => {},
  loop: true,
  autoplay: true,
  withoutControls: false,
  itemWidth: ITEM_WIDTH,
  sliderWidth: ITEM_WIDTH,
  carousalContainerStyle: {},
  showBullets: true,
};

AppCarousel.propTypes = {
  data: PropTypes.array.isRequired,
  renderItem: PropTypes.func.isRequired,
  carousalContainerStyle: PropTypes.object,
  onSnapToItem: PropTypes.func,
  loop: PropTypes.bool,
  autoplay: PropTypes.bool,
  withoutControls: PropTypes.bool,
  itemWidth: PropTypes.number,
  showBullets: PropTypes.bool,
};

export default AppCarousel;
