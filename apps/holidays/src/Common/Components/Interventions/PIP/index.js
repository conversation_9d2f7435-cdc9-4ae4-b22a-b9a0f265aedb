import React, { useEffect, useState } from 'react';
import { StyleSheet, View, TouchableOpacity, Animated } from 'react-native';
import { isEmpty } from 'lodash';
import {
  borderRadiusValues,
  holidayBorderRadius,
} from 'mobile-holidays-react-native/src/Styles/holidayBorderRadius';
import { holidayColors } from 'mobile-holidays-react-native/src/Styles/holidayColors';
import { marginStyles, paddingStyles } from 'mobile-holidays-react-native/src/Styles/Spacing';
import { fontStyles } from 'mobile-holidays-react-native/src/Styles/holidayFonts';
import { MEDIA_TYPE, getDimensions } from './constants';
import { handleDeepLinkUrl } from 'mobile-holidays-react-native/src/PhoenixGroupingV2/Utils/PhoenixGroupingSectionCardClickHandler';
import { PDT_EVENT_TYPES } from 'mobile-holidays-react-native/src/utils/HolidayPDTConstants';

/* Components */
import { ExpandContainer, CrossContainer } from './PIPComponents';
import {
  PipGifComponent,
  PipImageComponent,
  PipMediaStoryPage,
  PipStoryComponent,
  PipVideoComponent,
} from './PIPMediaComponents';
import StoryContainer from '../../StoryComponent/StoryContainer';

const MediaComponents = {
  [MEDIA_TYPE.IMAGE]: PipImageComponent,
  [MEDIA_TYPE.VIDEO]: PipVideoComponent,
  [MEDIA_TYPE.STORY]: PipStoryComponent,
  [MEDIA_TYPE.GIF]: PipGifComponent,
};

const PIP = ({
  interventionData,
  interventionResponseData = {},
  trackInterventionPDTClickEvents = () => {},
  trackLocalClickEvent = () => {},
  pageName = '',
  hideIntervention,
}) => {
  const {
    mediaUrl = '',
    mediaType = '',
    thumbnailUrl = '',
    knowMoreSection = {},
    redirectionUrl = '',
    media = [],
    templateType = '',
    header = '',
    headerColour = holidayColors.white,
    backgroundColour = holidayColors.black,
    videoFrameUrl = '',
  } = interventionResponseData || {};

  if (isEmpty(interventionResponseData)) {
    return null;
  }
  const [showData, setShowData] = useState(true);
  const [slideUpValue, setSlideUpValue] = useState(new Animated.Value(0));
  const [openStoryPage, setOpenStoryPage] = useState(false);
  const mediaStyle = { ...getDimensions({ templateType }), ...holidayBorderRadius.borderRadius16 };
  const positionStyles = {
    bottom: 10,
    left: 20,
  };

  useEffect(() => {
    Animated.timing(slideUpValue, {
      toValue: 1,
      duration: 500,
      useNativeDriver: true,
    }).start();
  }, []);

  const trackPipEvents = ({ eventName = '' }) => {
    const { type, id } = interventionResponseData || {};
    const event = `${type}_${eventName}`;
    trackLocalClickEvent({ eventName: event, prop1: id, pageName });
    trackInterventionPDTClickEvents({
      value: event + '|' + id,
      pageName,
      actionType: PDT_EVENT_TYPES.buttonClicked,
    });
  };

  const handleClose = () => {
    trackPipEvents({ eventName: 'Click_Base_Close' });
    setShowData(false);
  };

  const handleExpand = () => {
    trackPipEvents({ eventName: 'Click_Expand' });
    if (mediaType === MEDIA_TYPE.IMAGE || mediaType === MEDIA_TYPE.GIF) {
      handleDeepLinkUrl(redirectionUrl, '', false, pageName);
    } else if (mediaType === MEDIA_TYPE.STORY) {
      setOpenStoryPage(true);
      // handle video
    } else if (mediaType === MEDIA_TYPE.VIDEO) {
      setOpenStoryPage(true);
    }
  };

  const handleCloseStoryPage = () => {
    setOpenStoryPage(false);
    setShowData(false);
  };

  const renderMediaContent = () => {
    const mediaDetails = {
      mediaUrl,
      mediaType,
      videoFrameUrl,
      thumbnailUrl,
      knowMoreSection,
      redirectionUrl,
      media,
      templateType,
    };

    const MediaComponent = MediaComponents[mediaType];
    return (
      <MediaComponent
        mediaDetails={mediaDetails}
        mediaStyle={mediaStyle}
        templateType={templateType}
        textDetails={{ header, headerColour, backgroundColour }}
        handleClick={handleExpand}
      />
    );
  };

  const renderMediaActions = () => {
    return (
      <View style={styles.iconContainer}>
        <CrossContainer toggleModal={handleClose} />
        <ExpandContainer handleExpand={handleExpand} />
      </View>
    );
  };

  const hideInterventionStyle = hideIntervention ? { display: 'none' } : { display: 'flex' };
  return (
    <>
      {showData && (
        <Animated.View
          style={[
            styles.container,
            hideInterventionStyle,
            positionStyles,
            {
              transform: [
                {
                  translateY: slideUpValue.interpolate({
                    inputRange: [0, 1],
                    outputRange: [600, 0],
                  }),
                },
              ],
            },
          ]}
        >
          {renderMediaActions()}
          {renderMediaContent()}
          {/* Absolute View so that clicking anywhere in the view will open bigger view */}
          <TouchableOpacity
            style={{
              position: 'absolute',
              ...mediaStyle,
            }}
            onPress={handleExpand}
          ></TouchableOpacity>
        </Animated.View>
      )}
      {openStoryPage && (
        <PipMediaStoryPage
          openStoryPage={openStoryPage}
          handleCloseStoryPage={handleCloseStoryPage}
          trackPipEvents={trackPipEvents}
          knowMoreSection={knowMoreSection}
          storyDetails={
            mediaType === MEDIA_TYPE.STORY
              ? media
              : [
                  {
                    url: mediaUrl,
                    type: MEDIA_TYPE.VIDEO,
                    videoFrameUrl,
                  },
                ]
          }
          templateType={templateType}
        />
      )}
    </>
  );
};

const styles = StyleSheet.create({
  container: {
    position: 'absolute',
    ...holidayBorderRadius.borderRadius16,
    zIndex: 1,
  },
  image: {
    flex: 1,
    resizeMode: 'cover',
  },
  iconContainer: {
    position: 'absolute',
    top: 5,
    zIndex: 1,
    flexDirection: 'row',
    width: '100%',
  },
  iconContainerStyle: {
    backgroundColor: holidayColors.gray,
    ...holidayBorderRadius.borderRadius8,
    ...paddingStyles.pa2,
    ...marginStyles.ml6,
  },
  iconStyle: {
    width: 24,
    height: 24,
  },
  expandIconContainer: {
    ...marginStyles.mr6,
    marginLeft: 'auto',
  },
  textContainer: {
    position: 'absolute',
    bottom: 0,
    zIndex: 1,
    ...paddingStyles.pv10,
    ...paddingStyles.pl4,
    borderBottomLeftRadius: borderRadiusValues.br16,
    borderBottomRightRadius: borderRadiusValues.br16,
  },
  mediaText: {
    ...fontStyles.labelBaseBold,
    color: holidayColors.white,
  },
});

export default PIP;
