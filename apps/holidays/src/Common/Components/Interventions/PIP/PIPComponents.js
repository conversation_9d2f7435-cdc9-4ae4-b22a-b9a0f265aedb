import React from 'react';
import { StyleSheet, TouchableOpacity } from 'react-native';
import {
  borderRadiusValues,
  holidayBorderRadius,
} from 'mobile-holidays-react-native/src/Styles/holidayBorderRadius';
import { holidayColors } from 'mobile-holidays-react-native/src/Styles/holidayColors';
import { marginStyles, paddingStyles } from 'mobile-holidays-react-native/src/Styles/Spacing';
import { fontStyles } from 'mobile-holidays-react-native/src/Styles/holidayFonts';

/* Icons */
import { IMAGE_ICON_KEYS, getOpitimsedImageUrl } from '../../HolidayImageUrls';

/* Components */
import HolidayImageHolder from '../../HolidayImageHolder';

export const CrossContainer = ({ toggleModal }) => {
  const iconStyles = {
    ...styles.iconStyle,
    tintColor: holidayColors.white,
  };
  return (
    <TouchableOpacity onPress={toggleModal} style={styles.iconContainerStyle} activeOpacity={1}>
      <HolidayImageHolder
        imageUrl={getOpitimsedImageUrl(IMAGE_ICON_KEYS.CROSS_GREY)}
        style={iconStyles}
      />
    </TouchableOpacity>
  );
};

export const ExpandContainer = ({ handleExpand }) => {
  return (
    <TouchableOpacity
      onPress={handleExpand}
      style={[styles.iconContainerStyle, styles.expandIconContainer]}
      activeOpacity={1}
    >
      <HolidayImageHolder
        imageUrl={getOpitimsedImageUrl(IMAGE_ICON_KEYS.EXPAND)}
        style={styles.iconStyle}
      />
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  container: {
    position: 'absolute',
  },
  image: {
    flex: 1,
    resizeMode: 'cover',
  },
  iconContainer: {
    position: 'absolute',
    top: 5,
    zIndex: 1,
    flexDirection: 'row',
    width: '100%',
  },
  iconContainerStyle: {
    backgroundColor: 'rgba(0,0,0,0.5)',
    ...holidayBorderRadius.borderRadius8,
    ...paddingStyles.pa2,
    ...marginStyles.ml6,
  },
  iconStyle: {
    width: 24,
    height: 24,
  },
  expandIconContainer: {
    ...marginStyles.mr6,
    marginLeft: 'auto',
  },
  textContainer: {
    position: 'absolute',
    bottom: 0,
    zIndex: 1,
    ...paddingStyles.pv10,
    ...paddingStyles.pl4,
    borderBottomLeftRadius: borderRadiusValues.br16,
    borderBottomRightRadius: borderRadiusValues.br16,
  },
  mediaText: {
    ...fontStyles.labelBaseBold,
    color: holidayColors.white,
  },
});

