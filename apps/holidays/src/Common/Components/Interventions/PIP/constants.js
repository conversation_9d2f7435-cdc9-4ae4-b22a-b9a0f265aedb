export const getDimensions = ({ templateType }) => {
  switch (templateType) {
    case TEMPLATE_TYPES.PORTRAIT:
      return { width: 140, height: 216 };
    case TEMPLATE_TYPES.LANDSCAPE:
      return { width: 140, height: 152 };
    default:
      return { width: 140, height: 216 };
  }
};
export const MEDIA_TYPE = {
  IMAGE: 'IMAGE',
  VIDEO: 'VIDEO',
  STORY: 'STORY',
  GIF: 'GIF',
};

export const TEMPLATE_TYPES = {
  PORTRAIT: 'PORTRAIT',
  LANDSCAPE: 'LANDSCAPE',
};
