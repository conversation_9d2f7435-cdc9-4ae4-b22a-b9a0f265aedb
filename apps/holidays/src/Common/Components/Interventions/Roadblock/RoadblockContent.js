import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
import PropTypes from 'prop-types';
import { fontStyles } from 'mobile-holidays-react-native/src/Styles/holidayFonts';
import { holidayColors } from 'mobile-holidays-react-native/src/Styles/holidayColors';
import { paddingStyles } from 'mobile-holidays-react-native/src/Styles/Spacing';

export default RoadblockContent = (props) => {
  const {
    header,
    subHeader,
    description,
    headerFontColour = holidayColors.black,
    descriptionFontColour = holidayColors.black,
    imgUrl = '',
  } = props || {};
  return (
    <View style={styles.wrapper}>
      {!!header && (
        <Text style={[styles.header,{color:headerFontColour}]} numberOfLines={1}>
          {header}
        </Text>
      )}
      {!!subHeader && (
        <Text style={[styles.subheader,{color:headerFontColour}]} numberOfLines={2}>
          {subHeader}
        </Text>
      )}
      {!!description && <Text style={[styles.content,{color:descriptionFontColour}]} numberOfLines={imgUrl ? 5 : 10}>{description}</Text>}
    </View>
  );
};
RoadblockContent.propTypes = {
  header: PropTypes.string,
  subHeader: PropTypes.string,
  description:PropTypes.string,
  headerFontColour:PropTypes.string,
  descriptionFontColour:PropTypes.string,
  imgUrl:PropTypes.string,
};

const styles = StyleSheet.create({
  wrapper: {
    ...paddingStyles.pb16,
  },
  header: {
    ...fontStyles.labelBaseBold,
    color: holidayColors.black,
},
  subheader: {
   ...fontStyles.headingMedium,
   color: holidayColors.black,
  },
  content: { paddingTop: 25, color: holidayColors.lightGray },
});

