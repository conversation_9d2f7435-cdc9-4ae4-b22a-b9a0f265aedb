import React from 'react';
import { View, Image, StyleSheet } from 'react-native';
import PropTypes from 'prop-types';
export default RoadblockSquareImageview = ({ url }) => {
  return (
    !!url && (
      <View style={styles.wrapper}>
        <Image source={{ uri: url }} style={styles.image} />
      </View>
    )
  );
};
const styles = StyleSheet.create({
  wrapper: { height: 140, width: '100%', paddingVertical: 10 },
  image: { height: '100%', width: '100%',minHeight:120 ,resizeMode:'contain'},
});

RoadblockSquareImageview.propTypes = {
  url: PropTypes.string,
};
