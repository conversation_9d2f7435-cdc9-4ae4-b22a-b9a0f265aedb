import React from 'react';
import { View, Image, Dimensions, StyleSheet } from 'react-native';
import PropTypes from 'prop-types';
export default RoadblockCurvedImage = ({ url }) => {
  return (
    !!url && (
      <View style={styles.wrapper}>
        <Image source={{ uri: url }} style={styles.image} />
      </View>
    )
  );
};
RoadblockCurvedImage.propTypes = {
  url: PropTypes.string,
};

const styles = StyleSheet.create({
  wrapper: {
    marginBottom: 20,
  },
  image: {
    borderTopLeftRadius: 40,
    borderBottomLeftRadius: 40,
    width: 180,
    left: Dimensions.get('screen').width - 200,
    height: 80,
    resizeMode:'contain',
  },
});
