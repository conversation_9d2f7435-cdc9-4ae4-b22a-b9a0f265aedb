import React, { useEffect, useState } from 'react';
import { View, Text, StyleSheet, NativeModules } from 'react-native';
import { isEmpty } from 'lodash';
import { RESIZE_MODE_IMAGE } from 'mobile-holidays-react-native/src/HolidayConstants';
import { holidayBorderRadius } from 'mobile-holidays-react-native/src/Styles/holidayBorderRadius';
import { holidayColors } from 'mobile-holidays-react-native/src/Styles/holidayColors';
import { fontStyles } from 'mobile-holidays-react-native/src/Styles/holidayFonts';
import { marginStyles, paddingStyles } from 'mobile-holidays-react-native/src/Styles/Spacing';
import { rupeeFormatterUtils } from 'mobile-holidays-react-native/src/utils/HolidayUtils';
import { getOpitimsedImageUrl, IMAGE_ICON_KEYS } from '../../HolidayImageUrls';
import { getRefereeEligibilityDetails } from 'mobile-holidays-react-native/src/utils/NetworkUtils/referAndEarnApis';
import { RNE_EVENT_NAMES } from '../../../../ReferAndEarn/constants';
import { logHolidaysLandingPDTEvents } from '../../../../LandingNew/Utils/HolidayLandingPdtTrackingUtils';
import { PDT_EVENT_TYPES } from '../../../../utils/HolidayPDTConstants';
import { interventionTypes } from '../InterventionConstants';

/* Components */
import PrimaryButton from '../../Buttons/PrimaryButton';
import HolidayImageHolder from '../../HolidayImageHolder';
import HolidaysMessageStrip from '../../HolidaysMessageStrip';
import BottomSheetOverlay from '../../BottomSheetOverlay';
import { interventionTypesBottomSheetOverlayConfig } from '../interventionTypesConfig';

const buttonActions = {
  CLOSE: 'CLOSE',
  RETRY: 'RETRY',
  REDIRECT: 'REDIRECT',
};
const buttonActionText = {
  [buttonActions.RETRY]: 'TRY AGAIN',
  [buttonActions.CLOSE]: 'GOT IT',
  [buttonActions.REDIRECT]: 'BOOK A HOLIDAY TO REDEEM GIFT CARD',
};
const HolidayReferralReceiver = ({
  data = {},
  interventionData,
  handleClose,
  trackLocalClickEvent,
  pageName,
  toggleModal,
  isVisible,
}) => {
  const [referData, setReferData] = useState(data);
  const { state } = referData || {};
  const [refetch, setRefetch] = useState(false);
  useEffect(() => {
    async function fetchResponse() {
      const response = await getRefereeEligibilityDetails({
        referralCode: interventionData?.referralCode || '',
      });
      if (response.status) {
        setReferData({ ...referData, ...response });
        setRefetch(false);
      }
    }

    if (refetch) {
      fetchResponse();
    }
  }, [refetch]);

  useEffect(() => {
    setReferData(data);
  }, [data]);
  
  const trackClickEvents = () => {
    logHolidaysLandingPDTEvents({
      actionType: PDT_EVENT_TYPES.contentSeen,
      value: RNE_EVENT_NAMES.CLICK_ONBOARD_REFEREE,
      shouldTrackToAdobe:false
    });
    trackLocalClickEvent({
      pageName,
      eventName: RNE_EVENT_NAMES.CLICK_ONBOARD_REFEREE,
    });
  };
  useEffect(() => {
    trackClickEvents();
  }, []);

  const renderErrorState = () => {
    const { preHeading, heading, description, buttonAction, ctaText } = referData || {};
    const handleClick = () => {
      if (buttonAction === buttonActions.RETRY) {
        setRefetch(true);
      } else {
        handleClose();
      }
    };

    return (
      <BottomSheetOverlay
        toggleModal={toggleModal}
        {...interventionTypesBottomSheetOverlayConfig[interventionTypes.HOL_REFFERAL]}
        visible={isVisible}
        containerStyles={styles.containerStyles}
        headingContainerStyles={styles.headingContainerStyles}
      >
        <View style={errorStyles.container}>
          {!!preHeading && <Text style={errorStyles.preHeadingText}>{preHeading}</Text>}
          {!!heading && <Text style={errorStyles.headingText}>{heading}</Text>}
          {!!description && <Text style={errorStyles.description}>{description}</Text>}
          <PrimaryButton
            buttonText={
              refetch ? 'Loading...' : !isEmpty(ctaText) ? ctaText : buttonActionText[buttonAction]
            }
            btnContainerStyles={{ ...marginStyles.mv20 }}
            handleClick={handleClick}
          />
        </View>
      </BottomSheetOverlay>
    );
  };

  const renderSuccessState = () => {
    const {
      preHeading,
      amount,
      name = '',
      description,
      buttonAction,
      infoText,
      ctaText = '',
      ctaLink = '',
      heading = '',
    } = referData || {};
    const headingMessage = 'Your Holidays Gift Card worth';
    const handleClick = () => {
      if (!isEmpty(ctaLink)) {
        const { GenericModule } = NativeModules;
        GenericModule.openDeepLink(ctaLink);
      } else {
        handleClose();
      }
    };
    return (
      <BottomSheetOverlay
        toggleModal={toggleModal}
        {...interventionTypesBottomSheetOverlayConfig[interventionTypes.HOL_REFFERAL]}
        visible={isVisible}
        containerStyles={styles.containerStyles}
        headingContainerStyles={styles.headingContainerStyles}
      >
        <View style={successStyles.container}>
          <HolidayImageHolder
            imageUrl={getOpitimsedImageUrl(IMAGE_ICON_KEYS.GIFT_CARD_IMAGE_WITH_GRADIENT_MASK)}
            style={successStyles.giftCardIcon}
            resizeMode={RESIZE_MODE_IMAGE.CONTAIN}
          />
          {!!preHeading && <Text style={successStyles.preHeadingText}>{preHeading}</Text>}
          {!!headingMessage && (
            <Text style={successStyles.headingText}>
              {headingMessage}
              <Text style={{ color: holidayColors.green }}>
                &nbsp;{rupeeFormatterUtils(amount)}&nbsp;
              </Text>
              from {name} {heading}
            </Text>
          )}
          {!!description && <Text style={successStyles.description}>{description}</Text>}
          <HolidaysMessageStrip
            message={infoText}
            shouldShow={!!infoText}
            messageStyle={successStyles.infoItemText}
            containerStyles={successStyles.infoItemContainer}
          />
        </View>
        <View style={paddingStyles.pa10}>
          <PrimaryButton
            buttonText={!isEmpty(ctaText) ? ctaText : buttonActionText[buttonAction]}
            btnContainerStyles={{ ...marginStyles.mv20, ...paddingStyles.pa14 }}
            handleClick={handleClick}
          />
        </View>
      </BottomSheetOverlay>
    );
  };

  switch (state) {
    case 'Success':
      return renderSuccessState();
      break;
    default:
      return renderErrorState();
  }
};

const errorStyles = StyleSheet.create({
  container: {
    ...paddingStyles.pa10,
  },
  preHeadingText: {
    ...fontStyles.labelLargeRegular,
    color: holidayColors.gray,
    ...paddingStyles.pb10,
  },
  headingText: {
    ...fontStyles.headingMedium,
    color: holidayColors.black,
    ...paddingStyles.pb10,
  },
  description: {
    ...fontStyles.labelBaseRegular,
    color: holidayColors.gray,
    ...paddingStyles.pb10,
  },
  btnContainerStyles: {
    ...marginStyles.mv20,
    ...holidayBorderRadius.borderRadius8,
  },
  giftCardIcon: {
    width: 100,
    height: 100,
  },
});
const successStyles = StyleSheet.create({
  container: {
    ...paddingStyles.pa10,
    alignItems: 'center',
  },
  preHeadingText: {
    ...fontStyles.labelLargeRegular,
    color: holidayColors.gray,
    ...paddingStyles.pt16,
    ...paddingStyles.pb10,
  },
  headingText: {
    ...fontStyles.headingMedium,
    color: holidayColors.black,
    ...paddingStyles.pv6,
    textAlign: 'center',
  },
  description: {
    ...fontStyles.labelBaseRegular,
    color: holidayColors.gray,
    textAlign: 'center',
  },
  btnContainerStyles: {
    ...marginStyles.mv20,
    ...holidayBorderRadius.borderRadius8,
  },
  giftCardIcon: {
    width: 250,
    height: 163,
  },
  infoItemContainer: {
    width: '100%',
    justifyContent: 'center',
  },
  infoItemText: {
    ...fontStyles.labelSmallRegular,
    color: holidayColors.gray,
    textAlign: 'center',
  },
});
const styles = StyleSheet.create({
  containerStyles: {
    ...paddingStyles.pa16
  },
  headingContainerStyles: {
    ...paddingStyles.pb12
  }
});
export default HolidayReferralReceiver;
