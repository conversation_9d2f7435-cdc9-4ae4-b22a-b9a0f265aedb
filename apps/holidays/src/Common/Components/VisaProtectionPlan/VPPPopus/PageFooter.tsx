import React, { useMemo } from 'react';
import { StyleSheet, TextStyle, View, Text } from 'react-native';
import { holidayColors } from 'mobile-holidays-react-native/src/Styles/holidayColors';
import { paddingStyles } from 'mobile-holidays-react-native/src/Styles/Spacing';
import { rupeeFormatterUtils } from 'mobile-holidays-react-native/src/utils/HolidayUtils';
import PrimaryButton from '../../Buttons/PrimaryButton';
import { fontStyles } from 'mobile-holidays-react-native/src/Styles/holidayFonts';
import { PageFooterProps } from '../VPPTypes';
import { PAGE_FOOTER_BUTTON_TEXT } from '../VPPConstant';

const PageFooter = (props: PageFooterProps) => {
  const { totalPackagePrice, totalPackagePriceUnit, onUpdateClick } = props;

  const formattedPrice = useMemo(() => rupeeFormatterUtils(totalPackagePrice), [totalPackagePrice]);

  return (
    <View style={[styles.pageFooter]}>
      <View>
        <Text style={styles.netPrice}>{formattedPrice}</Text>
        <Text style={styles.grandTotal}>{totalPackagePriceUnit}</Text>
      </View>
      <View style={styles.footerRight}>
        <PrimaryButton
          buttonText={PAGE_FOOTER_BUTTON_TEXT}
          handleClick={onUpdateClick}
          btnContainerStyles={styles.footerButton}
          isDisable={false}
        />
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  pageFooter: {
    backgroundColor: holidayColors.black,
    paddingVertical: 10,
    marginBottom: 4,
    ...paddingStyles.ph16,
    flexDirection: 'row',
    alignItems: 'center',
  },
  footerRight: {
    marginLeft: 'auto',
  },
  footerButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    ...paddingStyles.ph16,
  },
  upperHeading: {
    ...fontStyles.labelSmallRegular,
    color: holidayColors.white,
    opacity: 0.7,
  },
  netPrice: {
    ...fontStyles.headingBase,
    color: holidayColors.white,
  } as TextStyle,
  grandTotal: {
    ...fontStyles.labelSmallRegular,
    color: holidayColors.disableGrayBg,
    opacity: 0.7,
  } as TextStyle,
});

export default PageFooter;
