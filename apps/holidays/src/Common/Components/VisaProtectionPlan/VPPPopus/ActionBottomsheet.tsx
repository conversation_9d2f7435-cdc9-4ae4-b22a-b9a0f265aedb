import React, { useCallback } from 'react';
import { StyleSheet, Text, TextStyle, TouchableOpacity, View } from 'react-native';
import { ActionBottomsheetProps } from '../VPPTypes';
import { VPP_POPUPS } from '../VPPConstant';
import { marginStyles, paddingStyles } from 'mobile-holidays-react-native/src/Styles/Spacing';
import { fontStyles } from 'mobile-holidays-react-native/src/Styles/holidayFonts';
import { holidayColors } from 'mobile-holidays-react-native/src/Styles/holidayColors';
import { ActionBottomsheetData } from '../VPPUtils';
import BottomSheetOverlay from '../../BottomSheetOverlay';
import PrimaryButton from '../../Buttons/PrimaryButton';
import SecondaryButton from '../../Buttons/SecondaryButton';
import HolidayImageHolder from '../../HolidayImageHolder';
import CrossIcon from '@mmt/legacy-assets/src/ic_cross__gray.webp';

const ActionBottomsheet = (props: ActionBottomsheetProps) => {
  const { popUpValue, onUpdateClick, handleGoBack, goBackToInitialState, isVisible ,onOuterClick=()=>{} } = props;

  const { title, subTitle, primaryButtonText, secondaryButtonText } =
    ActionBottomsheetData[popUpValue];

  const primaryButtonClick = {
    [VPP_POPUPS.UPDATE_BOTTOMSHEET]: onUpdateClick,
    [VPP_POPUPS.DESELECT_BOTTOMSHEET]: goBackToInitialState,
    [VPP_POPUPS.VISA_PROTECTION_PLAN]:goBackToInitialState,
    [VPP_POPUPS.INSURANCE]:goBackToInitialState
  };

  const secondaryButtonClick = {
    [VPP_POPUPS.UPDATE_BOTTOMSHEET]: handleGoBack,
    [VPP_POPUPS.DESELECT_BOTTOMSHEET]: onUpdateClick,
    [VPP_POPUPS.VISA_PROTECTION_PLAN]:onUpdateClick,
    [VPP_POPUPS.INSURANCE]:onUpdateClick
  };

  const noop = useCallback(() => {}, []);
  return (
    <BottomSheetOverlay
      title={''}
      containerStyles={styles.bottomSheetStyle}
      toggleModal={goBackToInitialState}
      visible={isVisible}
      showCross={false}
      onDismiss={noop}
    >
      <View style={styles.headerContainer}>
        <View style={styles.textContainerStyle}>
          <Text style={styles.headingTextStyle}>{title}</Text>
          <Text style={styles.subTitleTextStyle}>{subTitle}</Text>
        </View>
        <TouchableOpacity
          style={styles.closeButton}
          onPress={goBackToInitialState}
          activeOpacity={0.7}
        >
          <HolidayImageHolder 
            defaultImage={CrossIcon} 
            style={styles.closeIcon}
          />
        </TouchableOpacity>
      </View>
      <View style={styles.buttonContainerStyle}>
      <PrimaryButton
          buttonText={primaryButtonText}
          handleClick={primaryButtonClick[popUpValue]}
          btnContainerStyles={[styles.buttonStyle]}
          isDisable={false}
        />
        <SecondaryButton
          buttonText={secondaryButtonText}
          handleClick={secondaryButtonClick[popUpValue]}
          btnContainerStyles={[styles.buttonStyle]}
          isDisable={false}
        />
      </View>
    </BottomSheetOverlay>
  );
};

const styles = StyleSheet.create({
  bottomSheetStyle: {
    ...paddingStyles.pa20,
    flex: 1,
  },
  headerContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  closeButton: {
    width: 25,
    height: 25,
    borderRadius: 16,
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: holidayColors.lightGray,
    position: 'absolute',
    right: 10,
    top: 0,
  },
  closeIcon: {
    width: 12,
    height: 12,
    tintColor: holidayColors.white,
  },
  buttonContainerStyle: {
    marginTop:20
  },
  textContainerStyle: {
    width: '85%',
  },
  headingTextStyle: {
    ...marginStyles.mb20,
    color: holidayColors.black,
    ...fontStyles.headingMedium,
  } as TextStyle,
  subTitleTextStyle: {
    ...fontStyles.labelMediumRegular,
    color: holidayColors.black,
  } as TextStyle,
  buttonStyle: {
    paddingVertical: 10,
    paddingHorizontal: 10,
    ...marginStyles.mb16,
    height: 44,
  },
});

export default ActionBottomsheet;
