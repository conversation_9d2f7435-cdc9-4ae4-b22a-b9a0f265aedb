import React, { useCallback } from 'react';
import { FlatList, StyleSheet, Text, TextStyle, TouchableOpacity } from 'react-native';
import { handleCtaClick, vppCaptureReviewClickEvents } from '../VPPUtils';
import { paddingStyles } from 'mobile-holidays-react-native/src/Styles/Spacing';
import { holidayColors } from 'mobile-holidays-react-native/src/Styles/holidayColors';
import { ctaItemProps, InfoCardFooterProps } from './VPPStaticDetailType';
import { fontStyles } from 'mobile-holidays-react-native/src/Styles/holidayFonts';
import { ADDON_TRACKING_VALUES, VPP_TRACKING_ACTIONS, VPP_TRACKING_EVENT, VPP_TRACKING_PAGES } from '../VPPConstant';
import { PDT_EVENT_TYPES } from '../../../../utils/HolidayPDTConstants';

const InfoCardFooter = (props: InfoCardFooterProps) => {
  const { ctas = [], trackLocalClickEvent, isBottomSheet, onToggleModalBottomSheet ,trackEvent } = props;
  const handleFooterClick = useCallback(
    (item: ctaItemProps) => {
      const prop1Val = isBottomSheet ? VPP_TRACKING_PAGES.BASE_CARD : VPP_TRACKING_PAGES.DETAIL;
      vppCaptureReviewClickEvents({
        trackLocalClickEvent,
        eventName: VPP_TRACKING_EVENT,
        actionType: PDT_EVENT_TYPES.buttonClicked,
        suffix: VPP_TRACKING_ACTIONS.TNC,
        subPageName: prop1Val,
        prop1: prop1Val,
      });
      if (onToggleModalBottomSheet) {
        onToggleModalBottomSheet(false);
      }
      if (typeof handleCtaClick[item.onClick] === 'function') {
        handleCtaClick[item.onClick]({ item });
      }
       trackEvent && trackEvent({
                    actionType: PDT_EVENT_TYPES.buttonClicked,
                    value: ADDON_TRACKING_VALUES.VIEWTNC,
                })
    },
    [trackLocalClickEvent, onToggleModalBottomSheet, handleCtaClick],
  );

  const renderItem = useCallback(
    ({ item }: { item: ctaItemProps }) => (
      <TouchableOpacity onPress={() => handleFooterClick(item)} style={styles.itemStyle}>
        <Text style={styles.textStyle}>{item.label}</Text>
      </TouchableOpacity>
    ),
    [handleFooterClick],
  );

  return <FlatList data={ctas} contentContainerStyle={styles.wrapper} renderItem={renderItem} />;
};

const styles = StyleSheet.create({
  wrapper: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    ...paddingStyles.pa16,
  },
  itemStyle: {
    ...paddingStyles.ph20,
    ...paddingStyles.pb20,
  },
  textStyle: {
    ...fontStyles.labelMediumBold,
    color: holidayColors.primaryBlue,
  } as TextStyle,
});

export default InfoCardFooter;
