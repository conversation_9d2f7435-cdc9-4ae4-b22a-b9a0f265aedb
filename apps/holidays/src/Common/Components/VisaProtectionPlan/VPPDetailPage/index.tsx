import React, { useCallback, useState } from 'react';
import { View, StyleSheet, Platform, StatusBar, ScrollView } from 'react-native';
import { VPPDetailPageProps } from '../VPPTypes';
import VPPCard from '../VPPCard';
import { isEmpty, noop } from 'lodash';
import { holidayColors } from 'mobile-holidays-react-native/src/Styles/holidayColors';
import PageHeader from '../../PageHeader';
import { HolidayNavigation } from 'mobile-holidays-react-native/src/Navigation';
import { holidayNavigationPop } from '../../../../PhoenixDetail/Utils/DetailPageNavigationUtils';
import { handlePopUP, vppCaptureReviewClickEvents } from '../VPPUtils';
import {
  ADDON_TRACKING_VALUES,
  VPP_DETAIL_PAGE_HEADER,
  VPP_POPUPS,
  VPP_TRACKING_ACTIONS,
  VPP_TRACKING_EVENT,
  VPP_TRACKING_PAGES,
} from '../VPPConstant';
import PageFooter from '../VPPPopus/PageFooter';
import ActionBottomsheet from '../VPPPopus/ActionBottomsheet';
import VPPStaticDetails from './VPPStaticDetails';
import { PDT_EVENT_TYPES } from '../../../../utils/HolidayPDTConstants';
import { COMMON_OVERLAYS } from '../../CommonOverlay';
import { SUB_PAGE_NAMES } from '../../../../../src/HolidayConstants';
import { isMobileClient } from '../../../../utils/HolidayUtils';

const VPPDetailPage: React.FC<VPPDetailPageProps> = (props: VPPDetailPageProps) => {
  const { vppAddonDetail, onSelect, trackLocalClickEvent, addonSubType = '', addonType = '' ,trackEvent = noop} = props;
  const { addons = [], addonPriceMap = {} } = vppAddonDetail || {};
  const currentSelectionStatus = addons[0]?.isSelected ?? false;
  const priceMap = addonPriceMap[addons[0]?.id] ?? {};
  const [isVPPSelected, setIsVPPSelected] = useState(currentSelectionStatus);
  const [popupValue, setPopup] = useState('');

  const isActionBottomSheetVisible =
    popupValue === VPP_POPUPS.DESELECT_BOTTOMSHEET || popupValue === VPP_POPUPS.UPDATE_BOTTOMSHEET;

  const onSelectClick = useCallback(() => {
    const popUpVal = handlePopUP(currentSelectionStatus, !isVPPSelected);
    setIsVPPSelected(!isVPPSelected);
    setPopup(popUpVal);
    if (trackEvent) {
      trackEvent({
        actionType: PDT_EVENT_TYPES.buttonClicked,
        value: ADDON_TRACKING_VALUES.VPP_UNSELECT,
      })
    }
    else {
      vppCaptureReviewClickEvents({
        actionType: PDT_EVENT_TYPES.buttonClicked,
        trackLocalClickEvent,
        eventName: VPP_TRACKING_EVENT,
        suffix: isVPPSelected ? VPP_TRACKING_ACTIONS.SELECT : VPP_TRACKING_ACTIONS.REMOVE,
        subPageName: SUB_PAGE_NAMES.VPP_DETAIL,
        prop1: VPP_TRACKING_PAGES.DETAIL,
      });
    }
  }, [currentSelectionStatus, isVPPSelected]);

  const onUpdateClick = () => {
    if (vppAddonDetail?.addons[0]) {
      setPopup('');
      onSelect(vppAddonDetail?.addons[0]);
      goBackToReview();
      
    }
  };

  const goBackToReview = useCallback(() => {
    setPopup('');
    if(isMobileClient()) {
      HolidayNavigation.pop();
    } else {
    holidayNavigationPop({
      overlayKeys: [COMMON_OVERLAYS.VPP_OVERLAY],
      hideOverlays: props.hideOverlays,
    })
  }
  }, []);

  const goBackToInitialState = useCallback(() => {
    setPopup('');
    setIsVPPSelected(!isVPPSelected);
  }, [isVPPSelected]);

  const handleBackPress = useCallback(() => {
    vppCaptureReviewClickEvents({
      trackLocalClickEvent,
      actionType: PDT_EVENT_TYPES.buttonClicked,
      eventName: VPP_TRACKING_ACTIONS.BACK,
      subPageName: VPP_TRACKING_PAGES.DETAIL,
      prop1: VPP_TRACKING_PAGES.DETAIL,
    });
    if (popupValue === VPP_POPUPS.PAGE_FOOTER) {
      setPopup(VPP_POPUPS.UPDATE_BOTTOMSHEET);
    } else {
      goBackToReview();
    }
  }, [trackLocalClickEvent, popupValue]);
  return (
    <View style={styles.pageContainer}>
      <PageHeader
        showBackBtn
        showShadow
        title={VPP_DETAIL_PAGE_HEADER}
        onBackPressed={handleBackPress}
        activeTabIndex={0}
        headerWrapperStyle={{}}
      />
      <ScrollView contentContainerStyle={styles.scrollViewStyle} showsVerticalScrollIndicator={false}>
        {!isEmpty(vppAddonDetail) && (
          <VPPCard
            vppAddonDetail={vppAddonDetail}
            onSelect={onSelectClick}
            basePageCard={false}
            isSelected={isVPPSelected}
            trackLocalClickEvent={trackLocalClickEvent}
          />
        )}
        <VPPStaticDetails
          addonType={addonType}
          addonSubType={addonSubType}
          trackLocalClickEvent={trackLocalClickEvent}
          trackEvent={trackEvent}
        />
      </ScrollView>

      {popupValue === VPP_POPUPS.PAGE_FOOTER && (
        <PageFooter
          onUpdateClick={onUpdateClick}
          {...priceMap}
          trackLocalClickEvent={trackLocalClickEvent}
        />
      )}
      {isActionBottomSheetVisible && (
        <ActionBottomsheet
          isVisible={isActionBottomSheetVisible}
          onUpdateClick={onUpdateClick}
          popUpValue={popupValue}
          handleGoBack={goBackToReview}
          goBackToInitialState={goBackToInitialState}
          trackLocalClickEvent={trackLocalClickEvent}
        />
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  pageContainer: {
    height: '100%',
    paddingBottom: 20,
    backgroundColor: holidayColors.lightGray2,
  },

  title: {
    fontSize: 20,
    textAlign: 'center',
    margin: 10,
  },
  scrollViewStyle: {
    paddingVertical: 20,
  }
});

export default VPPDetailPage;
