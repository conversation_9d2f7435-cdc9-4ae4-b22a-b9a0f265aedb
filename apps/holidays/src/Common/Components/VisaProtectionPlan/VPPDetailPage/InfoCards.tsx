import React, { useMemo } from 'react';
import { InfoCardProps } from './VPPStaticDetailType';
import { StyleSheet, Text, TextStyle, View } from 'react-native';
import { holidayBorderRadius } from 'mobile-holidays-react-native/src/Styles/holidayBorderRadius';
import { marginStyles, paddingStyles } from 'mobile-holidays-react-native/src/Styles/Spacing';
import { holidayColors } from 'mobile-holidays-react-native/src/Styles/holidayColors';
import { fontStyles } from 'mobile-holidays-react-native/src/Styles/holidayFonts';
import IconList from './IconList';
import InfoCardFooter from './InfoCardFooter';
import { LIST_CONFIG_TYPE } from '../VPPConstant';
import StepperList from '../../StepperList';

const InfoCards = (props: InfoCardProps) => {
  const {
    title,
    listItems,
    footer,
    listConfig,
    trackLocalClickEvent,
    onToggleModalBottomSheet,
    isBottomSheet,
    trackEvent
  } = props;

  const getInfoList = useMemo(
    () => ({
      [LIST_CONFIG_TYPE.ICON]: IconList({ listItems, isBottomSheet }),
      [LIST_CONFIG_TYPE.STEPPER]: StepperList({ listItems }),
      [LIST_CONFIG_TYPE.BULLET]: IconList({ listItems, isBottomSheet }),
    }),
    [listItems],
  );

  return (
    <View style={styles.infoCardWrapper}>
      {!!title && <Text style={styles.title}>{title}</Text>}
      {getInfoList[listConfig.type]}
      {footer && (
        <InfoCardFooter
          {...footer}
          trackLocalClickEvent={trackLocalClickEvent}
          isBottomSheet={isBottomSheet}
          onToggleModalBottomSheet={onToggleModalBottomSheet}
          trackEvent={trackEvent}
        />
      )}
    </View>
  );
};
const styles = StyleSheet.create({
  infoCardWrapper: {
    ...holidayBorderRadius.borderRadius8,
    elevation: 1,
    ...paddingStyles.pt24,
    backgroundColor: holidayColors.white,
    overflow: 'hidden',
  },
  title: {
    ...fontStyles.labelMediumBold,
    color: holidayColors.black,
    ...marginStyles.mb12,
    ...marginStyles.mh16,
  } as TextStyle,
});
export default InfoCards;
