import React, { useEffect } from 'react';
import { StyleSheet, View } from 'react-native';
import { vppAddon, VPPCardProps } from '../VPPTypes';
import { holidayBorderRadius } from 'mobile-holidays-react-native/src/Styles/holidayBorderRadius';
import { marginStyles } from 'mobile-holidays-react-native/src/Styles/Spacing';
import { holidayColors } from 'mobile-holidays-react-native/src/Styles/holidayColors';
import LinearGradient from 'react-native-linear-gradient';
import CardHeader from './CardHeader';
import CardDetails from './CardDetails';
import Divider from '../../Divider';
import CardFooter from './CardFooter';
import PersuasionInfo from './PersuassianInfo';
import { VPP_TRACKING_ACTIONS, VPP_TRACKING_EVENT, VPP_TRACKING_PAGES } from '../VPPConstant';
import { vppCaptureReviewClickEvents } from '../VPPUtils';
import { PDT_EVENT_TYPES } from 'mobile-holidays-react-native/src/utils/HolidayPDTConstants';
import isEmpty from 'lodash/isEmpty';

const getVPPCardStyle = (basePageCard?: boolean) => ({
  ...styles.cardContainer,
  borderColor: basePageCard ? holidayColors.grayBorder : holidayColors.transparent,
});

const VPPCard: React.FC<VPPCardProps> = (props: VPPCardProps) => {
  const {
    vppAddonDetail,
    onSelect,
    basePageCard,
    handleCtaClick,
    isSelected,
    trackLocalClickEvent = () => {},
    inclusionMessage=''
  } = props;
  const { addonHeader, addons, persuasionData } = vppAddonDetail;
  const { cardDetails, disclaimer = '' } = addons[0];
  const prop1val = basePageCard ? VPP_TRACKING_PAGES.BASE_CARD : VPP_TRACKING_PAGES.DETAIL;
  const subPageName = basePageCard ? '' : VPP_TRACKING_PAGES.DETAIL;
  useEffect(() => {
    let suffix = '';
    if (!isEmpty(persuasionData)) {
      suffix += '_Promo0';
    }
    if (!!disclaimer) {
      suffix += '_Promo1';
    }
    if (basePageCard && Array.isArray(addons) && addons[0]?.isSelected) {
      suffix += '|Pre-selected';
    }
    vppCaptureReviewClickEvents({
      trackLocalClickEvent,
      eventName: 'VPP',
      suffix: suffix,
      actionType: PDT_EVENT_TYPES.contentSeen,
      subPageName: subPageName,
      prop1: prop1val,
    });
  }, []);
  const onVPPSelectClick = (vppAddon: vppAddon) => {
    onSelect(vppAddon);
  };
  return (
    <View style={getVPPCardStyle(basePageCard)}>
      <LinearGradient
        colors={[holidayColors.white, holidayColors.orange]}
        angle={60}
        useAngle
        locations={[0.5, 1]}
      >
        <CardHeader {...addonHeader} inclusionMessage={inclusionMessage}/>
        <PersuasionInfo {...persuasionData} />
        <Divider style={styles.divider} />
        <CardDetails {...cardDetails} showCtas={basePageCard} handleCtaClick={handleCtaClick} />
        <Divider style={styles.fullDivider} />
        <CardFooter vppAddon={addons[0]} onSelect={onVPPSelectClick} isSelected={isSelected} />
      </LinearGradient>
    </View>
  );
};

const styles = StyleSheet.create({
  cardContainer: {
    ...holidayBorderRadius.borderRadius8,
    ...marginStyles.mb16,
    elevation: 1,
    overflow: 'hidden',
    ...marginStyles.mh16,
    borderWidth: 1,
  },
  divider: {
    ...marginStyles.mh16,
    width: 'auto',
    backgroundColor: holidayColors.grayBorder,
  },
  fullDivider: {
    backgroundColor: holidayColors.grayBorder,
  },
});

export default VPPCard;
