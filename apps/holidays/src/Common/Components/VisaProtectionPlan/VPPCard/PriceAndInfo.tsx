import React, { useCallback } from 'react';
import { holidayBorderRadius } from 'mobile-holidays-react-native/src/Styles/holidayBorderRadius';
import { holidayColors } from 'mobile-holidays-react-native/src/Styles/holidayColors';
import { fontStyles } from 'mobile-holidays-react-native/src/Styles/holidayFonts';
import { marginStyles, paddingStyles } from 'mobile-holidays-react-native/src/Styles/Spacing';
import { Image, StyleSheet, Text, TextStyle, TouchableOpacity, View } from 'react-native';
import { CardFooterProps } from '../VPPTypes';

const iconArrowBlue = require('@mmt/legacy-assets/src/ic_tick.webp');

const getCtaStyle = (isSelected?: boolean) => {
  return {
    backgroundColor: isSelected ? holidayColors.lightBlueBg : holidayColors.white,
    ...styles.cta,
  };
};

const PriceAndInfo: React.FC<CardFooterProps> = (props: CardFooterProps) => {
  const { vppAddon, onSelect, isSelected } = props;
  const { selectedText, cardDetails } = vppAddon;
  const { heading, subHeading } = cardDetails.price;
  const cta = isSelected ? 'SELECTED' : 'SELECT';

  const handleOnPress = useCallback(() => {
    onSelect(vppAddon);
  }, [onSelect, vppAddon]);
  return (
    <View style={styles.wrapper}>
      <View style={styles.priceWrapper}>
        {!isSelected && (
          <View style={styles.pricingDetails}>
            {heading && <Text style={styles.priceTextStyle}>+ {heading}</Text>}
            {subHeading && <Text style={styles.subHeadingStyle}>{subHeading}</Text>}
          </View>
        )}
        {isSelected && <Text style={styles.selectedTextStyle}>{selectedText}</Text>}
      </View>
      <TouchableOpacity activeOpacity={0.5} onPress={handleOnPress}>
        <View style={getCtaStyle(isSelected)}>
          {isSelected ? <Image source={iconArrowBlue} style={styles.ctaIconStyle} /> : null}
          <Text style={styles.ctaText}>{cta}</Text>
        </View>
      </TouchableOpacity>
    </View>
  );
};

const styles = StyleSheet.create({
  wrapper: {
    flexDirection: 'row',
  },
  pricingDetails: {
    flexDirection: 'column',
  },
  priceWrapper: {
    flex: 1,
  },
  priceTextStyle: {
    color: holidayColors.black,
    ...fontStyles.labelMediumBlack,
  } as TextStyle,
  subHeadingStyle: {
    color: holidayColors.gray,
    ...fontStyles.labelSmallRegular,
  } as TextStyle,
  selectedTextStyle: {
    flexDirection: 'column',
    ...fontStyles.labelSmallRegular,
    ...marginStyles.mr30,
    color: holidayColors.gray,
  } as TextStyle,
  cta: {
    alignItems: 'center',
    alignSelf: 'flex-end',
    justifyContent: 'flex-end',
    flexDirection: 'row',
    ...holidayBorderRadius.borderRadius8, // Adjust as needed
    borderColor: holidayColors.primaryBlue,
    borderWidth: 1,
    ...paddingStyles.pl12,
    ...paddingStyles.pr16,
    ...paddingStyles.pv6,
  },
  ctaIconStyle: { width: 16, height: 16, tintColor: holidayColors.primaryBlue },
  ctaText: {
    ...paddingStyles.pl4,
    ...fontStyles.labelSmallBlack,
    color: holidayColors.primaryBlue,
  } as TextStyle,
});

export default PriceAndInfo;
