import React from 'react';
import { PersuasionDataProps } from '../VPPTypes';
import { StyleSheet, Text, TextStyle, View } from 'react-native';
import { fontStyles } from 'mobile-holidays-react-native/src/Styles/holidayFonts';
import { holidayColors } from 'mobile-holidays-react-native/src/Styles/holidayColors';
import { marginStyles, paddingStyles } from 'mobile-holidays-react-native/src/Styles/Spacing';
import HolidayImageHolder from '../../HolidayImageHolder';
const PersuasionInfo = (props: PersuasionDataProps) => {
  const { heading, icon } = props;
  if(!heading){
    return null;
  }
  return (
    <View style={styles.container}>
      {icon && <HolidayImageHolder imageUrl={icon} style={styles.icon} resizeMode={'contain'} defaultImage={null}/>}
      {heading && <Text style={styles.text}>{heading}</Text>}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    alignItems: 'center',
    ...paddingStyles.ph16,
    ...paddingStyles.pb12,
  },
  icon: {
    width: 16,
    height: 16,
  },
  text: {
    ...fontStyles.labelSmallBold,
    ...marginStyles.ml12,
    color: holidayColors.yellow,
  } as TextStyle,
});

export default PersuasionInfo;
