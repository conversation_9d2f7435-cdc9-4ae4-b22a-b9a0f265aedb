import React from 'react';
import { FlatList, TouchableOpacity, Text, ListRenderItem, View, TextStyle } from 'react-native';
import { ActionDetailsProps, ctaItemProps } from '../VPPTypes';
import { paddingStyles } from 'mobile-holidays-react-native/src/Styles/Spacing';
import { holidayColors } from 'mobile-holidays-react-native/src/Styles/holidayColors';
import { fontStyles } from 'mobile-holidays-react-native/src/Styles/holidayFonts';

const ActionDetails: React.FC<ActionDetailsProps> = (props: ActionDetailsProps) => {
  const { ctas, handleCtaClick } = props;

  const handleOnPress = (item: ctaItemProps) => {
    if (handleCtaClick) {
      handleCtaClick(item);
    }
  };

  const renderItem: ListRenderItem<ctaItemProps> = ({ item }) => {
    return (
      <TouchableOpacity onPress={() => handleOnPress(item)}>
        <Text style={styles.actionTextStyle}>{item.label}</Text>
      </TouchableOpacity>
    );
  };

  return (
    <FlatList
      data={ctas}
      renderItem={renderItem}
      horizontal
      scrollEnabled={false}
      contentContainerStyle={styles.flatListWrapperStyle}
      ItemSeparatorComponent={() => <View style={styles.itemSeperator} />}
      keyExtractor={(item, index) => index.toString()}
    />
  );
};

const styles = {
  flatListWrapperStyle: {
  },
  itemSeperator: {
    width: 20,
  },
  actionTextStyle: {
    ...fontStyles.labelSmallBold,
    color: holidayColors.primaryBlue,
  } as TextStyle,
};
export default ActionDetails;
