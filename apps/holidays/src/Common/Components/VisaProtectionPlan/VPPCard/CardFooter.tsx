import React from 'react';
import { CardFooterProps } from '../VPPTypes';
import { View, StyleSheet, Text, TextStyle } from 'react-native';
import { marginStyles, paddingStyles } from 'mobile-holidays-react-native/src/Styles/Spacing';
import PriceAndInfo from './PriceAndInfo';
import { holidayColors } from 'mobile-holidays-react-native/src/Styles/holidayColors';
import { VPP_COLORS } from '../VPPConstant';
import { fontStyles } from '../../../../Styles/holidayFonts';

const CardFooter: React.FC<CardFooterProps> = (props: CardFooterProps) => {
  const { vppAddon } = props;
  const { disclaimer } = vppAddon;
  return (
    <>
      <View style={styles.wrapper}>
        <PriceAndInfo {...props} />
      </View>
      <Text style={styles.disclaimerTextStyle}>{disclaimer}</Text>
    </>
  );
};

const styles = StyleSheet.create({
  wrapper: {
    ...marginStyles.ma16,
  },
  disclaimerTextStyle: {
    ...paddingStyles.pv8,
    ...paddingStyles.ph12,
    ...fontStyles.labelSmallBold,
    backgroundColor: VPP_COLORS.fadeYellow,
    color: holidayColors.yellow,
  } as TextStyle,
});

export default CardFooter;
