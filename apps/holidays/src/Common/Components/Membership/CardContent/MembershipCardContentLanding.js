import React from 'react';
import { Pressable, Text } from 'react-native';
import styles from '../styles/MembershipCardStyles';
import DynamicTextWrapper from '../DynamicTextWrapper';
import { noop } from 'lodash';
import { getTextStyles } from '../utils/MembershipUtils';

const MembershipCardContent = (props) => {
  const {
    onKnowMorePress = noop,
    cta = {},
    headerText = [],
  } = props || {};
  return (
    <>
      {headerText.length > 0 && (
        <Pressable onPress={cta?.text ? () => onKnowMorePress(cta?.text) : noop}>
          <Text style={styles.landingHeaderLineHeight}>
            <DynamicTextWrapper textData={headerText} />
            {Object.keys(cta).length > 0 && (
              <Text style={[styles.knowMoreText, getTextStyles(cta?.textStyle)]}>{` `}{cta?.text}</Text>
            )}
          </Text>
        </Pressable>
      )}
    </>
  );
};

export default MembershipCardContent;
