import React from 'react';
import { Text } from 'react-native';
import { getTextStyles } from '../utils/MembershipUtils';
import { hasMoreElements } from '../../../../utils/HolidayUtils';

/**
 * DynamicTextWrapper component.
 *
 * This component recursively renders a list of text elements with their respective styles.
 * It handles the case where a new line should be added between text elements based on the `nextLine` property.
 *
 * @param {Object} props - The component props.
 * @param {Array} props.textData - The array of text data to render.
 * @param {number} props.index - The current index of the text data being rendered.
 * @returns {React.Element|null} The rendered text element or null if no text data is provided.
 */
const DynamicTextWrapper = (props) => {
  const { textData = [], index = 0, containerStyles = {}} = props || {};
  // Early return if textData is empty
  if (textData.length === 0) return null;

  // Get the current text data and determine if a new line should be added
  const currentText = textData[index];
  const nextLine = index !== 0 && (textData[index - 1]?.nextLine ? '\n' : ' ');

  // Function to render the next DynamicTextWrapper
  const renderNextWrapper = () => {
    if (hasMoreElements(index, textData)) {
      return <DynamicTextWrapper textData={textData} index={index + 1} />;
    }
    return null;
  };

  return (
    <>
      <Text style={[containerStyles , getTextStyles(currentText?.textStyle), { padding: 1} ]}>
        {nextLine}
        {currentText?.text}
        {renderNextWrapper()}
      </Text>
    </>
  );
};

export default DynamicTextWrapper;
