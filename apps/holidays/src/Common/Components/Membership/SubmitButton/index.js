import React from 'react';
import { View } from 'react-native';
import styles from './styles';
import PrimaryButton from '../../Buttons/PrimaryButton';
import { noop } from 'lodash';

// SubmitButton component
const SubmitButton = (props) => {
  const { text = '', ctaButtonClick = noop, isDisable = false, containerStyles = {} } = props || {};
  return (
    text.length > 0 && (
      <View style={[styles.submitButn, containerStyles]}>
        <PrimaryButton
          buttonText={text}
          btnContainerStyles={styles.btnStyles}
          handleClick={ctaButtonClick}
          isDisable={isDisable}
        />
      </View>
    )
  );
};

export default React.memo(SubmitButton);
