import React, { memo } from 'react';
import { View, Image, TouchableOpacity, Text ,ScrollView} from 'react-native';
import styles from './styles';
import BottomSheetDescription from './BottomSheetDescription';
import SubmitButton from '../SubmitButton';
import BottomSheetHeader from './BottomSheetHeader';
import { PDTConstants } from '../../../../Review/HolidayReviewConstants';
import { PDT_EVENT_TYPES } from 'mobile-holidays-react-native/src/utils/HolidayPDTConstants';
import { logHolidayReviewPDTClickEvents } from 'mobile-holidays-react-native/src/PhoenixReview/Utils/HolidayReviewPDTTrackingUtils';
import { getImageUrl, IMAGE_ICON_KEYS } from '../../HolidayImageUrls';
import GifPlayOnce from 'mobile-holidays-react-native/src/Common/Components/Membership/ReviewBottomSheet/GifPlayOnce';
import { isIos } from '@mmt/core/helpers/platformHelper';
import { noop } from 'lodash';
import { MMT_BLACK_ATTACH_CARD_CALL } from 'mobile-holidays-react-native/src/PhoenixReview/Utils/HolidayReviewConstants';
// MMTBlackBottomSheet.js
const MMTBlackBottomSheet = ({
  bottomSheetDetail = {},
  togglePopup = () => {},
  ctaButtonClick = () => {},
  trackReviewLocalClickEvent = () => {},
  attachGiftCardData = null,
  isGifVisible = true,
  handleTermConditionClick = noop,
  handleContinueWithoutClaim = noop,
}) => {
  const {
    overlayImage = '',
    headerText = [],
    modalBody = {},
    myCashFooter = {},
    borderGradient = '',
    ctaButton = {},
    tnc = null,
  } = bottomSheetDetail;
  const [showGif, setShowGif] = React.useState(false);

  const isAttachCardButton = (ctaButton?.type === MMT_BLACK_ATTACH_CARD_CALL) && !attachGiftCardData?.success ;


  const ImageHolder = isIos() ? Image : GifPlayOnce;
  React.useEffect(() => {
    trackReviewLocalClickEvent(isGifVisible ? PDTConstants.GC_POPUP_SEEN_AUTO : PDTConstants.GC_POPUP_SEEN);
    logHolidayReviewPDTClickEvents({
      actionType: PDT_EVENT_TYPES.contentSeen,
      value: isGifVisible ? PDTConstants.GC_POPUP_SEEN_AUTO : PDTConstants.GC_POPUP_SEEN,
      shouldTrackToAdobe:false
    });
  }, []);
  return (
    <View style={styles.bottomSheetContainer}>
    <BottomSheetHeader headerImage={overlayImage} togglePopup={isAttachCardButton ? noop : togglePopup} {...{isAttachCardButton}} />
    <ScrollView showsVerticalScrollIndicator={false} style={styles.bottomSheetChildHeader}>
      {showGif && isGifVisible && (
        <View style={styles.overlayImageContainer}>
          <ImageHolder
            source={{ uri: getImageUrl(IMAGE_ICON_KEYS.CELEBTATION) }}
            style={styles.overlayImage}
          />
        </View>
      )}
      <View>
      <BottomSheetDescription
        tncData={tnc}
        {...{handleTermConditionClick,headerText,modalBody,myCashFooter,borderGradient,setShowGif}}
      />
      </View>
    </ScrollView>
    <View>
    {!attachGiftCardData?.success && (
        <View style={styles.submitButtonContainer}>
          <SubmitButton text={ctaButton?.text} ctaButtonClick={ctaButtonClick} />
        </View>
      )}
      {isAttachCardButton && (
        <TouchableOpacity style={styles.withoutClaimWrapper} onPress={handleContinueWithoutClaim}>
          <Text style={styles.withoutClaimTxt}>Continue without claiming</Text>
        </TouchableOpacity>
      )}
      </View>
    </View>
  );
};

export default memo(MMTBlackBottomSheet);
