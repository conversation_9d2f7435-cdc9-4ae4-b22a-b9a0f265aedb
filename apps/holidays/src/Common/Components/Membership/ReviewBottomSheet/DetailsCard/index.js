import React, { useEffect, useRef, useState } from 'react';
import { Image, Text, View, Animated, Dimensions } from 'react-native';
import LinearGradient from 'react-native-linear-gradient';
import styles from './styles.js';
import DynamicTextWrapper from '../../DynamicTextWrapper/index.js';
import { holidayColors } from 'mobile-holidays-react-native/src/Styles/holidayColors.js';
import { getTextStyles, validateBorderGradientColors } from '../../utils/MembershipUtils.js';
import { noop } from 'lodash';

/**
 * Renders a details card component.
 *
 * @param {Object} props - The component props.
 * @param {string} props.type - The type of the details card.
 * @returns {JSX.Element} The rendered details card component.
 */
const DetailsCard = (props) => {
  const { gc = {}, borderGradient = '', setShowGif = noop } = props || {};
  const animation = useRef(new Animated.Value(0)).current; // Start position outside the screen
  const effectivePriceOpacity = useRef(new Animated.Value(0)).current;
  const backgroundColor = useRef(new Animated.Value(0)).current;


  useEffect(() => {
    const startAnimation = () => {
      Animated.parallel([
        Animated.timing(backgroundColor, {
          toValue: 1,
          duration: 500,
          useNativeDriver: false,
          delay:500
        }).start(),
        Animated.timing(animation, {
          toValue: 12, // Move to the right side of the screen
          duration: 500, // Duration of the animation
          useNativeDriver: true,
          delay:500
        }),
        Animated.timing(effectivePriceOpacity, {
          toValue: 1, // Increase opacity
          duration: 500,
          useNativeDriver: true,
          delay:500
        }),
      ]).start();
      setShowGif(true);
    };
    startAnimation();
  }, []);
    const interpolatedBackgroundColor = backgroundColor.interpolate({
      inputRange: [0, 1],
      outputRange: [holidayColors.transparentWithOpacity, holidayColors.white],
    });
    return (
    <LinearGradient
      colors={
        validateBorderGradientColors(gc.gradiantColor)
          ? gc.gradiantColor.split(',')
          : [holidayColors.white, holidayColors.white]
      }
      start={{ x: 0, y: 0 }}
      end={{ x: 1, y: 0 }}
      style={styles.gradientBorder}
    >
      <View>
        {/* Render the heading for the upper section */}
        {gc?.header && (<DynamicTextWrapper textData={gc?.header} />)}
        <View style={gc?.header ? {} : {marginTop: -12}}>
          {/* Render the gift cards */}
          {gc?.gcList?.map((item) => (
            <View style={styles.giftCardFlex} key={item.name}>
              <View style={styles.giftCardIconTxtFlex}>
                {/* Render the gift card icon */}
                <Image  source={{uri: item.icon}} resizeMode='contain' style={styles.giftCardImgStyle} />
                {/* Render the text for the gift card */}
                <Text style={styles.subHeaderTxt}>{item.name}</Text>
              </View>
              <View>
                {/* Render the price for the gift card */}
                <Text style={styles.subHeaderTxt}>{item.desc}</Text>
              </View>
            </View>
          ))}
        </View>
      </View>
      <Animated.View style={[styles.lowerSectionDetailsCard,{ backgroundColor: interpolatedBackgroundColor}]}>
        {gc?.totalPrice && Object.keys(gc.totalPrice).length > 0 && (
          <Animated.View
            style={{
              width: '46%',
              transform: [{ translateX: animation}],
            }}
          >
            <View>
              {/* Render the header for the final total */}
              <Text style={[styles.finalTotalHeader, getTextStyles(gc?.totalPrice?.text?.textStyle)]}>
                {gc?.totalPrice?.text?.text}
              </Text>
            </View>
            <View>
              {/* Render the final price */}
              <Text style={[styles.finalPrice, getTextStyles(gc?.totalPrice?.price?.textStyle)]}>
                {gc?.totalPrice?.price?.text}
              </Text>
            </View>
          </Animated.View>
        )}
        {gc?.effectivePrice && Object.keys(gc.effectivePrice).length > 0 && (
          <Animated.View
            style={[
              styles.animatedbox,
              { opacity: effectivePriceOpacity }
            ]}
          >
            <LinearGradient
              colors={
                validateBorderGradientColors(borderGradient)
                  ? borderGradient.split(',')
                  : [holidayColors.black, holidayColors.black]
              }
              start={{ x: 0, y: 1 }}
              end={{ x: 0, y: 0 }}
              style={styles.animatedborderbox}
            >
              <LinearGradient
                colors={[holidayColors.white, holidayColors.white]}
                start={{ x: 0, y: 0 }}
                end={{ x: 1, y: 0 }}
                style={styles.animatedborderinsidebox}
                accessible
              >
                <Text style={[styles.packageText, getTextStyles(gc?.effectivePrice?.text?.color)]}>
                  {gc.effectivePrice?.text?.text}
                </Text>
                <Text
                  style={[styles.packagePriceText, getTextStyles(gc?.effectivePrice?.price?.textStyle)]}
                >
                  {gc.effectivePrice?.price?.text}
                </Text>
              </LinearGradient>
            </LinearGradient>
          </Animated.View>
        )}
      </Animated.View>
    </LinearGradient>
  );
};

export default React.memo(DetailsCard);
