import React from 'react';
import { View, Text, Image } from 'react-native';
import styles from './styles';
import DetailsCard from './DetailsCard';
import DynamicTextWrapper from '../DynamicTextWrapper';
import { noop } from 'lodash';
import TermAndCondition from '../TermAndCondition';

const BottomSheetDescription = ({
  headerText = [],
  modalBody = {},
  myCashData = {},
  borderGradient = '',
  setShowGif=noop,
  handleTermConditionClick = noop,
  tncData = null,
}) => {
  const { gc = {} } = modalBody;
  return (
    <>
      {headerText.length > 0 && (
        <View style={styles.descHeadingCont}>
          <DynamicTextWrapper textData={headerText} />
        </View>
      )}
      <DetailsCard {...{ gc, borderGradient , setShowGif }} />
      {Object.keys(myCashData).length > 0 && (
        <View style={styles.myCashDesc}>
          <Image style={styles.rewardsBullet} source={{ uri: myCashData.icon }} />
          <View>
            <Text style={styles.myCashDescTxt}>{myCashData.description}</Text>
          </View>
        </View>
      )}
      {tncData && <TermAndCondition {...{ tncData, handleTermConditionClick }} />}
    </>
  );
};

export default React.memo(BottomSheetDescription);
