import React, { useState, useEffect } from 'react';
import { View } from 'react-native';
import HolidayImageHolder from '../../HolidayImageHolder';
import { RESIZE_MODE_IMAGE } from '@mmt/holidays/src/HolidayConstants';
import { isMobileClient, isRawClient } from '../../../../utils/HolidayUtils';

const GifPlayOnce = ({ source, style }) => {
  const [key, setKey] = useState(0);
  const [isVisible, setIsVisible] = useState(true);

  const FastImage = isRawClient() ? Image : require('react-native-fast-image');

  useEffect(() => {
    // Reset visibility when source changes
    setIsVisible(true);
    setKey(prevKey => prevKey + 1);
  }, [source]);

  const handleLoadEnd = () => {
    // Hide the GIF after it has played once
    setTimeout(() => {
      setIsVisible(false);
    }, 6000);
  };

  if (!isVisible) {
    return null;
  }

  return (
    <View style={style}>
      {
        isMobileClient() ? <FastImage
        key={key}
        style={style}
        source={source}
        resizeMode={FastImage.resizeMode.contain}
        onLoadEnd={handleLoadEnd}
      /> : 
      <HolidayImageHolder 
        style={style}
        imageUrl={source}
        resizeMode={RESIZE_MODE_IMAGE.CONTAIN}
      />
      }
    </View>
  );
};

export default GifPlayOnce;
