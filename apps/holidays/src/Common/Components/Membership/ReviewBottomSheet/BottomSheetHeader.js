import React from 'react';
import { View, Image, TouchableOpacity } from 'react-native';
import CrossIcon from '@mmt/legacy-assets/src/crossIconV2.webp';
import styles from './styles';

const BottomSheetHeader = (props) => {
  const { headerImage = '', togglePopup = () => {}, isAttachCardButton = false } = props || {};
  return (
    <>
      {headerImage.length > 0 && (
        <Image style={styles.memberShipBadge} source={{ uri: headerImage }} />
      )}
      {!isAttachCardButton && (<TouchableOpacity style={styles.crossimageContainer} onPress={togglePopup}>
        <Image style={styles.headerCrossIcon} source={CrossIcon} />
      </TouchableOpacity>)}
    </>
  );
};

export default React.memo(BottomSheetHeader);
