import { StyleSheet } from 'react-native';
import { holidayColors } from '../../../../Styles/holidayColors';
import { fontStyles } from '../../../../Styles/holidayFonts';
import { marginStyles, paddingStyles } from '../../../../Styles/Spacing';

export default StyleSheet.create({
  gradientBorder: {
    borderRadius: 13,
    padding: 1,
  },
  container: {
    backgroundColor: holidayColors.white,
    paddingHorizontal: 12,
    paddingTop: 18,
    paddingBottom: 12,
    borderRadius: 12,
  },
  title: {
    ...fontStyles.labelBaseBlack,
    marginBottom: 12,
    color: holidayColors.black,
  },
  itemContainer: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginTop: 4,
    flexWrap: 'wrap',
  },
  bullet: {
    height: 8,
    width: 8,
    marginTop: 4,
    // alignSelf: 'center',
  },
  itemText: {
    flex: 1,
    marginLeft: 8,
  },
  knowMoreText: {
    ...paddingStyles.pl2,
    ...paddingStyles.pt4,
    marginBottom: -2,
  },
  cardImage: {
    height: 28,
    resizeMode: 'contain',
    position: 'absolute',
    top: -13,
    zIndex: 3,
    ...marginStyles.ml12,
    width: 170,
  },
  landingHeaderLineHeight: {
    lineHeight: 18,
  }
});
