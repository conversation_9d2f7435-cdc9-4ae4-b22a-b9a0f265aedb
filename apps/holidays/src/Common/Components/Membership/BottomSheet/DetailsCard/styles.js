import { holidayColors } from 'mobile-holidays-react-native/src/Styles/holidayColors';
import { fontStyles } from 'mobile-holidays-react-native/src/Styles/holidayFonts';
import { StyleSheet } from 'react-native';

const styles = StyleSheet.create({
  gradientBorder: {
    borderRadius: 16,
    padding: 1,
  },
  container: {
    backgroundColor: holidayColors.white,
    paddingHorizontal: 12,
    paddingVertical: 16,
    borderRadius: 16,
  },
  detailsCardCont: {},
  upperSectionDetailsCard: {
    display: 'flex',
    flexDirection: 'row',
  },
  upperSectionDetailsCardBorder: {
    paddingBottom: 16,
    borderBottomWidth: 1,
    borderBottomColor: holidayColors.grayBorder,
  },
  referEarnImgCont: {
    marginTop: 2,
  },
  referEarnImg: {
    height: 24,
    width: 24,
  },
  upperSectionDetailsCardDesc: {
    marginLeft: 12,
    flex:1
  },
  additionalSaveTxtCont: {},
  bottomSheetDetailCardHeading: {
    color: holidayColors.black,
    ...fontStyles.labelBaseBold,
    wordWrap: 'break-word',
  },
  maxGiftCards: {
    color: holidayColors.gray,
    ...fontStyles.labelSmallRegular,
    marginTop: 4,
  },
  giftCardFlex: {
    display: 'flex',
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 12,
    wordWrap: 'break-word',
  },
  giftCardIconTxtFlex: {
    flexDirection: 'row',
    width: '70%',
  },
  giftCardImgStyle: {
    height: 20,
    width: 20,
    marginRight: 6,
    alignSelf: 'center',
  },
  subHeaderTxt: {
    color: holidayColors.gray,
    ...fontStyles.labelSmallRegular,
  },
  myCash: {
    flexDirection: 'column',
    justifyContent: 'center',
  },
  myCashIcon: {
    height: 24,
    width: 24,
  },
  lowerSectionDetailsCard: {
    marginTop: 16,
    display: 'flex',
    flexDirection: 'row',
  },
  lowerSectionDetailsCardDesc: {
    marginLeft: 13,
  },
  offerTxtCont: {
    marginBottom: 4,
  },
});

export default styles;
