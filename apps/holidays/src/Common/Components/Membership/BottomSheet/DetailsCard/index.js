import React from 'react';
import { Image, Text, View } from 'react-native';
import LinearGradient from 'react-native-linear-gradient';
import DynamicTextWrapper from '../../DynamicTextWrapper';
import styles from './styles.js';
import { getBottomSheetDescriptionGCListMargin, validateBorderGradientColors } from '../../utils/MembershipUtils';
import { holidayColors } from 'mobile-holidays-react-native/src/Styles/holidayColors';
import { marginStyles } from 'mobile-holidays-react-native/src/Styles/Spacing';

/**
 * Renders a details card component.
 *
 * @param {Object} props - The component props.
 * @returns {JSX.Element} The rendered details card component.
 */
const DetailsCard = ({ modalBody = {}, borderGradient = '' }) => {
  const { gc = {}, mycash = {} } = modalBody;

  return (
    <LinearGradient
      colors={
        validateBorderGradientColors(borderGradient)
          ? borderGradient.split(',')
          : [holidayColors.black, holidayColors.black]
      }
      start={{ x: 0, y: 1 }}
      end={{ x: 0, y: 0 }}
      style={styles.gradientBorder}
    >
      <LinearGradient
        colors={
          validateBorderGradientColors(gc.gradiantColor)
            ? gc.gradiantColor.split(',')
            : [holidayColors.white, holidayColors.white]
        }
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 0 }}
        style={styles.container}
      >
        <View style={styles.detailsCardCont}>
          {/* Upper section of the details card */}
          <View
            style={[
              styles.upperSectionDetailsCard,
              Object.keys(mycash).length > 0 ? styles.upperSectionDetailsCardBorder : {},
            ]}
          >
            {/* Icon for the "Refer & Earn" section */}
            {gc.icon && (
              <View style={styles.referEarnImgCont}>
                <Image source={{ uri: gc.icon }} resizeMode='contain' style={styles.referEarnImg} />
              </View>
            )}
            <View style={[styles.upperSectionDetailsCardDesc, gc.icon ? {} : { marginLeft: 0, width: '100%' }]}>
              {gc.header && (
                <View>
                  <DynamicTextWrapper textData={gc.header} />
                </View>
              )}
              {gc.description && (
                <View style={{ ...marginStyles.mt4 }}>
                  <DynamicTextWrapper textData={gc.description} />
                </View>
              )}
              <View style={getBottomSheetDescriptionGCListMargin(gc.header, gc.description)}>
                {/* Render the default gift cards */}
                {gc?.gcList?.map((item, index) => (
                  <View style={styles.giftCardFlex} key={index + item?.name}>
                    <View style={styles.giftCardIconTxtFlex}>
                      {/* Icon for each gift card */}
                      {item?.icon && (
                          <Image source={{ uri: item?.icon }} resizeMode='contain' style={styles.giftCardImgStyle} />
                      )}
                      {/* Text for each gift card */}
                      <Text style={styles.subHeaderTxt}>{item?.name}</Text>
                    </View>
                    <View>
                      {/* Price of each gift card */}
                      <Text style={styles.subHeaderTxt}>{item?.desc}</Text>
                    </View>
                  </View>
                ))}
              </View>
            </View>
          </View>
          {/* Lower section of the details card */}
          {Object.keys(mycash).length > 0 && (
            <View style={styles.lowerSectionDetailsCard}>
              <View style={styles.myCash}>
                {/* Icon for the "My Cash" section */}
                <Image source={{ uri: mycash.icon }} style={styles.myCashIcon} />
              </View>
              <View style={styles.lowerSectionDetailsCardDesc}>
                <View style={styles.offerTxtCont}>
                  {/* Heading for the "My Cash" section */}
                  <Text style={styles.bottomSheetDetailCardHeading}>{mycash.header}</Text>
                </View>
                <View>
                  {/* Description for the "My Cash" section */}
                  <Text style={styles.subHeaderTxt}>{mycash.description}</Text>
                </View>
              </View>
            </View>
          )}
        </View>
      </LinearGradient>
    </LinearGradient>
  );
};

export default React.memo(DetailsCard);
