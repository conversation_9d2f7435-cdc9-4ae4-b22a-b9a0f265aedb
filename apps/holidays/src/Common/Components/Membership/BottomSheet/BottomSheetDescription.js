import React from 'react';
import { View } from 'react-native';
import DetailsCard from './DetailsCard';
import DynamicTextWrapper from '../DynamicTextWrapper/';
import TermAndCondition from '../TermAndCondition';
import styles from './styles';
import { noop } from 'lodash';

const BottomSheetDescription = ({
  headerText = [],
  modalBody = {},
  tncData = {},
  borderGradient = '',
  handleTermConditionClick = noop,
}) => {
  return (
    <View style={styles.bottomSheetDescCont}>
      <View style={styles.descHeadingCont}>
        <DynamicTextWrapper textData={headerText} />
      </View>
      <DetailsCard {...{ modalBody, borderGradient }} />
      <TermAndCondition {...{ tncData, handleTermConditionClick }} />
    </View>
  );
};

export default React.memo(BottomSheetDescription);
