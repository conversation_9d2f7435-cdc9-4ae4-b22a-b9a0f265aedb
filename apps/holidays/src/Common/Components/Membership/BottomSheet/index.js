import React, {useEffect} from 'react';
import { ScrollView, View } from 'react-native';
import BottomSheetDescription from './BottomSheetDescription';
import SubmitButton from '../SubmitButton';
import BottomSheetHeader from './BottomSheetHeader';
import { noop } from 'lodash';
import { PDT_EVENT_TYPES } from 'mobile-holidays-react-native/src/utils/HolidayPDTConstants';
import { TRACKING_EVENTS } from 'mobile-holidays-react-native/src/HolidayTrackingConstants';
import  styles  from './styles';
/**
 * MMTBlackBottomSheet component.
 *
 * This component represents a bottom sheet that displays information about a membership tier.
 * It consists of a ScrollView container that contains a Header component and a Description component.
 * At the bottom, there is a SubmitButton component.
 *
 * @param {Object} props - The component props.
 * @param {Object} props.bottomSheetDetail - The details of the bottom sheet.
 * @param {Function} props.togglePopup - The function to toggle the popup.
 * @param {Function} props.ctaButtonClick - The function to handle the CTA button clicks.
 * @returns {React.Element} The rendered MMTBlackBottomSheet component.
 */
const MMTBlackBottomSheet = (props) => {
  const { bottomSheetDetail = {}, mmtBlackBucketDetail = {}, togglePopup = noop, ctaButtonClick = noop, mmtBlackPdtEvents = noop, handleTermConditionClick = noop, trackClickEvent = noop } = props || {};
  const { overlayHeaderImage, headerText, modalBody, tnc, borderGradient, ctaButton } = bottomSheetDetail || {};

  useEffect(() => {
    const eventName = 'GC_Popup_Seen';
    const {gcBucket = null, myCashBucket = null, effectivePriceBucket = null} = mmtBlackBucketDetail || {};
    const evar46 = `${gcBucket}|${myCashBucket}|${effectivePriceBucket}`;
    mmtBlackPdtEvents({
      actionType: PDT_EVENT_TYPES.contentSeenUser,
      value: eventName,
    });
    trackClickEvent({ eventName, omniData: {[TRACKING_EVENTS.M_V46]: evar46} });
  }, []);

  return (
    <View style={styles.topcontainer}>
       <BottomSheetHeader
          headerImage={overlayHeaderImage}
          togglePopup={togglePopup}
        />
      <ScrollView showsVerticalScrollIndicator={false}>
        <BottomSheetDescription
          tncData={tnc}
          {...{ handleTermConditionClick, borderGradient, modalBody, headerText }}
        />
      </ScrollView>
      <View style={styles.submitbtnContainer}>
        <SubmitButton
          text={ctaButton?.text}
          ctaButtonClick={ctaButtonClick}
        />
      </View>
    </View>
  );
};

export default React.memo(MMTBlackBottomSheet);
