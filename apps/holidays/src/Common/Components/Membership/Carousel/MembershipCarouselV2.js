//@todo to make this file typescript in march release

import React, { useCallback, useRef, useEffect } from 'react';
import { View } from 'react-native';
import MembershipCard from '../MembershipCard';
import { MEMBERSHIP_CARD_TYPE } from '../utils/constants';
import Carousel from '../../Carousel';
import { getMemberShipCardTypeWithOrder } from '../utils/MembershipUtils';
import { PDT_EVENT_TYPES } from '../../../../utils/HolidayPDTConstants';
import { PDTConstants } from '../../../../Review/HolidayReviewConstants';

const MembershipCarouselV2 = (props) => {
  const {
    memberShipCardData,
    onKnowMorePress,
    mmtblackPdtEvents,
    trackMemberShipLoadEvent,
    contentType,
    containerStyles,
    customCardsWprStyle,
    cardItemStyle,
    gradientStyle,
    showFullBorderGradient,
    customStyles = {},
  } = props;

  useEffect(() => {
    const eventName = `Loaded_section_TOP`;
    const prop1 = getMemberShipCardTypeWithOrder(memberShipCardData);
    trackMemberShipLoadEvent({
      eventName,
      prop1,
    });
    mmtblackPdtEvents({
      actionType: PDT_EVENT_TYPES.contentSeen,
      value: `${eventName}|${prop1}`,
      shouldTrackToAdobe:false
    });
  }, []);

  const onViewableItemsChanged = useCallback(({ viewableItems, changed }) => {
    viewableItems.forEach(({ item }) => {
      const isPersonalisation = item?.type === MEMBERSHIP_CARD_TYPE.PZN;
      const prop1Val = isPersonalisation ? item?.personalizationDetail?.section?.type : '';
      const value = isPersonalisation
        ? `${PDTConstants.PZN_CARD_VIEWED}|${prop1Val}`
        : `${PDTConstants.GC_CARD_SEEN}`;
      mmtblackPdtEvents({ actionType: PDT_EVENT_TYPES.contentSeenUser, value ,shouldTrackToAdobe:false});
      trackMemberShipLoadEvent({
        eventName: isPersonalisation ? PDTConstants.PZN_CARD_VIEWED : PDTConstants.GC_CARD_SEEN,
        prop1: prop1Val,
        isPersonalisation,
      });
    });
  }, []);

  const viewabilityConfig = useRef({
    viewAreaCoveragePercentThreshold: 80,
    minimumViewTime: 0, // Reduced to trigger immediately
  });
  // Create viewabilityConfigCallbackPairs using useRef
  const viewabilityConfigCallbackPairs = useRef([
    { viewabilityConfig: viewabilityConfig.current, onViewableItemsChanged },
  ]);

  const renderItem = (item) => {
    const isPersonalisation = item?.type === MEMBERSHIP_CARD_TYPE.PZN;
    const details = isPersonalisation ? item?.personalizationDetail : item?.mmtBlackDetail;
    const { section } = details || {};
    return (
      <View style={cardItemStyle}>
        <MembershipCard
          sectionDetails={section}
          containerStyles={[{ zIndex: 1 }, containerStyles]}
          onKnowMorePress={onKnowMorePress}
          showFullBorderGradient={showFullBorderGradient}
          contentType={contentType}
          gradientStyle={gradientStyle}
          sendLoadEvents={false}
          mmtBlackPdtEvents={mmtblackPdtEvents}
        />
      </View>
    );
  };

  return (
    <Carousel
      data={memberShipCardData.cards}
      keyIdentifier={(item) => JSON.stringify(item)} // Add appropriate key identifier
      headerComp={<View />} // Add appropriate header component
      autoSlide={false}
      showDots={memberShipCardData.cards.length > 1}
      internalDots={false}
      dotHeight={6}
      dotWidth={6}
      onScroll={() => {}}
      dotsGap={3}
      customStyles={memberShipCardData.cards.length > 1 && customStyles}
      customCardsWprStyle={memberShipCardData.cards.length > 1 && customCardsWprStyle}
      viewabilityConfigCallbackPairs={viewabilityConfigCallbackPairs.current}
      bannerCard={renderItem}
    />
  );
};

export default MembershipCarouselV2;
