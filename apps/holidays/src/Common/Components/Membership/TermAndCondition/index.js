import React from 'react';
import { Text, View } from 'react-native';
import styles from './styles';
import { noop } from 'lodash';

const TermAndCondition = (props) => {
  const { tncData = {}, handleTermConditionClick = noop } = props || {};

  const handleTncLink = () => {
    handleTermConditionClick(tncData?.linkUrl);
  };

  return (
    <View style={styles.tncCont}>
      <View>
        <Text style={styles.tncTxt}>{tncData.header}</Text>
      </View>
      <View style={styles.tncDesc}>
        <Text style={styles.tncDescTxt}>
          {tncData.description}
          <Text suppressHighlighting={true} style={[styles.tncLink, { color: tncData.linkColor }]}
                onPress={handleTncLink}>
            {` `}
            {tncData.linkText}
          </Text>
        </Text>
      </View>
    </View>
  );
};

export default TermAndCondition;
