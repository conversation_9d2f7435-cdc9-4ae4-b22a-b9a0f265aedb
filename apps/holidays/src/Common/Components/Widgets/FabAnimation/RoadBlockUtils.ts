/**
 * Interface for InfoAttr structure similar to the sample code
 */
interface InfoAttr {
  attr1?: string;
  attr2?: string;
  attr3?: string;
  attr4?: string;
  fromCity?: string;
  destination?: string;
  pageName?: string;
  device?: string;
  chatId?: string;
  evar83?: string;
  evar57?: string;
  evar17?: string;
  evar81?: string;
  proactive?: boolean;
  m_v108?: string;
  packageId?: number;
  [key: string]: any; // Allow additional properties
}

/**
 * Platform types
 */
type Platform = 'ios' | 'android' | 'pwa' | 'desktop';

/**
 * Query parameters interface
 */
interface QueryParams {
  [key: string]: string;
}

/**
 * Chat configuration context interface
 */
interface ChatContext {
  lob: string;
  lobCategory: string;
  view: string;
  prevPage: string | null;
  platform?: Platform;
}

/**
 * Page context interface
 */
interface PageContext {
  lob: string;
  lobCategory: string;
  pageName: string;
  prevPageName: string;
  lobFunnel: string;
  subpageName: string;
  funnelStep: string;
  pgTimeInterval: string;
  navigation: string;
  subLob: string;
  pageUrl: string;
  platform?: string;
  device?: string;
  channel?: string;
}

/**
 * Search context interface
 */
interface SearchContext {
  lob: string;
  funnelSource: string;
  lobCategory: string;
  fromCity?: string;
  destination?: string;
  travelDate?: string;
  packageId?: string;
  dynamicPackageId?: string;
  paxConfig?: string;
  from?: {
    locus: {
      name: string;
      cityName: string;
    };
    cityName: string;
  };
  to?: {
    locus: {
      name: string;
      cityName: string;
    };
    cityName: string;
  };
}

/**
 * Expert metadata interface
 */
interface ExpertMetadata {
  lob: string;
  funnelType: string;
  page: string;
  crid: string;
  itid: string;
  redirectType: string;
  summarize: string;
  lobCode: string;
  src: string;
  currency: string;
  userCurrency: string;
  botType: string;
  entityType: string;
  multipleCTA: boolean;
  hideInput: boolean;
  extraData: Record<string, any>;
  [key: string]: any;
}

/**
 * Bot metadata interface
 */
interface BotMetadata {
  conversationId?: string;
  evaluationMode?: string;
}

/**
 * Complete chat configuration interface
 */
interface ChatConfig {
  context: ChatContext;
  contextMetadata: {
    pageContext: PageContext;
    searchContext: SearchContext;
  };
  expertMetadata: ExpertMetadata;
  botMetadata: BotMetadata;
}

/**
 * Safely parse JSON string with fallback value
 * @param jsonString - The JSON string to parse
 * @param fallback - The fallback value if parsing fails
 * @returns Parsed JSON object or fallback value
 */
const safeJsonParse = <T>(jsonString: string | undefined | null, fallback: T): T => {
  if (!jsonString) {
    return fallback;
  }

  try {
    return JSON.parse(jsonString) as T;
  } catch (error) {
    console.error('JSON parse error:', error);
    return fallback;
  }
};

/**
 * React Native compatible URL parameter parsing function
 * @param url - The URL to parse
 * @returns object with query parameters
 */
const getQueryParams = (url: string) => {
  const queryParams: Record<string, string> = {};
  const queryString = url.split('?')[1];

  if (queryString) {
    const paramPairs = queryString.split('&');

    for (const pair of paramPairs) {
      const [key, value] = pair.split('=');
      queryParams[decodeURIComponent(key)] = decodeURIComponent(value || '');
    }
  }

  return queryParams;
};

/**
 * Parses URL parameters and creates a chatConfig object
 * @param url - The URL to parse (optional, defaults to current window location)
 * @param uniqChatId
 * @param cmp
 * @returns chatConfig - The generated chat configuration
 */
export const createChatConfigFromUrl = (url: string | null = null, uniqChatId: string, cmp: string | null = null, ): ChatConfig => {
  try {
    let urlToParse: string;

    if (url) {
      // If URL is provided, use it
      urlToParse = url;
    } else if (typeof window !== 'undefined') {
      // If in browser environment, use current URL
      urlToParse = window.location.href;
    } else {
      // Fallback for non-browser environments
      console.error('No URL available to parse');
      return defaultConfig;
    }

    // Use React Native compatible URL parsing
    const queryParams = getQueryParams(urlToParse);

    // Helper function to get parameter values (mimics searchParams.get())
    const getParam = (key: string): string | null => {
      return queryParams[key] || null;
    };

    // Extract basic parameters
    const pageIdentifier = getParam('pageIdentifier');
    const entityType = getParam('entityType');
    const channel = getParam('channel');
    const userType = getParam('userType');
    const page = getParam('page');
    const dest = getParam('dest');
    const entitykey = getParam('entitykey');
    const depCity = getParam('depCity');
    const travelDate = getParam('travelDate');
    const packageId = getParam('packageId');
    const dynamicPackageId = getParam('dynamicPackageId');
    const paxConfig = getParam('paxConfig');
    const tenant = getParam('tenant');
    const pst = getParam('pst');
    const intent = getParam('intent');
    const openweb = getParam('openweb');
    const conversationId = getParam('conversationId');
    const evaluationMode = getParam('evaluationMode');
    const platform = getParam('platform');
    const hideInput = getParam('hideInput') === 'true';

    // Handle multipleCTA parameter
    const multipleCTAParam = getParam('multipleCTA');
    const multipleCTA = multipleCTAParam === null
      ? true
      : multipleCTAParam === 'true';

    // Parse infoAttr JSON parameter
    const infoAttrString = getParam('infoAttr');
    const infoAttr = safeJsonParse<InfoAttr>(infoAttrString, {});

    // Extract properties from infoAttr
    const {
      attr1,
      attr2,
      attr3,
      attr4,
      fromCity,
      destination,
      pageName,
      device,
      chatId,
      evar83,
      evar57,
      evar17,
      proactive,
      m_v108,
    } = infoAttr;

    // Query parameters are already available from our parsing above
    // (queryParams variable contains all parsed URL parameters)

    // Determine LOB and category based on pageIdentifier or defaults
    const isHolidays = pageIdentifier === 'holidays' || Boolean(dest) || Boolean(packageId);
    const lob = isHolidays ? 'HOLIDAYS' : 'FLIGHTS'; // Default fallback
    const lobCategory = isHolidays ? 'domhld' : 'dom'; // Default fallback

    // Validate and convert platform
    const validPlatform = (channel || platform)?.toLowerCase();
    const platformValue = ['ios', 'android', 'pwa', 'desktop'].includes(validPlatform || '')
      ? validPlatform as Platform
      : undefined;

    // Build the chatConfig object
    const chatConfig: ChatConfig = {
      context: {
        lob,
        lobCategory,
        view: pageIdentifier || page || 'landing',
        prevPage: null,
        platform: platformValue,
      },
      contextMetadata: {
        pageContext: {
          lob,
          lobCategory,
          pageName: pageName || pageIdentifier || page || 'landing',
          prevPageName: '',
          lobFunnel: '',
          subpageName: '',
          funnelStep: pageIdentifier || page || 'landing',
          pgTimeInterval: '',
          navigation: '',
          subLob: 'MMT',
          pageUrl: urlToParse,
          // Add additional page context from query params
          ...(platform && { platform }),
          ...(device && { device }),
          ...(channel && { channel }),
        },
        searchContext: {
          lob,
          funnelSource: lob,
          lobCategory,
          // Add search context from URL params and infoAttr
          ...(fromCity && {
            fromCity,
            from: {
              locus: {
                name: fromCity,
                cityName: fromCity,
              },
              cityName: fromCity,
            },
          }),
          ...(depCity && !fromCity && {
            fromCity: depCity,
            from: {
              locus: {
                name: depCity,
                cityName: depCity,
              },
              cityName: depCity,
            },
          }),
          ...(destination && {
            destination,
            to: {
              locus: {
                name: destination,
                cityName: destination,
              },
              cityName: destination,
            },
          }),
          ...(dest && !destination && {
            destination: dest,
            to: {
              locus: {
                name: dest,
                cityName: dest,
              },
              cityName: dest,
            },
          }),
          ...(travelDate && { travelDate }),
          ...(packageId && { packageId }),
          ...(dynamicPackageId && { dynamicPackageId }),
          ...(paxConfig && { paxConfig }),
        },
      },
      expertMetadata: {
        lob,
        funnelType: '',
        page: pageName || pageIdentifier || page || 'landing',
        crid: '',
        itid: '',
        redirectType: '',
        summarize: '',
        lobCode: 'B2C',
        src: 'MMT',
        currency: 'inr',
        userCurrency: 'INR',
        botType: 'TRAVELPLEX',
        entityType: entityType || 'Funnel_Holiday',

        // Add attributes from infoAttr
        ...(attr1 && { attr1 }),
        ...(attr2 && { attr2 }),
        ...(attr3 && { attr3 }),
        ...(attr4 && { attr4 }),

        // Add additional metadata from URL params and infoAttr
        ...(chatId && { uniqChatId: chatId }),
        ...(evar83 && { evar83 }),
        ...(evar57 && { evar57 }),
        ...(evar17 && { evar17 }),
        ...(cmp && cmp.trim() !== '' && { evar81: cmp }),
        ...(typeof proactive !== 'undefined' && { proactive }),
        ...(m_v108 && { m_v108 }),
        ...(channel && { channel }),
        ...(platform && { platform }),
        ...(userType && { userType }),
        ...(destination && { destination }),
        ...(fromCity && { fromCity }),
        ...(dest && { dest }),
        ...(depCity && { depCity }),
        ...(entitykey && { entitykey }),
        ...(packageId && { packageId }),
        ...(dynamicPackageId && { dynamicPackageId }),
        ...(paxConfig && { paxConfig }),
        ...(tenant && { tenant }),
        ...(intent && { intent }),
        ...(pst && { pst }),
        ...(openweb && { openweb }),

        multipleCTA,
        hideInput,
        uniqChatId,
        // Include all query params for backward compatibility
        ...queryParams,

        // Add extra data
        extraData: {
          ...queryParams,
          ...infoAttr,
          chatId,
          uniqChatId,
          url: urlToParse,
        },
      },

      // Add bot metadata if available
      botMetadata: {
        ...(conversationId && { conversationId }),
        ...(evaluationMode && { evaluationMode }),
      },
    };

    return chatConfig;
  } catch (error) {
    console.error('Error creating chatConfig from URL:', error);
    // Return a default chatConfig in case of error
    return defaultConfig;
  }
};

const defaultConfig =    {
  context: {
    lob: 'HOLIDAYS',
    lobCategory: 'domhld',
    view: 'landing',
    prevPage: null,
    platform: undefined,
  },
  contextMetadata: {
    pageContext: {
      lob: 'HOLIDAYS',
      lobCategory: 'domhld',
      pageName: 'landing',
      prevPageName: '',
      lobFunnel: '',
      subpageName: '',
      funnelStep: 'landing',
      pgTimeInterval: '',
      navigation: '',
      subLob: 'MMT',
      pageUrl: '',
    },
    searchContext: {
      lob: 'HOLIDAYS',
      funnelSource: 'HOLIDAYS',
      lobCategory: 'domhld',
    },
  },
  expertMetadata: {
    lob: 'HOLIDAYS',
    funnelType: '',
    page: 'landing',
    crid: '',
    itid: '',
    redirectType: '',
    summarize: '',
    lobCode: 'B2C',
    src: 'MMT',
    currency: 'inr',
    userCurrency: 'INR',
    botType: 'TRAVELPLEX',
    entityType: 'Funnel_Holiday',
    multipleCTA: true,
    hideInput: false,
    extraData: {},
  },
  botMetadata: {},
};
/**
 * Usage example:
 *
 * // Using current browser URL
 * const config1 = createChatConfigFromUrl();
 *
 * // Using a specific URL
 * const sampleUrl = "https://myra.makemytrip.com/chat?pageIdentifier=holidays&entityType=Funnel_Holiday&channel=IOS&userType=CUSTOMER&page=webengage&dest=Europe&entitykey=Europe&depCity=New Delhi&travelDate=2022-04-23&packageId=26465&dynamicPackageId=9a2a7efa-5dd3-430d-85d6-ee23b205593a&paxConfig=2|0|0&tenant=0&infoAttr={\"packageId\":26465,\"attr4\":\"WEB-QUERY-ROADBLOCK-I-ASHISH\",\"attr2\":\"Europe\",\"pageName\":\"rb_track_pagename\",\"chatId\":\"rb_track_chatid\",\"device\":\"IOS\",\"attr3\":\"chat\",\"attr1\":\"OBT\",\"evar17\":\"na\",\"evar81\":\"rb_track_cmp\",\"proactive\":false}&pst=1647412809669&intent=MMT.HumanSupport.TransferToLiveAgent&openweb=true";
 * const config2 = createChatConfigFromUrl(sampleUrl);
 */
