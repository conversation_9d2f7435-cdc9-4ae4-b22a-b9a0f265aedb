export interface FabClickProps {
  fabCta: {
    showCall: boolean;
    showQuery: boolean;
    showChat: boolean;
    showLocator: boolean;
  };
  setFabCta: Function;
  fab: boolean;
  trackLocalClickEvent: Function;
  trackPDTV3Event : Function;
}

export interface CommonFabProps {
  fromIcon: boolean;
  toggleFabValue: Function;
  trackLocalClickEvent: Function;
}
export interface CallProps extends CommonFabProps {
  fabData: {
    branch: string;
  };
}

export interface ClickEventProps {
  eventName : string;
  actionType : string;
  suffix : string;
  prop1 : string,
  prop66 : string,
  trackLocalClickEvent: Function;
  trackPDTV3Event: Function;
}
export interface ChatProps extends CommonFabProps {
  fabData: {
    destinationCity: string;
    branch: string;
    cmp: string;
    isListing: boolean;
    filterDetails: {
      filterString: string;
    };
    otherDetails: {
      travel_start_date: string;
    };
  };
  unmountIntervention: Function;
}

export interface QueryProps extends CommonFabProps {
  fabData: {
    fromSeo: boolean;
    destinationCity: string;
    branch: string;
    cmp: string;
    aff: string;
    pageName: string;
  };
  fabCta: {
    formId: string;
  };
}

export interface LocatorProps extends CommonFabProps {
  setLocatorState: Function;
}

export interface CallErrorProps {
  fabData: {
    branch: String;
  };
  error: {
    code: string;
    message: string;
  };
  isListing: boolean;
  trackErrorLocalClickEvent: Function;
}

export interface QueryErrorProps {
  error: {
    code: string;
    message: string;
  };
  fabData: { destinationCity: string; branch: string; aff: string; fromSeo: boolean };
  fabCta: { formId: string };
  pageName: string;
  isListing: boolean;
  trackErrorLocalClickEvent: Function;
}

export interface ChatErrorProps {
  fabData: {
    desitnationCity: string;
    branch: string;
  };
  error: {
    code: string;
    message: string;
  };
  isListing: boolean;
}
