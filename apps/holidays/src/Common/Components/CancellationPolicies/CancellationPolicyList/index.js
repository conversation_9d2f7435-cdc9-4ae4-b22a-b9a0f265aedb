import React from 'react';
import { StyleSheet, View, Text } from 'react-native';
import AtomicCss from '@mmt/legacy-commons/Styles/AtomicCss';
import { colors } from '@mmt/legacy-commons/Styles/globalStyles';
import { holidayColors } from '../../../../Styles/holidayColors';
import { fontStyles } from '../../../../Styles/holidayFonts';

const CancellationPolicyList = (props) => {
  const { policies } =   props || {};
  return (
     policies?.length > 0 && <View style={styles.policyList}>
      {policies?.map((item, index) => (
        <View key={index} style={styles.listItem}>
          <Text style={styles.bulletStyle}>{'\u2022'}</Text>
          {!!item && (
            <View style={styles.width90}>
              <Text style={styles.policyTextStyle}>{item}</Text>
            </View>
          )}
        </View>
      ))}
    </View>
  );
};

const styles = StyleSheet.create({
  policyList: {
    flexWrap: 'wrap',
    padding: 15,
    marginTop: 5,
    borderWidth:1,
    borderColor:holidayColors.grayBorder,
    borderRadius:16,
  },
  bulletStyle: {
    color: holidayColors.gray,
    ...fontStyles.labelBaseRegular,
    ...AtomicCss.marginRight10,
  },
  policyTextStyle: {
    ...fontStyles.labelBaseRegular,
    color:holidayColors.gray,
  },
  width90: {
    width: '90%',
  },
  listItem: {
    ...AtomicCss.flexRow,
    ...AtomicCss.marginBottom8,
  },
});

export default CancellationPolicyList;
