import React from 'react';
import { StyleSheet, View, Text } from 'react-native';
import AtomicCss from '@mmt/legacy-commons/Styles/AtomicCss';
import { colors } from '@mmt/legacy-commons/Styles/globalStyles';
import { holidayColors } from '../../../../Styles/holidayColors';
import { fontStyles } from '../../../../Styles/holidayFonts';
import { holidayBorderRadius } from '../../../../Styles/holidayBorderRadius';
import { paddingStyles, marginStyles } from '../../../../Styles/Spacing/index';

const PolicyDesc = ({ policyTitle, content }) => {
  return (
    <View style={AtomicCss.marginBottom5}>
      {!!policyTitle && (
        <View style={AtomicCss.marginBottom5}>
          <Text style={styles.policyTitle}>{policyTitle}</Text>
        </View>
      )}
      <View>{content}</View>
    </View>
  );
};

const styles = StyleSheet.create({
  policyList: {
    flexWrap: 'wrap',
    ...marginStyles.mt16,
    ...paddingStyles.pa16,
    borderWidth: 1,
    borderColor: holidayColors.grayBorder,
    ...holidayBorderRadius.borderRadius2,
  },
  policyTitle: {
    ...fontStyles.labelMediumBlack,
    color: holidayColors.green,
  },
});

export default PolicyDesc;
