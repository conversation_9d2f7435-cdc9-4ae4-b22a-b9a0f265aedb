import React from 'react';
import { View, StyleSheet, Text } from 'react-native';
import AtomicCss from '@mmt/legacy-commons/Styles/AtomicCss';
import VerticalStepInfo from '../VerticalStepperInfo';
import { fontStyles } from '../../../../Styles/holidayFonts';
import { paddingStyles, marginStyles } from '../../../../Styles/Spacing/index';
import {
  CANCELLATION_POLICY,
  FLEXI_DESCRIPTION,
  ZC_DESCRIPTION,
} from '../../../../PhoenixReview/Utils/HolidayReviewConstants';

const PolicyDetails = (props) => {
  const { selectedTab, penalties, zcOption, travellerCount = 2, isPerPerson = false } = props || {};
  const selectedTabDescription =
    props.selectedTab === CANCELLATION_POLICY ? ZC_DESCRIPTION : FLEXI_DESCRIPTION;
  const leftHeader = `${zcOption?.available ? 'Current' : 'Package'} ${selectedTab}`;
  const rightHeader = !zcOption?.selected
    ? `${selectedTab} with ${selectedTabDescription}`
    : `Package ${selectedTab}`;
  let stepperCount = 0;
  if (props?.penalties?.length > 0 && !zcOption?.selected) {stepperCount++;}
  if (props?.penalties?.length > 0 && zcOption?.available) {stepperCount++;}
  const stepperWidth = () => {
    let width = '100%';
    return width;
  };
  const calculatedWidth = stepperWidth();
  return (
    <View style={styles.flexColumn}>
      <View style={[styles.columnHeader, { width: calculatedWidth }]}>
        {props?.penalties?.length > 0 && !zcOption?.selected && (
          <Text style={styles.leftHeader}>{leftHeader}</Text>
        )}
        {props?.penalties?.length > 0 && zcOption?.available && (
          <Text
            numberOfLines={2}
            style={[!zcOption?.selected ? styles.rightHeader : {}, styles.rightHeaderFixed]}
          >
            {rightHeader}
          </Text>
        )}
      </View>
      <View style={[styles.wrapper]}>
        {/* left */}
        {props?.penalties?.length > 0 && !zcOption?.selected
          ? [
              <VerticalStepInfo
                mode={selectedTab}
                penalties={penalties}
                zcOption={zcOption}
                zcSelected={false}
                stepperCount={stepperCount}
                travellerCount = {travellerCount}
                isPerPerson = {isPerPerson}
              />,
            ]
          : []}
        {/* right */}
        {props?.penalties?.length > 0 && zcOption?.available
          ? [
              <VerticalStepInfo
                mode={selectedTab}
                penalties={penalties}
                zcOption={zcOption}
                zcSelected={true}
                stepperCount={stepperCount}
                travellerCount = {travellerCount}
                isPerPerson = {isPerPerson}
              />,
            ]
          : []}
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  flexColumn: { flexDirection: 'column' },
  columnHeader: {
    display: 'flex',
    flexDirection: 'row',
    alignItems: 'flex-start',
    ...paddingStyles.pv6,
    ...paddingStyles.pr6,
    flex: 1,
  },
  rightHeader: {
    ...marginStyles.ml10,
    display: 'flex',
    flexWrap: 'wrap',
    flex: 1,
  },
  wrapper: {
    display: 'flex',
    flexDirection: 'row',
  },
  leftHeader: {
    ...fontStyles.labelBaseBlack,
    flex: 1,
  },
  rightHeaderFixed: {
    ...fontStyles.labelBaseBlack,
  },
});
export default PolicyDetails;
