import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { holidayColors } from 'mobile-holidays-react-native/src/Styles/holidayColors';
import { fontStyles } from 'mobile-holidays-react-native/src/Styles/holidayFonts';
import { paddingStyles, marginStyles } from 'mobile-holidays-react-native/src/Styles/Spacing';
import { IMAGE_ICON_KEYS, getOpitimsedImageUrl } from '../../HolidayImageUrls';
import { RESIZE_MODE_IMAGE } from '../../../../HolidayConstants';

/* Components */
import HolidayImageHolder from '../../HolidayImageHolder';

const UploadImagePlaceholder = () => {
  const loaderHeader = 'Spotted a beautiful scenery or picture?';
  const loaderSubHeader = `Upload it, and we'll identify your next travel destination`;
  return (
    <View style={styles.loaderContainer}>
      <HolidayImageHolder
        imageUrl={getOpitimsedImageUrl(IMAGE_ICON_KEYS.IMAGE_SEARCH_UPLOAD_PLACEHOLDER)}
        style={{
          width: 350,
          height: 350,
        }}
        resizeMode={RESIZE_MODE_IMAGE.CONTAIN}
      />
      <View>
        <Text style={styles.loaderHeading}>{loaderHeader}</Text>
        <Text style={styles.loaderText}>{loaderSubHeader}</Text>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  loaderContainer: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  loaderHeading: {
    ...fontStyles.labelMediumBold,
    color: holidayColors.black,
    textAlign: 'center',
  },
  loaderText: {
    color: holidayColors.black,
    ...fontStyles.labelSmallRegular,
    ...marginStyles.mt4,
    alignItems: 'center',
    textAlign: 'center',
  },
});

export default UploadImagePlaceholder
