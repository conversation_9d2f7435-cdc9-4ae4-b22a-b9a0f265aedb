import React, { Fragment } from 'react';
import { View, StyleSheet, Image, TouchableOpacity, Text, ImageBackground } from 'react-native';
import arrowBack from '@mmt/legacy-assets/src/close_filter.webp';
import AtomicCss from '@mmt/legacy-commons/Styles/AtomicCss';
import HolidayInfo from './HolidayInfo';
import LinearGradient from 'react-native-linear-gradient';
import defaultBannerImg from '@mmt/legacy-assets/src/genericDarkTextureImage.webp';
import defaultProfileImage from '@mmt/legacy-assets/src/holidays/profile-sme.webp';
import { isEmpty } from 'lodash';
import { BACKGROUND_IMAGE, PROFILE_IMAGE } from '../../../../Grouping/HolidayGroupingConstants';

const TopSectionSME = ({ details, closeModal, profileTypeInfo }) => {
  const { smeImages, name, workDescription, credentialUnofficial } = details || {};

  let banner;
  let profilePic;

  if (smeImages && smeImages.length) {
    smeImages.forEach((image) => {
      const { type, url } = image || {};
      if (type === BACKGROUND_IMAGE) {
        banner = url;
      } else if (type === PROFILE_IMAGE) {
        profilePic = url;
      }
    });
  }

  const getCredentialUnofficial = () => {
    return (
      credentialUnofficial &&
      credentialUnofficial.length > 0 &&
      credentialUnofficial.slice(0, 2).map((item, index) => {
        const { text, value } = item || {};
        if (!isEmpty(text) && !isEmpty(value)) {
          return (
            <View style={[AtomicCss.flexRow, AtomicCss.flexWrap]}>
              <Text style={[AtomicCss.font10, AtomicCss.regularFont]}>{value} </Text>
              <Text style={[AtomicCss.font10, AtomicCss.regularFont]}> {text}</Text>
              {credentialUnofficial.length > 1 && index < 1 && (
                <Text style={[AtomicCss.regularFont, styles.separator]}> /</Text>
              )}
            </View>
          );
        } else {
          return [];
        }
      })
    );
  };

  return (
    <Fragment>
      <View style={{ borderRadius: 10 }}>
        <ImageBackground
          style={styles.height90}
          imageStyle={styles.topBorderRadius}
          source={banner ? { uri: banner } : defaultBannerImg}
        >
          <LinearGradient
            colors={['black', '#00000033', '#00000033']}
            start={{ x: 0, y: 1 }}
            end={{ x: 0, y: 0 }}
            style={styles.gradientStyle}
          />
        </ImageBackground>
        <HolidayInfo details={details} containerStyle={styles.holidayInfo} />
        <TouchableOpacity activeOpacity={0.8} onPress={closeModal} style={styles.backBtn}>
          <Image source={arrowBack} style={styles.arrowIcon} />
        </TouchableOpacity>
        {profileTypeInfo && (
          <Text style={[AtomicCss.regularTextFont, styles.role]}>{profileTypeInfo.typeName}</Text>
        )}
      </View>
      <View style={[AtomicCss.flexRow, AtomicCss.marginBottom10]}>
        <Image
          style={styles.HostImage}
          source={profilePic ? { uri: profilePic } : defaultProfileImage}
        />
        <View style={[AtomicCss.paddingLeft5, AtomicCss.paddingTop7, AtomicCss.flex1]}>
          <Text style={[AtomicCss.font18, AtomicCss.blackFont, AtomicCss.blackText]}>{name}</Text>
          {workDescription && workDescription.trim().length ? (
            <Text style={[AtomicCss.font12, AtomicCss.regularFont]}>{workDescription}</Text>
          ) : null}
          {credentialUnofficial && (
            <View style={[AtomicCss.flexRow, AtomicCss.flexWrap, { paddingTop: 2 }]}>
              {getCredentialUnofficial()}
            </View>
          )}
        </View>
      </View>
    </Fragment>
  );
};

const styles = StyleSheet.create({
  container: {
    paddingHorizontal: 17,
    paddingBottom: 28,
  },
  superHostIcon: {
    width: 135,
    height: 21,
    position: 'absolute',
    top: 0,
    alignSelf: 'center',
  },
  backBtn: {
    position: 'absolute',
    top: 2,
    right: 11,
    width: 24,
    height: 24,
    borderRadius: 12,
    padding: 6,
  },
  height90: { height: 90 },
  arrowIcon: { width: '100%', height: '100%', tintColor: '#ffffff' },
  HostImage: {
    width: 76,
    height: 76,
    alignSelf: 'flex-start',
    marginTop: -20,
    marginLeft: 10,
    borderRadius: 38,
    borderWidth: 2,
    borderColor: '#ffffff',
  },
  separator: {
    paddingHorizontal: 3,
    fontSize: 10,
  },
  role: {
    position: 'absolute',
    top: 0,
    backgroundColor: 'rgba(2, 2, 2, 0.5)',
    color: '#ffffff',
    paddingHorizontal: 15,
    fontSize: 10,
    paddingVertical: 8,
    borderRadius: 6,
    borderTopRightRadius: 0,
    borderBottomLeftRadius: 0,
  },
  holidayInfo: {
    position: 'absolute',
    color: '#ffffff',
    bottom: 0,
    right: 0,
    paddingTop: 17,
    paddingBottom: 10,
    paddingLeft: 100,
    paddingRight: 10,
    alignSelf: 'flex-end',
    justifyContent: 'flex-end',
  },
  topBorderRadius: {
    borderTopRightRadius: 6,
    borderTopLeftRadius: 6,
  },
  gradientStyle: {
    alignItems: 'center',
    justifyContent: 'center',
    flex: 1,
    borderTopRightRadius: 6,
    borderTopLeftRadius: 6,
  },
});

export default TopSectionSME;
