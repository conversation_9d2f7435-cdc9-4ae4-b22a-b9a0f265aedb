import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { fonts } from '@mmt/legacy-commons/Styles/globalStyles';
import { DESTINATION, DESTINATIONS, PACKAGES_CURATED, PACKAGE_CURATED, SME } from '../../../../Grouping/HolidayGroupingConstants';

const HolidayInfo = ({ details, containerStyle = {} }) => {
  const { smeAdditionalInfo, credentialUnofficial, profileType } = details || {};
  if (!(smeAdditionalInfo || credentialUnofficial)) {
    return null;
  }
  const renderStats = (key, value) => (
    <View style={styles.textContainer}>
      <Text style={[styles.stat, profileType === SME ? styles.white : styles.grey]}>{value}</Text>
      <Text style={[styles.category, profileType === SME ? styles.white : styles.grey]}>{key?.toUpperCase()}</Text>
    </View>);

  const renderSingleStats = (key, value) => (
    <View style={styles.textSingleContainer}>
      <Text style={[styles.singleStat, profileType === SME ? styles.white : styles.grey]}>{value}</Text>
      <Text style={[styles.category, profileType === SME ? styles.white : styles.grey]}>{key?.toUpperCase()}</Text>
    </View>);
  const renderSlash = () => (<View style={[styles.separator, profileType === SME ? styles.whiteBg : styles.greyBg]} />);


  if (profileType === SME) {
    let noOfPackages, noOfDestination;
    if (smeAdditionalInfo) {
      noOfPackages = smeAdditionalInfo.noOfPackages;
      noOfDestination = smeAdditionalInfo.noOfDestination;
      if (noOfDestination > 0  && noOfPackages > 0){
        return (
          <View style={[styles.container, containerStyle]}>
            {noOfPackages > 0 && renderStats(noOfPackages > 1 ? PACKAGES_CURATED : PACKAGE_CURATED, noOfPackages)}
            {noOfPackages > 0 && noOfDestination > 0 && renderSlash()}
            {noOfDestination > 0 && renderStats(noOfDestination > 1 ? DESTINATIONS : DESTINATION, noOfDestination)}
          </View>
        );
      } else if (noOfPackages > 0){
        return (
          <View style={[styles.container, containerStyle]}>
            {noOfPackages > 0 && renderSingleStats(noOfPackages > 1 ? PACKAGES_CURATED : PACKAGE_CURATED, noOfPackages)}
          </View>
        );

      } else if (noOfDestination > 0 ){
        return (
          <View style={[styles.container, containerStyle]}>
          {noOfDestination > 0 && renderSingleStats(noOfDestination > 1 ? DESTINATIONS : DESTINATION, noOfDestination)}
          </View>
        );

      } else {
        return null;
      }

    } else {
      return null;
    }
  } else {
    if (credentialUnofficial) {
      return (
        <View style={[styles.container, containerStyle]}>
          {credentialUnofficial.length === 1 && renderSingleStats(credentialUnofficial[0].text, credentialUnofficial[0].value)}
          {credentialUnofficial.length > 1 && renderStats(credentialUnofficial[0].text, credentialUnofficial[0].value)}
          {credentialUnofficial.length > 1 && renderSlash()}
          {credentialUnofficial.length > 1 && renderStats(credentialUnofficial[1].text, credentialUnofficial[1].value)}
        </View>
      );
    }

  }




};

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    marginRight: 10,
  },
  textContainer: {
    alignItems: 'center',
    width:'48%',
  },
  textSingleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  singleStat: {
    fontFamily: fonts.black,
    fontSize: 16,
    marginBottom: 2,
    marginRight: 3,

  },
  stat: {
    fontFamily: fonts.black,
    fontSize: 14,
    marginBottom: 2,

  },
  white: {
    color: '#fff',
  },
  grey: {
    color: '#2A2A2A',
  },
  whiteBg: {
    backgroundColor: '#fff',
  },
  greyBg: {
    backgroundColor: '#2A2A2A',
  },
  category: {
    fontFamily: fonts.regular,
    fontSize: 9,
    textAlign:'center',
  },
  separator: {
    height: '100%',
    width: 1,
    transform: [{ rotate: '10deg' }],
    alignSelf: 'flex-end',
    marginLeft:5,
    marginRight: 5,
    // marginBottom: 8,
  },
});

export default HolidayInfo;
