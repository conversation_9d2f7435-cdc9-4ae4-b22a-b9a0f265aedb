import React, { Fragment } from 'react';
import { View, StyleSheet, Image, TouchableOpacity, Text } from 'react-native';
import arrowBack from '@mmt/legacy-assets/src/close_filter.webp';
import AtomicCss from '@mmt/legacy-commons/Styles/AtomicCss';
import { BACKGROUND_IMAGE, PROFILE_IMAGE } from '../../../../Grouping/HolidayGroupingConstants';
import HolidayInfo from './HolidayInfo';
import defaultProfileImage from '@mmt/legacy-assets/src/holidays/profile-sme.webp';

const TopSectionHE = ({ details, closeModal, profileTypeInfo }) => {
  const { smeImages, name, workDescription } = details || {};
  let banner;
  let profilePic;
  if (smeImages && smeImages.length) {
    smeImages.forEach((image) => {
      const { type, url } = image || {};
      if (type === BACKGROUND_IMAGE) {
        banner = url;
      } else if (type === PROFILE_IMAGE) {
        profilePic = url;
      }
    });
  }
  return (
    <Fragment>
      <View style={styles.container}>
        <View style={styles.containerHeader}>
          {profileTypeInfo ? (
            <Text style={[AtomicCss.regularTextFont, styles.role]}>{profileTypeInfo.typeName}</Text>
          ) : (
            <Text />
          )}
          <TouchableOpacity activeOpacity={0.8} onPress={closeModal} style={styles.backBtn}>
            <Image source={arrowBack} style={styles.arrowIcon} />
          </TouchableOpacity>
        </View>
        <View style={[AtomicCss.flexRow, AtomicCss.marginTop10]}>
          <Image
            style={styles.HostImage}
            source={profilePic ? { uri: profilePic } : defaultProfileImage}
          />
          <View
            style={[AtomicCss.paddingLeft5, AtomicCss.flex1, { justifyContent: 'space-between' }]}
          >
            <View>
              <Text style={[AtomicCss.font18, AtomicCss.blackFont, AtomicCss.blackText]}>
                {name}
              </Text>
              {workDescription && workDescription.trim().length ? (
                <Text style={styles.workDescriptionText}>{workDescription}</Text>
              ) : null}
            </View>
            <View style={[AtomicCss.marginBottom8]}>
              <HolidayInfo details={details} containerStyle={styles.holidayInfo} />
            </View>
          </View>
        </View>
      </View>
    </Fragment>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: '#EAF5FF',
    borderTopLeftRadius: 6,
    borderTopRightRadius: 6,
    marginBottom: 15,
  },
  containerHeader: {
    justifyContent: 'space-between',
    flexDirection: 'row',
    alignItems: 'center',
  },
  workDescriptionText: {
    fontFamily: 'Lato-Regular',
    fontSize: 12,
    marginBottom: 10,
    paddingRight: 10,
  },
  backBtn: {
    right: 11,
    width: 24,
    height: 24,
    borderRadius: 12,
    padding: 6,
  },
  height90: { height: 90, borderTopLeftRadius: 6, borderTopRightRadius: 6 },
  arrowIcon: { width: '100%', height: '100%', tintColor: '#9B9B9B' },
  HostImage: {
    width: 85,
    height: 89,
    resizeMode: 'contain',
    alignSelf: 'flex-start',
  },
  role: {
    top: 0,
    backgroundColor: 'rgba(255, 255, 255, 0.7)',
    color: '#000000',
    paddingHorizontal: 15,
    fontSize: 10,
    fontWeight: 'bold',
    paddingVertical: 8,
    borderRadius: 6,
    borderTopRightRadius: 0,
    borderBottomLeftRadius: 0,
  },
  holidayInfo: {
    color: '#2A2A2A',
  },
});

export default TopSectionHE;
