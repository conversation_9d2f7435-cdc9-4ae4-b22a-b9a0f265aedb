import React from 'react';
import { View, Text, FlatList, StyleSheet, TouchableOpacity } from 'react-native';
import { GenericSectionHeadingCss } from '../../../../LandingNew/Components/phoenixCss';
import { holidayBorderRadius } from '../../../../Styles/holidayBorderRadius';
import { holidayColors } from '../../../../Styles/holidayColors';
import { marginStyles, paddingStyles } from '../../../../Styles/Spacing';
import { fontStyles } from '../../../../Styles/holidayFonts';
import { handleDeepLinkUrl } from '../../../../Grouping/Components/PhoenixSectionCardClickHandler';
import oneWayArrowIc from '@mmt/legacy-assets/src/grey_arrow_oneway.webp';

/* Components */
import HolidaySectionHeader from '../../Phoenix/HolidaySectionHeader';
import HolidayImageHolder from '../../HolidayImageHolder';
import { generateSectionKey } from '../../../../LandingNew/Utils/HolidayLandingUtils';
import { trackCardClickEvent } from '../../../../LandingNew/Utils/HolidayLandingUtilsV2';

const CYSSection = ({ data = {}, pageName }) => {
  const { cards = [], header = 'Continue Your Search' } = data || {};

  if (cards?.length === 0) {
    return [];
  }
  const renderCYSCard = ({ item, index }) => {
    const { travellers, departureDate, destinationCity, departureCity, pageUrl } = item || {};
    const handleCardClick = () => {
      const cardKey = `Card_${index}`;
      const sectionKey = generateSectionKey(data);
      trackCardClickEvent({
        eventName: 'generic_card',
        eventNameOmni: `${sectionKey}|${cardKey}`,
      });

      handleDeepLinkUrl(pageUrl, '', false, pageName);
    };
    return (
      <TouchableOpacity style={styles.cardContainer} onPress={handleCardClick} activeOpacity={0.7}>
        <View style={styles.destDetailContainer}>
          {!!departureCity && <Text style={styles.locationText}>{departureCity}</Text>}
          <HolidayImageHolder defaultImage={oneWayArrowIc} style={styles.imageStyles} />
          {!!destinationCity && <Text style={styles.locationText}>{destinationCity}</Text>}
        </View>
        <View style={styles.destDetailContainer}>
          {!!departureDate && <Text style={styles.detailsText}>{departureDate}</Text>}
          {departureDate && travellers && <View style={styles.dot} />}
          {!!travellers && <Text style={styles.detailsText}>{travellers}</Text>}
        </View>
      </TouchableOpacity>
    );
  };
  return (
    <HolidaySectionHeader heading={header} styles={GenericSectionHeadingCss}>
      <FlatList
        data={cards}
        renderItem={renderCYSCard}
        horizontal
        contentContainerStyle={GenericSectionHeadingCss.container}
        showsHorizontalScrollIndicator={false}
        keyExtractor={(item, index) => `${item.destinationCity}-${index}`}
      />
    </HolidaySectionHeader>
  );
};

const styles = StyleSheet.create({
  cardContainer: {
    ...paddingStyles.pa16,
    ...marginStyles.mr16,
    ...holidayBorderRadius.borderRadius16,
    borderColor: holidayColors.grayBorder,
    borderWidth: 1,
    backgroundColor: holidayColors.white,
  },
  destDetailContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  locationText: {
    ...fontStyles.labelBaseBold,
    color: holidayColors.black,
  },
  detailsText: {
    ...fontStyles.labelSmallRegular,
    color: holidayColors.gray,
  },
  imageStyles: {
    width: 15,
    height: 15,
    ...marginStyles.mh10,
  },
  dot: {
    width: 5,
    height: 5,
    borderRadius: 5,
    backgroundColor: holidayColors.gray,
    ...marginStyles.mh8,
  },
});
export default CYSSection;
