import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { holidayColors } from 'mobile-holidays-react-native/src/Styles/holidayColors';
import { fontStyles } from 'mobile-holidays-react-native/src/Styles/holidayFonts';
import { marginStyles, paddingStyles } from 'mobile-holidays-react-native/src/Styles/Spacing';

const CHAR_LIMIT = 24;
const BULLET_SPACE = 2;

const getTotalMaxDestCount = (list = []) => {
  let totalChars = 0;
  let count = 0;
  list.forEach((item) => {
    totalChars = totalChars + item.length + BULLET_SPACE;
    if (totalChars <= CHAR_LIMIT) {
      count++;
    }
  });

  return count;
};
const PackageDuration = ({ destinationList = [] }) => {
  const renderBulletPoints = () => {
    let renderedBulletPoints = [];
    const count = getTotalMaxDestCount(destinationList);

    for (let i = 0; i < count; i++) {
      renderedBulletPoints.push(
        <View key={`${destinationList[i]}-${i}`} style={styles.durationItem}>
          <Text style={styles.bulletText}>{destinationList[i]}</Text>
          {i < count - 1 && <Text style={styles.bulletImage}>{'\u2022'}</Text>}
        </View>,
      );
    }

    if(destinationList.length > count) {
        const countLeft = destinationList.length - count
        renderedBulletPoints.push(
        <View key={`${countLeft}-more`} style={styles.durationItem}>
          <Text style={styles.moreText}>+ {countLeft}</Text>
        </View>,
      );
    }

    return renderedBulletPoints;
  };

  return (
    <View style={styles.container}>
      <View style={styles.durationItemsContainer}>{renderBulletPoints()}</View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    ...paddingStyles.pb4,
  },
  durationItemsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    alignItems: 'center',
  },
  durationItem: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  bulletImage: {
    ...fontStyles.labelSmallRegular,
    color: holidayColors.gray,
    marginRight: 6,
  },
  bulletText: {
    ...fontStyles.labelSmallBold,
    color: holidayColors.black,
    marginRight: 6,
  },
  listingText: {
    ...fontStyles.labelSmallRegular,
    color: holidayColors.gray,
    ...marginStyles.mr10,
  },
  moreText: {
    ...fontStyles.labelSmallBold,
    color: holidayColors.gray,
  },
});

export default PackageDuration;
