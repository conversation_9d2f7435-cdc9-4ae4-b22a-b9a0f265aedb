import React, { useState } from 'react';
import { View, Text, FlatList, StyleSheet, TouchableOpacity } from 'react-native';
import { GenericSectionHeadingCss } from '../../../../LandingNew/Components/phoenixCss';
import { holidayBorderRadius } from '../../../../Styles/holidayBorderRadius';
import { holidayColors } from '../../../../Styles/holidayColors';
import { fontStyles } from '../../../../Styles/holidayFonts';
import { marginStyles, paddingStyles } from '../../../../Styles/Spacing';
import { capitalizeText } from 'mobile-holidays-react-native/src/utils/textTransformUtil';

const CityTabList = ({
  handleChangeTab,
  currentTab = '',
  cityWiseData = {},
  sectionCode,
  trackLocalClickEvent,
}) => {
  const allAvailableCitites = Object.keys(cityWiseData);
  if (allAvailableCitites.length === 2) { // one is by default ALL and one is extra city
    return [];
  }

  const renderTab = ({ item, index }) => {
    const handleTabClick = () => {
      handleChangeTab(item);
      trackLocalClickEvent({ eventName: `Tab_${sectionCode}_${index}` });
    };

    return (
      <TouchableOpacity
        style={[styles.tabContainer, currentTab === item && styles.activeTabContainer]}
        onPress={handleTabClick}
      >
        <Text style={[styles.tabText, currentTab === item && styles.activeTabText]}>
          {capitalizeText(item)} <Text style={styles.cityCountText}>{`(${cityWiseData[item].length})`}</Text>
        </Text>
      </TouchableOpacity>
    );
  };

  return (
    <FlatList
      horizontal
      renderItem={renderTab}
      data={allAvailableCitites}
      showsHorizontalScrollIndicator={false}
      contentContainerStyle={styles.tabListContainer}
      keyExtractor={(item, index) => `${item}-${index}`}
    />
  );
};

const styles = StyleSheet.create({
  tabListContainer: {
    ...GenericSectionHeadingCss.container,
    ...paddingStyles.pb10,
    flexDirection: 'row',
  },
  tabContainer: {
    ...paddingStyles.pa10,
    ...marginStyles.mr10,
    ...holidayBorderRadius.borderRadius8,
    borderColor: holidayColors.grayBorder,
    borderWidth: 1,
  },
  activeTabContainer: {
    borderColor: holidayColors.primaryBlue,
  },
  tabText: {
    ...fontStyles.labelSmallBlack,
    color: holidayColors.black,
  },
  activeTabText: {
    color: holidayColors.primaryBlue,
  },
  cityCountText: {
    ...fontStyles.labelSmallBold,
    color: holidayColors.gray,
  },
});
export default CityTabList;
