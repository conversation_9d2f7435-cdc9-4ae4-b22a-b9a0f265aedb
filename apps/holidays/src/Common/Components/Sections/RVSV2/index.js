import React, { useState, useCallback, useEffect, useRef } from 'react';
import moment from 'moment';
import { View, Text, FlatList, StyleSheet, TouchableOpacity } from 'react-native';
import { GenericSectionHeadingCss } from '../../../../LandingNew/Components/phoenixCss';
import { borderRadiusValues, holidayBorderRadius } from '../../../../Styles/holidayBorderRadius';
import { holidayColors } from '../../../../Styles/holidayColors';
import { fontStyles } from '../../../../Styles/holidayFonts';
import { marginStyles, paddingStyles } from '../../../../Styles/Spacing';
import { handleDeepLinkUrl } from '../../../../Grouping/Components/PhoenixSectionCardClickHandler';
import { generateSectionKey } from '../../../../LandingNew/Utils/HolidayLandingUtils';
import { trackCardClickEvent } from '../../../../LandingNew/Utils/HolidayLandingUtilsV2';
import {
  getPageSectionVisitResponse,
  sectionTrackingPageNames,
  setPageSectionVisitResponse,
} from '../../../../utils/SectionVisitTracking';
import { LANDING_TRACKING_PAGE_NAME } from '../../../../LandingNew/LandingConstants';
import CardSeperator from '@mmt/legacy-assets/src/holidays/hol-card-seperator.png';
import { RESIZE_MODE_IMAGE } from '../../../../HolidayConstants';

/* Components */
import HolidayImageHolder from '../../HolidayImageHolder';
import HolidaySectionHeader from '../../Phoenix/HolidaySectionHeader';
import CityTabList from './CityTabList';
import PackageDuration from './PackageDuration';
import { isEmpty } from 'lodash';
import { PDT_EVENT_TYPES } from '../../../../utils/HolidayPDTConstants';
import { getCityWiseDataForPDO } from 'mobile-holidays-react-native/src/utils/HolidayUtils';

const ALL_CITY = 'ALL';
const getSectionPageName = (pageName) => {
  let sectionPageName = '';
  switch (pageName) {
    case LANDING_TRACKING_PAGE_NAME:
      sectionPageName = sectionTrackingPageNames.LANDING_PAGE_RVS_V2;
      break;
    case 'collections-v1':
    case 'listing-v1':
      sectionPageName = sectionTrackingPageNames.PHOENIX_GROUPING_PAGE_V2_RVS_V2;
      break;
    default:
      sectionPageName = '';
      break;
  }
  return sectionPageName;
};
const RVSSectionV2 = ({ itemData = [], data, pageName, trackLocalClickEvent, trackViewedSectionClickEvent = () => {}, trackPDTV3Event = () => {} }) => {
  const { header = 'Recently Viewed Section', sectionCode = '' } = data || '';
  const cards = itemData || []
  if (cards?.length === 0) {
    return [];
  }
  const cityWiseData = getCityWiseDataForPDO(cards) || {};
  const [currentTab, setCurrentTab] = useState(ALL_CITY);
  const flatListRef = useRef(null);
  const sectionKey = generateSectionKey(data);
  const sectionPageName = getSectionPageName(pageName);
  let sectionVisitResponse = {};

  useEffect(() => {
    sectionVisitResponse = getPageSectionVisitResponse({
      pageName: sectionPageName,
    });
  }, []);

  const renderRVSCard = ({ item, index }) => {
    const {
      packageName = '',
      itineraries = [],
      departureDate,
      travellers,
      price = [],
      duration = '',
      packageImageUrl = '',
      description = '',
      descriptionIcon = '',
      updatedAt = '',
      cta = '',
      ctaPageUrl = '',
      cardType = '',
    } = item || {};

    const trackHandleCtaClickEvents = () => {
      const cardKey = `Card_${index}`;
      const eventName = `${sectionKey}|${cardKey}_${cardType}`
      trackPDTV3Event({
        actionType: PDT_EVENT_TYPES.contentClicked,
        value : eventName,
        shouldTrackToAdobe:false
      });
      trackViewedSectionClickEvent();
      trackLocalClickEvent({
        eventName ,
      });
    }
    const handelCtaClick = () => {
      if (ctaPageUrl) {
        trackHandleCtaClickEvents();
        handleDeepLinkUrl(ctaPageUrl, '', '', pageName);
      }
    };

    return (
      <TouchableOpacity style={styles.container} onPress={handelCtaClick} activeOpacity={0.7} >
        <View style={styles.rvsCardContainer}>
          {/* Package Details */}
          <View style={styles.packageDetailsContainer}>
            <View style={styles.packageNameContainer}>
              {!!packageName && (
                <Text style={styles.packageName} numberOfLines={1}>
                  {packageName}
                </Text>
              )}
              <PackageDuration destinationList={itineraries} />
              <View style={styles.packageDetails}>
                {!!departureDate && <Text style={styles.packageDetailsText}>{departureDate}</Text>}
                <View style={styles.dot} />
                {!!travellers && <Text style={styles.packageDetailsText}>{travellers}</Text>}
              </View>
            </View>
            <View style={styles.packageImageContainer}>
              <View style={styles.packageImage}>
                <HolidayImageHolder style={styles.packageImage} imageUrl={packageImageUrl} />
              </View>
              <View style={styles.durationContainer}>
                {!!duration && <Text style={styles.durationText}>{duration}</Text>}
              </View>
            </View>
          </View>
          {/* Package Details */}
          <HolidayImageHolder
            defaultImage={CardSeperator}
            style={styles.dashedLine}
            resizeMode={RESIZE_MODE_IMAGE.COVER}
          />
          {/* Package Price */}
          <View style={styles.priceContainer}>
            {price.map((item) => (
              <Text style={[styles.priceText, item?.isEmphasized && styles.priceTextEmphasized]}>
                {item.text}
              </Text>
            ))}
          </View>
          {/* Package Price */}
        </View>
        <View style={styles.footerContainer}>
          <View style={styles.footerDescriptionContainer}>
            <HolidayImageHolder
              imageUrl={descriptionIcon}
              style={styles.descriptionIcon}
              resizeMode={RESIZE_MODE_IMAGE.CONTAIN}
            />
            {!!description && (
              <Text
                style={[
                  styles.descriptionText,
                  updatedAt
                    ? styles.descriptionTextWidthWithTime
                    : styles.descriptionTextWidthWithoutTime,
                ]}
                numberOfLines={1}
              >
                {description}
              </Text>
            )}
            {Boolean(description && updatedAt) && <View style={styles.dot} />}
            {!!updatedAt && (
              <Text style={styles.descriptionText}>
                {moment(new Date(Number(updatedAt))).fromNow()}
              </Text>
            )}
            {!!cta && (
              <View style={styles.ctaTextContainer}>
                <Text style={styles.ctaText}>{cta}</Text>
              </View>
            )}
          </View>
        </View>
      </TouchableOpacity>
    );
  };

  const onViewableItemsChanged = React.useRef(({ viewableItems, changed }) => {
    viewableItems?.forEach((viewableItem) => {
      const { item = {}, isViewable = false, index } = viewableItem;
      const { packageName = '', cardType = '' } = item;
      const cardKey = `Card_${index}_${cardType}`;

      if (!packageName || !isViewable) {
        return;
      }
      if (!sectionVisitResponse?.[packageName]) {
        sectionVisitResponse[packageName] = 1;
        setPageSectionVisitResponse({
          pageName: sectionPageName,
          value: sectionVisitResponse,
        });
        const eventName = `Viewed_${sectionKey}|${cardKey}`;
        trackPdoViewEvents(eventName,packageName);
      }
    });
  });

  const trackPdoViewEvents = (eventName, packageName) => {
    trackPDTV3Event({
      actionType: PDT_EVENT_TYPES.contentSeen,
      value: eventName,
      shouldTrackToAdobe:false

    });
    trackLocalClickEvent({ eventName, prop1: packageName });
  };

  const viewConfigRef = React.useRef({ viewAreaCoveragePercentThreshold: 50 });

  const handleChangeTab = (index) => {
    setCurrentTab(index);
    flatListRef.current.scrollToIndex({ index: 0 });
  };

  const handleLayout = (event) => {
    const { order, header } = data || {};
      trackPdoViewEvents(`PDO_LOADED_${order}`, header);
  };

  return (
    <View onLayout={handleLayout}>
      <HolidaySectionHeader heading={header} styles={GenericSectionHeadingCss}>
        <CityTabList
          currentTab={currentTab}
          cityWiseData={cityWiseData}
          handleChangeTab={handleChangeTab}
          sectionCode={sectionCode}
          trackLocalClickEvent={trackLocalClickEvent}
        />
        <FlatList
          horizontal
          ref={flatListRef}
          data={cityWiseData[currentTab]}
          renderItem={renderRVSCard}
          contentContainerStyle={GenericSectionHeadingCss.container}
          showsHorizontalScrollIndicator={false}
          keyExtractor={(item, index) => `${item.packageName}-${index}`}
          viewabilityConfig={viewConfigRef.current}
          onViewableItemsChanged={onViewableItemsChanged.current}
        />
      </HolidaySectionHeader>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    borderColor: holidayColors.grayBorder,
    borderWidth: 1,
    ...holidayBorderRadius.borderRadius16,
    ...marginStyles.mr10,
    width: 290,
    height: 180,
  },
  rvsCardContainer: {
    ...paddingStyles.pa12,
    borderTopLeftRadius: borderRadiusValues.br16,
    borderTopRightRadius: borderRadiusValues.br16,
    backgroundColor: holidayColors.white,
  },
  packageDetailsContainer: {
    flexDirection: 'row',
    alignItems: 'flex-start',
  },
  packageNameContainer: {
    width: '80%',
  },
  packageName: {
    ...fontStyles.labelSmallRegular,
    color: holidayColors.gray,
    ...paddingStyles.pb4,
  },
  packageImageContainer: {
    flexDirection: 'column',
    marginLeft: 'auto',
    alignItems: 'center',
  },
  packageImage: {
    width: 48,
    height: 48,
    borderRadius: 12,
  },
  durationContainer: {
    borderWidth: 1,
    ...holidayBorderRadius.borderRadius4,
    borderColor: holidayColors.darkBlue,
    backgroundColor: holidayColors.white,
    marginTop: -8,
    ...paddingStyles.ph4,
  },
  durationText: {
    ...fontStyles.labelSmallBold,
    fontSize: 10,
    color: holidayColors.darkBlue,
    textAlign: 'center',
    width: '100%',
  },
  packageDetails: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  packageDetailsText: {
    ...fontStyles.labelSmallRegular,
    color: holidayColors.gray,
  },
  dot: {
    width: 3,
    height: 3,
    borderRadius: 5,
    backgroundColor: holidayColors.gray,
    ...marginStyles.mh4,
  },
  dashedLine: {
    ...marginStyles.mv10,
    height: 1,
  },
  priceContainer: {
    flexDirection: 'row',
  },
  priceText: {
    ...fontStyles.labelBaseRegular,
    color: holidayColors.gray,
  },
  priceTextEmphasized: {
    ...fontStyles.labelBaseBlack,
    color: holidayColors.black,
  },
  footerContainer: {
    backgroundColor: holidayColors.lightBlueBg,
    borderBottomLeftRadius: borderRadiusValues.br16,
    borderBottomRightRadius: borderRadiusValues.br16,
    ...paddingStyles.pa10,
    flexGrow: 1,
    justifyContent: 'center',
  },
  footerDescriptionContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  descriptionIcon: {
    width: 16,
    height: 16,
    borderRadius: 8,
    ...marginStyles.mr8,
  },
  descriptionText: {
    ...fontStyles.labelSmallRegular,
    color: holidayColors.gray,
  },
  descriptionTextWidthWithTime: {
    maxWidth: '44%',
  },
  descriptionTextWidthWithoutTime: {
    flexShrink: 1,
  },
  ctaTextContainer: {
    marginLeft: 'auto',
    ...paddingStyles.pl10,
  },
  ctaText: {
    ...fontStyles.labelSmallBlack,
    color: holidayColors.primaryBlue,
  },
});
export default RVSSectionV2;
