import React from 'react';
import { View, Text } from 'react-native';
import HTMLView from 'react-native-htmlview';
import entities from 'entities';
import { isRawClient } from '../../../../utils/HolidayUtils';

const htmlStyleForWeb = {
  fontSize: 12,
  color: '#000000',
  fontFamily: 'Lato-Regular',
};

const getFormattedHtmlText = ({ htmlText = ''}) => {
  let formattedText = htmlText;
  const charToReplace = {
    '<b>': '<b style= "font-family:Lato-bold">',
  };
  Object.keys(charToReplace).forEach(
    (key) => (formattedText = formattedText.replaceAll(key, charToReplace[key])),
  );

  return formattedText;
};

const HtmlHeadingV2 = ({ htmlText, style, mWebStyle = htmlStyleForWeb }) => {
  const renderNode = (node, index, siblings, parent, defaultRenderer) => {
    if (node.type === 'tag') {
      if (node.name === 'p') {
        return <Text style={style.heading}>{defaultRenderer(node.children, parent)}</Text>;
      } else if (node.name === 'b') {
        return <Text style={style.bold}>{defaultRenderer(node.children, parent)}</Text>;
      }
    } else if (node.type === 'text') {
      return entities.decodeHTML(node.data);
    }
    return undefined;
  };

  if (isRawClient()) {
    const textStyles = {
      ...mWebStyle,
      lineHeight: 1.5,
    };
    return (
      <View style={{...style, flex: 1}}>
        <div
          style={textStyles}
          dangerouslySetInnerHTML={{ __html: getFormattedHtmlText({ htmlText }) }}
        />
      </View>
    );
  }

  return <HTMLView value={`<p>${htmlText}</p>`} renderNode={renderNode} />;
};

export default HtmlHeadingV2;
