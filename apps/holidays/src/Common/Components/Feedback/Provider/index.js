import React, { useState, useCallback,useEffect} from 'react';
import { TouchableOpacity, Text } from 'react-native';
import FeedbackContext from '../context';
import FeedbackModal from '../Modal';
import { getCanShowSurvey, updateSurveyInfo } from '../common/Utils';
import { setFeedbackFormVisibility } from '../redux/actions';
import { connect } from 'react-redux';
import { FeedbackFormParams } from '../common/Utils';

const FeedbackProvider = ({children,feedbackFormDisplayed,setFeedbackFormVisibility}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [params, setParams] = useState({});



const openFeedbackModal = useCallback(
    (params = {}) => {
      const { onClose = () => {},surveyKey ,skipCheck = false} = params;
      if (skipCheck){
                surveyUpdate(true,surveyKey,params);
              }
       else {
        if (!feedbackFormDisplayed){
          getCanShowSurvey(surveyKey).then((canShowSurvey) => {
            if (canShowSurvey) {setFeedbackFormVisibility(true);} //to hide feedback form in that session
            surveyUpdate(canShowSurvey,surveyKey,params);
          });
        }
        else {
          onClose();
        }
       }

    },
    [setIsOpen, setParams],
  );
  const surveyUpdate = (canShowSurvey,surveyKey,params)=>{
        const { onClose = () => {}} = params || {};
        if (canShowSurvey) {
          setIsOpen(true);
          params.apiParams = {
            ...FeedbackFormParams,
            ...params.apiParams,
          };
          setParams(params);
        } else {
          if (surveyKey) {
            updateSurveyInfo(surveyKey);
            onClose();
          }
        }
      };
  const closeFeedbackModal = useCallback(() => {
    setIsOpen(false);
    const { onClose } = params;
    if (typeof onClose === 'function'){
        params.onClose();
    }
    setParams({});
  }, [setIsOpen, setParams, params]);

  return (
    <FeedbackContext.Provider value={{ openFeedbackModal }}>
      {children}
      <FeedbackModal
        apiParams={params?.apiParams}
        isOpen={isOpen}
        onRequestClose={closeFeedbackModal}
        surveyKey={params?.surveyKey}
      />

    </FeedbackContext.Provider>
  );
};
const mapStateToProps = (state) => {
  return {
    feedbackFormDisplayed: state.feedback.feedbackFormDisplayed,
  };
};

export default connect(mapStateToProps,{setFeedbackFormVisibility})(FeedbackProvider);
