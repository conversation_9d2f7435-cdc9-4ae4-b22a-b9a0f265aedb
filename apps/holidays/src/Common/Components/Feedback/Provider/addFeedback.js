import React, { useContext } from 'react';
import FeedbackContext from '../context';
import withFeedback from './withFeedback';

export default (WrappedComponent) => {
  const InnerComponent = (props) => {
    const feedback = useContext(FeedbackContext);
    const { openFeedbackModal } = feedback;
    return <WrappedComponent {...props} openFeedbackModal={openFeedbackModal} />;
  };

  return withFeedback(InnerComponent);
};
