import { FEEDBACK_JOURNEY_STATES } from '../constants';
import {
  SET_SURVEY_DATA,
  CLEAR_SURVEY_DATA,
  SET_JOURNEY_STATE,
  SET_FEEDBACK_FORM,
} from '../constants/actionConstants';
const initialState = {
  survey: null,
  masterQuestions: [],
  isLoading: true,
  surveyResponse: {},
  feedbackJourneyState: FEEDBACK_JOURNEY_STATES.INITIAL,
  feedbackFormDisplayed:false,
};

export default (state = initialState, action) => {
  switch (action.type) {
    case SET_SURVEY_DATA:
      const { survey, masterQuestions, surveyResponse } = action.payload;
      return {
        ...state,
        survey,
        masterQuestions,
        surveyResponse,
      };
    case CLEAR_SURVEY_DATA:
      return {
        ...state,
        survey: null,
        masterQuestions: [],
        surveyResponse: {},
        feedbackJourneyState: FEEDBACK_JOURNEY_STATES.INITIAL,
      };
    case SET_JOURNEY_STATE:
      return {
        ...state,
        feedbackJourneyState: action.payload,
      };
     case SET_FEEDBACK_FORM:
       return {
         ...state,
         feedbackFormDisplayed:action.value,
       };
    default:
      return state;
  }
};
