import { FEEDBACK_JOURNEY_STATES, FEEDBACK_SERVER, SURVEY_TAKEN } from '../constants';
import {
  CLEAR_SURVEY_DATA,
  SET_SURVEY_DATA,
  SET_JOURNEY_STATE,
  SET_FEEDBACK_FORM,
} from '../constants/actionConstants';
import { getRequestHeaders } from '../../../../utils/HolidayNetworkUtils';

export const getFeedbackQuestions = (params = {}) => async (dispatch) => {
  try {
    /**
     * TODO: Replace this with service created by <PERSON><PERSON><PERSON><PERSON><PERSON> for creating requests
     */
    const urlTocheckStatus = `${FEEDBACK_SERVER}/surveymgmt/v1/surveyresponse/uniqueId/${params?.bookingId}`;
    const checkStatusResponse = await fetch(urlTocheckStatus, {
      method: 'GET',
      headers: await getRequestHeaders(false, false),
    });
    const checkStatusResponseBody = await checkStatusResponse.json();
    if (checkStatusResponseBody?.surveyStatus != SURVEY_TAKEN){
      const url = `${FEEDBACK_SERVER}/surveymgmt/v1/commonnps/CreateSurvey`;
      dispatch({
        type: CLEAR_SURVEY_DATA,
      });
      dispatch({
        type: SET_JOURNEY_STATE,
        payload: FEEDBACK_JOURNEY_STATES.FETCH_LOADING,
      });
      const response = await fetch(`${url}?${new URLSearchParams(params)}`, {
        method: 'GET',
        headers: await getRequestHeaders(false, false),
      });
      if (!response && !response.ok) {
        return null;
      }
      const responseBody = await response.json();

      const { survey, masterQuestions, surveyResponse } = responseBody;
      dispatch({
        type: SET_SURVEY_DATA,
        payload: {
          survey,
          masterQuestions,
          surveyResponse,
        },
      });

      dispatch({
        type: SET_JOURNEY_STATE,
        payload: FEEDBACK_JOURNEY_STATES.FORM,
      });
      return responseBody;
    }
    return null;
  } catch (e) {
    dispatch({
      type: SET_JOURNEY_STATE,
      payload: FEEDBACK_JOURNEY_STATES.ERROR,
    });
    return null;
  }
};
export const getSurveyStatus = (params = {}) => async (dispatch) => {
  try {
    /**
     * TODO: Replace this with service created by Abhishek for creating requests
     */

    const url = `${FEEDBACK_SERVER}/surveymgmt/v1/surveyresponse/token/`;

    const response = await fetch(`${url}/${params.token}`, {
      method: 'GET',
      headers: await getRequestHeaders(false, false),
    });
    if (!response && !response.ok) {
      return null;
    }
    const responseBody = await response.json();

    const { surveyStatus } = responseBody;

    console.log(surveyStatus);
    return responseBody;
  } catch (e) {
    dispatch({
      type: SET_JOURNEY_STATE,
      payload: FEEDBACK_JOURNEY_STATES.ERROR,
    });
    return null;
  }
};

export const submitFeedbackResponse = (params = {}) => async (dispatch) => {
  try {
    /**
     * TODO: Replace this with service created by Abhishek for creating requests
     */

    const url = `${FEEDBACK_SERVER}/surveymgmt/v1/surveyresponse`;
    dispatch({
      type: SET_JOURNEY_STATE,
      payload: FEEDBACK_JOURNEY_STATES.SUBMIT_LOADING,
    });
    const response = await fetch(`${url}`, {
      method: 'PUT',
      headers: await getRequestHeaders(false, false),
      body: JSON.stringify(params),
    });
    if (!response && !response.ok) {
      return null;
    }
    const responseBody = await response.json();

    dispatch({
      type: SET_JOURNEY_STATE,
      payload: FEEDBACK_JOURNEY_STATES.SUCCESS,
    });
    return responseBody;
  } catch (e) {
    dispatch({
      type: SET_JOURNEY_STATE,
      payload: FEEDBACK_JOURNEY_STATES.ERROR,
    });
    return null;
  }
};

export const clearSurveyData = () => (dispatch) => {
  dispatch({
    type: CLEAR_SURVEY_DATA,
  });
};
export const setFeedbackFormVisibility = (val) => (dispatch) => {
  dispatch({
    type: SET_FEEDBACK_FORM,
    value:val,
  });
};

