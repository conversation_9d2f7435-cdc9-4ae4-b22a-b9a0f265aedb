import { Platform } from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { getUserDetails } from '@mmt/legacy-commons/Native/UserSession/UserSessionModule';

const SURVEY_INFO_KEY = 'mmtSurveyInfo';
const lastVisitTimeLimit = 10;
const noOfVisitsLimit = 10;

export const getCanShowSurvey = async (surveyKey) => {
  const surveyInfo = await AsyncStorage.getItem(SURVEY_INFO_KEY);

  if (surveyInfo) {
    const parsedSurveyInfo = JSON.parse(surveyInfo);
    const currentInfo = parsedSurveyInfo[surveyKey];
    if (currentInfo) {
      const timeSinceLastVisit = +new Date() - currentInfo?.lastVisitTime;
      return (
        noOfVisitsLimit <= currentInfo.noOfVisits ||
        lastVisitTimeLimit <= Math.floor(timeSinceLastVisit / (1000 * 60 * 60 * 24))
      );
    } else {
      return true;
    }
  } else {
    return true;
  }
};

export const updateSurveyInfo = async (surveyKey, reset = false) => {
  const surveyInfo = await AsyncStorage.getItem(SURVEY_INFO_KEY);

  let parsedSurveyInfo;
  if (surveyInfo) {
    parsedSurveyInfo = JSON.parse(surveyInfo);
  } else {
    parsedSurveyInfo = {
      [surveyKey]: {
        noOfVisits: 0,
      },
    };
  }

  if (reset) {
    parsedSurveyInfo[surveyKey] = {
      noOfVisits: 1,
      lastVisitTime: +new Date(),
    };
  } else {
    const noOfVisits = parsedSurveyInfo[surveyKey]?.noOfVisits || 0;
    parsedSurveyInfo[surveyKey] = {
      noOfVisits: noOfVisits + 1,
      lastVisitTime: +new Date(),
    };
  }

  await AsyncStorage.setItem(SURVEY_INFO_KEY, JSON.stringify(parsedSurveyInfo));
};

export const FeedbackFormParams = {
                    bookingStatus: 'Confirmed',
                    source: Platform.OS,
                    lob: 'Flights_Dom',
                    npsRating: '3',
                    tenant: 'MMT',
      };
