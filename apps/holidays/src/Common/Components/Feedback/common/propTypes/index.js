import PropTypes from 'prop-types';

export const QuestionPropType = PropTypes.shape({
  id: PropTypes.string,
  text: PropTypes.string,
  buckets: PropTypes.arrayOf(
    PropTypes.shape({
      id: PropTypes.string,
      message: PropTypes.string,
      questions: PropTypes.arrayOf(
        PropTypes.shape({
          questionId: PropTypes.string,
        }),
      ),
    }),
  ),
  options: PropTypes.arrayOf(
    PropTypes.shape({
      id: PropTypes.string,
      text: PropTypes.string,
    }),
  ),
});
