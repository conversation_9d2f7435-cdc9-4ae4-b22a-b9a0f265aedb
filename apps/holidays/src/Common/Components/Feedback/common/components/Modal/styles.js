import { StyleSheet } from 'react-native';

const styles = StyleSheet.create({
  modalContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  overlay: {
    backgroundColor: 'rgba(0,0,0,0.7)',
    flex: 1,
    position: 'absolute',
    bottom: 0,
    top: 0,
    left: 0,
    right: 0,
  },
  modalBase: {
    minHeight: 350,
    width: '90%',
    backgroundColor: '#fff',
    alignItems: 'center',
    borderRadius: 4,
    position: 'relative',
  },
  crossIcon: {
    height: 14,
    width: 14,
  },
  closeModalButton: {
    position: 'absolute',
    top: 14,
    right: 14,
  },
});

export default styles;
