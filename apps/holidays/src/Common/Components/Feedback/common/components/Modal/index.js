import React from 'react';
import { View, Modal, TouchableOpacity, Image, Text } from 'react-native';
import LinearGradient from 'react-native-linear-gradient';
import PropTypes from 'prop-types';

import ConfirmationImage from '@mmt/legacy-assets/src/Feedback/confirmation-image.webp';
import CrossIcon from '@mmt/legacy-assets/src/ic_cross__gray.webp';

import styles from './styles';

const customModalDefaultPropOnRequestClose = () => {};

const CustomModal = ({ isOpen = false, onRequestClose = customModalDefaultPropOnRequestClose, children, minHeight }) => {
  return (
    <Modal
      visible={isOpen}
      animationType={'fade'}
      transparent={true}
      onRequestClose={onRequestClose}
    >
      <View style={styles.modalContainer}>
        <TouchableOpacity activeOpacity={1} style={styles.overlay} onPress={onRequestClose} />
        <View style={[styles.modalBase, minHeight ? {minHeight: minHeight} : {}]}>
          <TouchableOpacity onPress={onRequestClose} style={styles.closeModalButton}>
            <Image source={CrossIcon} style={styles.crossIcon} />
          </TouchableOpacity>
          {children}
        </View>
      </View>
    </Modal>
  );
};

export default CustomModal;

CustomModal.propTypes = {
  isOpen: PropTypes.bool,
  onRequestClose: PropTypes.func,
  text: PropTypes.string,
  subText: PropTypes.string,
  closeButtonLabel: PropTypes.string,
  openFeedbackButtonLabel: PropTypes.string,
};
