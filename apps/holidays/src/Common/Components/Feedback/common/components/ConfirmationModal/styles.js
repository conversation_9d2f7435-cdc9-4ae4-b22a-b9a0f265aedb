import { StyleSheet } from 'react-native';
import { marginStyles } from 'mobile-holidays-react-native/src/Styles/Spacing';
import { holidayColors } from 'mobile-holidays-react-native/src/Styles/holidayColors';
import { fontStyles } from 'mobile-holidays-react-native/src/Styles/holidayFonts';
import { paddingStyles } from '../../../../../../Styles/Spacing';

const styles = StyleSheet.create({
  topImage: {
    marginTop: 32,
  },
  textContainer: {
    width: '100%',
    alignItems: 'center',
    justifyContent: 'center',
    padding: 15,
  },
  infoText: {
    ...fontStyles.labelMediumBold,
    lineHeight: 20,
    ...marginStyles.mt14,
    color: holidayColors.black,
    textAlign: 'center',
  },
  infoSubText: {
    ...fontStyles.labelSmallRegular,
    lineHeight: 19,
    ...marginStyles.mt8,
    textAlign: 'center',
  },
  actionContainer: {
    flex: 1,
    backgroundColor: holidayColors.lightGray2,
    width: '100%',
    alignItems: 'center',
    justifyContent: 'center',
    borderBottomLeftRadius: 4,
    borderBottomRightRadius: 4,
  },
  openFeedbackAction: {
    width: 224,
    ...paddingStyles.pv10,
  },
  closeModalButtonGradient: {
    width: 224,
    ...marginStyles.mt10,
  },
});

export default styles;
