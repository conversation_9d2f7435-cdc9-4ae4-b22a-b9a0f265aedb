import React from 'react';
import { View, TouchableOpacity, Image, Text } from 'react-native';
import LinearGradient from 'react-native-linear-gradient';
import PropTypes from 'prop-types';

import ConfirmationImage from '@mmt/legacy-assets/src/Feedback/confirmation-image.webp';
import Modal from '../Modal';
import SecondaryButton from 'mobile-holidays-react-native/src/Common/Components/Buttons/SecondaryButton';
import PrimaryButton from 'mobile-holidays-react-native/src/Common/Components/Buttons/PrimaryButton';

import styles from './styles';

const confirmationModalDefaultPropOnRequestClose = () => { };
const confirmationModalDefaultPropHandleCloseButton = () => { };
const confirmationModalDefaultPropHandleOpenFeedbackButton = () => { };

const ConfirmationModal = ({
  isOpen = false,
  onRequestClose = confirmationModalDefaultPropOnRequestClose,
  text,
  subText,
  closeButtonLabel,
  openFeedbackButtonLabel,
  handleCloseButton = confirmationModalDefaultPropHandleCloseButton,
  handleOpenFeedbackButton = confirmationModalDefaultPropHandleOpenFeedbackButton,
  minHeight,
}) => {
  return (
    <Modal isOpen={isOpen} minHeight={minHeight} onRequestClose={onRequestClose}>
      <Image source={ConfirmationImage} style={styles.topImage} />
      <View style={styles.textContainer}>
        <Text style={styles.infoText}>{text}</Text>
        <Text style={styles.infoSubText}>{subText}</Text>
      </View>
      <View style={styles.actionContainer}>
        {openFeedbackButtonLabel ? (
          <SecondaryButton
            buttonText={openFeedbackButtonLabel}
            handleClick={handleOpenFeedbackButton}
            btnContainerStyles={styles.openFeedbackAction}
          />
        ) : (
          []
        )}
        {closeButtonLabel ? (
          <PrimaryButton
            buttonText={closeButtonLabel}
            handleClick={handleCloseButton}
            btnContainerStyles={styles.closeModalButtonGradient}
          />
        ) : (
          []
        )}
      </View>
    </Modal>
  );
};

export default ConfirmationModal;

ConfirmationModal.propTypes = {
  isOpen: PropTypes.bool,
  onRequestClose: PropTypes.func,
  text: PropTypes.string,
  subText: PropTypes.string,
  closeButtonLabel: PropTypes.string,
  openFeedbackButtonLabel: PropTypes.string,
  handleCloseButton: PropTypes.func,
  handleOpenFeedbackButton: PropTypes.func,
};
