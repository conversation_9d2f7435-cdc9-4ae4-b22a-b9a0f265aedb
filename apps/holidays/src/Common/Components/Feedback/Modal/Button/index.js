import React from 'react';
import { TouchableOpacity, Text } from 'react-native';
import LinearGradient from 'react-native-linear-gradient';
import PropTypes from 'prop-types';

import styles from './styles';

const buttonDefaultPropOnPress = () => {};

const Button = ({ onPress = buttonDefaultPropOnPress, label = 'Submit' }) => {
  return (
    <TouchableOpacity style={styles.buttonBase} onPress={onPress}>
      <LinearGradient
        start={{
          x: 1.0,
          y: 0.0,
        }}
        end={{
          x: 0.0,
          y: 1.0,
        }}
        colors={['#065AF3', '#53B2FE']}
        style={styles.submitButton}
      >
        <Text style={styles.submitButtonText}>{label}</Text>
      </LinearGradient>
    </TouchableOpacity>
  );
};

export default Button;

Button.propTypes = {
  onPress: PropTypes.func,
  label: PropTypes.string,
};
