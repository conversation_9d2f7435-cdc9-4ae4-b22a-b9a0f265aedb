import React, { useState, useCallback, useEffect } from 'react';
import { View, ScrollView, StyleSheet } from 'react-native';
import { connect } from 'react-redux';
import PropTypes from 'prop-types';
import BottomSheet from '../../BottomSheet';
import { getFeedbackQuestions, submitFeedbackResponse, clearSurveyData } from '../redux/actions';
import { FEEDBACK_JOURNEY_STATES, SURVEY_TAKEN } from '../constants';
import { updateSurveyInfo } from '../common/Utils';

import Question from './Question';
import Success from './Success';
import Button from './Button';
import Loader from './Loader';
import Error from './Error';

import { QuestionPropType } from '../common/propTypes/index';
const noOpFunc = () => {};

const feedbackModalDefaultPropApiParams = {};
const feedbackModalDefaultPropMasterQuestions = [];
const feedbackModalDefaultPropSurveyResponse = {};

const FeedbackModal = ({
  getFeedbackQuestions = noOpFunc,
  isOpen = false,
  onRequestClose = noOpFunc,
  apiParams = feedbackModalDefaultPropApiParams,
  survey = null,
  masterQuestions = feedbackModalDefaultPropMasterQuestions,
  surveyResponse = feedbackModalDefaultPropSurveyResponse,
  submitFeedbackResponse = noOpFunc,
  feedbackJourneyState = FEEDBACK_JOURNEY_STATES.FETCH_LOADING,
  clearSurveyData = noOpFunc,
  surveyKey,
}) => {
  const [answers, setAnswers] = useState({});

  useEffect(() => {
    if (isOpen && apiParams?.bookingId) {
      getFeedbackQuestions(apiParams);
      updateSurveyInfo(surveyKey, apiParams?.category, true);
    } else {
      clearSurveyData();
    }
  }, [isOpen, clearSurveyData, apiParams,surveyKey]);

  const handleAnswerChange = useCallback(
    (key, val,isClearRequired) => {
      setAnswers((answers) => {
        const answersObject = isClearRequired ? {} : answers;
        return {
          ...answersObject,
          [key]: val,
        };
      });
    },
    [setAnswers],
  );

  const handleFeedbackSubmit = () => {
    const requestParams = Object.keys(answers).map((questionId) => {
      return {
        ...answers[questionId],
        questionId,
      };
    });

    submitFeedbackResponse({
      ...surveyResponse,
      answers: requestParams,
    });
  };

  const onDonePress = useCallback(() => {
    clearSurveyData();
    onRequestClose();
  }, [onRequestClose, clearSurveyData]);

  const includesAnyState = () =>{
   const index = Object.keys(FEEDBACK_JOURNEY_STATES).filter((key)=>{
      return FEEDBACK_JOURNEY_STATES[key] == feedbackJourneyState;});
      if ( index?.length > -1){
      return true;
      }
      return false;
  };

  return  surveyResponse?.surveyStatus && surveyResponse?.surveyStatus !== SURVEY_TAKEN  ? (
    <BottomSheet isCloseBtnVisible isOpen={isOpen && includesAnyState()}
    onBackPressed={onRequestClose} closeButtonStyle={{marginBottom:0}}>
      <View style={[styles.questionsContainer]}>
        {(feedbackJourneyState === FEEDBACK_JOURNEY_STATES.FETCH_LOADING ||
          feedbackJourneyState === FEEDBACK_JOURNEY_STATES.SUBMIT_LOADING) && (
          <Loader
            loaderText={
              feedbackJourneyState === FEEDBACK_JOURNEY_STATES.FETCH_LOADING
                ? 'Loading questions. Please wait...'
                : 'Submiting feedback. Please wait...'
            }
          />
        )}
        {feedbackJourneyState === FEEDBACK_JOURNEY_STATES.SUCCESS && (
          <Success onDonePress={onDonePress} />
        )}
        {feedbackJourneyState === FEEDBACK_JOURNEY_STATES.ERROR && (
          <Error onDonePress={onDonePress} />
        )}
        {feedbackJourneyState === FEEDBACK_JOURNEY_STATES.FORM && (
          <>
            <ScrollView style={styles.questionsScrollView}>
              {survey?.questions?.map?.(({ questionId }) => {
                return (
                  <Question
                    questions={masterQuestions}
                    key={questionId}
                    questionId={questionId}
                    handleAnswerChange={handleAnswerChange}
                    value={answers[questionId]}
                    answers={answers}
                  />
                );
              })}
            </ScrollView>
            <View style={styles.buttonContainer}>
              <Button onPress={handleFeedbackSubmit} label="Submit" />
            </View>
          </>
        )}
      </View>
    </BottomSheet>
  ) : null;
};

const styles = StyleSheet.create({
  questionsContainer: {
    backgroundColor: '#fff',
    width: '100%',
    flexDirection: 'column',
    justifyContent: 'flex-start',
    borderWidth: 1,
    borderColor: '#fff',
    position: 'relative',
  },
  buttonContainer: {
    position: 'absolute',
    bottom: 0,
    height: 56,
    marginBottom: 5,
    flexDirection: 'row',
    width: '100%',
    backgroundColor: '#fff',
    alignItems: 'center',
    justifyContent: 'center',
  },
  questionsScrollView: {
    paddingBottom: 60,
    maxHeight: '95%',
  },
});

const mapStateToProps = (state) => {
  return {
    survey: state.feedback.survey,
    masterQuestions: state.feedback.masterQuestions,
    surveyResponse: state.feedback.surveyResponse,
    feedbackJourneyState: state.feedback.feedbackJourneyState,
  };
};

export default connect(mapStateToProps, {
  getFeedbackQuestions,
  submitFeedbackResponse,
  clearSurveyData,
})(FeedbackModal);

FeedbackModal.propTypes = {
  getFeedbackQuestions: PropTypes.func,
  isOpen: PropTypes.bool,
  onRequestClose: PropTypes.func,
  apiParams: PropTypes.object,
  survey: PropTypes.shape({
    title: PropTypes.string,
    surveyFilledText: PropTypes.string,
    surveyFilledSubText: PropTypes.string,
    questions: PropTypes.arrayOf(
      PropTypes.shape({
        questionId: PropTypes.string,
      }),
    ),
  }),
  masterQuestions: PropTypes.arrayOf(QuestionPropType),
  surveyResponse: PropTypes.shape({
    surveyIdentifier: PropTypes.string,
    token: PropTypes.string,
    channel: PropTypes.string,
    uniqueId: PropTypes.string,
  }),
  submitFeedbackResponse: PropTypes.func,
  feedbackJourneyState: PropTypes.oneOf(Object.values(FEEDBACK_JOURNEY_STATES)),
  clearSurveyData: PropTypes.func,
};
