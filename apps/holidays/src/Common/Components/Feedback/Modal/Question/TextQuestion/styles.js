import { StyleSheet } from 'react-native';
import { holidayColors } from '../../../../../../Styles/holidayColors';
import { fontStyles } from '../../../../../../Styles/holidayFonts';

const styles = StyleSheet.create({
  questionContainer: {
    flexDirection: 'column',
    width: '100%',
    marginTop: 40,
    marginBottom: 12,
  },
  questionText: {
    ...fontStyles.labelBaseRegular,
    lineHeight: 19,
    color:holidayColors.black,
  },
  question:{marginBottom: 18,marginTop:20},
  inputContainer: {
    flexDirection: 'row',
    width: '100%',
    marginTop: 22,
    marginBottom:20,
  },
  input: {
    borderWidth: 1,
    borderColor: '#9B9B9B',
    padding: 10,
    width: '100%',
    height: 110,
    borderRadius: 4,
  },
});

export default styles;
