import React, { useEffect } from 'react';
import { View, TextInput, Text } from 'react-native';
import PropTypes from 'prop-types';

import { QuestionPropType } from '../../../common/propTypes';

import styles from './styles';

const textQuestionDefaultPropQuestion = {};
const textQuestionDefaultPropValue = {};
const textQuestionDefaultPropHandleAnswerChange = () => {};

const TextQuestion = ({ question = textQuestionDefaultPropQuestion, value = textQuestionDefaultPropValue, handleAnswerChange = textQuestionDefaultPropHandleAnswerChange }) => {
  useEffect(() => {
    return () => {
      handleAnswerChange(question.id, { text: '' });
    };
  }, [handleAnswerChange, question.id]);

  return (
    <View style={styles.question}>
      <Text style={styles.questionText}>{question.text}</Text>
      <View style={styles.inputContainer}>
        <TextInput
          multiline
          style={styles.input}
          onChangeText={(text) => handleAnswerChange(question.id, { text })}
          value={value?.text}
          placeholder="Please share your feedback here"
          textAlignVertical="top"
        />
      </View>
    </View>
  );
};

export default TextQuestion;

TextQuestion.propTypes = {
  question: QuestionPropType,
  value: PropTypes.shape({
    rating: PropTypes.number,
  }),
  handleAnswerChange: PropTypes.func,
};
