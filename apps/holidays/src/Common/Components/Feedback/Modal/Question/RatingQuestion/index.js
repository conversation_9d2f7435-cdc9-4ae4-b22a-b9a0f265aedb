import React, { useMemo, useEffect } from 'react';
import { View, TouchableOpacity, Image, Text } from 'react-native';
import PropTypes from 'prop-types';
import Rating1 from '@mmt/legacy-assets/src/Feedback/rating-1.webp';
import Rating2 from '@mmt/legacy-assets/src/Feedback/rating-2.webp';
import Rating3 from '@mmt/legacy-assets/src/Feedback/rating-3.webp';
import Rating4 from '@mmt/legacy-assets/src/Feedback/rating-4.webp';
import Rating5 from '@mmt/legacy-assets/src/Feedback/rating-5.webp';
import Rating1Active from '@mmt/legacy-assets/src/Feedback/rating-1-active.webp';
import Rating2Active from '@mmt/legacy-assets/src/Feedback/rating-2-active.webp';
import Rating3Active from '@mmt/legacy-assets/src/Feedback/rating-3-active.webp';
import Rating4Active from '@mmt/legacy-assets/src/Feedback/rating-4-active.webp';
import Rating5Active from '@mmt/legacy-assets/src/Feedback/rating-5-active.webp';

import Question from '..';
import { QuestionPropType } from '../../../common/propTypes';

import styles from './styles';

const ratingImages = {
  Rating1,
  Rating2,
  Rating3,
  Rating4,
  Rating5,
  Rating1Active,
  Rating2Active,
  Rating3Active,
  Rating4Active,
  Rating5Active,
};

const ratingQuestionDefaultPropQuestions = [];
const ratingQuestionDefaultPropQuestion = {};
const ratingQuestionDefaultPropValue = {};
const ratingQuestionDefaultPropHandleAnswerChange = () => {};
const ratingQuestionDefaultPropAnswers = {};

const RatingQuestion = ({ questions = ratingQuestionDefaultPropQuestions, question = ratingQuestionDefaultPropQuestion, value = ratingQuestionDefaultPropValue, handleAnswerChange = ratingQuestionDefaultPropHandleAnswerChange, answers = ratingQuestionDefaultPropAnswers }) => {
  useEffect(() => {
    return () => {
      handleAnswerChange(question.id, null);
    };
  }, [handleAnswerChange, question.id]);

  const additionalQuestionsMessage = useMemo(() => {
    let additionalQuestions = [],message = '';
    if (value?.rating && question?.buckets?.length) {
      const currentBucket = question.buckets.find((bucket) => {
        return +value.rating >= +bucket.minRating && +value.rating <= +bucket.maxRating;
      });

      if (currentBucket?.questions?.length) {
        additionalQuestions = currentBucket.questions;
      }
      message = currentBucket.message;
    }

    return {additionalQuestions,message};
  }, [value, question]);

  return (
    <View style={styles.questionContainer}>
      <Text style={styles.questionText}>{question.text}</Text>
      <View style={styles.optionsContainer}>
        {[...new Array(5)].map((_, index) => {
          const rating = index + 1;
          const isSelected = value?.rating === rating;
          return (
            <TouchableOpacity
              style={[styles.option, isSelected && styles.selectedOption]}
              key={rating}
              onPress={() => handleAnswerChange(question.id, { rating },true)}
            >
              <Image source={ratingImages[`Rating${rating}${isSelected ? 'Active' : ''}`]} />
            </TouchableOpacity>
          );
        })}
      </View>
      <View style={styles.messageView}>
        {!!additionalQuestionsMessage?.message && <Text style={styles.message}>{additionalQuestionsMessage?.message}</Text>}
      </View>

      <View>
        {additionalQuestionsMessage?.additionalQuestions.map(({ questionId, condition }) => {
          return (
            <Question
              questions={questions}
              key={questionId}
              questionId={questionId}
              handleAnswerChange={handleAnswerChange}
              value={answers[questionId]}
              answers={answers}
              condition={condition}
            />
          );
        })}
      </View>
    </View>
  );
};

export default RatingQuestion;

RatingQuestion.propTypes = {
  questions: PropTypes.arrayOf(QuestionPropType),
  question: QuestionPropType,
  value: PropTypes.shape({
    rating: PropTypes.number,
  }),
  handleAnswerChange: PropTypes.func,
  answers: PropTypes.object,
};
