import React, { useEffect } from 'react';
import { View, TouchableOpacity, Text } from 'react-native';
import PropTypes from 'prop-types';

import { QuestionPropType } from '../../../common/propTypes';

import styles from './styles';

const checkboxQuestionDefaultPropQuestion = {};
const checkboxQuestionDefaultPropValue = { answerIds: [] };
const checkboxQuestionDefaultPropHandleAnswerChange = () => {};

const CheckboxQuestion = ({
  question = checkboxQuestionDefaultPropQuestion,
  handleAnswerChange = checkboxQuestionDefaultPropHandleAnswerChange,
  value = checkboxQuestionDefaultPropValue,
  isL2Question = false,
  isSingleSelect = false,
}) => {
  useEffect(() => {
    return () => {
      handleAnswerChange(question.id, { answerIds: [] });
    };
  }, [handleAnswerChange, question.id]);

  const handleOptionSelect = (optionId) => {
    if (isSingleSelect) {
      handleAnswerChange(question.id, {
        answerIds: [optionId],
      });
    } else {
      if (value?.answerIds?.indexOf?.(optionId) > -1) {
        handleAnswerChange(question.id, {
          answerIds: value.answerIds.filter((answerId) => {
            return answerId !== optionId;
          }),
        });
      } else {
        handleAnswerChange(question.id, {
          answerIds: [...value.answerIds, optionId],
        });
      }
    }
  };

  return (
    <View style={[styles.questionContainer, isL2Question && styles.conditionalQuestion]}>
      <Text style={styles.questionText}>{question.text}</Text>
      <View style={styles.optionsContainer}>
        {question.options.map((option) => {
          const isOptionSelected = value?.answerIds?.indexOf?.(option.id) > -1;
          return (
            <TouchableOpacity onPress={() => handleOptionSelect(option.id)} key={option.id}>
              <View
                style={[
                  styles.option,
                  isL2Question && styles.conditionalQuestionOption,
                  isOptionSelected && styles.selectedOption,
                ]}
              >
                <Text style={[styles.optionText, isOptionSelected && styles.selectedOptionText]}>
                  {option.text}
                </Text>
              </View>
            </TouchableOpacity>
          );
        })}
      </View>
    </View>
  );
};

export default CheckboxQuestion;

CheckboxQuestion.propTypes = {
  question: QuestionPropType,
  value: PropTypes.shape({
    rating: PropTypes.number,
  }),
  handleAnswerChange: PropTypes.func,
  isL2Question: PropTypes.bool,
  isSingleSelect: PropTypes.bool,
};
