import React from 'react';
import PropTypes from 'prop-types';
import RatingQuestion from './RatingQuestion';
import TextQuestion from './TextQuestion';
import CheckboxQuestion from './CheckboxQuestion';
import { QUESTION_TYPES } from '../../constants';
import { QuestionPropType } from '../../common/propTypes';

const questionDefaultPropQuestions = [];
const questionDefaultPropHandleAnswerChange = () => {};
const questionDefaultPropAnswers = {};
const questionDefaultPropCondition = {};

const Question = ({ questions = questionDefaultPropQuestions, value, handleAnswerChange = questionDefaultPropHandleAnswerChange, answers = questionDefaultPropAnswers, questionId = '', condition = questionDefaultPropCondition }) => {
  /**
   * do not show the question if it does not match the condition
   */
  let isL2Question = false;
  if (condition?.onQuestionId && condition?.onAnswerIds?.length) {
    isL2Question = true;
    const selectedQuestionanswers = answers?.[condition?.onQuestionId]?.answerIds || [];
    if (selectedQuestionanswers?.length) {
      const uniqueIds = [...new Set([...selectedQuestionanswers, ...condition.onAnswerIds])];
      if (uniqueIds.length === selectedQuestionanswers.length + condition.onAnswerIds.length) {
        return null;
      }
    } else {
      return null;
    }
  }

  const question = questions.find((q) => {
    return q.id === questionId;
  });

  switch (question.type) {
    case QUESTION_TYPES.RATING:
      return (
        <RatingQuestion
          question={question}
          questions={questions}
          handleAnswerChange={handleAnswerChange}
          value={value}
          answers={answers}
          isL2Question={isL2Question}
        />
      );
    case QUESTION_TYPES.TEXT_BOX:
      return (
        <TextQuestion
          question={question}
          questions={questions}
          handleAnswerChange={handleAnswerChange}
          value={value}
          answers={answers}
          isL2Question={isL2Question}
        />
      );
    case QUESTION_TYPES.CHECKBOX:
      return (
        <CheckboxQuestion
          question={question}
          questions={questions}
          handleAnswerChange={handleAnswerChange}
          value={value}
          answers={answers}
          isL2Question={isL2Question}
        />
      );
    default:
      /**
       * As discussed with Mayank Gaur and Nishant, if the question
       * does not match any type it should be treated as radio button
       * allowing to only select a single option
       */
      return (
        <CheckboxQuestion
          isSingleSelect
          question={question}
          questions={questions}
          handleAnswerChange={handleAnswerChange}
          value={value}
          answers={answers}
          isL2Question={isL2Question}
        />
      );
  }
};

export default Question;

Question.propTypes = {
  questions: PropTypes.arrayOf(QuestionPropType),
  value: PropTypes.shape({
    text: PropTypes.string,
    rating: PropTypes.number,
    answerIds: PropTypes.arrayOf(PropTypes.string),
  }),
  handleAnswerChange: PropTypes.func,
  answers: PropTypes.object,
  questionId: PropTypes.string,
  condition: PropTypes.shape({
    onAnswerIds: PropTypes.arrayOf(PropTypes.string),
    onQuestionId: PropTypes.string,
  }),
};
