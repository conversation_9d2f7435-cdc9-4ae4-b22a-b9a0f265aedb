import { StyleSheet } from 'react-native';
import { holidayColors } from '../../../../../../Styles/holidayColors';
import { fontStyles } from '../../../../../../Styles/holidayFonts';

const styles = StyleSheet.create({
  questionContainer: {
    flexDirection: 'column',
    width: '100%',
  },
  questionText: {
    lineHeight: 18,
    ...fontStyles.labelMediumBold,
    color:holidayColors.black,
  },
  message:{
    ...fontStyles.labelBaseBold,
    lineHeight: 17,
  },
  messageView:{
    marginTop:20,
  },
  optionsContainer: {
    flexDirection: 'row',
    width: '100%',
    marginTop: 22,
    justifyContent: 'center',
  },
  option: {
    width: 50,
    height: 50,
    backgroundColor: '#f2f3fc',
    borderWidth: 1,
    borderRadius: 4,
    borderColor: '#f2f3fc',
    flex: 1,
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    marginHorizontal: 5,
  },
  selectedOption: {
    backgroundColor: '#6575BA',
    borderColor: '#6575BA',
  },
});

export default styles;
