import { StyleSheet } from 'react-native';
import { fonts } from '@mmt/legacy-commons/Styles/globalStyles';
import { holidayColors } from '../../../../../../Styles/holidayColors';
import { fontStyles } from '../../../../../../Styles/holidayFonts';

const styles = StyleSheet.create({
  questionContainer: {
    flexDirection: 'column',
    width: '100%',
    marginTop: 20,
    borderRadius: 4,
  },
  conditionalQuestion: {
    backgroundColor: 'rgba(28, 32, 92, 0.1)',
    paddingVertical: 16,
    paddingHorizontal: 10,
    marginBottom:5,
  },
  questionText: {
    lineHeight: 17,
    ...fontStyles.labelBaseRegular,
    color:holidayColors.black,
  },
  optionsContainer: {
    flexDirection: 'row',
    width: '100%',
    marginTop: 22,
    flexWrap: 'wrap',
  },
  option: {
    paddingHorizontal: 9,
    paddingVertical: 6,
    backgroundColor: '#F2F3FC',
    borderRadius: 20,
    marginRight: 6,
    marginBottom: 10,
  },
  conditionalQuestionOption: {
    backgroundColor: '#fff',
    borderRadius: 4,
  },
  selectedOption: {
    backgroundColor: '#6575BA',
  },
  optionText: {
    fontSize: 12,
    lineHeight: 14,
    color: '#000',
    fontFamily: fonts.regular,
  },
  selectedOptionText: {
    color: '#fff',
  },
});

export default styles;
