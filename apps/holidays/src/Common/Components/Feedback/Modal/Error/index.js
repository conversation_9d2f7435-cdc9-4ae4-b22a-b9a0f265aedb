import React from 'react';
import { SafeAreaView, View, Image, Text } from 'react-native';
import PropTypes from 'prop-types';
import ErrorIcon from '@mmt/legacy-assets/src/error_icon.webp';

import Button from '../Button';

import styles from './styles';

const errorDefaultPropOnDonePress = () => {};

const Error = ({ onDonePress = errorDefaultPropOnDonePress }) => {
  return (
      <View style={styles.container}>
        <View style={styles.errorContainer}>
          <Image source={ErrorIcon} />
          <Text style={styles.errorTextHeader}>An error occurred</Text>
          <Text style={styles.errorText}>Please try again later</Text>
        </View>

        <View style={styles.buttonContainer}>
          <Button onPress={onDonePress} label="Done" />
        </View>
      </View>
  );
};

export default Error;

Error.propTypes = {
  onDonePress: PropTypes.func,
};
