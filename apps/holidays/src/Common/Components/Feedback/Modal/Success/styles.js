import { StyleSheet } from 'react-native';
import { fonts } from '@mmt/legacy-commons/Styles/globalStyles';

const styles = StyleSheet.create({
  container: {
    flexDirection: 'column',
    justifyContent: 'center',
    alignItems: 'center',
    width: '100%',
  },
  successImage: {
    width: 200,
    height: 78,
  },
  infoTextHeader: {
    fontSize: 18,
    fontWeight: '700',
    textAlign: 'center',
    marginTop: 18,
    fontFamily: fonts.semiBold,
  },
  infoText: {
    fontSize: 14,
    fontWeight: '400',
    textAlign: 'center',
    marginTop: 10,
    fontFamily: fonts.regular,
  },
  buttonContainer: {
    width: '100%',
    marginTop: 24,
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
  },
});

export default styles;
