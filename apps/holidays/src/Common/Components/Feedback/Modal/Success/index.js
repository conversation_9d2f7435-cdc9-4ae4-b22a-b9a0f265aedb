import React from 'react';
import { SafeAreaView, View, Image, Text } from 'react-native';
import { connect } from 'react-redux';
import PropTypes from 'prop-types';
import SuccessImage from '@mmt/legacy-assets/src/Feedback/successImage.webp';

import Button from '../Button';

import styles from './styles';

const successDefaultPropOnDonePress = () => {};

const Success = ({
  onDonePress = successDefaultPropOnDonePress,
  text = 'Thank you for your feedback with us',
  subText = 'Your feedback is important',
}) => {
  return (
      <View style={styles.container}>
        <Image source={SuccessImage} style={styles.successImage} />
        <Text style={styles.infoTextHeader}>{text}</Text>
        <Text style={styles.infoText}>{subText}</Text>
        <View style={styles.buttonContainer}>
          <Button onPress={onDonePress} label="Done" />
        </View>
      </View>
  );
};

const mapStateToProps = (state) => {
  return {
    text: state.feedback?.survey?.surveyFilledText,
    subText: state.feedback?.survey?.surveyFilledSubText,
  };
};

export default connect(mapStateToProps, null)(Success);

Success.propTypes = {
  onDonePress: PropTypes.func,
  text: PropTypes.string,
  subText: PropTypes.string,
};
