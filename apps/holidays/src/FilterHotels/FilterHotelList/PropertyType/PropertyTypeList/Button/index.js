import React from 'react';
import {Text,TouchableOpacity} from 'react-native';
import styles from './ButtonCss';
import LinearGradient from 'react-native-linear-gradient';

let gradientColor = ['#065af3', '#53b2fe'];



class Button extends React.Component{
constructor(props){
    super(props);
    this.handleClick = this.handleClick.bind(this);
}

handleClick(){
    if (this.props.handleClick){
      this.props.handleClick();
    }
  }

render(){

    const CapsuleBtnStyle = [styles.capsuleBtn];
    const buttonStyle = [styles.buttonStyle];
    const buttonTxtStyle = [styles.buttonTxtStyle];
    const btnWidth = this.props.btnWidth;

    if (this.props.btnBg === 'white'){
       gradientColor = ['#fff','#fff'];
       buttonTxtStyle.push(styles.whiteBtnTxt);
       buttonStyle.push(styles.whiteButtonStyle);
    }
    if (this.props.btnBg === 'whiteWithBorder'){
        gradientColor = ['#fff','#fff'];
        buttonTxtStyle.push(styles.whiteBtnTxt);
        buttonStyle.push(styles.whiteButtonBorderStyle);
        CapsuleBtnStyle.push(styles.noShadowStyle);
     }
    if (this.props.btnBg === 'gradientBtn'){
        gradientColor = ['#065af3', '#53b2fe'];
        buttonTxtStyle.push(styles.gradientBtnTxt);
     }
     if (this.props.btnBg === 'gradientBtn' && this.props.btnType === 'flat'){
        gradientColor = ['#065af3', '#53b2fe'];
        buttonTxtStyle.push(styles.gradientBtnTxt);
        CapsuleBtnStyle.push(styles.flatBtn);
     }

    return (

        <TouchableOpacity style={[buttonStyle,{width:btnWidth}]} activeOpacity={0.6} onPress={this.handleClick}>
          <LinearGradient
            start={{x: 1.0, y: 0.0}}
            end={{x: 0.0, y: 1.0}}
            colors={gradientColor}
            style={CapsuleBtnStyle} >
                <Text style={buttonTxtStyle}>{this.props.btnTxt}</Text>
            </LinearGradient>

         </TouchableOpacity>

    );
}
}

export default Button;
