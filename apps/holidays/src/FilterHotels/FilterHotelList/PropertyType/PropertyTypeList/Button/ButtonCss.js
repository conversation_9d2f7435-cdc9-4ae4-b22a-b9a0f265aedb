import getPlatformElevation from '@mmt/legacy-commons/Styles/getPlatformElevation';
import {fonts} from '@mmt/legacy-commons/Common/Components/MIMABottomOverlay/OverlayMessage/globalStyles';

const styles = {
  capsuleBtn: {
    borderRadius: 25,
    paddingHorizontal: 20,
    paddingVertical: 14,
    ...getPlatformElevation(2),

  },
  flatBtn: {
    borderRadius: 4,
    paddingHorizontal: 20,
    paddingVertical: 14,
    ...getPlatformElevation(2),

  },
  buttonStyle: {
    backgroundColor: 'transparent',
    ...getPlatformElevation(2),

  },
  buttonTxtStyle: {
    fontSize: 18,
    fontFamily: fonts.black,
    textAlign: 'center',
    color: '#fff',
  },
  whiteButtonStyle: {
    backgroundColor: 'transparent',
    borderRadius: 25,
    margin: 5,
  },
  whiteButtonBorderStyle: {
    backgroundColor: '#fff',
    borderRadius: 25,
    borderWidth: 1,
    borderColor: '#008cff',
    ...getPlatformElevation(0),
  },
  whiteBtnTxt: {
    color: '#008cff',
  },
  gradientBtnTxt: {
    color: '#fff',
  },
  noShadowStyle: {
    ...getPlatformElevation(0),
    paddingVertical: 12,
  },
};
export default styles;
