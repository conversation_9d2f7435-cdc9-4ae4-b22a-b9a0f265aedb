import React, {Fragment} from 'react';
import RangeSlider from './RangeSlider';
import BasePage from '@mmt/legacy-commons/Common/navigation/BasePage';
import FilterSectionHeader from '../../../FilterSectionHeader';
class BudgetFilter extends BasePage {
  render() {
    const {appliedPriceRange, days, fullPriceRange, updateAppliedPriceRangeFilterData} = this.props;

    const minRange =  appliedPriceRange.min ? appliedPriceRange.min : fullPriceRange.min;
    const maxRange =  appliedPriceRange.max ? appliedPriceRange.max : fullPriceRange.max;

    if (minRange === maxRange){
      return [];
    }

    return (
      <Fragment>
        <FilterSectionHeader title={days ? `Price Range (for ${days} nights)` : 'Price Range'}/>
        <RangeSlider
          fullPriceRange={fullPriceRange}
          appliedPriceRange={appliedPriceRange}
          updateAppliedPriceRangeFilterData={updateAppliedPriceRangeFilterData}/>
      </Fragment>
    );
  }
}
export default BudgetFilter;
