import React from 'react';
import { Text, View, StyleSheet} from 'react-native';
import { formatDate, getPaxDetails } from '../../../utils/HolidayUtils';
import { holidayColors } from 'mobile-holidays-react-native/src/Styles/holidayColors';
import { fontStyles } from 'mobile-holidays-react-native/src/Styles/holidayFonts';
import { getDayName } from 'mobile-holidays-react-native/src/Common/HolidaysCommonUtils';
import { marginStyles } from 'mobile-holidays-react-native/src/Styles/Spacing';

const RoomHeader = ({ roomDetails, checkInDate, containerStyles }) => {
  const { adult: adultCount, child: childCount, infantCount } = getPaxDetails({roomDetails});
  const kidsCount = childCount + infantCount;
  const roomCount = roomDetails.length;
  return (
    <View style={[marginStyles.mb10, containerStyles]}>
      <Text style={styles.roomTitle}>Room</Text>
      <View style={styles.roomDetails}>
        <Text style={styles.roomType}>
          {roomCount} {roomCount > 1 ? 'Rooms' : 'Room'} for {adultCount}{' '}
          {adultCount > 1 ? 'Adults' : 'Adult'}
        </Text>
        {kidsCount > 0 && (
          <Text style={styles.roomType}>
            and {kidsCount} {kidsCount > 1 ? 'children' : 'child'}
          </Text>
        )}
        {!!checkInDate && (
          <Text style={styles.roomType}>
            {' '}({formatDate(checkInDate, 'DD MMM')}, {getDayName(checkInDate, true)})
          </Text>
        )}
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  roomTitle: {
    ...fontStyles.headingBase,
    color: holidayColors.black,
  },
  roomDetails: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    ...marginStyles.mt4,
  },
  roomType: {
    ...fontStyles.labelBaseRegular,
    color: holidayColors.gray,
  },
});
export default RoomHeader;
