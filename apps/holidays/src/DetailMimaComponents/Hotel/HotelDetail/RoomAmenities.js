import React, { useState } from 'react';
import { View, Text, StyleSheet, Image, TouchableOpacity, Dimensions, ScrollView } from 'react-native';
import { has } from 'lodash';
import cStyles from '@mmt/legacy-commons/Styles/AtomicCss';
import PlaceholderImageView from '@mmt/legacy-commons/Common/Components/PlaceholderImageView';
import { holidayColors } from 'mobile-holidays-react-native/src/Styles/holidayColors';
import { fontStyles } from 'mobile-holidays-react-native/src/Styles/holidayFonts';
import { paddingStyles, marginStyles } from 'mobile-holidays-react-native/src/Styles/Spacing/index';
import { holidayBorderRadius } from 'mobile-holidays-react-native/src/Styles/holidayBorderRadius';
import TickIcon from '@mmt/legacy-assets/src/green.webp';
import BottomSheetOverlay from '../../../Common/Components/BottomSheetOverlay';
import { logPhoenixDetailPDTEvents } from 'mobile-holidays-react-native/src/utils/PhoenixDetailPDTTrackingUtils';
import { PDT_EVENT_TYPES } from 'mobile-holidays-react-native/src/utils/HolidayPDTConstants';

/**
 * Return image urls for the room.
 * This image will be displayed in the accordian of the hotel detail page.
 * Empty string is returned if no image URL is found.
 * @param imageDataList
 */

function getRoomImage(imageDataList) {
  const image = imageDataList?.find((item) => has(item, 'mainImage.path'))?.mainImage?.path;
  return image ?? '';
}

const RoomAmenities = ({ trackOmniture, roomData, enableHotelClick = false, handlehotelClick }) => {
  const { roomInformation, imageDataList } = roomData || {};
  const { amenities } = roomInformation || {};
  const roomImageUrl = getRoomImage(imageDataList);
  const [modalVisibility, setModalVisibility] = useState(false);

  const captureClickEvents = (eventName = '') => {
    logPhoenixDetailPDTEvents({
      actionType: PDT_EVENT_TYPES.buttonClicked,
      value: eventName.replace(/_/g, '|'),
    })
    trackOmniture(eventName);
  }
  const handleClickAmenities = () => {
    setModalVisibility(true);
    captureClickEvents('view_amenities');
  };

  const AmenitiesListItem = ({ amenity }) => (
    <View style={[cStyles.alignCenter, cStyles.marginBottom10]}>
      <Image source={TickIcon} style={styles.amenitiesImage} />
      <View>
        <Text style={styles.amenitiesText}>{amenity}</Text>
      </View>
    </View>
  );

  const renderAmenities = (amenitiesList) => {
    const amenitiesDom = [];
    const defaultAmenitiesCount = 2;
    amenitiesList.slice(0, defaultAmenitiesCount).forEach((amenity, index) => (
      amenitiesDom.push(<AmenitiesListItem key={index} amenity={amenity} />)
    ));
    if (amenitiesList.length > defaultAmenitiesCount) {
      amenitiesDom.push(
        <>
          <TouchableOpacity onPress={handleClickAmenities}>
            <View style={[cStyles.alignCenter, cStyles.justifyEnd]}>
              <Text style={styles.noOfAmenities}>
                {amenitiesList.length - defaultAmenitiesCount}+
              </Text>
              <View>
                <Text style={styles.amenitiesText}>Amenities</Text>
              </View>
            </View>
          </TouchableOpacity>
          {modalVisibility &&
          <BottomSheetOverlay
            title={'Amenities'}
            toggleModal={() => setModalVisibility(!modalVisibility)}
            visible={modalVisibility}
            containerStyles={styles.containerStyles}
            headingContainerStyles={styles.headingContainerStyles}
          >
            <ScrollView contentContainerStyle={{ ...paddingStyles.pb40 }} showsVerticalScrollIndicator={false}>
              <View style={cStyles.flex1}>
                {renderModalAmenities(amenitiesList)}
              </View>
            </ScrollView>
          </BottomSheetOverlay>
          }
        </>,
      );
    }
    return amenitiesDom;
  };

  const renderModalAmenities = (amenitiesList) => {
    return (
      <TouchableOpacity>
        <View style={styles.modalAmenityList}>
          {amenitiesList?.map((item) => (
            <View key={item} style={styles.modalAmenity}>
              <Image source={TickIcon} style={styles.tickIcon} />
              <Text key={item} style={styles.modalAmenityName}>
                {item}
              </Text>
            </View>
          ))}
        </View>
      </TouchableOpacity>
    );
  };

  return amenities?.length > 0 ? (
    <View style={styles.amenitiesWrap}>
      {enableHotelClick ? (
        <TouchableOpacity onPress={handlehotelClick}>
          <PlaceholderImageView url={roomImageUrl} style={styles.iconHotelPic} />
        </TouchableOpacity>
      ) : (
        <View>
          <PlaceholderImageView url={roomImageUrl} style={styles.iconHotelPic} />
        </View>
      )}
      {renderAmenities(amenities)}
    </View>
  ) : (
    []
  );
};

const styles = StyleSheet.create({
  amenitiesImage: {
    width: 22,
    height: 22,
    resizeMode: 'contain',
  },
  noOfAmenities: {
    ...marginStyles.mt2,
    ...fontStyles.labelBaseBlack,
    color: holidayColors.primaryBlue,
    height: 22,
  },
  amenitiesText: {
    ...fontStyles.labelSmallRegular,
    color: holidayColors.black,
    ...paddingStyles.ph6,
  },
  iconHotelPic: {
    width: 42,
    height: 42,
    resizeMode: 'cover',
    ...holidayBorderRadius.borderRadius8,
  },
  amenitiesWrap: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    flexWrap: 'wrap',
  },
  modalAmenityList: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    ...marginStyles.mt10,
  },
  modalAmenity: {
    width: '25%',
    alignItems: 'center',
    ...marginStyles.mb30,
  },
  modalAmenityName: {
    color: holidayColors.gray,
    ...fontStyles.labelSmallRegular,
    textAlign: 'center',
  },
  tickIcon: {
    width: 16,
    height: 8,
    ...marginStyles.mb6,
  },
  containerStyles: {
    ...paddingStyles.pa16,
    maxHeight: Dimensions.get('window').height - 100
  },
  headingContainerStyles: {
    ...paddingStyles.pb10,
  }
});

export default RoomAmenities;
