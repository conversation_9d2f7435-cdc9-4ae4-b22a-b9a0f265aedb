import React from 'react';
import { View, Text, Image, StyleSheet, TouchableOpacity } from 'react-native';
import { getTransferFacilitiesIcon } from '../../../PhoenixDetail/Utils/PhoenixDetailUtils';
import { holidayColors } from 'mobile-holidays-react-native/src/Styles/holidayColors';
import { fontStyles } from 'mobile-holidays-react-native/src/Styles/holidayFonts';
import { marginStyles } from 'mobile-holidays-react-native/src/Styles/Spacing';
import AtomicCss from '@mmt/legacy-commons/Styles/AtomicCss';
import { isEmpty } from 'lodash';
import { smallHeightSeperator } from 'mobile-holidays-react-native/src/Styles/holidaySpacing';
import GreenTickIcon from '@mmt/legacy-assets/src/greenTick_plain.webp';
import iconCross from '@mmt/legacy-assets/src/ic_cross__gray.webp';
import { actionStyle } from '../../../PhoenixDetail/Components/DayPlan/dayPlanStyles';

const getFacilityItem = ({ facility }) => {
  const defaultIcon = require('../../../PhoenixDetail/Components/images/ic_star.webp');
  return (
    <View style={styles.facilitiesItemContainer}>
      <Image
        source={getTransferFacilitiesIcon(facility)}
        style={styles.amenitiesIcon}
        defaultSource={defaultIcon}
      />
      <Text style={styles.facilitiesItem}>{facility.title} </Text>
    </View>
  );
};
/* Returns UI element for transfer Facilities */
const TransferFacilities = ({ vehicleInfo, activeSuffix = {} }) => {
  const { facilities } = vehicleInfo || {};
  if (!facilities || !facilities.length > 0) {
    // Return empty array when no facilities are available.
    return [];
  }
  const FacilitiesView = facilities.map((facility, index) =>
    getFacilityItem({ facility, key: index.toString() }),
  );

  return (
    <View>
      <View style={[smallHeightSeperator, marginStyles.mv16, activeSuffix]} />
      <Text style={styles.facilitiesHeading}>Facilities:</Text>
      <View style={styles.facilitiesList}>{FacilitiesView}</View>
      <View style={[smallHeightSeperator, marginStyles.mv16, activeSuffix]} />
    </View>
  );
};

const DriverSpokenLanguages = ({ vehicleInfo }) => {
  const { driverSpokenLanguages} = vehicleInfo || {};

  if (isEmpty(driverSpokenLanguages)) {
    // Return empty array when no driver Spoken Languages are available.
    return [];
  }
  const FacilitiesView = (
    <View style={[AtomicCss.flexRow, { alignItems: 'center', marginBottom: 5 }]}>
      <Text style={styles.facilitiesItem}>{driverSpokenLanguages} </Text>
    </View>
  );
  return (
    <View>
      <Text style={styles.facilitiesHeading}>Driver Spoken Languages:</Text>
      <View style={styles.facilitiesList}>{FacilitiesView}</View>
      <View style={[smallHeightSeperator, { marginTop: 7 }]} />
    </View>
  );
};

const Inclusion = ({ additionalInfo = {}, onReadMoreClick = null }) => {
  const MAX_INCLUSION_COUNT = 3;
  const { inclusions } = additionalInfo || {};
  if (!inclusions || !inclusions.length > 0) {
    // Return empty array when no facilities are available.
    return [];
  }
  const showReadMore = onReadMoreClick && inclusions.length > MAX_INCLUSION_COUNT;
  // let InclusionsView = [];

  // inclusions.every((inclusion) => {
  //   if (InclusionsView.length < MAX_INCLUSION_COUNT) {
  //     InclusionsView.push(
  //       <View style={styles.inclusionItemContainer}>
  //         <Image source={GreenTickIcon} style={styles.tickStyle} />
  //         <Text style={styles.inclusionItemText}>{inclusion}</Text>
  //       </View>,
  //     );
  //     return true;
  //   } else {
  //     return false;
  //   }
  // });
  const InclusionsView = inclusions.slice(0, MAX_INCLUSION_COUNT).map((inclusion, index) => (
    <View style={styles.inclusionItemContainer} key={index.toString()}>
      <Image source={GreenTickIcon} style={styles.tickStyle} />
      <Text style={styles.inclusionItemText}>{inclusion}</Text>
    </View>
  ));

  return (
    <View>
      <View style={marginStyles.mb10}>
        <Text style={styles.inclusionHeader}>Inclusion</Text>
      </View>
      {InclusionsView}
      {showReadMore && (
        <TouchableOpacity onPress={onReadMoreClick}>
          <Text style={[actionStyle]}>Read More</Text>
        </TouchableOpacity>
      )}
    </View>
  );
};

const Exclusion = ({ additionalInfo = {}, onReadMoreClick = null }) => {
  const { exclusions } = additionalInfo || {};
  const MAX_EXCLUSION_COUNT = 3;

  if (!exclusions || !exclusions.length > 0) {
    // Return empty array when no facilities are available.
    return [];
  }
  // let ExclusionView = [];

  // exclusions.every((exclusion) => {
  //   if (ExclusionView.length < MAX_EXCLUSION_COUNT) {
  //     ExclusionView.push(
  //       <View style={styles.inclusionItemContainer}>
  //         <Image source={iconCross} style={styles.crossStyle} />
  //         <Text style={styles.inclusionItemText}>{exclusion}</Text>
  //       </View>,
  //     );
  //     return true;
  //   } else {
  //     return false;
  //   }
  // });
  const ExclusionView = exclusions.slice(0, MAX_EXCLUSION_COUNT).map((exclusion, index) => (
    <View style={styles.inclusionItemContainer} key={index.toString()}>
      <Image source={iconCross} style={styles.crossStyle} />
      <Text style={styles.inclusionItemText}>{exclusion}</Text>
    </View>
  ));

  return (
    <View>
      <View style={marginStyles.mb10}>
        <Text style={styles.inclusionHeader}>Exclusion</Text>
      </View>
      {ExclusionView}
      {onReadMoreClick && exclusions.length > MAX_EXCLUSION_COUNT && (
        <TouchableOpacity onPress={onReadMoreClick}>
          <Text style={actionStyle}>Read More</Text>
        </TouchableOpacity>
      )}
    </View>
  );
};

const ImportantRead = ({ additionalInfo }) => {
  const { importantInfos } = additionalInfo || {};

  if (!importantInfos || !importantInfos.length > 0) {
    // Return empty array when no facilities are available.
    return [];
  }
  // let InfoView = [];

  // importantInfos.forEach((info) => {
  //   InfoView.push(
  //     <View style={styles.inclusionItemContainer}>
  //       <Image source={GreenTickIcon} style={styles.tickStyle} />
  //       <Text style={styles.inclusionItemText}>{info}</Text>
  //     </View>,
  //   );
  // });
  const InfoView = importantInfos.map((info, index) => (
    <View style={styles.inclusionItemContainer}>
      <Image source={GreenTickIcon} style={styles.tickStyle} />
      <Text style={styles.inclusionItemText}>{info}</Text>
    </View>
  ));

  return (
    <View>
      <View style={marginStyles.mb16}>
        <Text style={styles.inclusionHeader}>Important Read</Text>
      </View>
      {InfoView}
    </View>
  );
};

const styles = StyleSheet.create({
  transferModel: {
    ...fontStyles.labelMediumBlack,
    color: holidayColors.black,
    ...marginStyles.mb6,
  },
  facilitiesHeading: {
    ...fontStyles.labelBaseBold,
    color: holidayColors.black,
    ...marginStyles.mr10,
  },
  facilitiesList: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    ...marginStyles.mt12,
  },
  facilitiesItemContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    ...marginStyles.mb4,
  },
  facilitiesItem: {
    ...fontStyles.labelSmallRegular,
    color: holidayColors.black,
    ...marginStyles.mr10,
  },
  inclusionItemContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    ...marginStyles.mb10,
  },
  inclusionHeader: {
    ...fontStyles.labelBaseBlack,
    color: holidayColors.black,
  },
  inclusionItemText: {
    ...fontStyles.labelSmallRegular,
    color: holidayColors.gray,
  },
  tickStyle: {
    width: 15,
    height: 15,
    marginRight: 5,
    tintColor: holidayColors.green,
  },
  crossStyle: {
    width: 10,
    height: 10,
    marginRight: 5,
    tintColor: holidayColors.lightGray,
  },
  amenitiesIcon: {
    width: 16,
    height: 16,
    resizeMode: 'cover',
    marginRight: 4,
  },
});

export {
  DriverSpokenLanguages,
  TransferFacilities,
  getFacilityItem,
  Inclusion,
  Exclusion,
  ImportantRead,
};
