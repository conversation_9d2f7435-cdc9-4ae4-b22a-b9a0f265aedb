import React from 'react';
import { StyleSheet, View, Text } from 'react-native';
import AtomicCss from '@mmt/legacy-commons/Styles/AtomicCss';
import { holidayBorderRadius } from 'mobile-holidays-react-native/src/Styles/holidayBorderRadius';
import { fontStyles } from 'mobile-holidays-react-native/src/Styles/holidayFonts';
import { holidayColors } from 'mobile-holidays-react-native/src/Styles/holidayColors';
import { marginStyles } from 'mobile-holidays-react-native/src/Styles/Spacing';
import PlaceholderImageView from '@mmt/legacy-commons/Common/Components/PlaceholderImageView';
import { isEmpty } from 'lodash';

const TransferCarDetails = ({ imageUrl, vehicleInfo, privateText }) => {
  const { model, vehicleName } = vehicleInfo || {};
  const transferText = `${privateText} ${isEmpty(vehicleName) ? '' : vehicleName}`;
  return (
    <View style={[AtomicCss.flexRow, AtomicCss.marginBottom20, { marginRight: 120 }]}>
      <PlaceholderImageView url={imageUrl} style={styles.cabStyle} />
      <View>
        {!isEmpty(model) && <Text style={styles.transferModel}>{model} or similar</Text>}
        <Text style={styles.transferDetail}>
          {transferText}
        </Text>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  cabStyle: {
    width: 120,
    height: 74,
    marginRight: 15,
    resizeMode: 'contain',
    ...holidayBorderRadius.borderRadius16,
    borderWidth: 1,
    borderColor: holidayColors.grayBorder,
  },
  transferModel: {
    ...fontStyles.labelMediumBlack,
    color: holidayColors.black,
    ...marginStyles.mb6,
  },
  transferDetail: {
    ...fontStyles.labelBaseRegular,
    color: holidayColors.lightGray,
  },
});

export default TransferCarDetails;
