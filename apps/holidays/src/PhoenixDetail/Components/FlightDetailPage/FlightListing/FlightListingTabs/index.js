import React, {useState} from 'react';
import {StyleSheet, Text, TouchableOpacity, View} from 'react-native';
import { fontStyles } from '../../../../../Styles/holidayFonts';
import { holidayColors } from '../../../../../Styles/holidayColors';
import { paddingStyles } from '../../../../../Styles/Spacing';

const FlightListingTabs = (props) => {
  const {data} = props;
  const [tabActive, setTabActive] = useState(props.currentTab);

  const handleTab = (index) => {
    setTabActive(index);
    props.switchTab(index);
  };
  return (
    <View style={styles.flListingTabs}>
      {data.map((list, i) => (
        <TouchableOpacity
          style={[styles.tab, i === tabActive ? styles.tabActive : {}]}
          key={i}
          onPress={() => handleTab(i)}
        >
          <View>
            <Text style={i === tabActive ? styles.activeTabText : styles.tabText}>
              {list.source},{'\n'}
              {list.loc}, {list.date}
            </Text>
          </View>
        </TouchableOpacity>
      ))}
    </View>
  );
};

const styles = StyleSheet.create({

  flListingTabs: {
    flexDirection: 'row',
    backgroundColor: holidayColors.white,
    alignItems: 'center',
    justifyContent: 'center',
    borderBottomWidth: 1,
    paddingLeft: 0,
    paddingRight: 0,
    borderColor: '#bababa',
  },
  tab: {
    flex: 1,
    alignItems: 'center',
    borderBottomWidth: 4,
    borderColor: holidayColors.white,
    ...paddingStyles.pa10,
    marginBottom: -2,

  },
  tabActive: {
    borderColor: holidayColors.primaryBlue,
  },
  activeTabText: {
    ...fontStyles.labelBaseBlack,
    color: holidayColors.primaryBlue,
  },
  tabText: {
    ...fontStyles.labelBaseBold,
    color: holidayColors.lightGray,
  },
});

export default FlightListingTabs;
