import React from 'react';
import {Image, StyleSheet, Text, View} from 'react-native';
import cStyles from '@mmt/legacy-commons/Styles/AtomicCss';
import iconTickWhite from '../../../../images/ic_tickWhite.png';
import { fontStyles } from 'mobile-holidays-react-native/src/Styles/holidayFonts';
import { holidayColors } from 'mobile-holidays-react-native/src/Styles/holidayColors';

const AvailabilityTooltip = ({data = []}) => {
  return (
    <View style={styles.unAvailTooltip}>
      <View style={cStyles.marginBottom10}>
        <Text style={styles.item}>{data?.heading}</Text>
      </View>
      <View style={{flexDirection: 'row'}}>
        {data?.items.map((item, index) => {
          return (
            <View style={[cStyles.flexRow, cStyles.alignCenter, cStyles.marginBottom5, cStyles.marginRight5]}>
              <View style={[cStyles.marginRight5]}><Image source={iconTickWhite} style={styles.iconTickWhite}/></View>
              <View><Text style={styles.item}>{item}</Text></View>
            </View>
          );
        })}
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  unAvailTooltip: {
    backgroundColor: 'black',
    borderRadius: 4,
    padding: 10,
    position: 'absolute',
    right: 0,
    top: 25,
    zIndex: 2,
    width: '100%',

  },
  item: {
    ...fontStyles.labelSmallBold,
    color: holidayColors.white,
  },
  iconTickWhite: {
    width: 9,
    height: 7,
    resizeMode: 'cover',
  },
});

export default AvailabilityTooltip;
