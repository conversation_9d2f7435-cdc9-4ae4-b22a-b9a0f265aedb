import React from 'react';
import { Image, StyleSheet, Text, TouchableOpacity, TouchableWithoutFeedback, View } from 'react-native';
import cStyles from '@mmt/legacy-commons/Styles/AtomicCss';
import iconInfo from '../../../images/ic_infoRed.png';
import { getFlightDate, getFlightTime, parseFlightDate } from '../../../../Utils/FlightUtils';
import { flightDuration } from '../../../../../ChangeFlight/HolidayFlightUtils';
import { getAirlineIconUrl, getPriceText, persuasionTypes } from '../FlightsUtils';
import { ischeckinBaggageInfoAvailable } from '../../../../../utils/HolidayUtils';
import { fontStyles } from 'mobile-holidays-react-native/src/Styles/holidayFonts';
import { holidayColors } from 'mobile-holidays-react-native/src/Styles/holidayColors';
import { borderRadiusValues, holidayBorderRadius } from '../../../../../Styles/holidayBorderRadius';
import { marginStyles, paddingStyles } from '../../../../../Styles/Spacing';

/* Components */
import BasePage from '@mmt/legacy-commons/Common/navigation/BasePage';
import ItineraryUnitExtraInfoMessages from '../../../ItineraryUnitExtraInfoMessages';
import BaggageIconDetail from '../../../../../Common/Components/BaggageIconDetail';
import SelectedTag from '../../../../../Common/Components/Tags/SelectedTag';
import DetailListingSelectButton from '../../../DetailListingSelectButton';
import AvailabilityTooltip from './AvailabilityTooltip';
import { getImageUrl, IMAGE_ICON_KEYS } from '../../../../../Common/Components/HolidayImageUrls';
class FlightListingCard extends BasePage {
  constructor(props) {
    super(props);
    this.state = {
      tooltipActive: false,
    };
  }

  setSelection = () => {
    this.props.setSelection(this.props.flight,this.props.pageIndex);
  }

  openFlightDetail = () => {
    this.props.openFlightDetail(this.props.flight,this.props.pageIndex);
  }

  getPersuasion = (position) => {
    const { persuasionData } = this.props;
    if (!persuasionData) {
      return [];
    }
    let content = [];

    persuasionData.forEach((item, index) => {
      const {persuasionType} = item;
      if (persuasionType === persuasionTypes.UNAVAILABLE && position === 1) {
        content.push(
          <View style={styles.unavailableTooltipWrap}>
            <TouchableOpacity
              onPress={() => this.setState({tooltipActive: !this.state.tooltipActive})}
              activeOpacity={0.7}
            >
              <View style={[cStyles.flexRow, cStyles.alignCenter]}>
                <View><Text style={styles.unavailableCard}>Unavailable</Text></View>
                <View style={cStyles.marginLeft5}><Image source={iconInfo} style={styles.iconInfo} /></View>
              </View>
            </TouchableOpacity>
          </View>
        );
      } else if (persuasionType === persuasionTypes.SPECIALFARE && position === 2) {
        content.push(
          <View style={styles.specialFareView}>
            <Text style={styles.specialFareText}>
              {item.heading1}
              <Text style={styles.specialFareTextBold}>{item.flight}</Text>
            </Text>
          </View>
        );
      }
    });
    return content;
  }

  getPersuasionData = (position) => {
    const { persuasionData } = this.props;
    if (!persuasionData) {
      return null;
    }
    for (let i = 0; i < persuasionData.length; i++) {
      const {persuasionType} = persuasionData[i];
      if (persuasionType === persuasionTypes.UNAVAILABLE && position === 1) {
        return persuasionData[i];
      }
    }
    return null;
  }

  getAirlineImageComponent = (airlineCode) => {
    if (airlineCode === 'Multiple Flight Codes') {
      return (
        <View style={cStyles.marginRight10}>
          <Image source={getImageUrl(IMAGE_ICON_KEYS.AIRPLANE_ICON)} style={styles.airplaneIcon}/>
          <Image source={getImageUrl(IMAGE_ICON_KEYS.AIRPLANE_ICON)} style={styles.airplaneIcon}/>
        </View>
      );
    } else {
      return (
        <Image source={{uri: getAirlineIconUrl(airlineCode)}} style={[styles.airlineLogo, cStyles.marginRight10]} />
      );
    }
  }

  render() {
    const {
      selectedFlightSellableId,
      flight,
    } = this.props;
    const { flightMetadataDetail = {} } = flight || {};
    const { checkInBaggage = true, flightExtraInfo = [] } = flightMetadataDetail || {};
    const unavailablePersuasionData = this.getPersuasionData(1);
    const selected = selectedFlightSellableId === flight.sellableId;
    const flightInfo = flight.flightTag;
    const showBaggageInfo = ischeckinBaggageInfoAvailable(flightMetadataDetail);
    return <>
      <TouchableWithoutFeedback onPress={this.openFlightDetail}>
        <View style={styles.flightListingContainer}>
        <View
            style={[styles.flightListingCard, selected ? styles.flightListingCardSelected : {}]}
            key={flight.sellableId}
        >
          <View style={styles.flDetails}>
            <View style={[cStyles.flexRow, cStyles.alignCenter, cStyles.flex1]}>
              <Image
                source={{ uri: getAirlineIconUrl(flight.airlineCode) }}
                style={[styles.airlineLogo, cStyles.marginRight10]}
              />
              <View>
                <Text style={styles.airlineName}>{flight.airlineName}</Text>
                {flightInfo ? <Text style={styles.airlineCode}>{flightInfo}</Text> : null}
              </View>
              <View style={cStyles.pushRight}>
                {showBaggageInfo ? <BaggageIconDetail checkInBaggage={checkInBaggage} /> : null}
              </View>
            </View>
            {this.getPersuasion(1)}
            {this.state.tooltipActive ? (
              <AvailabilityTooltip data={unavailablePersuasionData} />
            ) : null}
          </View>
          <View style={styles.cardContent}>
            <View style={styles.cardContentRow}>
              <View style={styles.flex1}>
                <Text style={styles.flightTime}>{getFlightTime(parseFlightDate(flight.departure))}</Text>
                <Text style={styles.flightDate}>{getFlightDate(flight.departure)}</Text>
                <Text style={styles.flightDetails}>{flight.fromAirport.airportCity}</Text>
              </View>
              <View style={[cStyles.alignCenter, marginStyles.mh10]}>
                <View><Text style={styles.duration}>{flightDuration(flight.duration)}</Text></View>
                <View style={styles.durationDivider}>
                  {flight.stops > 0 && <View style={styles.stopPointer} />}
                  {flight.stops > 1 && <View style={styles.stopPointer} />}
                </View>
                <View>
                  <Text style={styles.noOfStops}>
                    {flight.stops === 0 ? 'Non Stop' : (flight.stops > 1 ? `${flight.stops} Stops` : `${flight.stops} Stop`)}
                  </Text>
                </View>
              </View>
              <View style={styles.flex1}>
                <Text style={styles.flightTime}>{getFlightTime(parseFlightDate(flight.arrival))}</Text>
                <Text style={styles.flightDate}>{getFlightDate(flight.arrival)}</Text>
                <Text style={styles.flightDetails}>{flight.toAirport.airportCity}</Text>
              </View>
            </View>
            {selected ? (
              <View>
                <SelectedTag />
              </View>) : (
              <View style={styles.priceContainer}>
                <Text style={styles.price}>{getPriceText(flight.price)}</Text>
              </View>
            )}
          </View>
          <View style={styles.viewDetailsWrap}>
            {(flight.airlineName !== flight.oprAirlineName) &&
              <View style={styles.operatedByContainer}>
                <Text style={styles.operatedBy}>Operated by {flight.oprAirlineName}</Text>
              </View>
            }
            {!selected && (
                <DetailListingSelectButton
                  onPress={this.setSelection}
                  textStyles={styles.cardLink}
                />
            )}
          </View>
          <ItineraryUnitExtraInfoMessages extraInfo={flightExtraInfo}/>
        </View>
        </View>
      </TouchableWithoutFeedback>
      {this.getPersuasion(2)}
    </>;
  }
}

const styles = StyleSheet.create({
  flightListingContainer: {
    ...marginStyles.mh16,
    ...marginStyles.mt16,
    zIndex: 1,
  },
  flightListingCard: {
    ...holidayBorderRadius.borderRadius16,
    ...paddingStyles.pa12,
    borderBottomWidth: 1,
    borderColor: holidayColors.white,
    backgroundColor: holidayColors.white,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.20,
    shadowRadius: 1.41,
    elevation: 2,
  },
  flightListingCardSelected: {
    borderColor: holidayColors.lightBlueBg,
    backgroundColor: holidayColors.lightBlueBg,
    elevation: 2,
  },
  unavailableCard: {
    ...fontStyles.labelSmallRegular,
    color: holidayColors.red,
  },
  cardContent: {
    flexDirection: 'row',
  },
  cardContentRow: {
    flexDirection: 'row',
    flex: 1,
  },
  flDetails: {
    marginBottom: 10,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    zIndex:5,
  },
  operatedByContainer: {
    flex: 2,
    paddingVertical: 5,
  },
  operatedBy: {
    ...fontStyles.labelSmallRegular,
    color: holidayColors.gray,
  },
  cardLink: {
    textAlign: 'right',
  },
  airlineLogo: {
    width: 25,
    height: 25,
    resizeMode: 'cover',
  },
  airlineName: {
    ...fontStyles.labelSmallBold,
    color: holidayColors.black,
  },
  airlineCode: {
    ...fontStyles.labelSmallRegular,
    color: holidayColors.gray,
  },
  viewDetailsWrap: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
    alignItems: 'center',
  },
  duration: {
    ...fontStyles.labelSmallBold,
    color: holidayColors.black,
  },
  durationDivider: {
    borderBottomWidth: 0.5,
    borderColor: holidayColors.grayBorder,
    marginTop: 3,
    width: 45,
    alignItems: 'center',
    flexDirection: 'row',
    justifyContent: 'center',
  },
  stopPointer: {
    marginBottom: -2,
    width: 4,
    height: 4,
    backgroundColor: holidayColors.white,
    borderWidth: 1,
    marginHorizontal: 5,
  },

  unavailableTooltipWrap: {
    paddingRight: 2,
    zIndex: 100,
    elevation: 100,
  },

  iconInfo: {
    width: 16,
    height: 16,
    resizeMode: 'cover',
  },
  specialFareTextBold: {
    ...fontStyles.labelSmallBold,
    lineHeight: 18,
    color: holidayColors.black,
  },
  specialFareView: {
    ...marginStyles.mh16,
    paddingVertical: 15,
    justifyContent: 'center',
    paddingHorizontal: 10,
    backgroundColor: holidayColors.fadedGreen,
    borderBottomLeftRadius: borderRadiusValues.br16,
    borderBottomRightRadius: borderRadiusValues.br16,
    top: -5,
    zIndex: 0,
  },
  specialFareText: {
    ...fontStyles.labelSmallRegular,
    color: holidayColors.black,
  },
  airplaneIcon: {
    height: 15,
    width: 15,
    transform: [{ rotate: '-45deg'}],
  },
  priceContainer: {
    height: 24,
    minWidth: 48,
    justifyContent: 'center',
    alignItems: 'flex-end',
  },
  price: {
    ...fontStyles.labelMediumBlack,
    color: holidayColors.black,
  },
  flightTime: {
    ...fontStyles.labelMediumBlack,
    color: holidayColors.black,
  },
  flightDate: {
    ...fontStyles.labelSmallBold,
    color: holidayColors.gray,
    ...paddingStyles.pv2,
  },
  flightDetails: {
    ...fontStyles.labelSmallRegular,
    color: holidayColors.gray,
    flexWrap: 'wrap',
  },
  noOfStops: {
    ...fontStyles.labelSmallBold,
    color: holidayColors.primaryBlue,
    ...paddingStyles.pt2,
  },
  flex1: {
    flex: 1,
  },
});

export default FlightListingCard;
