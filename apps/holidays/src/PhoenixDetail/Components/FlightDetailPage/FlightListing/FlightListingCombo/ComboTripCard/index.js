import React, {useMemo, useState} from 'react';
import {ScrollView, StyleSheet, Text, TouchableOpacity, View} from 'react-native';
import cStyles from '@mmt/legacy-commons/Styles/AtomicCss';
import {fonts} from '@mmt/legacy-commons/Styles/globalStyles';
import { getAirlineIconUrl, getFlightLegDetails } from '../../FlightsUtils';
import { ischeckinBaggageInfoAvailable } from '../../../../../../utils/HolidayUtils';
import { fontStyles } from 'mobile-holidays-react-native/src/Styles/holidayFonts';
import { holidayColors } from 'mobile-holidays-react-native/src/Styles/holidayColors';
import { marginStyles, paddingStyles } from '../../../../../../Styles/Spacing';

/* Components */
import ItineraryUnitExtraInfoMessages from '../../../../ItineraryUnitExtraInfoMessages';
import BaggageIconDetail from '../../../../../../Common/Components/BaggageIconDetail';
import HolidayImageHolder from '../../../../../../Common/Components/HolidayImageHolder';

const ComboTripCard = (props) => {
  const {
    tripSeq,
    departureLoc,
    departureDate,
    arrivalLoc,
    arrivalDate,
    flName,
    departureTime,
    duration,
    stopCount,
    stops,
    arrivalTime,
    availableOptions,
    activeOption,
    updateComboOption,
    flightLegs,
    airlineCode,
    oprAirlineName,
    overnightInfo,
    flightLeg,
  } = props;

  const { flightMetadataDetail = {} } =  flightLeg || {};
  const { checkInBaggage = true, flightExtraInfo = []} = flightMetadataDetail || {};
  const showBaggageInfo = ischeckinBaggageInfoAvailable(flightMetadataDetail);
  const renderAvailableOptions = useMemo(() => {
    if (availableOptions && availableOptions.length > 1) {
      return (
        <ScrollView
          horizontal
          showsHorizontalScrollIndicator={false}
          style={{backgroundColor: '#edf7ff', marginHorizontal: -15, marginBottom: 10}}
          contentContainerStyle={{paddingVertical: 8, paddingHorizontal: 15}}
        >
          <View style={cStyles.flexRow}>
            <View style={[styles.tripTitleWrap]}>
              <Text style={styles.availableText}>Available Options</Text>
            </View>
            <View style={[cStyles.flexRow, styles.tripContentWrap]}>
              {availableOptions.map((item, index) => (
                <Option
                  item={item}
                  key={index}
                  activeOption={activeOption}
                  updateComboOption={updateComboOption}
                />
              ))}
            </View>
          </View>
        </ScrollView>
      );
    }
    return [];
  });

  const flightLegDetails = useMemo(() => {
    return getFlightLegDetails(flightLegs);
  }, [flightLegs]);

  const getTripContentData = () => {
    let tripContent = `${departureLoc}- ${arrivalLoc} | ${flName} | `;
    const {flightIds} = flightLegDetails;
    flightIds.forEach((id, i) => {
      if (i < flightIds.length - 1) {
        tripContent += `${id}, `;
      } else {
        tripContent += id;
      }
    });
    return tripContent;
  };

  return (
    <View style={styles.tripCard}>
      <View style={[cStyles.flexColumn, cStyles.marginBottom10]}>
        <View>
          <Text style={styles.tripSeqText}>{tripSeq}</Text>
        </View>
        <View style={styles.tripContentContainer}>
            <Text style={styles.tripContent}>
              {getTripContentData()}
            </Text>
          {showBaggageInfo ? <BaggageIconDetail checkInBaggage={checkInBaggage} /> : null}
        </View>
      </View>
      {renderAvailableOptions}

      <View style={[cStyles.flexRow]}>
        <View style={styles.tripTitleWrap} />
        <View style={[styles.tripContentWrap]}>
          <View style={[cStyles.flexRow, cStyles.alignCenter]}>
            <View style={[cStyles.marginRight15, cStyles.flexRow]}>
              <HolidayImageHolder imageUrl={getAirlineIconUrl(airlineCode)} style={styles.airlineLogo}/>
            </View>
            <View style={styles.cardContentRow}>
              <View>
                <Text style={styles.flightTime}>{departureTime}</Text>
                <Text style={styles.flightDate}>{departureDate}</Text>
                <Text style={styles.flightLocation}>{departureLoc}</Text>
              </View>
              <View style={[cStyles.alignCenter]}>
                <View><Text style={styles.duration}>{duration}</Text></View>
                <View style={styles.durationDivider}>
                  {stopCount > 0 && <View style={styles.stopPointer} />}
                  {stopCount > 1 && <View style={styles.stopPointer} />}
                </View>
                <View><Text style={styles.noOfStops}>{stops}</Text></View>
              </View>
              <View>
                <Text style={styles.flightTime}>{arrivalTime}</Text>
                <Text style={styles.flightDate}>{arrivalDate}</Text>
                <Text style={styles.flightLocation}>{arrivalLoc}</Text>
              </View>
            </View>
          </View>
        </View>
      </View>
      {(flName !== oprAirlineName) &&
      <View style={[cStyles.flex1, cStyles.paddingTop5]}>
        <Text style={[cStyles.font12, cStyles.greyText]}>Operated by {oprAirlineName}</Text>
      </View>
      }
      <ItineraryUnitExtraInfoMessages extraInfo={flightMetadataDetail?.flightExtraInfo}/>
    </View>

  );
};

const Option = ({item, activeOption, updateComboOption}) => {
  const {departure, arrival, iconUrl, index} = item;
  const selectedTab = index === activeOption ? styles.activeOption : styles.optionLabel;
  return (
    <View style={[
      styles.availableSlotTags,
      cStyles.marginRight10,
      index === activeOption ? {backgroundColor: '#008cff'} : null,
    ]}>
      <TouchableOpacity
        style={cStyles.flexRow}
        onPress={() => updateComboOption(item)}
        // disabled={index === activeOption}
      >
        <HolidayImageHolder imageUrl={iconUrl} style={styles.iconAirlineLogo} />
        <View style={styles.optionText}>
          <Text style={selectedTab}>{departure}</Text>
          <Text style={selectedTab}> - </Text>
          <Text style={selectedTab}>{arrival}</Text>
        </View>
      </TouchableOpacity>
    </View>
  );
};

const styles = StyleSheet.create({
  tripCard: {
    width: '100%',
    ...paddingStyles.pa16,
  },
  tripSeqText: {
    ...fontStyles.labelSmallBlack,
    color: holidayColors.gray,
  },
  cardContentRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    flex: 1,
    alignItems: 'center',
  },
  tripTitleWrap: {
    ...marginStyles.mr16,
    justifyContent: 'center',
    alignItems: 'center',
  },
  tripContentWrap: {
    width: '85%',
    flex: 5,
  },
  tripContentContainer: {
    flexDirection: 'row',
    ...marginStyles.mr16,
    alignItems: 'flex-start',
  },
  tripContent: {
    ...fontStyles.labelSmallBold,
    color: holidayColors.gray,
    flex: 1,
    flexWrap: 'wrap',
  },
  durationDivider: {
    borderBottomWidth: 1,
    borderColor: holidayColors.fadedGreen,
    marginTop: 3,
    width: 45,
    alignItems: 'center',
    flexDirection: 'row',
    justifyContent: 'center',
  },
  stopPointer: {
    marginBottom: -2.5,
    width: 4,
    height: 4,
    backgroundColor: 'white',
    borderWidth: 1,
    borderColor: holidayColors.fadedGreen,
    marginHorizontal: 5,
  },
  airlineLogo: {
    width: 32,
    height: 32,
    borderRadius: 16,
    resizeMode: 'center',
  },
  availableSlotTags: {
    shadowColor: holidayColors.white,
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.22,
    shadowRadius: 2.22,
    elevation: 3,
    backgroundColor: 'white',
    padding: 3,
    borderRadius: 34,
    flexDirection: 'row',
    alignItems: 'center',
    minWidth: 75,
    overflow: 'hidden',
  },
  iconAirlineLogo: {
    width: 18,
    height: 18,
    borderRadius: 9,
    resizeMode: 'center',
    backgroundColor: holidayColors.white,
  },
  optionText: {
    marginLeft: 5,
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    paddingRight: 5,
    paddingLeft: 1,
  },
  optionLabel: {
    color: holidayColors.gray,
    fontSize: 10,
    fontFamily: fonts.bold,
  },
  activeOption: {
    color: holidayColors.white,
    fontSize: 10,
    fontFamily: fonts.bold,
  },
  optionsContainer: {
    paddingVertical: 8,
  },
  availableText: {
    ...fontStyles.labelSmallBlack,
    color: holidayColors.primaryBlue,
  },
  duration: {
    ...fontStyles.labelSmallBold,
    color: holidayColors.gray,
  },
  flightTime: {
    ...fontStyles.labelMediumBlack,
    color: holidayColors.black,
  },
  flightDate: {
    ...fontStyles.labelSmallBold,
    color: holidayColors.gray,
  },
  flightLocation: {
    ...fontStyles.labelSmallRegular,
    color: holidayColors.gray,
  },
  noOfStops: {
    ...fontStyles.labelSmallBold,
    color: holidayColors.gray,
  },
});

export default ComboTripCard;
