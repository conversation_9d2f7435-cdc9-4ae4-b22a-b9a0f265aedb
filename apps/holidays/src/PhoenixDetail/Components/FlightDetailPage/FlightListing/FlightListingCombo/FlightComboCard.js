import React, {useEffect, useMemo, useState} from 'react';
import {Image, Text, TouchableOpacity, TouchableWithoutFeedback, View, StyleSheet, FlatList} from 'react-native';
import cStyles from '@mmt/legacy-commons/Styles/AtomicCss';
import {getFlightTime, parseFlightDate, getFlightDate} from '../../../../Utils/FlightUtils';
import {flightDuration} from '../../../../../ChangeFlight/HolidayFlightUtils';
import {cloneDeep, isEmpty} from 'lodash';
import {fonts} from '@mmt/legacy-commons/Styles/globalStyles';
import { getSegmentFlights, getSegments } from '../../FlightFilterPage/FilterUtils';
import { getAirlineIconUrl, getComboTripName, getPriceText } from '../FlightsUtils';
import { HOLIDAYS_FLIGHT_DETAILS, HOLIDAYS_FLIGHT_OVERLAY, HOLIDAYS_FLIGHT_OVERLAY_LISTING } from '../../../../Utils/PheonixDetailPageConstants';
import { trackPhoenixDetailLocalClickEvent } from '../../../../Utils/PhoenixDetailTracking';
import { holidayBorderRadius } from '../../../../../Styles/holidayBorderRadius';
import { holidayColors } from '../../../../../Styles/holidayColors';
import { fontStyles } from '../../../../../Styles/holidayFonts';
import { marginStyles } from '../../../../../Styles/Spacing';

/* Components */
import ComboTripCard from './ComboTripCard';
import { logPhoenixDetailPDTEvents } from 'mobile-holidays-react-native/src/utils/PhoenixDetailPDTTrackingUtils';
import { PDT_EVENT_TYPES } from 'mobile-holidays-react-native/src/utils/HolidayPDTConstants';

const FlightComboCard = (props) => {
  const {
    item,
    validCombo,
    selectedGroupId,
    active,
    onSelectPress,
    listingDataType,
    onViewDetailPress,
    oldPrice,
    discountedFactor,
  } = props;
  const [comboFlights, setComboFlights] = useState([]);
  useEffect(() => {
    const flightIndexes = [];

    const segments = getSegments(item, listingDataType);
    if (active) {
      const validFlightCombo = String(validCombo).split('#');
      if (segments && segments.length > 0) {
        segments.forEach((obts, i) => {
          const segFlights = getSegmentFlights(obts, listingDataType);
          if (validFlightCombo.length > i) {
            const flightIndex = segFlights.findIndex(
              (row) => row.sellableId === validFlightCombo[i],
            );
            if (flightIndex !== -1) {
              flightIndexes.push({ index: flightIndex, segmentIndex: i });
            } else {
              flightIndexes.push({ index: 0, segmentIndex: i });
            }
          } else {
            flightIndexes.push({ index: 0, segmentIndex: i });
          }
        });
      }
    } else {
      if (segments && segments.length > 0) {
        segments.forEach((obts, i) => {
          const segFlights = getSegmentFlights(obts, listingDataType);
          if (segFlights && segFlights.length > 0) {
            flightIndexes.push({ index: 0, segmentIndex: i });
          }
        });
      }
    }
    setComboFlights(flightIndexes);
  }, [JSON.stringify(item)]);

  const availableOptionsMap = useMemo(() => {
    const segments = getSegments(props.item, listingDataType);
    const AllOptions = [];
    if (segments && segments.length > 0) {
      segments.forEach((obts, i) => {
        const OptionsForASegment = [];
        const segFlights = getSegmentFlights(obts, listingDataType);
        segFlights.forEach((segFlight, j) => {
          const {departure, arrival, airlineCode, id} = segFlight;
          OptionsForASegment.push({
            segmentIndex: i,
            index: j,
            departure: getFlightTime(parseFlightDate(departure)),
            arrival: getFlightTime(parseFlightDate(arrival)),
            iconUrl: getAirlineIconUrl(airlineCode),
            id: id,
          });
        });
        AllOptions.push(OptionsForASegment);
      });
    }
    return AllOptions;
  }, [item]);

  /**
   * Called on selection of another flight in a segment
   * This function updates the selected flight index in a particular segment
   * comboFlights array length is equal to the number of segments in a flight group
   **/
  const captureClickEvents = ({eventName = '', suffix = '', value = ''}) => {
    logPhoenixDetailPDTEvents({
      actionType: PDT_EVENT_TYPES.buttonClicked,
      value,
      subPageName: HOLIDAYS_FLIGHT_OVERLAY_LISTING,
    })
    trackPhoenixDetailLocalClickEvent({
      eventName,
      suffix,
      prop1: HOLIDAYS_FLIGHT_OVERLAY_LISTING,
    })
  }
  const updateComboOption = (optionObject) => {
    const {segmentIndex, index, id} = optionObject;
    let temp = cloneDeep(comboFlights);
    // temp = temp.filter(x => (x.segmentIndex !== segmentIndex));
    // temp.push({index: index, segmentIndex: segmentIndex});
    temp[segmentIndex] = {index: index, segmentIndex: segmentIndex};
    // temp = orderBy(temp, ['segmentIndex', 'index'], ['asc', 'asc']);
    setComboFlights(temp);
    captureClickEvents({
      eventName: 'flight_select_combo_',
      value: `flight|select|combo|${id}`,
      suffix: id,
    });
    if (active) {
      /**
       * Update sellableId if another flight option is
       * selected for the selected flight combo
       */
      onOptionUpdate(temp);
    }
  };

  const getFlightData = (flight) => {
    const {index, segmentIndex} = flight;
    const segments = getSegments(props.item, listingDataType);
    if (segments.length > segmentIndex) {
      const segFlights = getSegmentFlights(segments[segmentIndex], listingDataType);
      if (segFlights.length > index) {
        return segFlights[index];
      }
    }
    return null;
  };

  const renderFlightSegment = (flight, tripNum, totalSegments) => {
    const flightData = getFlightData(flight);
    if (flightData) {
      return renderTrip(flightData, tripNum, flight, totalSegments);
    }
    return [];
  };

  const renderTrip = (leg, tripNum, flight, totalSegments) => {
    const {
      fromAirport,
      toAirport,
      departure,
      arrival,
      airlineName,
      duration,
      stops,
      flightLegs,
      airlineCode,
      oprAirlineName,
      isOvernight,
      overnightLabel,
    } = leg;
    const {index, segmentIndex} = flight;
    return (
      <ComboTripCard
        key={tripNum}
        tripSeq={getComboTripName(listingDataType, tripNum, totalSegments)}
        departureLoc={fromAirport ? fromAirport.airportCity : ''}
        arrivalLoc={toAirport ? toAirport.airportCity : ''}
        flName={airlineName}
        flightLegs={flightLegs}
        departureTime={getFlightTime(parseFlightDate(departure))}
        departureDate={getFlightDate(departure)}
        arrivalTime={getFlightTime(parseFlightDate(arrival))}
        arrivalDate={getFlightDate(arrival)}
        duration={flightDuration(duration)}
        stopCount={stops}
        stops={stops !== 0 ? (stops > 1 ? `${stops} Stops` : `${stops} Stop`) : 'Non stop'}
        availableOptions={availableOptionsMap[segmentIndex]}
        activeOption={index}
        updateComboOption={updateComboOption}
        airlineCode={airlineCode}
        oprAirlineName={oprAirlineName}
        overnightInfo={{ isOvernight, overnightLabel }}
        flightLeg={leg}
      />
    );
  };

  const onPressHandling = () => {
    const flightSelections = getFlightSelections(comboFlights);
    onSelectPress(selectedGroupId, flightSelections, item.overnightDelays);
  };

  /**
   * This function updates flight option of already selected combo
   **/
  const onOptionUpdate = (selectedComboFlights) => {
    const flightSelections = getFlightSelections(selectedComboFlights);
    onSelectPress(selectedGroupId, flightSelections, item.overnightDelays);
  };

  const getFlightSelections = (selectedComboFlights) => {
    const segments = getSegments(item, listingDataType);
    const flightSelections = [];
    selectedComboFlights.forEach((item, index) => {
      const temp_segment = segments[item.segmentIndex];
      const temp_segment_flights = getSegmentFlights(temp_segment, listingDataType);
      const temp_flight = temp_segment_flights[item.index];
      flightSelections.push({
        sellableId: temp_flight.sellableId,
        flightSequence: item.segmentIndex + 1,   //temp_flight.flightSequence doesn't exist in flight obj
      });
      flightSelections.sort((x, y) => {
        if (x.flightSequence < y.flightSequence) {
          return -1;
        } else if (x.flightSequence > y.flightSequence) {
          return 1;
        }
        return 0;
      });
    });
    return flightSelections;
  };

  const onViewDetailPressHeader = () => {
    const flights = comboFlights.map((item, index) => getFlightData(item));
    onViewDetailPress(flights, item.overnightDelays, active);
  };

  return (
    <View>
      <View style={[styles.comboTripWrap, active ? styles.activeComboContainer : styles.comboContainer]}>
      <View>
          <Header
            price={item.currPackagePriceDiff}
            onViewDetailPressHeader={onViewDetailPressHeader}
            onSelectPress={onPressHandling}
            active={active}
          />
          <FlatList
          data={comboFlights}
          renderItem={({ item: flight, index: tripNum }) =>
            renderFlightSegment(flight, tripNum, comboFlights.length)
          }
          ItemSeparatorComponent={() => <View style={styles.seperator} />}
        />
      </View>
      </View>
    </View>
  );
};

const Header = ({price, onViewDetailPressHeader, onSelectPress, active}) => {
  return (
    <View style={styles.comboCard}>
      <View style={[cStyles.marginRight10, {alignItems: 'center', justifyContent: 'center'}]}>
        <Text style={styles.heading}>Flights Combo</Text>
      </View>
      <View style={styles.priceDetailsWrap}>
        {active ? (
          <View style={styles.selected}>
            <Image source={require('@mmt/legacy-assets/src/holidays/ic_pink_tick.webp')} style={styles.selectedIcon}/>
            <Text style={styles.selectedText}>SELECTED</Text>
          </View>) : (
          <View>
            <Text style={styles.heading}>{getPriceText(price)}</Text>
          </View>)}
        <TouchableOpacity onPress={() => onViewDetailPressHeader()}>
          <View style={[cStyles.marginTop5]}>
            <Text style={styles.subHeading}>View Details</Text>
          </View>
        </TouchableOpacity>
      </View>
      <View style={styles.cancellationWrap}>
        {!active &&
          <View style={styles.SelectButtonView}>
            <TouchableOpacity onPress={() => onSelectPress()} activeOpacity={0.7}>
              <Text style={styles.subHeading}>Select</Text>
            </TouchableOpacity>
          </View>}
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  comboCard: {
    flexDirection: 'row',
    padding: 15,
    borderBottomWidth: 1,
    borderColor: holidayColors.grayBorder,
  },
  heading: {
    ...fontStyles.headingBase,
    color: holidayColors.black,
  },
  subHeading: {
    ...fontStyles.labelSmallBold,
    color: holidayColors.primaryBlue,
  },
  priceDetailsWrap: {
    flex: 2,
    justifyContent: 'space-between',
    alignItems: 'flex-end',
    borderLeftWidth: 1,
    borderColor: holidayColors.grayBorder,
    paddingHorizontal: 10,
  },
  cancellationWrap: {
    flex: 1,
    paddingLeft: 10,
    justifyContent: 'center',
    alignItems: 'center',
    borderLeftWidth: 1,
    borderColor: holidayColors.grayBorder,
  },
  cancellationTextView: {
    alignItems: 'flex-end',
  },
  cancellationText: {
    color: '#2f8f85',
    fontFamily: fonts.black,
    fontSize: 10,
  },
  iconArrowTail: {
    width: 17,
    height: 17,
    resizeMode: 'cover',
  },
  iconArrowDown: {
    width: 19,
    height: 19,
    resizeMode: 'cover',
  },
  comboTripWrap: {
    shadowColor: 'rgba(74, 74, 74, 0.1)',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.22,
    shadowRadius: 2.22,
    elevation: 3,
    ...marginStyles.mb16,
  },
  activeComboContainer: {
    backgroundColor: holidayColors.lightBlueBg,
    borderWidth: 1,
    borderColor: holidayColors.primaryBlue,
    ...holidayBorderRadius.borderRadius16,
  },
  comboContainer: {
    backgroundColor: holidayColors.white,
    borderColor: holidayColors.grayBorder,
    ...holidayBorderRadius.borderRadius16,
  },
  SelectButtonView: {
    alignItems: 'center',
    paddingLeft: 7,
  },
  selected: {
    flexDirection: 'row',
    borderRadius: 11,
    paddingVertical: 2,
    paddingHorizontal: 4,
    backgroundColor: '#ff6666',
  },
  selectedIcon: {
    height: 10,
    width: 10,
    marginRight: 2,
  },
  selectedText: {
    color: '#ffffff',
    fontFamily: fonts.bold,
    fontSize: 9,
    letterSpacing: 0,
  },
  seperator: {
    borderBottomWidth: 1,
    borderColor: holidayColors.grayBorder,
  },
});

export default FlightComboCard;
