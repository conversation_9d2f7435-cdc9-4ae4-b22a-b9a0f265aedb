import React from 'react';
import {StyleSheet, Text, TouchableOpacity, View, Image} from 'react-native';
import cStyles from '@mmt/legacy-commons/Styles/AtomicCss';
import {flightDetailTypes} from '../../../../DetailConstants';
import {getFlightTime, parseFlightDate} from '../../../../Utils/FlightUtils';
import HolidayImageHolder from '../../../../../Common/Components/HolidayImageHolder';
import { getAirlineIconUrl, getPriceText } from '../FlightsUtils';
import { rupeeFormatterUtils } from '../../../../../utils/HolidayUtils';
import { fontStyles } from 'mobile-holidays-react-native/src/Styles/holidayFonts';
import { holidayColors } from 'mobile-holidays-react-native/src/Styles/holidayColors';
import SelectedTag from '../../../../../Common/Components/Tags/SelectedTag';
import { marginStyles, paddingStyles } from '../../../../../Styles/Spacing';
import PrimaryButton from '../../../../../Common/Components/Buttons/PrimaryButton';


const FlightShortListCard = (props) => {
  const {data, selected, onUpdatePress, pageName} = props;
  return data && data.length ? (
    <View style={styles.shortListCard}>
      <FlightShortData {...props} />
      <View>
        <PackagePrice {...props} />
        <View style={styles.buttonContainer}>
          {selected && (
            <View>
              <SelectedTag />
            </View>
          )}
          {!selected && (
            <PrimaryButton
              buttonText={'Update'}
              btnContainerStyles={styles.update}
              handleClick={() => onUpdatePress({ pageName })}
            />
          )}
        </View>
      </View>
    </View>
  ) : (
    []
  );
};

const PackagePrice = (props) => {
  const {packagePrice ,addonPrice = 0 } = props;
  const priceWithAddon =packagePrice + addonPrice
  return (
    <View style ={styles.sectionPadding}>
      <Text style={styles.comboPrice}>{rupeeFormatterUtils(priceWithAddon)}</Text>
      <Text style={styles.perPerson}>Per Person</Text>
    </View>
  );
};

const FlightShortData = (props) => {
  const {listingDataType, data, comboPrice} = props;

  const createDestinationText = () => {
    const dest = [];
    data.forEach((item, index) => {
      const {fromAirport, toAirport} = item;
      const from = fromAirport.airportCity;
      const to = toAirport.airportCity;
      dest.push(`${from} - ${to}`);
    });
    return dest.join(' | ');
  };

  if (listingDataType === flightDetailTypes.DOM_RETURN) {
    const HeadingLabel = [
      'Departure',
      'Return',
    ];

    const getFlightData = (item, index) => {
      const {price, departure, arrival, airlineCode} = item;
      return index < 2 ? (
        <View style={[cStyles.flex1, {marginRight: 12}]}>
          <View style={[cStyles.marginBottom5]}>
            <Text style={styles.comboHeading}>{HeadingLabel[index]}</Text>
          </View>
          <View style={styles.airlineContainer}>
            <View style={[cStyles.marginRight5]}>
              <HolidayImageHolder imageUrl={getAirlineIconUrl(airlineCode)} style={styles.imgAirlineLogo}/>
            </View>
              <View>
                <Text style={styles.destinationsText}>{getFlightTime(parseFlightDate(departure))} - {getFlightTime(parseFlightDate(arrival))}</Text>
              </View>
          </View>
        </View>
      ) : [];
    };

    return (
      <View style={{flex: 1, flexDirection: 'row', padding : 10}}>
        {data.map((item, index) => getFlightData(item, index))}
      </View>
    );
  } else {
    const destinationText = createDestinationText();
    return (
      <View style={styles.comboSection}>
        <View>
          <Text style={styles.comboHeading}>Multi City Combo</Text>
          <Text style={styles.destinationsText}>{destinationText}</Text>
          <Text style={[styles.comboPrice, cStyles.marginTop10]}>{getPriceText(comboPrice)}</Text>
        </View>
      </View>
    );
  }
};

const styles = StyleSheet.create({
  shortListCard: {
    flexDirection: 'row',
    backgroundColor: holidayColors.fadedYellow,
    ...paddingStyles.pa16,
  },
  airlineContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  imgAirlineLogo: {
    width: 30,
    height: 30,
    resizeMode: 'cover',
    borderRadius: 2,
  },
  flScheduleWrap: {
    paddingRight: 10,
    borderRightWidth: 1,
    borderColor: '#c5c5c5',
  },
  comboHeading: {
    color: holidayColors.lightGray,
    ...fontStyles.labelSmallBold,
    lineHeight: 16,
    letterSpacing: 0,
  },
  destinationsText: {
    ...fontStyles.labelSmallRegular,
    color: holidayColors.gray,
    lineHeight: 15,
    letterSpacing: 0,
  },
  comboPrice: {
    ...fontStyles.labelMediumBlack,
    color: holidayColors.black,
    lineHeight: 19,
    letterSpacing: 0,
  },
  separator: {
    borderWidth: 0.5,
    borderColor: '#535353',
    marginHorizontal: 6,
  },
  buttonContainer: {
    justifyContent: 'center',
    alignItems: 'flex-end',
  },
  update: {
    ...paddingStyles.pv6,
    ...paddingStyles.ph12,
  },
  updateText: {
    color: holidayColors.white,
    ...fontStyles.labelSmallBlack,
    letterSpacing: 0,
  },
  perPerson: {
    ...fontStyles.labelSmallRegular,
    color: holidayColors.gray,
    lineHeight: 16,
    marginLeft: 5,
  },

  sectionPadding: {
   marginBottom: 10,
    alignSelf:'flex-end',
  },

  comboSection: {
    flex: 1,
  },
});

export default FlightShortListCard;
