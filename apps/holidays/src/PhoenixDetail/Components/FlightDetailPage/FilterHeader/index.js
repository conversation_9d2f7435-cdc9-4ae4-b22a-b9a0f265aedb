import React from 'react';
import {View, StyleSheet} from 'react-native';
import iconClose from '@mmt/legacy-assets/src/holidays/iconCross.webp';
import { PageHeaderBackButton, PageHeaderTitle } from '../../../../Common/Components/PageHeader';
import { fontStyles } from '../../../../Styles/holidayFonts';
import { holidayColors } from '../../../../Styles/holidayColors';
import { paddingStyles } from '../../../../Styles/Spacing';
import TextButton from 'mobile-holidays-react-native/src/Common/Components/Buttons/TextButton';

const FilterHeader = (props) => {
    const {handleClose, headerTxt, clearFilterData} = props;
    return (
        <View style={styles.header}>
        <PageHeaderBackButton iconSource={iconClose} onBackPressed={handleClose} />
        <View style={styles.headerContent}>
          <PageHeaderTitle title={headerTxt} />
          <TextButton
              buttonText="CLEAR ALL"
              handleClick={clearFilterData}
              btnTextStyle={styles.anchorText}
            />
        </View>
      </View>
    );
};

const styles = StyleSheet.create({
    header: {
        backgroundColor: holidayColors.white,
        shadowColor: '#000',
        shadowOffset: {
            width: 0,
            height: 1,
        },
        shadowOpacity: 0.20,
        shadowRadius: 1.41,
        elevation: 3,
        flexDirection: 'row',
        alignItems: 'center',
        ...paddingStyles.pa16,
    },
    iconClose: {
        width: 16,
        height: 16,
        resizeMode: 'cover',
    },
    headerContent: {
        justifyContent: 'space-between',
        flexDirection: 'row',
        flex: 1,
    },
    anchorText: {
        ...fontStyles.labelSmallBold,
        color: holidayColors.primaryBlue,
    },

});

export default FilterHeader;
