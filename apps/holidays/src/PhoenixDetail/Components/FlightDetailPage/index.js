import React from 'react';
import {ScrollView, StyleSheet, Text, View,Dimensions } from 'react-native';
import cStyles from '@mmt/legacy-commons/Styles/AtomicCss';
import FlightDetailCards from './FlightCard';
import FlightTopCard from './FlightTopCard';
import BasePage from '@mmt/legacy-commons/Common/navigation/BasePage';
import { itineraryUnitTypes, packageActionComponent } from '../../DetailConstants';
import { HOLIDAYS_FLIGHT_OVERLAY, HOLIDAYS_FLIGHT_OVERLAY_PAGE, PHOENIX_DETAIL_OVERLAY } from '../../Utils/PheonixDetailPageConstants';
import { HOLIDAY_ROUTE_KEYS, HolidayNavigation } from '../../../Navigation';
import { trackPhoenixDetailLocalClickEvent } from '../../Utils/PhoenixDetailTracking';
import DummyFlightCards from '../DummyFlight/dummyFlightCard';
import { paddingStyles } from 'mobile-holidays-react-native/src/Styles/Spacing';
import { Overlay } from '../DetailOverlays/OverlayConstants';

const screenHeight = Dimensions.get('window').height;
import {isMobileClient, isRawClient} from "../../../utils/HolidayUtils";
import { holidayNavigationPush } from '../../Utils/DetailPageNavigationUtils';

export const RecheckinBaggage = (props) => {
  const {flightCardData} = props;
  const Content = (
    <View style={styles.flLayoverTag}>
      <View style={[cStyles.marginBottom3]}><Text style={[cStyles.font11, cStyles.defaultText]}><Text
        style={cStyles.blackFont}>Change Planes | 3h 00m | </Text>Layover in Mysore | </Text></View>
      <View><Text style={[cStyles.font11, cStyles.blackFont, cStyles.redText]}>Re-checkin of baggage
        required</Text></View>
    </View>
  );
  return (
    <>
      {/* <View style={[cStyles.marginBottom20, cStyles.marginTop15]}>
        <Text style={[cStyles.font18, cStyles.blackFont, cStyles.defaultText]}>Re-checkin of baggage required</Text>
      </View> */}
      {flightCardData.map((item, i) =>
        <View style={styles.flightCard} key={i}>
          <View style={styles.cardHeader}>
            <Text style={[cStyles.font11, cStyles.boldFont]}>
              {item.flHeader.startLoc} - {item.flHeader.destinationLoc} | {item.flHeader.flDate}
            </Text>
          </View>
          <FlightDetailCards
            data={item.flData.sourceDetails}
            isStopInfo={true}
            isFreeMeals={false}
            isChargeableMeals={false}
            infoTag={Content}
          />
          <FlightDetailCards
            data={item.flData.destinationDetails}
            isStopInfo={false}
            isFreeMeals={false}
            isChargeableMeals={false}
          />
        </View>,
      )}
    </>
  );
};

export const FreeMeals = (props) => {
  const {flightCardData} = props;
  return (
    <>
      {/* <View style={[cStyles.marginBottom20, cStyles.marginTop15]}>
        <Text style={[cStyles.font18, cStyles.blackFont, cStyles.defaultText]}>Included Meals - free</Text>
      </View> */}
      {flightCardData.map((item, i) =>
        <View style={styles.flightCard} key={i}>
          <View style={styles.cardHeader}>
            <Text
              style={[cStyles.font11, cStyles.boldFont]}>{item.flHeader.startLoc} - {item.flHeader.destinationLoc} | {item.flHeader.flDate}</Text>
          </View>
          <FlightDetailCards
            data={item.flData.sourceDetails}
            isStopInfo={false}
            isFreeMeals={true}
            isChargeableMeals={false}
          />
        </View>,
      )}
    </>
  );
};

export default class FlightDetailPage extends BasePage {
  constructor(props) {
    super(props);
  }

  openFlightListingPage = () => {
    const {item, dynamicId, requestParam, pricingDetail, onComponentChange, onPackageComponentToggle, subtitleData, packageDetailDTO, roomDetails, trackLocalClickEvent, trackLocalPageLoadEvent,showOverlay,hideOverlays,clearOverlays, ifFlightGroupFailed} = this.props;
    const {flightSelections, overnightDelays} = requestParam;
    const {extraData, day, data} = item || {};
    const {listingFlightSequence} = extraData || {};
    const {flightId} = data;

    const requestParams = {};
    if (listingFlightSequence) {
      requestParams.listingFlightSequence = listingFlightSequence;
    }
    if (flightSelections && flightSelections.length > 0) {
      requestParams.flightSelections = flightSelections;
    }
    if (overnightDelays) {
      requestParams.overnightDelays = overnightDelays;
    }
    trackPhoenixDetailLocalClickEvent({ eventName: 'change_', suffix: `${itineraryUnitTypes.FLIGHT}_${flightId}_${day}`, prop1: HOLIDAYS_FLIGHT_OVERLAY_PAGE, sendGIData:isRawClient() });
    const flightOverlayData = {
      flightRequestObject: requestParams,
      dynamicId: dynamicId,
      pricingDetail: pricingDetail,
      onComponentChange: onComponentChange,
      onPackageComponentToggle: onPackageComponentToggle,
      subtitleData: subtitleData,
      lastPage: 'PhoneixDetailOverlay', // Required to pop all scenes above phoenix overlay
      accessRestriction: this.props.accessRestriction,
      packageDetailDTO,
      roomDetails,
      trackLocalClickEvent,
      trackLocalPageLoadEvent,
      showOverlay,
      hideOverlays,
      clearOverlays,
      isFlightFailed: ifFlightGroupFailed,
    };

    if (isMobileClient()) {
      HolidayNavigation.push(HOLIDAY_ROUTE_KEYS.FLIGHT_LISTING, {
        flightRequestObject: requestParams,
        dynamicId: dynamicId,
        pricingDetail: pricingDetail,
        roomDetails,
        trackLocalClickEvent,
        trackLocalPageLoadEvent,
        isFlightFailed: ifFlightGroupFailed,
      });
    } else {
      holidayNavigationPush({
        pageKey: HOLIDAY_ROUTE_KEYS.FLIGHT_LISTING,
        overlayKey: Overlay.FLIGHT_OVERLAY,
        props: flightOverlayData,
        showOverlay: this.props.showOverlay,
        hideOverlays: this.props.hideOverlays,
      })
    }
  }

  onRemovePress = () => {
    const { item } = this.props;
    const {day, data} = item || {};
    const {flightId} = data;
    this.props.onPackageComponentToggle(false, packageActionComponent.FLIGHT);
    trackPhoenixDetailLocalClickEvent({ eventName: 'remove_', suffix: `${itineraryUnitTypes.FLIGHT}_${flightId}_${day}`, prop1: HOLIDAYS_FLIGHT_OVERLAY_PAGE, sendGIData:isRawClient() });
    isMobileClient()
        ? HolidayNavigation.pop()
        : this.props.hideOverlays([Overlay.FLIGHT_OVERLAY, Overlay.VIEW_DETAILS]);
  }

  render() {
    const {item, accessRestriction} = this.props || {};
    const {removeFlightRestricted = false, changeFlightRestricted = false} = accessRestriction || {};
    const {data, extraData} = item || {};
    const {flightLegs = [], stops = 0} = data || {};
    const { flightMetadataDetail = {} } = [data]?.[0] || {};
    const { isDummy = false } = flightMetadataDetail || {};
    return (
      <View style={styles.container}>
        <ScrollView>
          <View style={{flex: 1}}>
            <FlightTopCard
              flights = {[data]}
              roundTrip={false}
              navigation={this.props.navigation}
              showOptions={true}
              onChangePress={this.openFlightListingPage}
              onRemovePress={this.onRemovePress}
              removeFlightRestricted={removeFlightRestricted}
              changeFlightRestricted={changeFlightRestricted}
            />
            {isDummy ? (
              <View style={{ padding: 10 }}>
                <DummyFlightCards flightLegs={flightLegs} accessRestriction={accessRestriction} />
              </View>
            ) : (
              flightLegs.map((flightLeg, i) => {
                return (
                  <FlightDetailCards
                    flightLeg={flightLeg}
                    stops={stops}
                    baggageInfoMap={extraData.baggageInfoMap}
                  />
                );
              })
            )}
          </View>
        </ScrollView>
      </View>
    );
  }
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    ...paddingStyles.pa16,
  },
  cardHeader: {
    padding: 10,
    backgroundColor: '#f2f2f2',
  },
  flLayoverTag: {
    backgroundColor: '#e4f3ff',
    alignItems: 'center',
    justifyContent: 'center',
    padding: 5,
    width: '100%',
  },
});
