import React, {useState} from 'react';
import {Image, StyleSheet, TouchableOpacity, View} from 'react-native';


import iconCheck from '../../../images/ic_check.png';
import iconUnCheck from '../../../images/ic_unCheck.png';
import BasePage from '@mmt/legacy-commons/Common/navigation/BasePage';

export default class FilterCheckbox extends BasePage {

  constructor(props) {
    super(props);
    this.state = {
      checkBoxActive: props.active,
    };
  }

  componentWillReceiveProps(nextProps) {
    this.setState({
      checkBoxActive: nextProps.active,
    });
  }

  toggleCheckbox = () => {
     this.props.toggleSelection(!this.state.checkBoxActive,this.props.name);
     this.setState({
      checkBoxActive: !this.state.checkBoxActive,
    });
  };

  render() {
    return (
      <TouchableOpacity onPress={this.toggleCheckbox}>
        <View>
          {this.state.checkBoxActive ?
            <Image source={iconCheck} style={styles.iconCheck}/> : <Image source={iconUnCheck} style={styles.iconCheck}/>
          }
        </View>
      </TouchableOpacity>
    );
  }
}

const styles = StyleSheet.create({
  iconCheck: {
    width: 18,
    height: 18,
    resizeMode: 'cover',
  },
});
