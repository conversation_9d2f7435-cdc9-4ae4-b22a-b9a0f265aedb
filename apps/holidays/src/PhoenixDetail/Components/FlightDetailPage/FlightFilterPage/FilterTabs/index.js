import React, { useEffect, useState } from 'react';
import {StyleSheet, TouchableOpacity, View} from 'react-native';
import BasePage from '@mmt/legacy-commons/Common/navigation/BasePage';
import { holidayColors } from '../../../../../Styles/holidayColors';


export default class FilterTabs extends BasePage {

  constructor(props) {
    super(props);
    this.state = {
      active: props.active,
    };
  }

  componentWillReceiveProps(nextProps) {
    this.setState({
      active: nextProps.active,
    });
  }

   toggleActive = () => {
     this.props.toggleSelection(!this.state.active,this.props.name);
    this.setState({
      active: !this.state.active,
    });
  }

  render() {
    const {tabWidth, children} = this.props;
    return (
      <TouchableOpacity onPress={this.toggleActive}>
        <View
          style={this.state.active ? [styles.filterTabs, styles.filterTabsActive, {width: tabWidth}] : [styles.filterTabs, {width: tabWidth}]}>{children}</View>
      </TouchableOpacity>
    );
  }
}

const styles = StyleSheet.create({
  filterTabs: {
    backgroundColor: holidayColors.white,
    paddingHorizontal: 15,
    paddingVertical: 8,
    borderRadius: 4,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.22,
    shadowRadius: 2.22,
    elevation: 3,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 10,
    marginTop: 10,
  },
  filterTabsActive: {
    borderWidth: 1,
    borderColor: holidayColors.primaryBlue,
    backgroundColor: holidayColors.lightBlueBg,
  },
});
