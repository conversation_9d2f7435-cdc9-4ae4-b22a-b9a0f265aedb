import BasePage from '@mmt/legacy-commons/Common/navigation/BasePage';
import { Platform, ScrollView, StyleSheet, Switch, Text, View } from 'react-native';
import cStyles from '@mmt/legacy-commons/Styles/AtomicCss';
import FilterTabs from './FilterTabs';
import FilterCheckbox from './FilterCheckBox';
import React from 'react';
import PriceRange from './PriceRange';
import { fontStyles } from '../../../../Styles/holidayFonts';
import { holidayColors } from '../../../../Styles/holidayColors';
import FilterItemSection from './FilterItemSection';
import { paddingStyles } from 'apps/holidays/src/Styles/Spacing';

export default class FilterSection extends BasePage {

  constructor(props) {
    super(props);
    this.filterData = this.props.filterData;
  }

  setStops = (active,stops) => {
    this.props.setStops(stops,active,this.props.index);
  }

  setDepartureTime = (active, time) => {
    this.props.setDepartureTime(time,active,this.props.index);
  }

  setArrivalTime = (active, time) => {
    this.props.setArrivalTime(time,active,this.props.index);
  }

  setAirLine = (active, airline) => {
    this.props.setAirLine(airline,active,this.props.index);
  }

  setPriceRange = (priceRange) => {
    this.props.setPriceRange(priceRange,this.props.index);
  };

  setOvernightFlights = (active) => {
    this.props.setOvernightFlights(active, this.props.index);
  }

  componentWillReceiveProps(nextProps) {
    this.filterData = nextProps.filterData;
  }

  render() {
    return <ScrollView>
        <View>

          {/*Overnight Flights*/}
          {this.filterData.overnightFlights &&
            <View style={{flexDirection: 'row', alignItems: 'center', ...styles.filterBlk}}>
              <Text style={{
                fontFamily: 'Lato-Black',
                fontSize: 14,
                flex: 1,
                lineHeight: 18,
                letterSpacing: 0,
                color: '#4a4a4a',
              }}>{this.filterData.overnightFlights.title}</Text>
              <Switch
                trackColor={{ false: '#cccccc', true: '#d7edff' }}
                thumbColor={[this.filterData.overnightFlights.selected ? '#008cff' : '#f5f5f5']}
                onValueChange={(state) => this.setOvernightFlights(state)}
                value={this.filterData.overnightFlights.selected}
              />
            </View>}

          {/* No of Stops */}
          <FilterItemSection heading={'No of Stops'}>
            {this.filterData.noOfStops.map((list, index) => (
              <FilterTabs
                name={list.title}
                active={list.selected}
                tabWidth={95}
                key={index}
                toggleSelection={this.setStops}
              >
                <View style={[styles.filterTabContent]}>
                  <View>
                    <Text style={styles.stopsTitle}>{list.title}</Text>
                  </View>
                  <View>
                    <Text style={styles.stopsValue}>{list.stopTitle}</Text>
                  </View>
                </View>
              </FilterTabs>
            ))}
          </FilterItemSection>

          {/* Departure time */}
          <FilterItemSection heading={'Departure Time'}>
          {this.filterData.departureTime.map((list, index) => (
              <FilterTabs
                name={list.title}
                active={list.selected}
                tabWidth={155}
                key={index}
                toggleSelection={this.setDepartureTime}
              >
                <View style={[cStyles.flexRow, cStyles.alignCenter]}>
                  <View>
                    <Text style={styles.smallRegularTitle}>{list.title}</Text>
                  </View>
                </View>
              </FilterTabs>
            ))}
          </FilterItemSection>

          {/* Arrival time */}
          <FilterItemSection heading={'Arrival Time'}>
              {this.filterData.arrivalTime.map((list, index) =>
                <FilterTabs name = {list.title} active = {list.selected} tabWidth={155} key={index} toggleSelection = {this.setArrivalTime}>
                  <View style={[cStyles.flexRow, cStyles.alignCenter]}>
                    <View><Text style={styles.smallRegularTitle}>{list.title}</Text></View>
                  </View>
                </FilterTabs>
              )}
          </FilterItemSection>

          {/* Airlines */}
          <FilterItemSection heading={'Airlines'}>
              {this.filterData.airlines.map((list, index) =>
                <View key={index}
                      style={[cStyles.flexRow, cStyles.alignCenter, cStyles.marginBottom10, {width: 155}]}>
                  <FilterCheckbox name = {list.title} active = {list.selected} tabWidth={155} key={index} toggleSelection = {this.setAirLine}/>
                  <View style={[cStyles.marginLeft12]}><Text style={styles.smallRegularTitle}>{list.title}</Text></View>
                </View>
              )}
          </FilterItemSection>
          {this.filterData.defaultPriceRange &&
            <View style={styles.filterBlk}>
              <PriceRange priceRange = {this.filterData.priceRange} defaultPriceRange = {this.filterData.defaultPriceRange} setPriceRange = {this.setPriceRange}/>
            </View>}
        </View>
    </ScrollView>;
  }
}

const styles = StyleSheet.create({
  galleryView: {
    width: '50%',
  },
  modalView: {
    backgroundColor: 'white',
    width: '100%',
    height: '100%',
    flex: 1,
  },
  filterTabContent: {
    alignItems: 'center',
    justifyContent: 'center',
  },
  stopsTitle: {
    ...fontStyles.labelMediumBold,
    color: holidayColors.gray,
  },
  stopsValue: {
    ...fontStyles.labelSmallBold,
    color: holidayColors.gray,
  },
  smallRegularTitle: {
    ...fontStyles.labelSmallRegular,
    color: holidayColors.gray,
  },
  smallRegularTitle: {
    ...fontStyles.labelSmallRegular,
    color: holidayColors.gray,
  },
  filterBtn: {
    borderRadius: 4,
    padding: 15,
    margin: 10,
    alignItems: 'center',
    justifyContent: 'center',
},
  filterSectionHeading: {
    ...fontStyles.labelBaseBlack,
    color: holidayColors.gray,
  },
  markerStyle: {
    height: 30,
    width: 30,
    borderRadius: 30,
    backgroundColor: '#fff',
    elevation: 3,
    borderWidth: 1,
    borderColor: '#ccc',
    ...Platform.select({
      ios: {
        marginTop: 0,
      },
      android: {
        marginTop: 5,
      },
    }),
  },
  filterBlk: {
    ...paddingStyles.pa16,
  },
});
