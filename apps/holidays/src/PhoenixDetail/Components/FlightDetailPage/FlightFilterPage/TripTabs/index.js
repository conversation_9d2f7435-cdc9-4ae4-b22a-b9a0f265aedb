import React, {useState} from 'react';
import {StyleSheet, Text, TouchableOpacity, View} from 'react-native';
import { holidayColors } from 'mobile-holidays-react-native/src/Styles/holidayColors';
import { fontStyles } from 'mobile-holidays-react-native/src/Styles/holidayFonts';

const TripTabs = (props) => {
    const [activeTab, setActiveTab] = useState(props.activeTab);
    const toggleTabs = (index) => {setActiveTab(index);
        props.switchPage(index);
    };
    return (
        <View style={[styles.tripTabsWrap]}>
            {props.tabData.map((item, i) =>
                <TouchableOpacity
                    key={i}
                    onPress={() => toggleTabs(i)}
                    style={i !== activeTab ? [styles.tripTabs] : [styles.tripTabs, styles.tripTabsActive]}
                >
                    <View>
                        <Text style={i !== activeTab ? [styles.tabText] : [styles.tabText, styles.tabTextActive]}>{item?.toUpperCase()}</Text>
                    </View>
                </TouchableOpacity>
            )}
        </View>
    );
};

const styles = StyleSheet.create({
    tripTabsWrap: {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-between',
        backgroundColor: holidayColors.lightGray2,
        borderBottomWidth: 2,
        borderColor: holidayColors.grayBorder,
    },
    tripTabs: {
        padding: 10,
        borderBottomWidth: 2,
        borderColor: holidayColors.grayBorder,
        position: 'relative',
        top: 2,
        justifyContent: 'center',
        alignItems: 'center',
        flex: 1,

    },
    tripTabsActive: {
        borderColor: holidayColors.primaryBlue,
    },
    tabText: {
        ...fontStyles.labelBaseBold,
        color: holidayColors.gray,
    },
    tabTextActive: {
        color: holidayColors.primaryBlue,
    },

});

export default TripTabs;
