import { flightDetailTypes } from '../../../DetailConstants';
import { getComboTripName, getTripName, LoggingTripName } from '../FlightListing/FlightsUtils';
import { HOLIDAYS_FLIGHT_OVERLAY } from '../../../Utils/PheonixDetailPageConstants';
import { trackPhoenixDetailLocalClickEvent } from '../../../Utils/PhoenixDetailTracking';
import { logPhoenixDetailPDTEvents } from 'mobile-holidays-react-native/src/utils/PhoenixDetailPDTTrackingUtils';
import { PDT_EVENT_TYPES } from 'mobile-holidays-react-native/src/utils/HolidayPDTConstants';

export const isFilterApplied = (filterData) => {
  if (filterData) {
    const {noOfStops,departureTime,arrivalTime,airlines,priceRange, overnightFlights} = filterData;
    if (isSpecificFilterApplied(noOfStops)
      || isSpecificFilterApplied(departureTime)
      || isSpecificFilterApplied(arrivalTime)
      || isSpecificFilterApplied(airlines)
      || isPriceRangeApplied(filterData)
      || isOvernightFlightsApplied(overnightFlights)) {
      return true;
    }
  }
  return false;
};

export const isPriceRangeApplied = (filterData = {}) => {
  if (filterData.priceRange && filterData.defaultPriceRange && (filterData.priceRange.min > filterData.defaultPriceRange.min || filterData.priceRange.max < filterData.defaultPriceRange.max))
    {return true;}
  else
    {return false;}
};

export const isOvernightFlightsApplied = (overnightFlights = {}) => {
  return overnightFlights ? overnightFlights.selected : false;
};

export const isSpecificFilterApplied = (list = []) => {
  let flag = false;
  for (let i = 0; i < list.length; i++) {
    if (list[i].selected) {
      flag = true;
      break;
    }
  }
  return flag;
};

export const getDefaultFilterState = (uniqueAirLines = [],defaultPriceRange = {}, showOvernightFlightsFilter = false) => {
  const filters = {
      noOfStops: [
        {
          title: '0',
          stopTitle: 'Non-Stop',
          selected: false,
        },
        {
          title: '1',
          stopTitle: 'Stop',
          selected: false,
        },
        {
          title: '2+',
          stopTitle: 'Stops',
          selected: false,
        },
      ],
      departureTime: [
        {
          title: 'Before 6 AM',
          selected: false,
        },
        {
          title: '6 AM - 12 PM',
          selected: false,
        },
        {
          title: '12 PM - 6 PM',
          selected: false,
        },
        {
          title: '6 PM - 12 AM',
          selected: false,
        },
      ],
      arrivalTime: [
        {
          title: 'Before 6 AM',
          selected: false,
        },
        {
          title: '6 AM - 12 PM',
          selected: false,
        },
        {
          title: '12 PM - 6 PM',
          selected: false,
        },
        {
          title: '6 PM - 12 AM',
          selected: false,
        },
      ],
      airlines: uniqueAirLines,
      priceRange: null,
      defaultPriceRange : defaultPriceRange,
    };
  if (showOvernightFlightsFilter) {
    filters.overnightFlights = {
      title: 'Overnight Flights',
      selected: false,
    };
  }
  return filters;
};

export const filterFlightsOnStops = (flights = [],noOfStops = []) => {
  const result = [];
  noOfStops.forEach((item) => {
    if (item.selected === true) {
      if (item.title === '0') {
        flights.forEach((item) => {
          if (item.stops === 0) {
            result.push(item);
          }
        });
      } if (item.title === '1') {
        flights.forEach((item) => {
          if (item.stops === 1) {
            result.push(item);
          }
        });
      } if (item.title === '2+') {
        flights.forEach((item) => {
          if (item.stops >= 2) {
            result.push(item);
          }
        });
      }
    }
  });
  return result;
};

export const filterFlightsOnTime = (flights = [],time = [],type) => {

  const result = [];
  time.forEach((item) => {
    if (item.selected === true) {
      if (item.title === 'Before 6 AM') {
        flights.forEach((flight) => {
          const flightDate = new Date(type === 'DEP' ? flight.departure : flight.arrival);
          if (flightDate.getHours() <= 6) {
            result.push(flight);
          }
        });
      } if (item.title === '6 AM - 12 PM') {
        flights.forEach((flight) => {
          const flightDate = new Date(type === 'DEP' ? flight.departure : flight.arrival);
          if (flightDate.getHours() >= 6 && flightDate.getHours() <= 12) {
            result.push(flight);
          }
        });
      } if (item.title === '12 PM - 6 PM') {
        flights.forEach((flight) => {
          const flightDate = new Date(type === 'DEP' ? flight.departure : flight.arrival);
          if (flightDate.getHours() >= 12 && flightDate.getHours() <= 18) {
            result.push(flight);
          }
        });
      } if (item.title === '6 PM - 12 AM') {
        flights.forEach((flight) => {
          const flightDate = new Date(type === 'DEP' ? flight.departure : flight.arrival);
          if ((flightDate.getHours() >= 18 && flightDate.getHours() <= 23) || flightDate.getHours() === 0) {
            result.push(flight);
          }
        });
      }
    }
  });
  return result;
};

export const filterFlightsOnAirlines = (flights = [], airlines = []) => {
  const result = {};
  airlines.forEach((airline) => {
    if (airline.selected && airline.selected === true) {
      flights.forEach((item) => {
       for (let i = 0; i < item.flightLegs.length; i++) {
         if (item.flightLegs[i].airlineName === airline.title) {
           result[item.sellableId] = item;
           break;
         }
       }
      });
    }
  });
  return Object.values(result);
};

export const filterOvernightFlights = (flights) => {
  const newFlights = flights.filter((item) => {
    if (item.overnightDelay > 0) {
      return true;
    } else {
      return false;
    }
  });
  return newFlights;
};

export const getAirlinesList = (flights = []) => {
  const result = {};
      flights.forEach((item) => {
        for (let i = 0; i < item.flightLegs.length; i++) {
            result[item.flightLegs[i].airlineName] = {'title': item.flightLegs[i].airlineName, selected: false};
        }
      });
  return Object.values(result);
};


export const filter = (filterData = [], flights = [],type) => {
  for (let i = 0; i < filterData.length; i++) {
    if (!isFilterApplied(filterData[i])) {
      continue;
    } else {
      if (flights[i] && flights[i].length > 0 && isPriceRangeApplied(filterData[i])) {
        flights[i] = flights[i].filter(flight => flight.discountedPrice >= filterData[i].priceRange.min && flight.discountedPrice <= filterData[i].priceRange.max);
      }
      if (flights[i] && flights[i].length > 0 && isSpecificFilterApplied(filterData[i].noOfStops)) {
        flights[i] = filterFlightsOnStops(flights[i], filterData[i].noOfStops);
      }
      if (flights[i] && flights[i].length > 0 && isSpecificFilterApplied(filterData[i].departureTime)) {
        flights[i] = filterFlightsOnTime(flights[i], filterData[i].departureTime, 'DEP');
      }
      if (flights[i] && flights[i].length > 0 && isSpecificFilterApplied(filterData[i].arrivalTime)) {
        flights[i] = filterFlightsOnTime(flights[i], filterData[i].arrivalTime, 'ARL');
      }
      if (flights[i] && flights[i].length > 0 && isSpecificFilterApplied(filterData[i].airlines)) {
        flights[i] = filterFlightsOnAirlines(flights[i], filterData[i].airlines);
      }
      if (flights[i] && flights[i].length > 0 && isOvernightFlightsApplied(filterData[i].overnightFlights)) {
        flights[i] = filterOvernightFlights(flights[i]);
      }
    }
  }
  return flights;
};

export const toggleSelection = (list = [], stops, selected) => {
  list.forEach((item) => {
    if (item.title === stops) {
      item.selected = selected;
    }
  });
};

export const togglePriceRange = (filterData = {}, priceRange) => {
  filterData.priceRange = priceRange;
};

export const clearFilter = (filterData = [])  => {
  filterData.forEach((item) => {
    item.noOfStops.forEach((item) => {
      item.selected = false;
    });
    item.departureTime.forEach((item) => {
      item.selected = false;
    });
    item.arrivalTime.forEach((item) => {
      item.selected = false;
    });
    item.airlines.forEach((item) => {
      item.selected = false;
    });
    item.priceRange = null;
  });
};

export const getDefaultFilterStateV2 = (flightGroupRows = [], listingDataType, discountedFactor, price) => {
  let tabName = [];
  let filterData = [];
  let uniqueAirlines = new Map();
  let priceRange = {min: 0, max: 0}; // [min, max]

  let maxTrips = 0;

  const overnightFilterMap = new Map();

  flightGroupRows.forEach((flightGroup, flightGroupIndex) => {
    const temp_segment = getSegments(flightGroup, listingDataType);
    // Store max number of trips
    if (temp_segment && temp_segment.length > maxTrips) {
      maxTrips = temp_segment.length;
    }

    // store max and min price for price range slider
    if (flightGroup.currPackagePriceDiff < priceRange.min) {
      priceRange.min = flightGroup.currPackagePriceDiff;
    }
    if (flightGroup.currPackagePriceDiff > priceRange.max) {
      priceRange.max = flightGroup.currPackagePriceDiff;
    }

    // create airline name map
    temp_segment.forEach((item, index) => {
      const temp_flight_segment = getSegmentFlights(item, listingDataType);
      temp_flight_segment.forEach((flight) => {
        flight.flightLegs.forEach((leg) => {
          uniqueAirlines.set(leg.airlineName, {
            title: leg.airlineName,
            code: leg.airlineCode,
            selected: false,
          });
        });
      });
    });

    // overnightFilters
    const overnightFiltersArray = flightGroup.overnightDelays.split('_');
    overnightFiltersArray.forEach((item, index) => {
      if (item !== '0') {
        overnightFilterMap.set(index, true);
      } else {
        if (!overnightFilterMap.has(index)) {
          overnightFilterMap.set(index, false);
        }
      }
    });
  });

  uniqueAirlines = Array.from(uniqueAirlines.values());

  // create tab name data and populate filter data with each tab filters
  for (let i = 0; i < maxTrips; i++) {
    tabName.push(getComboTripName(listingDataType, i, maxTrips));
    filterData.push(getDefaultFilterState(uniqueAirlines, priceRange, overnightFilterMap.get(i)));
  }
  return {
    tabName: tabName,
    filterData: filterData,
    uniqueAirlines: uniqueAirlines,
    priceRange: priceRange,
  };
};

export const getSegments = (item, listingDataType) => {
  if (listingDataType === flightDetailTypes.OBT) {
    const {obtSegments = []} = item;
    return obtSegments;
  } else {
    const {segmentFlights = []} = item;
    return segmentFlights;
  }
};

export const getSegmentFlights = (item, listingDataType) => {
  if (listingDataType === flightDetailTypes.OBT) {
    const {segmentFlights = []} = item;
    return segmentFlights;
  } else {
    const {domFlights = []} = item;
    return domFlights;
  }
};

/**
 * Used to set filters flight in place of previous flights.
 * */
export const setSegmentFlights = (item, flights, listingDataType) => {
  if (listingDataType === flightDetailTypes.OBT) {
    item.segmentFlights = flights;
  } else {
    item.domFlights = flights;
  }
};


/**
 * This function removes a combo if any of its trip is an empty array.
 * This function is used after applying the filters to flightGroupRows
 * */
export const clearFlightGroups = (flightGroups, listingDataType) => {
  flightGroups = flightGroups.filter((group) => {
    const segments = getSegments(group, listingDataType);
    for (let i = 0; i < segments.length; i++) {
      const segmentFlights = getSegmentFlights(segments[i], listingDataType);
      if (!segmentFlights || segmentFlights.length === 0) {
        return false;
      }
    }
    return true;
  });
  return flightGroups;
};

export const SortFilterTypes = {
  PRICE: 'PRICE',
  DURATION: 'DURATION',
  DEPARTURE: 'DEPARTURE',
  ARRIVAL: 'ARRIVAL',
};

/**
 * initial sorting options state.
 */
export const getInitialSortingOptionsData = () => {
  const data = {
    sortingDataIndex: null,
    optionIndex: null,
    isSortingApplied: false,
    sortingData: [{
      name: 'Price',
      options: ['Low to High', 'High to Low'],
      type: SortFilterTypes.PRICE,
    }, {
      name: 'Duration',
      options: ['Shortest First'],
      type: SortFilterTypes.DURATION,
    }, {
      name: 'Departure',
      options: ['Earliest First', 'Latest First'],
      type: SortFilterTypes.DEPARTURE,
    }, {
      name: 'Arrival',
      options: ['Earliest First', 'Latest First'],
      type: SortFilterTypes.ARRIVAL,
    }],
  };
  return data;
};

/**
 * return sort page UI data for flight combo listing page
 * */
export const getInitialSortingOptionsDataV2 = () => {
  const data = {
    sortingDataIndex: null,
    optionIndex: null,
    isSortingApplied: false,
    sortingData: [{
      name: 'Price',
      options: ['Low to High', 'High to Low'],
      type: SortFilterTypes.PRICE,
    }],
  };
  return data;
};

export const isSortingApplied = (sortingOptions) => {
  const {isSortingApplied} = sortingOptions;
  return isSortingApplied;
};

/**
 * Generic function to update option selection
 * Where X could be any sortFilterType
 */
export const sortByX = (sortingOptions, sortIndex, optionIndex) => {
  sortingOptions.sortingDataIndex = sortIndex;
  sortingOptions.optionIndex = optionIndex;
  sortingOptions.isSortingApplied = true;
  return sortingOptions;
};

/**
 * Generic function to sort flight combo's and dom rt flights.
 * */
export const sortingX = (xArray = [], sortingOptions, combo = false) => {
  const {sortingDataIndex, optionIndex, sortingData} = sortingOptions;
  const sortingOptionData = sortingData[sortingDataIndex];
  const optionData = sortingOptionData.options[optionIndex];
  switch (sortingOptionData.type) {
    case SortFilterTypes.PRICE:
      return combo ? sortComboByPrice(optionData, xArray) : sortByPrice(optionData, xArray);
    case SortFilterTypes.DURATION:
      return combo ? xArray : sortByDuration(optionData, xArray);
    case SortFilterTypes.DEPARTURE:
      return combo ? xArray : sortByDeparture(optionData, xArray);
    case SortFilterTypes.ARRIVAL:
      return combo ? xArray : sortByArrival(optionData, xArray);
    default: break;
  }
  return xArray;
};

export const sortByDuration = (optionData, flights = []) => {
  if (optionData === 'Shortest First') {
    flights.sort((x, y) => {
      return x.duration - y.duration;
    });
  }
  return flights;
};

export const captureFilterClickEvents = ({
  eventName = '',
  suffix = '',
  prop1 = '',
}) => {
  const value = eventName + suffix ;
  logPhoenixDetailPDTEvents({
    actionType: PDT_EVENT_TYPES.buttonClicked,
    value : value.replace(/_/g, '|'),
    subPageName: prop1,
  });
  trackPhoenixDetailLocalClickEvent({
    eventName,
    suffix,
    prop1,
  });
};

export const sortByDeparture = (optionData, flights = []) => {
  if (optionData === 'Earliest First') {
    flights.sort((x, y) => {
      const time1 = new Date(x.departure).getTime();
      const time2 = new Date(y.departure).getTime();
      return time1 - time2;
    });
  } else if (optionData === 'Latest First') {
    flights.sort((x, y) => {
      const time1 = new Date(x.departure).getTime();
      const time2 = new Date(y.departure).getTime();
      return time2 - time1;
    });
  }
  return flights;
};

export const sortByArrival = (optionData, flights = []) => {
  if (optionData === 'Earliest First') {
    flights.sort((x, y) => {
      const time1 = new Date(x.arrival).getTime();
      const time2 = new Date(y.arrival).getTime();
      return time1 - time2;
    });
  } else if (optionData === 'Latest First') {
    flights.sort((x, y) => {
      const time1 = new Date(x.arrival).getTime();
      const time2 = new Date(y.arrival).getTime();
      return time2 - time1;
    });
  }
  return flights;
};

export const sortByPrice = (optionData, flights = []) => {
  if (optionData === 'Low to High') {
    flights.sort((x, y) => {
      return x.price - y.price;
    });
  } else if (optionData === 'High to Low') {
    flights.sort((x, y) => {
      return y.price - x.price;
    });
  }
  return flights;
};

const sortComboByPrice = (optionData, groups = []) => {
  if (optionData === 'Low to High') {
    groups.sort((x, y) => {
      return x.currPackagePriceDiff - y.currPackagePriceDiff;
    });
  } else if (optionData === 'High to Low') {
    groups.sort((x, y) => {
      return y.currPackagePriceDiff - x.currPackagePriceDiff;
    });
  }
  return groups;
};

export const log_filter = ({data, prefix,  prop1 = '' } = {}) => {
  if (data && data.length > 0) {
    let arr = [];
    data.forEach((item, index) => {
      const {selected, title} = item;
      if (selected) {
        arr.push(title);
      }
    });
    if (arr.length > 0) {
      const out = arr.join(',');
      captureFilterClickEvents({
        eventName: prefix,
        suffix: out,
        prop1,
        value: prefix?.replace(/_/g,'|') + out,
      });
    }
  }
};

export const logSelectedFilters = ({ filterData, listingDataType, prop1 = '' } = {}) => {
  for (let i = 0; i < filterData.length; i++) {
    const data = filterData[i];
    const tabText = getTripName(i, filterData.length - 1, listingDataType);
    log_filter({ data: data.noOfStops, prefix: `filter_${tabText}_noOfStops_`, prop1 });
    log_filter({data: data.departureTime, prefix: `filter_${tabText}_departureTime_`, prop1 });
    log_filter({data: data.arrivalTime, prefix: `filter_${tabText}_arrivalTime_`, prop1 });
    log_filter({ data: data.airlines, prefix: `filter_${tabText}_airlines_`, prop1 });
    if (data.priceRange) {
      captureFilterClickEvents({
        eventName: `filter_${tabText}_priceRange_`,
        suffix: `${data.priceRange.min},${data.priceRange.max}`,
        prop1,
      });
    }
  }
};

export const log_sortBy = ({ data, prefix, listingDataType, index,  prop1 = ''} = {}) => {
  const {sortingDataIndex, optionIndex, isSortingApplied, sortingData} = data || {};
  if (isSortingApplied) {
    const eventName = listingDataType === flightDetailTypes.DOM_RETURN ? `${prefix}${LoggingTripName[index]}_` : `${prefix}`;
    const suffix = `${sortingData[sortingDataIndex].type}_${sortingData[sortingDataIndex].options[optionIndex]}`;
    captureFilterClickEvents({
      eventName,
      suffix,
      prop1,
    });
  }
};
