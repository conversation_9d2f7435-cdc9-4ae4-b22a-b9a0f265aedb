import React from 'react';
import {
  BackHandler,
  Dimensions,
  FlatList,
  Platform,
  ScrollView,
  StatusBar,
  StyleSheet,
  Text,
  View,
} from 'react-native';
import BasePage from '@mmt/legacy-commons/Common/navigation/BasePage';
import { statusBarHeightForIphone } from '@mmt/legacy-commons/Styles/globalStyles';
import PhoenixHeader from '../PhoenixHeader';
import FlightShortListCard from './FlightListing/FlightShortListCard';
import FlightTopCard from './FlightTopCard';
import FlightDetailCards from './FlightCard';
import { parseFlightDate } from '../../Utils/FlightUtils';
import { fetchFlightBaggageDetail } from '../../../utils/HolidayNetworkUtils';
import { DAYS, MONTH_ARR_CAMEL, SUB_PAGE_NAMES } from '../../../HolidayConstants';
import HolidayDataHolder from '../../../utils/HolidayDataHolder';
import { HOLIDAYS_FLIGHT_DETAILS, HOLIDAYS_FLIGHT_OVERLAY_DETAIL, HOLIDAYS_FLIGHT_OVERLAY_LISTING } from '../../Utils/PheonixDetailPageConstants';
import isEmpty from 'lodash/isEmpty';
import { HolidayNavigation } from '../../../Navigation';
import { trackPhoenixDetailLocalClickEvent } from '../../Utils/PhoenixDetailTracking';
import { fontStyles } from 'mobile-holidays-react-native/src/Styles/holidayFonts';
import { holidayColors } from 'mobile-holidays-react-native/src/Styles/holidayColors';
import { marginStyles, paddingStyles } from 'mobile-holidays-react-native/src/Styles/Spacing';
import { holidayBorderRadius } from 'mobile-holidays-react-native/src/Styles/holidayBorderRadius';
import { logPhoenixDetailPDTEvents } from 'mobile-holidays-react-native/src/utils/PhoenixDetailPDTTrackingUtils';
import { PDT_EVENT_TYPES } from 'mobile-holidays-react-native/src/utils/HolidayPDTConstants';
import withBackHandler from '../../../hooks/withBackHandler';
class FlightDetail extends BasePage {

  constructor(props) {
    super(props);
    this.state = {
      baggageDetail: props.baggageInfo,
    };
    HolidayDataHolder.getInstance().setCurrentPage('flightDetail');
  }

  componentDidMount() {
    super.componentDidMount();
    if (isEmpty(this.props.baggageInfo)) {
      this.fetchBaggageInfo();
    }
  }
  onBackClick = ()=> {
    this.onBack();
    return true;
  }

  componentWillUnmount() {
    super.componentWillUnmount();
  }

  captureClickEvents = ({ eventName = '', suffix = '', }) => {
    const value = eventName + suffix;
    logPhoenixDetailPDTEvents({
      actionType: PDT_EVENT_TYPES.buttonClicked,
      value: value.replace(/_/g, '|'),
      subPageName: SUB_PAGE_NAMES.FLIGHT_DETAIL
    })
    trackPhoenixDetailLocalClickEvent({ eventName, suffix, prop1: HOLIDAYS_FLIGHT_OVERLAY_DETAIL });
  }

  logOmniture = ({prefix, addSuffix = true}) => {
    const flightIds = [];
    const {data = {}} = this.props;
    const {flights = []} = data;
    const newFlights = flights.map((item) => item.flight);
    newFlights.forEach((item) => {
      const {flightId} = item || {};
      flightIds.push(`${flightId}`);
    });
    this.captureClickEvents({ eventName: prefix, suffix: addSuffix ? flightIds.join(',') : '', prop1: HOLIDAYS_FLIGHT_OVERLAY_DETAIL });
  }

  onBack = () => {
    HolidayNavigation.pop();
    this.logOmniture({prefix: 'back', addSuffix: false});
    return true;
  }

  onChangePress = () => {
    if (this.props.onBackPress) {
      this.props.onBackPress();
    } else {
      HolidayNavigation.pop();
    }
  }

  onRemove = () => {
    this.logOmniture({prefix: 'remove_flight_'});
    this.props.onRemovePress();
  }

  render() {
    const {data = {}, onUpdatePress, subtitleData, accessRestriction, shortListCardData, initialScrollIndex, showOptions} = this.props;
    const {removeFlightRestricted = false, changeFlightRestricted = false} = accessRestriction || {};
    const {flights = []} = data;
    const newFlights = flights.map((item) => item.flight);
    const {roundTrip} = data;
    return (
      <View style={styles.pageContainer}>
        <PhoenixHeader title = {'Flight Details'} subtitleData={subtitleData} handleClose={() => HolidayNavigation.pop()}/>
        <FlightShortListCard {...shortListCardData} onUpdatePress={onUpdatePress} pageName = {HOLIDAYS_FLIGHT_OVERLAY_DETAIL} />
        <FlightTopCard
          containerStyles={styles.flightTopCardContainer}
          flights={newFlights}
          roundTrip={roundTrip}
          onChangePress = {this.onChangePress}
          onRemovePress={this.onRemove}
          showOptions={showOptions}
          removeFlightRestricted={removeFlightRestricted}
          changeFlightRestricted={changeFlightRestricted}
        />
        <FlatList
          style = {styles.listStyle}
          data={flights}
          renderItem={({item}) => this.renderItem(item)}
          keyExtractor={(item, index) => `Card-${index}`}
          showsVerticalScrollIndicator
          initialScrollIndex={initialScrollIndex}
          ItemSeparatorComponent={this.FlatListItemSeparator}
        />
      </View>
    );
  }

  fetchBaggageInfo = async () => {
    const res = await fetchFlightBaggageDetail(this.props.requestObject);
    const {baggageInfoMap = {}} = res || {};
    if (baggageInfoMap) {
      this.setState({
        baggageDetail: baggageInfoMap,
      });
    }
  }

  renderItem = (item) => {
    return this.getListCard(item);
  }

  getListCard = (item) => {
    const {departure, flightLegs = [], fromAirport, toAirport, stops} = item.flight || {};
    const depAirportCity = fromAirport ? fromAirport.airportCity : '';
    const retAirportCity = toAirport ? toAirport.airportCity : '';
    const departureDate = parseFlightDate(departure);
    const departureMonth = MONTH_ARR_CAMEL[departureDate.getMonth()];
    const departureDay = DAYS[departureDate.getDay()];
    const departureDayNumber = departureDate.getDate();
    return (
    <View style={styles.listItemStyleOuter}>
      <Text style={styles.headingStyle}>{item.heading}</Text>
      <View style={styles.listItemStyleInner} >
        <Text style={styles.subHeadingStyle}>
            {depAirportCity} to {retAirportCity} | {departureDay} {departureMonth}{' '}
            {departureDayNumber}
          </Text>
        {flightLegs.map((flightLeg, index)=> {
          return (
            <FlightDetailCards
              flightLeg = {flightLeg}
              stops={stops}
              baggageInfoMap={this.state.baggageDetail}
              key={`FlightDetailCard-${index}`}
            />
          );
        })}
      </View>
    </View>);
  }

  FlatListItemSeparator = () => {
    return (
      <View
        style={styles.itemSeperator}
      />
    );
  }

}
const styles = StyleSheet.create({
  pageContainer: {
    flex: 1,
    backgroundColor: holidayColors.lightGray2,
  },
  headingStyle: {
    ...fontStyles.labelMediumBlack,
    color: holidayColors.gray,
  },
  subHeadingStyle: {
    ...fontStyles.labelBaseBlack,
    color: holidayColors.gray,
    ...paddingStyles.pb8,
  },
  listStyle: {
    width: '100%',
    height: '100%',
  },
  listItemStyleInner: {
    ...marginStyles.mt16,
    ...paddingStyles.pa16,
    ...holidayBorderRadius.borderRadius16,
    backgroundColor: holidayColors.white,
  },
  listItemStyleOuter: {
    ...paddingStyles.ph16,
    ...paddingStyles.pb16,
  },
  iconBell: {
    width: 10,
    height: 14,
    resizeMode: 'cover',
  },
  baggageInfoTag: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 10,
    paddingVertical: 5,
    backgroundColor: '#ffeae1',
    justifyContent: 'center',
    borderBottomColor: '#bababa',
    borderBottomWidth: 1,
  },
  itemSeperator: {
    height: 5,
    width: '100%',
    backgroundColor: holidayColors.lightGray2,
  },
  flightTopCardContainer: {
    ...paddingStyles.pa16,
  },
});
export default withBackHandler(FlightDetail)
