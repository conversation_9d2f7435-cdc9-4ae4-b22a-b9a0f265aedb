import React from 'react';
import {StyleSheet, View} from 'react-native';
import ModifySearch from '../ModifySearch';
import DarkHeader from '../DarkHeader';


interface DetailPageHeaderProps {
  showSearch: boolean;
  showPageHeader: boolean;
  onBackPress: () => void;
  onSharePress: () => void;
  onFavPress: (isFav: boolean) => void,
  isShortListed: boolean;
  heading: string;
  editTravelDetails: () => void;
}

const DetailPageHeader = ({
                            showSearch,
                            showPageHeader,
                            onBackPress,
                            onSharePress,
                            onFavPress,
                            isShortListed,
                            heading,
                            editTravelDetails,
                          }: DetailPageHeaderProps) => {
  return (
    <>
      {(showPageHeader && (
        <View style={[styles.container, showPageHeader ? {} : headerPositionStyling.container]}>
          <DarkHeader onBackPress={onBackPress} onSharePress={onSharePress} onFavPress={onFavPress} heading={heading}
                      isShortListed={isShortListed}/>
          {showSearch &&
          <View style={styles.ml29}>
            <View style={styles.modifySeactWrap}>
              <ModifySearch editTravelDetails={editTravelDetails}/>
            </View>
          </View>
          }
        </View>
      ) || null)}

    </>

  );
};

const headerPositionStyling = StyleSheet.create({
  container: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
  },
});

const styles = StyleSheet.create({

  container: {
    backgroundColor: '#fff',
    paddingBottom: 15,
    elevation: 4,
    paddingTop: 14,
    paddingHorizontal: 16,
    shadowColor: 'rgba(0, 0, 0, 0.2)',
    shadowOpacity: 0.2,
    shadowRadius: 7,
    shadowOffset: {
      width: 0,
      height: 1,
    },
  },
  actions: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 6,
  },
  icon: {
    width: 16,
    height: 16,
  },
  mr13: {marginRight: 13},
  mr26: {marginRight: 26},
  ml26: {marginLeft: 26},
  ml29: {marginLeft: 29},
  heading: {
    fontFamily: 'Lato-Bold',
    fontSize: 16,
    color: '#4a4a4a',
    flexWrap: 'wrap',
  },
  row: {flexDirection: 'row', alignItems: 'center'},
  modifySeactWrap: {
    paddingTop: 15,
  },
});

export default DetailPageHeader;
