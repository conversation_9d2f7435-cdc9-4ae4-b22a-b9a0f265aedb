import React from 'react';
import {StyleSheet, View} from 'react-native';
import getPlatformElevation from '@mmt/legacy-commons/Styles/getPlatformElevation';
import {fonts} from '@mmt/legacy-commons/Styles/globalStyles';
import HorizontalCalendar from '../HorizontalCalender';
import { Sorter, getTabNames, TABS } from './Sorter';
import NotificationMessage from '../NotificationMessage';
import { detailReviewFailure } from '../../DetailConstants';
import {isEmpty} from 'lodash';
import { holidayColors } from '../../../Styles/holidayColors';


const DateSection = React.forwardRef((props, ref) => {

  if (!props.isVisible) {
    return null;
  }
  let notificationMessage = 'Please select another option for selected';
  const {
    toggleDayPlan,
    currentActivePlanOnItinerary,
    detailData,
    reviewError = {},
    failedItineraryName,
    componentFailureData,
    componentCount,
    sightseeingCount = 0,
    fromPreSales = false,
  } = props || {};

  const errorType = () => {
    let tabNames = [];
    if (!isEmpty(reviewError)){
      if (reviewError.error) {
        const {errorType} = reviewError.error;
        if (errorType) {
          if (errorType === detailReviewFailure.REVIEW_FAILED_TYPE_HOTEL) {
            notificationMessage += ' Hotel ';
            notificationMessage += failedItineraryName;
          } else if (errorType === detailReviewFailure.REVIEW_FAILED_TYPE_FLIGHT) {
            notificationMessage += ' Flight(s)';
          } else {
            notificationMessage = 'Looks like something went wrong. Please select another package.';
          }
          for (let i = 0; i < getTabNames().length; i++) {
            if (getTabNames()[i].toLowerCase().includes(errorType.toLowerCase())) {
              tabNames.push(getTabNames()[i]);
            }
          }
        }
      }
    } else if (!isEmpty(componentFailureData)) {
      const {componentErrors = {}} = componentFailureData || {};
      const {FLIGHT = [], HOTEL = []} = componentErrors;
      if (HOTEL && HOTEL.length > 0) {
        notificationMessage += ' Hotel ';
        notificationMessage += failedItineraryName;
      }
      else if (FLIGHT && FLIGHT.length > 0) {notificationMessage += ' Flight(s)';}
      else {notificationMessage = 'Looks like something went wrong. Please select another package.';}
      if (HOTEL && HOTEL.length > 0 && getTabNames().includes('Hotels')) {
        tabNames.push('Hotels');
      }
      if (FLIGHT && FLIGHT.length > 0 && getTabNames().includes('Commute')) {
        tabNames.push('Commute');
      }
    }
    return tabNames;
  };

  const errorData = errorType();
  return (
    <View style={styles.section}>
      <Sorter
        toggleDayPlan={toggleDayPlan}
        errorType = {errorData}
        currentActivePlanOnItinerary={currentActivePlanOnItinerary}
        detailData={detailData}
        componentCount={componentCount}
        sightseeingCount={sightseeingCount}
        fromPreSales={fromPreSales}
        index={props.index}
        onPressHandler={props.onPressHandler}
      />
      {currentActivePlanOnItinerary !== TABS.SUMMARY && currentActivePlanOnItinerary !== TABS.VISA  ? (
        <View style={styles.hrCalendarWrapper}>
        <HorizontalCalendar {...props} ref={ref} />
        </View>
      ) : []}
      {errorData.length > 0 &&
        <NotificationMessage
          type="error"
          message = {notificationMessage}
        />}
      <View style={styles.boarderStyle}/>
    </View>
);
});

const styles = StyleSheet.create({
  section: {
    backgroundColor: holidayColors.midLightBlue,
  },
  boarderStyle: {
    borderTopColor: '#EEEEEE',
    borderTopWidth: 1,
  },
});

export default DateSection;
