import { getFlightTime } from '../../../Utils/PhoenixDetailUtils';
import { getFlightDate, parseFlightDate } from '../../../Utils/FlightUtils';
import { Image, Text, View } from 'react-native';
import styles from './FlightCardStyles';
import React from 'react';
import { getImageUrl, IMAGE_ICON_KEYS } from '../../../../Common/Components/HolidayImageUrls';

const FlightTimings = ({ departure, arrival, from, to }) => {
  const depTime = getFlightTime(parseFlightDate(departure));
  const arrTime = getFlightTime(parseFlightDate(arrival));

  return (
    <View style={styles.flightTimings}>
        <View style={{flex:0.05}}></View>
      <View style={{ flex: 0.28 }}>
        <Text style={styles.flightTime}>{depTime}</Text>
        <Text style={styles.flightDate}>{getFlightDate(departure)}</Text>
        <Text style={styles.flightLocation} numberOfLines={1}>{from}</Text>
      </View>
        <View style={{flex:0.04}}></View>
      <View style={{ flex: 0.30}}>
        <View style={styles.flightRoute}>
          <View style={styles.flightRouteLine} />
          <View />
          <Image source={getImageUrl(IMAGE_ICON_KEYS.AIRPLANE_ICON)} style={styles.airplaneIcon}/>
          <View  />
        </View>
      </View>
        <View style={{flex:0.05}}></View>
      <View style={{flex: 0.28 }}>
        <Text style={styles.flightTime}>{arrTime}</Text>
        <Text style={styles.flightDate}>{getFlightDate(arrival)}</Text>
        <Text style={styles.flightLocation} numberOfLines={1}>{to}</Text>
      </View>
    </View>
  );
};

export default React.memo(FlightTimings);
