import React from 'react';
import { StyleSheet, View } from 'react-native';
import { getFlightDuration, getFlightObject } from '../../../Utils/PhoenixDetailUtils';
import {
  ComponentAccessRestriction,
  FlightDetail,
  FlightLeg,
  Flights,
  ItineraryUnit,
} from '../../../Types/PackageDetailApiTypes';
import { FlightCards } from './FlightCards';
import {
  trackLocalClickEvent,
  trackPhoenixDetailLocalClickEvent,
} from '../../../Utils/PhoenixDetailTracking';
import { FlightListingRequest } from '../../../Types/FlightListingApiTypes';
import { itineraryUnitTypes } from '../../../DetailConstants';
import { createBaggageDetailRequestBody } from '../../FlightDetailPage/FlightListing/FlightsUtils';
import { ischeckinBaggageInfoAvailable, isMobileClient } from '../../../../utils/HolidayUtils';
import { HOLIDAY_ROUTE_KEYS, HolidayNavigation } from '../../../../Navigation';
import { getEnableCarouselViewDetail } from '../../../../utils/HolidaysPokusUtils';
import { dayPlanRowContainerStyle, dayPlanRowHeadingStyles } from '../dayPlanStyles';
import { holidayColors } from '../../../../Styles/holidayColors';

/* Components */
import BaggageIconDetail from '../../../../Common/Components/BaggageIconDetail';
import DummyFlightRow from '../../DummyFlight/dummyFlightRow';
import FlightMessages from '../../FlightMessages';
import {Overlay} from "../../DetailOverlays/OverlayConstants";
import { holidayNavigationPush } from '../../../Utils/DetailPageNavigationUtils';

/* Components */
import HtmlHeadingV2 from 'apps/holidays/src/Common/Components/HTML/V2';
import ItineraryUnitExtraInfoMessages from '../../ItineraryUnitExtraInfoMessages';
import { logPhoenixDetailPDTEvents } from 'apps/holidays/src/utils/PhoenixDetailPDTTrackingUtils';
import { PDT_EVENT_TYPES } from 'apps/holidays/src/utils/HolidayPDTConstants';
export interface FlightRowProps {
  day: number;
  flightDetail: FlightDetail;
  itineraryUnit: ItineraryUnit;
  accessRestriction: ComponentAccessRestriction | null | undefined;
  onViewDetailPress: (
    pos: number,
    hotelSellableId: any,
    day: any,
    lastPageName: string,
    lob: string,
  ) => void;
  openFlightListingPage: (flightSequence: number) => void;
  removeFlights: () => void;
  ifFlightGroupFailed: boolean;
  showOverlay: (key: any, data: any) => void,
  hideOverlays: (data: any) => void,
  clearOverlays: () => void
  flightReqParams: Object;
  trackLocalPageLoadEvent: Function;
  lastPageName: string;
  onPackageComponentToggle: Function;
  destinationName: string;
  onComponentChange: Function;
  pricingDetail: Object;
  subtitleData: [];
  roomDetails: Object;
  fromPresales: boolean;
  showBottomDivider: boolean;
  packageContent: {
    flightContent: {
      baggageInfoMap: Object;
    };
  };
  packageDetailDTO: {
    dynamicPackageId: string;
  };
}

const FlightRow = (props: FlightRowProps) => {
  const {
    ifFlightGroupFailed, day, flightDetail, itineraryUnit,
    accessRestriction, onViewDetailPress, packageContent,
    removeFlights, flightReqParams, packageDetailDTO, trackLocalPageLoadEvent,
    lastPageName, onPackageComponentToggle, destinationName, onComponentChange, pricingDetail,
    subtitleData, roomDetails, fromPresales = false, showOverlay, hideOverlays, clearOverlays, showBottomDivider = true,
  } = props || {};

  const { text, flight }: ItineraryUnit = itineraryUnit;
  const sellableId: string = flight.sellableId;
  const flightObject: Flights | null = getFlightObject(flightDetail, sellableId);
  const { flightMetadataDetail = {} } = flightObject || {};
  const {
    checkInBaggage = true,
    isDummy = false,
    isOvernight = false,
    flightExtraInfo = [],
  } = flightMetadataDetail || {};

  if (!flightObject) {
    return null;
  }
  const flightLegs: FlightLeg[] = flightObject.flightLegs;
  const dur: number = flightObject.duration;
  const stops: number = flightObject.stops;
  const duration: string = getFlightDuration(dur);
  const showBaggageInfo = ischeckinBaggageInfoAvailable(flightMetadataDetail);


  const captureClickEvents = ({eventName = '', suffix = ''}) => {
    const value = eventName + suffix;
    logPhoenixDetailPDTEvents({
      actionType : PDT_EVENT_TYPES.buttonClicked,
      value : value.replace(/_/g, '|')
    })
    trackPhoenixDetailLocalClickEvent({
      eventName,
      suffix,
      prop1: `base:${itineraryUnitTypes.FLIGHT}`,
    })
  }


  const openFlightPage = ({ replace = false, showUpgradeableFlights = false } = {}) => {
    const { flightSelections, overnightDelays }: FlightListingRequest = flightReqParams;
    const requestParams: FlightListingRequest = {};
    if (flightObject.flightSequence) {
      requestParams.listingFlightSequence = flightObject.flightSequence;
    }
    if (flightSelections && flightSelections.length > 0) {
      requestParams.flightSelections = flightSelections;
    }
    if (overnightDelays) {
      requestParams.overnightDelays = overnightDelays;
    }
    captureClickEvents({
      eventName: showUpgradeableFlights ? 'upgrade_flights_' : 'change_',
      suffix: showUpgradeableFlights
        ? `${day}`
        : `${itineraryUnitTypes.FLIGHT}_${destinationName}_${day}`,
    });
    const flightListingProps = {
      flightRequestObject: requestParams,
      dynamicId: packageDetailDTO.dynamicPackageId,
      pricingDetail: pricingDetail,
      onComponentChange: onComponentChange,
      onPackageComponentToggle: onPackageComponentToggle,
      subtitleData: subtitleData,
      accessRestriction: accessRestriction,
      lastPage: lastPageName,
      packageDetailDTO,
      roomDetails,
      trackLocalClickEvent,
      trackLocalPageLoadEvent,
      isFlightFailed: ifFlightGroupFailed,
      showOverlay,
      hideOverlays,
      clearOverlays,
      showUpgradeableFlights,
      flightUpgradeDetails: flightDetail?.flightUpgradeDetail || {},
      back,
      };
      if(replace) {
        hideOverlays([Overlay.FLIGHT_DETAIL])
      }
      holidayNavigationPush({
        props: flightListingProps,
        pageKey: HOLIDAY_ROUTE_KEYS.FLIGHT_LISTING,
        overlayKey: Overlay.FLIGHT_OVERLAY,
        navigationFunction: replace ? HolidayNavigation.replace : HolidayNavigation.push,
        hideOverlays,
        showOverlay,
      });
  };

  const back = () => {
    hideOverlays([Overlay.FLIGHT_DETAIL]);
  };

  const openFlightDetailPage = () => {
    captureClickEvents({
      eventName: 'view_',
      suffix: `${itineraryUnitTypes.FLIGHT}_${destinationName}_${day}`,
    });
    const { flightSelections, overnightDelays }: FlightListingRequest = flightReqParams;
    const data = { initialScrollIndex: 0, flights: [], roundTrip: false };
    const flightName = ['Departure Flights', 'Return Flights'];
    let heading = '';
    const len = flightSelections ? flightSelections.length : 2;
    if (flightObject.flightSequence < len) {
      heading = flightName[0];
    } else {
      heading = flightName[1];
    }
    data.flights.push({
      heading: heading,
      flight: flightObject,
    });
    const requestObject = createBaggageDetailRequestBody(
      flightSelections,
      packageDetailDTO.dynamicPackageId,
      overnightDelays,
    );
    let baggageInfo = null;
    if (
      packageContent &&
      packageContent.flightContent &&
      packageContent.flightContent.baggageInfoMap
    ) {
      baggageInfo = packageContent.flightContent.baggageInfoMap;
    }

    const flightDetailProps = {
      data: data,
      onUpdatePress: () => {},
      shortListCardData: null,
      onRemovePress: () => remove(),
      requestObject: requestObject,
      subtitleData: subtitleData,
      accessRestriction: accessRestriction,
      showOptions: true,
      baggageInfo: baggageInfo,
      trackLocalClickEvent: trackLocalClickEvent,
      onBackPress: () => openFlightPage({ replace: true }),
      back,
      onChangePress: () => openFlightPage({ replace: true}),
    };
    holidayNavigationPush({
      props: flightDetailProps,
      pageKey: HOLIDAY_ROUTE_KEYS.FLIGHT_DETAIL,
      overlayKey: Overlay.FLIGHT_DETAIL,
      showOverlay,
      hideOverlays,
    });
  };

  const onViewDetail = (
    pos: number,
    hotelSellableId: any,
    day: any,
    lastPageName: string,
    lob: string,
  ) => {
    const showCarouselView = fromPresales ? false : getEnableCarouselViewDetail();
    showCarouselView
      ? onViewDetailPress(pos, hotelSellableId, day, lastPageName, lob)
      : openFlightDetailPage();
  };

  const remove = () => {
    isMobileClient() ? HolidayNavigation.pop() : clearOverlays();
    captureClickEvents({
      eventName: 'remove',
      suffix: `${day}_flights_confirm`,
    });
    removeFlights();
  };

  if (isDummy) {
    return (
      <DummyFlightRow
        flightObject={flightObject}
        itineraryUnit={itineraryUnit}
        removeFlights={removeFlights}
        day={day}
      />
    );
  }
  const borderColor = showBottomDivider ? holidayColors.grayBorder : holidayColors.white;
  return (
    <View style={[styles.flightRow, { borderColor }]}>
      <View
        style={[styles.headingContainer, { marginBottom: flightObject?.overnightLabel ? 10 : 20 }]}
      >
        <View style={styles.headingText}>
          <HtmlHeadingV2
            htmlText={`${text} -${duration}`}
            style={dayPlanRowHeadingStyles}
            mWebStyle={StyleSheet.flatten(dayPlanRowHeadingStyles.heading)}
          />
        </View>
        <View style={styles.baggageIconContainer}>
          {showBaggageInfo ? <BaggageIconDetail checkInBaggage={checkInBaggage} /> : null}
        </View>
      </View>
      <FlightCards
        ifFlightGroupFailed={ifFlightGroupFailed}
        day={day}
        flightLegs={flightLegs}
        stops={stops}
        accessRestriction={accessRestriction}
        onViewDetailPress={onViewDetail}
        sellableId={sellableId}
        openFlightListingPage={openFlightPage}
        removeFlights={removeFlights}
      />
      {/* Add All Flight Related Messages in the below Component */}
      <ItineraryUnitExtraInfoMessages extraInfo={flightExtraInfo} />
      {/* Upgrade Flight Messages in the below Component */}
      <FlightMessages
        flightDetail={flightDetail}
        flightObject={flightObject}
        fromPresales={fromPresales}
        openFlightPage={openFlightPage}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  flightRow: {
    ...dayPlanRowContainerStyle,
  },
  headingContainer: {
    flexDirection: 'row',
  },
  headingText: {
    flex: 1,
    flexGrow: 1,
    flexWrap: 'wrap',
    flexDirection: 'row',
  },
  upgradeFlightContainer: {
    marginLeft: 'auto',
  },
  baggageIconContainer: {
    marginLeft: 'auto',
  },
});

export default FlightRow;
