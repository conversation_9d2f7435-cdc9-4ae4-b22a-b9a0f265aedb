import { Text, View } from 'react-native';
import styles from './FlightCardStyles';
import HolidayImageHolder from '../../../../Common/Components/HolidayImageHolder';
import { getAirlineIconUrl } from '../../FlightDetailPage/FlightListing/FlightsUtils';
import React from 'react';

const AirlineDetails = ({ airlineCode, flightId }) => (
  <View style={styles.airlineDetails}>
    <HolidayImageHolder
      imageUrl={getAirlineIconUrl(airlineCode)}
      style={styles.airlineIcon}
      resizeMode={'contain'}
    />
    <Text style={styles.airlineCode}>{flightId}</Text>
  </View>
);

export default React.memo(AirlineDetails);
