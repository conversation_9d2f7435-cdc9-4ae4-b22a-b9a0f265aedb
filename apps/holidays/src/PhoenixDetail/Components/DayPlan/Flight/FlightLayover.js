import React from 'react';
import { Text, View } from 'react-native';
import { getFlightDuration } from '../../../Utils/PhoenixDetailUtils';
import styles from './FlightCardStyles';

const FlightLayover = ({ duration, airport, flightChange, airportCode, style }) => {
  if (flightChange) {
    return (
      <View style={[styles.layover, style]}>
        <Text>Change flight | {airport}</Text>
      </View>
    );
  } else {
    const layoverText = airportCode ? `${airportCode}, ${airport}` : airport;
    return (
      <View style={[styles.layover, style]}>
        <Text style={styles.layoverText2}>
          <Text style={styles.layoverText1}>{getFlightDuration(duration)}  </Text>
          Layover in {layoverText}
        </Text>
      </View>
    );
  }
};

export default React.memo(FlightLayover);
