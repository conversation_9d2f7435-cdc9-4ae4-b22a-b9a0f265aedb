import { StyleSheet } from 'react-native';
import { fontStyles } from '../../../../Styles/holidayFonts';
import { holidayColors } from '../../../../Styles/holidayColors';
import { fonts } from '@mmt/legacy-commons/Styles/globalStyles';
import { actionStyle } from '../dayPlanStyles';

const styles = StyleSheet.create({
  flightDetails: {
    flexDirection: 'row',
  },
  airlineDetails: {
    alignItems: 'center',
    marginRight: 10,
    maxWidth: 54,
  },
  airlineIcon: {
    height: 36,
    width: 36,
    borderRadius:8,
  },
  airlineCode: {
    ...fontStyles.labelSmallRegular,
    color: holidayColors.gray,
    marginTop: 3,
  },
  flightTimings: {
    flex: 1,
    flexDirection: 'row',
  },
  flightTime: {
    ...fontStyles.labelBaseBlack,
    color: holidayColors.black,
  },
  flightLocation: {
    ...fontStyles.labelSmallRegular,
    color: holidayColors.gray,
  },
  flightDate: {
    ...fontStyles.labelSmallBold,
    color: holidayColors.gray,
  },
  flightRoute: {
    position: 'relative',
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 4,
  },
  flightRouteLine: {
    position: 'absolute',
    width: '100%',
    height: 3,
    backgroundColor: holidayColors.grayBorder,
    top: 6,
  },
  flightRouteBullet: {
    borderWidth: 1,
    borderColor: holidayColors.gray,
    backgroundColor: holidayColors.white,
    width: 5,
    height: 5,
    borderRadius: 4,
    marginTop: 5,
  },
  airplaneIcon: {
    height: 15,
    width: 15,
    tintColor: holidayColors.black,
  },
  moreFlights: {
    fontSize: 10,
    fontFamily: fonts.medium,
    color: holidayColors.gray,
    textAlign: 'center',
    marginTop: 3,
  },
  footer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginTop: 12,
  },
  anchorTextUnavailable: {
    ...actionStyle,
    color: holidayColors.red,
  },
  layover: {
    height: 27,
    width: '100%',
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: holidayColors.lightGray2,
    marginVertical: 19,
    borderRadius: 5,
  },
  layoverText1: {
    ...fontStyles.labelSmallBold,
    letterSpacing: 0,
    color: holidayColors.gray,
  },
  layoverText2: {
    ...fontStyles.labelSmallRegular,
    letterSpacing: 0,
    color: holidayColors.black,
  },
});

export default styles;
