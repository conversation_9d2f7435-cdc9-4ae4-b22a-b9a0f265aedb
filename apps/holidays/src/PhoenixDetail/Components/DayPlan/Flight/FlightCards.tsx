import React from "react";
import { ComponentAccessRestriction, FlightLeg } from "../../../Types/PackageDetailApiTypes";
import { FlatList } from "react-native";
import FlightCard from "./FlightCard";

interface FlightListingParameters {
  replace: boolean;
  showUpgradeableFlights?: boolean;
}

interface FlightCardsProps {
  day: number;
  flightLegs: FlightLeg[];
  stops: number;
  accessRestriction: ComponentAccessRestriction | null | undefined;
  onViewDetailPress: (pos: number, sellableId: any, day: any, lastpagename: string, lob: string) => void;
  sellableId: string;
  openFlightListingPage: (parameters: FlightListingParameters) => void;
  removeFlights: () => void;
  ifFlightGroupFailed: boolean;
}

export const FlightCards = (flightCardProps: FlightCardsProps) => {
  const { flightLegs } = flightCardProps || {};
  const renderFlightCard = ({ item, index }) => (
    <FlightCard
      key={index}
      flightCardProps={flightCardProps}
      flightData={item}
      index={index} />
  );

  const keyExtractor = (item, index) => index.toString();

  return (
    <FlatList
      data={flightLegs}
      renderItem={renderFlightCard}
      keyExtractor={keyExtractor}
      ListEmptyComponent={null}
    />
  );
};
