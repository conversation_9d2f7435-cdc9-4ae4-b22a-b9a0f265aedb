import FlightLayover from './FlightLayover';
import React from 'react';
import { FlatList } from 'react-native';

const Layovers = ({ via, viaList, stops, index, flightLegs, to, toAirportCode }) => {
  const renderItem = ({ item, index }) => (
    <FlightLayover
      key={index}
      duration={item.duration}
      airport={item.airport}
      flightChange={item.flightChange}
    />
  );

  if (via && viaList) {
    return (
      <FlatList
        data={viaList}
        renderItem={renderItem}
        keyExtractor={(_, index) => index.toString()}
      />
    );
  }

  if (stops > 0 && !via && (index < flightLegs.length - 1)) {
    return (
      <FlightLayover
        duration={flightLegs[index + 1].layoverDuration}
        airport={to}
        airportCode={toAirportCode}
        flightChange={false}
      />
    );
  }

  return [];
};

export default React.memo(Layovers);
