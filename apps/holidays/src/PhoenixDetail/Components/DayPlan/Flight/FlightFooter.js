import { Text,  View } from 'react-native';
import styles from './FlightCardStyles';
import AtomicCss from '@mmt/legacy-commons/Styles/AtomicCss';
import DynamicCoachMark from '@mmt/legacy-commons/Common/Components/CoachMarks/DynamicCoachMark';
import { actionSeperator, actionStyle } from '../dayPlanStyles';
import { OVERLAY_CAROUSAL_POSITION } from '../../../Utils/PheonixDetailPageConstants';
import { HOLIDAY_ROUTE_KEYS } from '../../../../Navigation';
import { itineraryUnitTypes } from '../../../DetailConstants';
import React from 'react';
import TextButton from '../../../../Common/Components/Buttons/TextButton';

const FlightFooter = (props) => {

  const {
    ifFlightGroupFailed,
    removeTransferCardConfirmation,
    openFlightListingPage,
    onViewDetailPress,
    sellableId,
    accessRestriction,
    day,
  } = props || {};

  const changeFlightRestricted = accessRestriction ? accessRestriction.changeFlightRestricted : false;
  const removeFlightRestricted = accessRestriction ? accessRestriction.removeFlightRestricted : false;
  const availabilityText = !ifFlightGroupFailed ? 'View Details' : 'Currently Unavailable';
  const availabilityTextStyle = ifFlightGroupFailed ? styles.anchorTextUnavailable : actionStyle;

  const onApplyClick = () => {
    openFlightListingPage({ replace: false });
  };

  const onViewDetailsClicked = () => {
    onViewDetailPress(OVERLAY_CAROUSAL_POSITION.FLIGHT, sellableId, day, HOLIDAY_ROUTE_KEYS.DETAIL, itineraryUnitTypes.FLIGHT);
  };

  return (<View style={styles.footer}>
    <View style={AtomicCss.alignCenter}>
      {(!removeFlightRestricted || !changeFlightRestricted)
        && <DynamicCoachMark cueStepKey="changeOrRemove"
                          offsetHeight={70}
                          offsetWidth={70}
                          extraInfo={{ from: 'flightsRow', day }}>

          <View style={AtomicCss.flexRow}>
            {!removeFlightRestricted &&
              <TextButton
                buttonText="Remove"
                handleClick={removeTransferCardConfirmation}
                btnTextStyle={actionStyle}
              />
              }

            {!changeFlightRestricted && !removeFlightRestricted
              && <Text style={actionSeperator}>|</Text>}

            {!changeFlightRestricted &&
              <TextButton
                buttonText="Change"
                handleClick={onApplyClick}
                btnTextStyle={actionStyle}
              />
              }

          </View>
        </DynamicCoachMark>}
    </View>
    <TextButton
      buttonText={availabilityText}
      handleClick={onViewDetailsClicked}
      btnTextStyle={availabilityTextStyle}
      disabled={ifFlightGroupFailed}
    />
  </View>);
};

export default React.memo(FlightFooter);
