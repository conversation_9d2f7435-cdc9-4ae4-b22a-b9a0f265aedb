import React, { useMemo } from 'react';
import { FlatList, StyleSheet, Text, View } from 'react-native';
import LinearGradient from 'react-native-linear-gradient';
import { fonts, colors } from '@mmt/legacy-commons/Styles/globalStyles';
import { DayStripItem } from '../../Types/PackageDetailApiTypes';
import { DAY_STRIP_COMPONENTS } from '../../Utils/PheonixDetailPageConstants';
import { holidayColors } from 'mobile-holidays-react-native/src/Styles/holidayColors';
import { fontStyles } from '../../../Styles/holidayFonts';
import { marginStyles } from '../../../Styles/Spacing';
import { itineraryUnitSubTypes, itineraryUnitTypes } from '../../DetailConstants';
import { TABS } from './Sorter';
import { getFlightObject } from '../../Utils/PhoenixDetailUtils';
import DottedLine from 'mobile-holidays-react-native/src/Common/Components/DottedLine';

interface DayPlanHeaderProps {
  index: number;
  dayStripMap: Map<string, DayStripItem>;
  itineraryUnits: Object;
  currentActivePlanOnItinerary: any;
  destination: any;
}

const DayPlanHeader = ({
  index: day,
  dayStripMap,
  itineraryUnits,
  currentActivePlanOnItinerary,
  destination,
  packageDetail = {},
}: DayPlanHeaderProps): JSX.Element => {
  const data = Array.from(dayStripMap.values());

  if (data.length === 0) {
    data.push({ count: 1, name: 'Hotel', selected: true });
  }

  const renderContent = (item: any, index: any) => {
    const { count, name, selected } = item || {};
    const value =
      name === DAY_STRIP_COMPONENTS.ACTIVITIES && count === 1
        ? DAY_STRIP_COMPONENTS.ACTIVITY
        : name;
    return (
      <View style={styles.dayPlanItem}>
        {(name === DAY_STRIP_COMPONENTS.ACTIVITY || name === DAY_STRIP_COMPONENTS.ACTIVITIES) && (
          <Text style={[styles.dayPlanItemText, selected ? styles.dayPlanItemTextBold : {}]}>
            {count}{' '}
          </Text>
        )}
        <Text style={[styles.dayPlanItemText, selected ? styles.dayPlanItemTextBold : {}]}>
          {value}
        </Text>
      </View>
    );
  };

  const renderSeparator = () => {
    return <View style={styles.dayPlanItemBullet} />;
  };

  const subtitle = useMemo(() => {
    let selectedFlight = itineraryUnits.filter(
      (data) => data.itineraryUnitType === itineraryUnitTypes.FLIGHT,
    )[0];
    if (!selectedFlight) {
      const selectedCommute = itineraryUnits.filter(
        (data) => data.itineraryUnitType === itineraryUnitTypes.COMMUTE,
      )[0];

      selectedFlight = selectedCommute?.commute?.filter(
        (data) => data.itineraryUnitType === itineraryUnitTypes.FLIGHT && data.isRemoved !== true,
      )[0];
    }
    if (
      selectedFlight &&
      (currentActivePlanOnItinerary === TABS.FLIGHT || currentActivePlanOnItinerary === TABS.DAY)
    ) {
      const selectedFlightData = getFlightObject(
        packageDetail?.flightDetail,
        selectedFlight?.flight?.sellableId || '',
      );

      if (selectedFlight?.itineraryUnitSubType === itineraryUnitSubTypes.FLIGHT_ARRIVE) {
        return `Arrival in ${selectedFlightData?.toAirport?.airportCity}`;
      }
      if (selectedFlight?.itineraryUnitSubType === itineraryUnitSubTypes.FLIGHT_DEPART) {
        return `Departure from ${selectedFlightData?.fromAirport?.airportCity}`;
      }
    }

    const selectedTransfer = itineraryUnits.filter(
      (carData) =>
        carData.itineraryUnitType === itineraryUnitTypes.CAR &&
        carData.itineraryUnitSubType === itineraryUnitSubTypes.CAR_INTERCITY,
    )[0];
    if (selectedTransfer && currentActivePlanOnItinerary === TABS.DAY) {
      return selectedTransfer.shortText;
    }

    return destination.name;
  }, [day, itineraryUnits, destination, currentActivePlanOnItinerary]);

  return (
    <View style={styles.dayPlanHeaderContainer}>
      <View style={styles.dayPlanHeader}>
        <LinearGradient
          start={{ x: 1.0, y: 0.0 }}
          end={{ x: 0.0, y: 1.0 }}
          colors={[holidayColors.orangeGradient, holidayColors.orangeGradientDark]}
          style={styles.dayPlanNumber}
        >
          <Text style={styles.dayPlanNumberText}>Day {day}</Text>
        </LinearGradient>
        {/* {!!subtitle && <Text style={styles.dayPlanIncludedText}>{subtitle} </Text>}
        {!!subtitle && <View style={styles.seperator} />} */}
        <Text style={styles.dayPlanIncludedText}>Includes: </Text>
        <FlatList
          data={data}
          horizontal
          renderItem={({ item, index }) => renderContent(item, index)}
          keyExtractor={(item) => item.name}
          ItemSeparatorComponent={renderSeparator}
          initialNumToRender={7}
          showsHorizontalScrollIndicator={false}
        />
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  dayPlanHeaderContainer: {
    borderBottomWidth: 1,
    borderBottomColor: holidayColors.grayBorder,
  },
  dayPlanHeader: {
    flexDirection: 'row',
    alignSelf: 'flex-start',
    alignItems: 'center',
    width: '98%',
    ...marginStyles.mv8,
  },
  dayPlanNumber: {
    minHeight: 28,
    alignItems: 'center',
    justifyContent: 'center',
    paddingHorizontal: 15,
    marginHorizontal: 10,
    borderRadius: 20,
  },
  dayPlanNumberText: {
    ...fontStyles.labelSmallBold,
    color: holidayColors.white,
  },
  dayPlanIncludedText: {
    ...fontStyles.labelSmallBold,
    color: holidayColors.gray,
    display: 'flex',
    alignItems: 'center',
  },
  dayPlanItemText: {
    ...fontStyles.labelSmallRegular,
    color: holidayColors.gray,
  },
  dayPlanItemTextBold: {
    ...fontStyles.labelSmallBold,
    color: holidayColors.gray,
  },
  dayPlanItem: {
    flexDirection: 'row',
    alignItems: 'center',
    minHeight: 28,
  },
  dayPlanItemBullet: {
    backgroundColor: holidayColors.lightGray,
    borderRadius: 4,
    width: 5,
    height: 5,
    ...marginStyles.mh6,
    ...marginStyles.mt12,
  },
  seperator: {
    width: 1,
    height: '50%',
    backgroundColor: holidayColors.grayBorder,
    ...marginStyles.mr8,
    ...marginStyles.ml4,
  },
});

export default DayPlanHeader;
