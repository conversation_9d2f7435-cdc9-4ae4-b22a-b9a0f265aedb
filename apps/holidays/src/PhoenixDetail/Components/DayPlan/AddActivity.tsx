import React from 'react';
import LinearGradient from 'react-native-linear-gradient';
import { Image, StyleSheet, Text, View } from 'react-native';
import SecondaryButton from '../../../Common/Components/Buttons/SecondaryButton';
import { colors, fonts } from '@mmt/legacy-commons/Styles/globalStyles';
import { smallHeightSeperator } from 'mobile-holidays-react-native/src/Styles/holidaySpacing';
import { holidayColors } from 'mobile-holidays-react-native/src/Styles/holidayColors';
import { fontStyles } from 'mobile-holidays-react-native/src/Styles/holidayFonts';

const AddActivity = ({ openActivityListingPage }) => {
  return (
    <View style={styles.container}>
      <LinearGradient
        start={{ x: 1.0, y: 1.0 }}
        end={{ x: 0.0, y: 1.0 }}
        colors={['#e6faf7', '#e9f1d1']}
        style={styles.viewContainer}>
        <View style={[styles.contentRow, styles.bannerContent]}>
          <Text style={styles.body}>Spend time at Leisure or add an activity to your day!</Text>
        </View>
      </LinearGradient>
      <View style={styles.activityBtnWrap}>
        <SecondaryButton
          buttonText={'Add Activity to day'}
          handleClick={openActivityListingPage}
          btnContainerStyles={styles.viewActivityBtn}
        />
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    paddingBottom: 22,
    backgroundColor: holidayColors.white,
    position: 'relative',
    ...smallHeightSeperator,
  },
  viewContainer: {
    borderRadius: 4,
  },
  contentRow: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    alignItems: 'center',
    justifyContent: 'center',
  },
  body: {
    ...fontStyles.labelBaseRegular,
    letterSpacing: 0,
    color: holidayColors.gray,
  },
  activityBtnWrap: {
    marginTop: -16,
    alignItems: 'center',
    width: '100%',
  },
  viewActivityBtn: {
    width: 250,
  },
  bannerContent: {
    paddingHorizontal: 18,
    paddingTop: 13,
    paddingBottom: 24,
  },
});

export default AddActivity;
