import React, { useMemo } from 'react';
import { StyleSheet, View } from 'react-native';
import { holidayColors } from '../../../../Styles/holidayColors';
import { getImageUrl, IMAGE_ICON_KEYS } from '../../../../Common/Components/HolidayImageUrls';
import { itineraryUnitTypes } from '../../../DetailConstants';
import { marginStyles } from '../../../../Styles/Spacing';
import { last, cloneDeep } from 'lodash';
import { getShowNewActivityDetail } from 'mobile-holidays-react-native/src/utils/HolidaysPokusUtils';

/* Components */
import FlightRowV2 from '../../ItineraryV2/Flight/FlightRow';
import TransferRowV2 from '../../ItineraryV2/Transfer/TransferRow';
import NoCommuteV2 from '../../ItineraryV2/Commute/NoCommute';
import NoCommute from '../../combo/NoCommute';
import HolidayImageHolder from '../../../../Common/Components/HolidayImageHolder';
import FlightRow from '../Flight/FlightRow';
import TransferRow from '../transferRow';
import ViewTransportOptions from './ViewTransportOptions';

const Commute = (props) => {
  const {
    ifFlightGroupFailed,
    day,
    city = '',
    flightDetail,
    itineraryUnit,
    accessRestriction,
    onViewDetailPress,
    packageContent,
    removeFlights,
    flightReqParams,
    packageDetailDTO,
    trackLocalPageLoadEvent,
    lastPageName,
    onPackageComponentToggle,
    destinationName,
    onComponentChange,
    pricingDetail,
    subtitleData,
    roomDetails,
    fromPresales = false,
    trackLocalClickEvent,
    packageDetail,
    detailData,
    commuteCtaObj,
        showOverlay,
        hideOverlays,
        clearOverlays,
    defaultCollapsedState,
  } = props || {};
  const { commute = [], isCommuteChangeable = false } = itineraryUnit || {};
  const showNewActivityDetail = getShowNewActivityDetail();
  const clonedCommute = cloneDeep(commute);
  if (isCommuteChangeable) {
    // This is added to stop adding add transport option when it is already present after re-renering
    const addTransportPresent = commute?.filter((item) => item.itineraryUnitType === itineraryUnitTypes.ADD_TRANSPORT_OPTION);
    if(addTransportPresent?.length <= 0) {
      commute.push({
        itineraryUnitType: itineraryUnitTypes.ADD_TRANSPORT_OPTION,
        '@id': Math.floor(Math.random() * 1000000),
      });
    }
  }

  const renderFlightRow = (item, index) => {
    const { isRemoved = false, subText, removedText } = item || {};
    const commuteLength = clonedCommute?.length || 0;
    const onlyOneItem = commuteLength === 1;
    const isLastCommute = commuteLength === index + 1;
    const isLastItem = commute?.length === index + 1;
    const isFirstItem = index === 0;
    const showDivider = clonedCommute.length - 1 !== index;
    const backgroundColor = isFirstItem ? holidayColors.white : holidayColors.grayBorder;
    const showCommuteBorder = !(onlyOneItem || isLastCommute)
    
    const FlightRowComponent = showNewActivityDetail ? FlightRowV2 : FlightRow;
    const NoCommuteComponent = showNewActivityDetail ? NoCommuteV2 : NoCommute;
    
    const renderFlightCommute = () => {
      return isRemoved ? (
        <View style={{...marginStyles.mt30, flex: 1}}>
          <NoCommuteComponent
            itineraryUnit={item}
            day={day}
            city={city}
            subText={subText}
            removedText={removedText}
            showBottomDivider={showDivider}
            showNewActivityDetail={showNewActivityDetail}
            defaultCollapsedState={defaultCollapsedState}
            type={'Flight'}
          />
        </View>
      ) : (
        <View style={showNewActivityDetail ? {...marginStyles.mt10} : styles.rowContainer}>
          <FlightRowComponent
            key={index}
            itineraryUnit={item}
            ifFlightGroupFailed={ifFlightGroupFailed}
            day={day}
            city={city}
            flightDetail={flightDetail}
            accessRestriction={accessRestriction}
            flightReqParams={flightReqParams}
            onViewDetailPress={onViewDetailPress}
            packageContent={packageContent}
            subtitleData={subtitleData}
            pricingDetail={pricingDetail}
            packageDetailDTO={packageDetailDTO}
            removeFlights={removeFlights}
            trackLocalClickEvent={trackLocalClickEvent}
            onComponentChange={onComponentChange}
            onPackageComponentToggle={onPackageComponentToggle}
            destinationName={destinationName}
            lastPageName={lastPageName}
            roomDetails={roomDetails}
            trackLocalPageLoadEvent={trackLocalPageLoadEvent}
            fromPresales={fromPresales}
            showBottomDivider={showDivider}
            showOverlay={showOverlay}
            hideOverlays={hideOverlays}
            clearOverlays={clearOverlays}
            isFlightRowFromCommute={true}
            defaultCollapsedState={defaultCollapsedState}
            showCommuteBorder={showCommuteBorder}
          />
        </View>
      );
    };
    return (
      <View style={styles.container} key={item['@id']}>
        {showNewActivityDetail ? (
          renderFlightCommute()
        ) : (
          <View
            style={[
              styles.innerContainer,
              {
                borderColor:
                  onlyOneItem || isLastCommute
                    ? holidayColors.transparent
                    : holidayColors.grayBorder,
              },
            ]}
          >
            <View>
              <View style={[styles.lineAboveImage, { backgroundColor }]} />
              <HolidayImageHolder
                imageUrl={getImageUrl(IMAGE_ICON_KEYS.FLIGHT_ICON_WHITE)}
                style={styles.image}
              />
            </View>
            {renderFlightCommute()}
          </View>
        )}

        {/*{isLastItem && !onlyOneItem && <View style={styles.dot}/>}*/}
      </View>
    );
  };

  const renderTransferRow = (item, index) => {
    const { isRemoved, subText, removedText } = item || {};
    const commuteLength = commute?.length || 0;
    const clonedCommuteLength = clonedCommute?.length;
    const onlyOneItem = clonedCommuteLength === 1;
    const isLastCommute = clonedCommuteLength === index + 1;
    const isLastItem = commute?.length === index + 1;
    const isFirstItem = index === 0;
    const showDivider = clonedCommuteLength - 1 !== index;
    const backgroundColor = isFirstItem ? holidayColors.white : holidayColors.grayBorder;
    const showCommuteBorder = !(onlyOneItem || isLastCommute)

    const TransferRowComponent = showNewActivityDetail ? TransferRowV2 : TransferRow;
    const NoCommuteComponent = showNewActivityDetail ? NoCommuteV2 : NoCommute;
    
    const renderTransportCommute = () => {
      return isRemoved ? (
        <View style={marginStyles.mt20}>
          <NoCommuteComponent
            day={day}
            city={city}
            itineraryUnit={item}
            subText={subText}
            removedText={removedText}
            showBottomDivider={showDivider}
            type={'Private Transfer'}
          />
        </View>
      ) : ( 
        <TransferRowComponent
          key={index}
          itineraryUnit={item}
          day={day}
          city={city}
          packageDetail={packageDetail}
          accessRestriction={accessRestriction}
          onViewDetailPress={onViewDetailPress}
          packageDetailDTO={packageDetailDTO}
          roomDetails={roomDetails}
          onComponentChange={onComponentChange}
          destinationName={destinationName}
          lastPageName={lastPageName}
          detailData={detailData}
          fromPresales={fromPresales}
          showBottomDivider={showDivider}
          isFromCommute={true}
          defaultCollapsedState={defaultCollapsedState}
          showCommuteBorder={showCommuteBorder}
          showOverlay={showOverlay}
          hideOverlays={hideOverlays}
        />
      );
    };
    return (
      <View style={styles.container} key={item['@id']}>
        {/* Since we need to show dotted line for old view so we need different view and new view is handled in Dayplanrow HOC  */}
        {showNewActivityDetail ? (
          renderTransportCommute()
        ) : (
          <View
            style={[
              styles.innerContainer,
              {
                borderColor:
                  onlyOneItem || isLastCommute
                    ? holidayColors.transparent
                    : holidayColors.grayBorder,
              },
            ]}
          >
            <View>
              <View style={[styles.lineAboveImage, { backgroundColor }]} />
              <HolidayImageHolder
                imageUrl={getImageUrl(IMAGE_ICON_KEYS.CAB_ICON_WHITE)}
                style={styles.image}
              />
            </View>
            {renderTransportCommute()}
          </View>
        )}

        {/*{isLastItem && !onlyOneItem && <View style={styles.dot}/>}*/}
      </View>
    );
  };

  const renderViewTransportOption = (item, index) => {
    const onlyOneItem = clonedCommute?.length <= 1;
    const isLastItem = commute?.length === index + 1;
    const isLastCommute = clonedCommute?.length === index + 1;
    return (
      <View style={[styles.container]} key={item['@id']}>
        {showNewActivityDetail ? (
          <View style={styles.viewTransportContainer}>
            <ViewTransportOptions commuteCtaObj={commuteCtaObj} />
          </View>
        ) : (
          <View
            style={[
              styles.innerContainer,
              { borderColor: holidayColors.transparent },
              clonedCommute.length === 0 ? marginStyles.mt10 : {},
              showNewActivityDetail ? marginStyles.mt10 : {},
            ]}
          >
            <ViewTransportOptions commuteCtaObj={commuteCtaObj} />
          </View>
        )}

        {/*{isLastItem && !onlyOneItem && <View style={styles.dot} />}*/}
      </View>
    );
  };

  const renderCommuteItem = (item, index) => {
    if (!item) {
      return [];
    }
    switch (item.itineraryUnitType) {
      case itineraryUnitTypes.FLIGHT:
        return renderFlightRow(item, index);
      case itineraryUnitTypes.CAR:
        return renderTransferRow(item, index);
      case itineraryUnitTypes.ADD_TRANSPORT_OPTION:
        return useMemo(() => renderViewTransportOption(item, index),[]);
      default:
        return []; // In case there are other types we're not handling, render nothing.
    }
  };

  if (commute) {
    return <View>{commute.map(renderCommuteItem)}</View>;
  }

  return [];
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: holidayColors.white,
  },
  innerContainer: {
    borderLeftWidth: 2,
    marginLeft: 30,
    flexDirection: 'row',
  },
  rowContainer: {
    width: '100%',
    flex:1,
    backgroundColor: holidayColors.white,
},
  itineraryV2CommuteStyles: {
    borderLeftWidth: 2,
    marginLeft: 30,
    flexDirection: 'row',
  },
  ninetyPercentView: {
    width: '96%',
  },
  dot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: holidayColors.lightGray,
    ...marginStyles.mt4,
    marginStart: 27,
  },
  image: {
    width: 22,
    height: 22,
    marginLeft: -11,
  },
  lineAboveImage: {
    width: 2,
    height: 20,
    marginLeft: -2,
  },
  divider: {
    height: 1,
    backgroundColor: holidayColors.grayBorder,
    marginLeft: 36,
  },
  viewTransportContainer: {
    ...marginStyles.mb10, 
    borderColor: holidayColors.grayBorder,
    borderBottomWidth: 1,
  },
});

export default Commute;
