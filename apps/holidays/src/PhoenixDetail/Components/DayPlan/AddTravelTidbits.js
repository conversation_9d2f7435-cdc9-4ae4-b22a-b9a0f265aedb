import React from 'react';
import { Image, StyleSheet, Text, View } from 'react-native';
import SecondaryButton from '../../../Common/Components/Buttons/SecondaryButton';
import { holidayColors } from 'mobile-holidays-react-native/src/Styles/holidayColors';
import { fontStyles } from 'mobile-holidays-react-native/src/Styles/holidayFonts';
import { marginStyles, paddingStyles } from '../../../Styles/Spacing';
import { TABS } from './Sorter';
import { largeHeightSeperator } from '../../../Styles/holidaySpacing';

const AddTravelTidbits = (props) => {
  const { openTravelTidbits, currentActivePlanOnItinerary } = props || {};
  const getName = () => {
    switch (currentActivePlanOnItinerary) {
      case TABS.DAY:
        return 'Activity';
      case TABS.COMMUTE:
        return 'Transfer';
      case TABS.MEALS:
        return 'Meal';
      default:
        return 'Activity';
    }
  };

  const getSubTitle = () => {
    switch (currentActivePlanOnItinerary) {
      case TABS.DAY:
        return 'an activity, transfer or meal';
      case TABS.COMMUTE:
        return 'a transfer';
      case TABS.MEALS:
        return 'a meal';
      case TABS.ACTIVITIES:
          return 'a activity';
      default:
        return 'an activity, transfer or meal';
    }
  };

  const TITLE = `Add ${getName()} to your day`;
  const SUBTITLE= `Spend the day at leisure or add ${getSubTitle()} to your day`;
  return (
    <View style={styles.container}>
      <View style={styles.innerContainer}>
        <View style={styles.header}>
          <Image accessibilityLabel={'Travel Tidbits'} source={require('../images/parachute.png')} style={styles.image} />
          <View style={styles.headerContainer}>
          <Text style={styles.headerText}>{TITLE}</Text>
          <Text style={styles.headerSubText}>{SUBTITLE}</Text>
          </View>
        </View>
        <SecondaryButton
          buttonText={'ADD TO DAY'}
          handleClick={openTravelTidbits}
          btnContainerStyles={styles.viewActivityBtn}
        />
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: holidayColors.white,
    ...largeHeightSeperator,
  },
  innerContainer: {
    margin: 16,
    backgroundColor: holidayColors.lightBlueBg,
    borderRadius: 8,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    ...paddingStyles.pa16,
  },
  headerContainer: {
    flexDirection: 'column',
    flex:1,
    ...marginStyles.ml16,
    ...marginStyles.mr16,
  },
  headerText:{
    ...fontStyles.labelBaseBold,
    letterSpacing: 0,
    color: holidayColors.black
  },
  headerSubText:{
    ...fontStyles.labelSmallRegular,
    letterSpacing: 0,
    color: holidayColors.gray,
    ...marginStyles.mt4,
    ...marginStyles.mr10,
    ...paddingStyles.pr10,
  },
  body: {
    ...fontStyles.labelBaseRegular,
    letterSpacing: 0,
    color: holidayColors.gray,
  },

  viewActivityBtn: {
    width: '90%',
    backgroundColor: holidayColors.lightBlueBg,
    borderRadius: 8,
    alignSelf: 'center',
    ...marginStyles.mb12,
  },
  image: {
    width: 50,
    height: 50,
  },
});

export default AddTravelTidbits;
