import React from 'react';
import {Image, StyleSheet, View, TouchableOpacity, Text} from 'react-native';
import PropTypes from 'prop-types';
import AtomicCss from '@mmt/legacy-commons/Styles/AtomicCss';
import {DETAIL_MAIN_IMAGE_SIZE, overlays} from '../../DetailConstants';
import {isNotNullAndEmptyCollection, isRawClient} from '../../../utils/HolidayUtils';
import ImageTileView from '../ImageTileView';
import VideoPlayer from '@mmt/legacy-commons/Common/Components/VideoPlayer';

const iconBack = require('@mmt/legacy-assets/src/iconBackWhite.webp');
const iconShare = require('@mmt/legacy-assets/src/iconShare.webp');
const iconLike = require('@mmt/legacy-assets/src/iconLike.webp');
const iconLikeFilled = require('@mmt/legacy-assets/src/white_heart.webp');

export const MAX_IMAGE_TILES = 4;

class VideoSection extends React.Component {
    constructor(props) {
      super(props, 'videoSection');
      this.state = {
        paused: this.props.videoPaused,
      };
    }

    componentWillReceiveProps(nextProps) {
      if (this.state.paused !== nextProps.videoPaused) {
        this.setState({
          paused: nextProps.videoPaused,
        });
      }
    }

    updateVideoPausedState = (pausedState) => {
      this.state.paused = pausedState;
    }

    getDefaultImageObj = (imageDetail) => {
      if (imageDetail && imageDetail.images && imageDetail.images.length > 0) {
        return imageDetail.images[0];
      }
      return {
        name: 'dummy',
        path: 'dummy',
      };
    };

    render() {
      let totalCount = 0;
      let moreCount = 0;
      const imageTiles = [];
      if (this.props.imageDetail && isNotNullAndEmptyCollection(this.props.imageDetail.images)) {
        totalCount = this.props.imageDetail.images.length;
        for (let i = 0; i < this.props.imageDetail.images.length; i += 1) {
          if (i < MAX_IMAGE_TILES) {
            imageTiles.push(this.props.imageDetail.images[i]);
          }
        }
      }
      if (totalCount > MAX_IMAGE_TILES) {
        moreCount = totalCount - MAX_IMAGE_TILES;
      }
      return (
        <View style={styles.container}>
          <View style={styles.media}>
            <VideoPlayer
              source={{uri: this.props.videoUrl}}
              resizeMode="stretch"
              disableBack
              disableFullscreen={false}
              disablePlayPause={false}
              muted
              showControls={false}
              paused={this.state.paused}
              posterResizeMode="stretch"
              style={styles.media}
              updateVideoPausedState={this.updateVideoPausedState}
              dash
            />
          </View>

          <View style={styles.imageContainer}>
            {imageTiles.map((imageTile, index) => {
                return (
                    <TouchableOpacity
                        activeOpacity={0.9}
                        onPress={() => this.props.togglePopup(overlays.IMAGE_OVERLAY)}>
                      <ImageTileView
                          imageObj={imageTile}
                          imageSize={DETAIL_MAIN_IMAGE_SIZE}
                          imageStyle={styles.mainImage}
                      />
                    </TouchableOpacity>
                );
            })
            }
            {moreCount > 0 &&
            <TouchableOpacity
              activeOpacity={0.9}
              onPress={() => this.props.togglePopup(overlays.IMAGE_OVERLAY)}
              style={styles.moreBtn}
            >
              <Text style={styles.btnText}>{moreCount}</Text>
              <Text style={styles.btnText}>MORE</Text>
            </TouchableOpacity>
            }

          </View>
          <View style={styles.controlWrapper}>
            <TouchableOpacity
              activeOpacity={0.7}
              style={styles.backWrapper}
              onPress={() => this.props.onBackPressed()}
            >
              <Image style={styles.iconBack} source={iconBack} />
            </TouchableOpacity>
            <View style={[AtomicCss.pushRight, AtomicCss.flexRow]}>
              {!isRawClient() &&
                <TouchableOpacity
                  activeOpacity={0.7}
                  style={styles.shareWrapper}
                  onPress={() => this.props.sharePackage()}
                >
                  <Image style={styles.iconShare} source={iconShare} />
                </TouchableOpacity>
                        }
              {!isRawClient() &&
                <TouchableOpacity
                  activeOpacity={0.7}
                  style={styles.likeWrapper}
                  onPress={() => this.props.updateShortListedPackage(!this.props.isShortListed)}
                >
                  <Image
                    style={styles.iconLike}
                    source={this.props.isShortListed ? iconLikeFilled : iconLike}
                  />
                </TouchableOpacity>
                        }
            </View>
          </View>
        </View>
      );
    }
}

const styles = StyleSheet.create({
  container: {
    position: 'relative',
    zIndex: 10,
    marginTop: -62,
    backgroundColor: '#fff',
  },
  mainImg: {
    width: '100%',
    height: 170,
  },
  backWrapper: {
    paddingRight: 5,
    paddingLeft: 5,
    paddingVertical: 15,
    marginRight: 'auto',
  },
  iconBack: {
    width: 30,
    height: 30,
  },
  iconShare: {
    width: 30,
    height: 30,
  },
  iconLike: {
    width: 30,
    height: 30,
  },
  shareWrapper: {
    paddingVertical: 15,
    paddingHorizontal: 5,
  },
  likeWrapper: {
    paddingVertical: 15,
    paddingHorizontal: 5,
  },
  controlWrapper: {
    position: 'absolute',
    top: 0,
    flexDirection: 'row',
    width: '100%',
    marginTop: 5,
    marginHorizontal: 2,
  },
  media: {
    width: '100%',
    height: 170,
  },
  imageContainer: {
    flexDirection: 'row',
    marginBottom: 11,
    paddingHorizontal: 15,
    marginTop: 15,
  },
  mainImage: {
    width: 55,
    height: 55,
    marginRight: 11,
  },
  moreBtn: {
    width: 55,
    height: 55,
    paddingTop: 9,
    backgroundColor: '#008cff',
    flexDirection: 'column',
    alignItems: 'center',
  },
  btnText: {
    color: '#fff',
    marginBottom: 5,
    fontFamily: 'Lato-Bold',
    fontSize: 12,


  },
});

VideoSection.propTypes = {
  togglePopup: PropTypes.func.isRequired,
  imageDetail: PropTypes.object.isRequired,
  videoUrl: PropTypes.string.isRequired,
  isShortListed: PropTypes.bool.isRequired,
  updateShortListedPackage: PropTypes.func.isRequired,
  sharePackage: PropTypes.func.isRequired,
  onBackPressed: PropTypes.func.isRequired,
  videoPaused: PropTypes.bool.isRequired,
};

export default VideoSection;
