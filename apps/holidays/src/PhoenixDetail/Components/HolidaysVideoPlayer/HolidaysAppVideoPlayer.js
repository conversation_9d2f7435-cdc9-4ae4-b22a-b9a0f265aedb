import React from 'react';
import AppVideoPlayer from '@mmt/legacy-commons/Common/Components/VideoPlayer/app';
import styles from '@mmt/legacy-commons/Common/Components/VideoPlayer/app/appPlayerCss';
import {Animated, Image, ImageBackground, Text, TouchableOpacity, View} from 'react-native';
import {ViewGallery} from '../../PhoenixDetailCss';

export default class HolidaysAppVideoPlayer extends AppVideoPlayer {
  renderSeekbar() {
    return (
      <View style={[styles.seekbar.container, this.props.showGallery ? {width: '25%'} : {}]}>
        <View
          style={styles.seekbar.track}
          onLayout={event => this.player.seekerWidth = event.nativeEvent.layout.width}
        >
          <View style={[
            styles.seekbar.fill,
            {
              width: this.state.seekerFillWidth,
              backgroundColor: this.props.seekColor || '#FFF',
            },
          ]}
          />
        </View>
        <View
          style={[
            styles.seekbar.handle,
            {left: this.state.seekerPosition},
          ]}
          {...this.player.seekPanResponder.panHandlers}
        >
          <View style={[
            styles.seekbar.circle,
            {backgroundColor: this.props.seekColor || '#FFF'}]}
          />
        </View>
      </View>
    );
  }

  renderBottomControls() {
    const {isFullscreen} = this.state;
    const timerControl = this.props.disableTimer ? this.renderNullControl() : this.renderTimer();
    const seekbarControl = this.props.disableSeekbar ? this.renderNullControl() : this.renderSeekbar();
    const playPauseControl = this.props.disablePlayPause ? this.renderNullControl() : this.renderPlayPause();
    const volumeControl = this.props.disableVolume ? this.renderNullControl() : this.renderVolume();
    const fullscreenControl = this.props.disableFullscreen ? this.renderNullControl() : this.renderFullscreen();
    const fullScreenBottomControlsStyle = isFullscreen ? styles.controls.bottomControlGroupFS : {};
    return (
      <Animated.View style={[
        styles.controls.bottom,
        {
          opacity: this.animations.bottomControl.opacity,
          marginBottom: this.animations.bottomControl.marginBottom,
        },
      ]}
      >
        <ImageBackground
          source={require('@mmt/legacy-assets/src/img/bottom-vignette.webp')}
          style={[styles.controls.column]}
          imageStyle={[styles.controls.vignette]}
        >
          <View style={[
            styles.controls.row,
            styles.controls.bottomControlGroup,
            fullScreenBottomControlsStyle,
          ]}
          >{playPauseControl}
            {timerControl}
            {seekbarControl}
            {volumeControl}
            {fullscreenControl}
            {this.renderGalleryButton()}
          </View>
        </ImageBackground>
      </Animated.View>
    );
  }

  renderGalleryButton = () => {
    if (this.props.showGallery) {
      return (
        <TouchableOpacity activeOpacity={0.7} onPress={() => this.props.openGallery()}>
          <View style={ViewGallery.container}>
            <Image style={ViewGallery.icon} source={require('@mmt/legacy-assets/src/holidays/ic-view-gallery.png')}/>
            <Text style={ViewGallery.text}>Gallery</Text>
            <Image style={ViewGallery.rightArrow}
                   source={require('@mmt/legacy-assets/src/holidays/rightArrowWhite.webp')}/>
          </View>
        </TouchableOpacity>
      );
    }
    return [];
  };
}
