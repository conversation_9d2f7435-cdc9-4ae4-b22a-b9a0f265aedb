import React, { useCallback, useEffect, useState } from 'react';
import { BackHandler, View } from 'react-native';
import VisaErrorView from './ErrorView';
import VisaProgressView from './VisaProgressView';
import PackageAddOns from './PackageAddOns';
import { packageVisaPromise } from '../../../utils/HolidayNetworkUtils';
import { PACKAGE_FEATURES, VIEW_STATE, VISA_API_STATUS_CODE } from '../../Utils/PheonixDetailPageConstants';
import { holidayNavigationPop } from '../../../PhoenixDetail/Utils/DetailPageNavigationUtils';
import { Overlay } from '../DetailOverlays/OverlayConstants';
import { isMobileClient } from '../../../utils/HolidayUtils';
import { HolidayNavigation } from '../../../Navigation';

import useBackHandler from '../../../hooks/useBackHandler';

const PackageAddOnsContainer = (props) => {
  const { packageDetail, updateVisa, packageFeature,hideOverlays } = props;
  const { type = ''} = packageFeature || {};
  const { dynamicId } = packageDetail || {};
  const [viewState, setViewState] = useState(VIEW_STATE.LOADING);
  const [response, setResponse] = useState(null);

  const onBackPressed = useCallback(() => {
    if (isMobileClient()) {
      HolidayNavigation.pop();
    } else {
      holidayNavigationPop({
        overlayKeys:[Overlay.PACKAGE_ADD_ONS],
        hideOverlays,
      })
    }
  }, []);

  const backHandlerCallback = React.useCallback(() => {
    onBackPressed();
    return true;
  }, [onBackPressed]);
  useBackHandler(backHandlerCallback);

  const isNonVisa = () => {
    if (type !== PACKAGE_FEATURES.VISA) {
      setViewState(VIEW_STATE.SUCCESS);
      return true;
    }
    return false;
  };

  const fetchVisaResponse = async (dynamicId) => {
    const response = await packageVisaPromise(dynamicId);
    return response.json();
  };

  const fetchVisaData = async () => {
    if (isNonVisa()) return; // early return for non-visa
    // Continue with the fetch process
    setViewState(VIEW_STATE.LOADING);
    try {
      const response = await fetchVisaResponse(dynamicId);
      const { statusCode, success } = response || {};
      if (!success || statusCode !== VISA_API_STATUS_CODE.SUCCESS) {
        setViewState(VIEW_STATE.ERROR); // show Error view when API fails.
        return;
      }
      setResponse(response);
      setViewState(VIEW_STATE.SUCCESS);
    } catch (error) {
      setViewState(VIEW_STATE.ERROR);
    }
  };

  useEffect(() => {
    fetchVisaData();
  }, []);

  return (<View style={{ flex: 1 }}>
      {viewState === VIEW_STATE.SUCCESS
        && <PackageAddOns
          {...props}
          data={null}
          onBackPressed={onBackPressed}
          hitVisaAndTourApi={fetchVisaData}
          response={response}
          hideOverlays={hideOverlays}
          updateVisa={updateVisa}
        />}
      {viewState === VIEW_STATE.ERROR && <VisaErrorView onBackPressed={onBackPressed} onRefreshPressed={fetchVisaData}/>}
      {viewState === VIEW_STATE.LOADING && <VisaProgressView />}
    </View>
  );
};

export default PackageAddOnsContainer;
