import React from 'react';
import { StyleSheet, View } from 'react-native';
import { marginStyles, } from '../../../Styles/Spacing';
import { holidayColors } from '../../../Styles/holidayColors';
import { fontStyles } from '../../../Styles/holidayFonts';
import Button from '../ItineraryV2/Activity/ActivityListingPage/Button';

const TourGuide = (props) => {
  const { isEditable = false } = props;

  return (
    isEditable && (
      <View style={styles.buttonContainer}>
        <Button buttonText="Select" activeImg={true} btnContainerStyles={styles.button} />
      </View>
    )
  );
};

const styles = StyleSheet.create({
  container: {
  },
  headerContainer: {
    flexDirection: 'row',
  },
  tour: {
    height: 20,
    width: 20,
  },
  header: {
    ...fontStyles.labelMediumBlack,
    ...marginStyles.ml8,
    color: holidayColors.black,
  },
  button: {
    width: 110,
    height: 32,
    ...marginStyles.mt16,
  },
  buttonContainer: {
    alignItems: 'flex-end',
    ...marginStyles.mr8,
    ...marginStyles.mb8,
  },
});
export default TourGuide;
