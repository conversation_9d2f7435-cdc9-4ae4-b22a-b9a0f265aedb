import React from 'react';
import { Image, StyleSheet, Text, View } from 'react-native';
import errorImageIcon from '@mmt/legacy-assets/src/visa_error_image.webp';
import { HolidayNavigation } from '../../../Navigation';
import PageHeader from '../PageHeader';
import { VISA_ASSISTANCE_AND_TOUR_GUIDE } from '../../Utils/PheonixDetailPageConstants';
import { marginStyles, paddingStyles } from '../../../Styles/Spacing';
import { fontStyles } from '../../../Styles/holidayFonts';
import { holidayColors } from '../../../Styles/holidayColors';
import PrimaryButton from '../../../Common/Components/Buttons/PrimaryButton';


const VisaErrorView = ({ onRefreshPressed, onBackPressed }) => {
  const buttonText = onRefreshPressed ? 'REFRESH' : 'Back to previous page';
  const handleClick = onRefreshPressed ? onRefreshPressed : () => HolidayNavigation.pop();
  return (
    <View style={styles.container}>
      <PageHeader
        showBackBtn
        showShadow
        title={VISA_ASSISTANCE_AND_TOUR_GUIDE}
        subTitle={'Your feedback is important to us'}
        onBackPressed={onBackPressed}
        containerStyles={paddingStyles.pa16}
      />
      <View style={styles.errorDetails}>
        <View style={styles.errorDetailsText}>
          <Image style={styles.errorImage1} source={errorImageIcon} />
          <Text style={styles.title}>{VISA_ASSISTANCE_AND_TOUR_GUIDE}</Text>
          <Text style={styles.subTitle}>
            {'Something went wrong, please try again later'} {'\n'}
            {'Or contact our customer support team for assistance.'}
          </Text>
        </View>
        <PrimaryButton
          buttonText={buttonText}
          btnContainerStyles={styles.button}
          handleClick={handleClick}
          onPress={handleClick}
        />
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'space-between',
    backgroundColor: holidayColors.white,
    width: '100%',
  },
  errorDetails: {
    alignItems: 'center',
    paddingVertical: 15,
    flex: 1,
    width: '100%',
  },
  errorDetailsText: {
    alignItems: 'center',
    justifyContent: 'center',
    flex: 1,
  },
  errorImage1: {
    width: 196,
    height: 196,
    ...marginStyles.mb30,
  },
  title: {
    ...fontStyles.headingMedium,
    color: holidayColors.black,
    ...marginStyles.mb10,
  },
  subTitle: {
    ...fontStyles.labelBaseRegular,
    color: holidayColors.gray,
    ...marginStyles.mb70,
    lineHeight: 20,
    textAlign: 'center',
  },
  button: {
    paddingHorizontal: 80,
  },
});

export default VisaErrorView;
