import React from 'react';
import { Image, StyleSheet, Text, View } from 'react-native';
import { marginStyles, paddingStyles } from '../../../Styles/Spacing';
import { holidayColors } from '../../../Styles/holidayColors';
import { fontStyles } from '../../../Styles/holidayFonts';
import HolidayImageHolder from '../../../Common/Components/HolidayImageHolder';

const AddOnsHeadingAndDescription = ({ heading = '', imageUrl = '', description = '' }) => {
  return (
    <View>
      {!!heading && (
        <View style={styles.headerContainer}>
          <HolidayImageHolder imageUrl={imageUrl} style={styles.icon} />
          <Text style={styles.header}>{heading}</Text>
        </View>
      )}
      {!!description && <Text style={styles.description}>{description}</Text>}
    </View>
  );
};

const styles = StyleSheet.create({
  headerContainer: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    ...marginStyles.mb4,
  },
  icon: {
    height: 20,
    width: 20,
  },
  header: {
    ...fontStyles.labelMediumBlack,
    ...marginStyles.ml8,
    color: holidayColors.black,
  },
  description: {
    ...fontStyles.labelBaseRegular,
    ...marginStyles.mt8,
    ...marginStyles.mr8,
    color: holidayColors.gray,
  },
});
export default AddOnsHeadingAndDescription;
