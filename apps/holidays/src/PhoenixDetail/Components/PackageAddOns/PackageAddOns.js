import React from 'react';
import { Platform, StyleSheet, View } from 'react-native';
import PageHeader from '../../../Common/Components/PageHeader';
import { paddingStyles, marginStyles } from '../../../Styles/Spacing';
import { holidayColors } from '../../../Styles/holidayColors';
import VisaAssistance from './VisaAssistance';
import TourGuide from './TourGuide';
import { PACKAGE_FEATURES } from '../../Utils/PheonixDetailPageConstants';
import Heading from './HeadingAndDescription';

const PackageAddOns = (props) => {
  const { onBackPressed, response, updateVisa, packageFeature, hideOverlays } = props || {};
  const { type = '', imageUrl = '', title = '', subtitle = '', description = '', isEditable = false, } = packageFeature || {};

  return (
    <View style={styles.container}>
      <PageHeader
        title={title}
        showShadow={true}
        showBackBtn={true}
        onBackPressed={onBackPressed}
        containerStyles={styles.header}
      />
      <View style={styles.inclusionContainer}>
        <Heading heading={subtitle} imageUrl={imageUrl} description={description} />
        {type === PACKAGE_FEATURES.VISA && (
          <VisaAssistance data={response} updateVisa={updateVisa} hideOverlays={hideOverlays}/>
        )}
        {type === PACKAGE_FEATURES.ADDON_INCLUSION && <TourGuide isEditable={isEditable} />}
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  header: {
    backgroundColor: holidayColors.white,
  },
  container: {
    flex: 1,
    backgroundColor: holidayColors.lightGray2,
    marginTop: Platform.OS === 'ios' ? 0 : 24,
  },
  inclusionContainer: {
    backgroundColor: holidayColors.white,
    justifyContent: 'center',
    borderRadius: 10,
    ...paddingStyles.pa16,
    ...marginStyles.mh16,
    ...marginStyles.mv8,
  },
});
export default PackageAddOns;
