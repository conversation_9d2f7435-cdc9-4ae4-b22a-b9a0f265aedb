import React, {useState} from 'react';
import {View} from 'react-native';
import ChangeHotel from '../ChangeHotel';
import Header from '../../../Header';
import HotelListCard from '../HotelListCard';
import SelectPriceCard from '../../../SelectPriceCard';

const similarHotelData = [
    {
        propertyType: 'Hotel',
        userRating: 4.5,
        propertyName: 'Munnar Hill Resort',
        address: 'Munnar, Kerala',
        landmark: '400m from Tea Museum',
        date: 'Sun, 03 Jan 2021 - Tue 05 Jan 2021',
        starRating: 4,
        roomType: 'Standard Twin room',
        additionalFacililities: [
            'Breakfast', 'Pay at Hotel',
        ],
        price: 329.99,
    },
];

const ChangeHotelPage = (props) => {
    const [selectCard, setSelectCard] = useState();
    const toggleSelect = (index) => {
        setSelectCard(index === selectCard ? null : index);
    };
    return (
        <View>
            <Header
                adultCount={999}
                kidsCount={99}
                checkInDate={12 / 12 / 2900}
                cityName={'cityName'}
                onBackPress={() => console.log('Handle back press')}
            />
            <SelectPriceCard/>
            <ChangeHotel/>
            {similarHotelData.map((item, index) =>
                <HotelListCard
                    key={index}
                    propertyType={item.propertyType}
                    userRating={item.userRating}
                    propertyName={item.propertyName}
                    address={item.address}
                    landmark={item.landmark}
                    date={item.date}
                    starRating={item.starRating}
                    roomType={item.roomType}
                    price={item.price}
                    showPrice={true}

                    selectCard={selectCard}
                    index={index}
                    handleCardSelect={() => toggleSelect(index)}
                />,
            )}
        </View>
    );
};

export default ChangeHotelPage;
