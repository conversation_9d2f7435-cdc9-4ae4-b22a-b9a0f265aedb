import React, {useState} from 'react';
import {Image, StyleSheet, Text, TouchableOpacity, View} from 'react-native';
import AtomicCss from '@mmt/legacy-commons/Styles/AtomicCss';
import { colors, fonts } from '@mmt/legacy-commons/Styles/globalStyles';
import icSearch from '@mmt/legacy-assets/src/search.webp';
import iconClose from '../../../images/ic_closeInverse.png';
import iconblueCross from '../../../images/ic_blueCross.png';
import {isEmpty} from 'lodash';
import { HOLIDAY_ROUTE_KEYS, HolidayNavigation } from '../../../../../Navigation';
import { holidayColors } from '../../../../../Styles/holidayColors';
import { fontStyles } from '../../../../../Styles/holidayFonts';
import { holidayBorderRadius } from '../../../../../Styles/holidayBorderRadius';
import { paddingStyles } from '../../../../../Styles/Spacing/index';
const ChangeHotel = (props) => {
    const {selectedHotel, dynamicPackageId, onItemClicked, locationSearchText, hotelCount = 0, refreshListing, bundled} = props || {};
    const {hotelSequence} = selectedHotel || {};

    /*Do not show change hotel UI in case of FD packages.*/
    if (bundled){
        return [];
    }

    const searchAuto = () => {
        HolidayNavigation.push(HOLIDAY_ROUTE_KEYS.HOTEL_LISTING_SEARCH, {dynamicPackageId, hotelSequence, onItemClicked});
    };
    return (
        <>
            <View style={styles.changeHotelCard}>
                <View style={[AtomicCss.flexRow, AtomicCss.alignCenter, AtomicCss.spaceBetween]}>
                    <View><Text style={[AtomicCss.font16, AtomicCss.blackFont, AtomicCss.blackText]}>Change Hotel</Text></View>
                    <TouchableOpacity onPress={searchAuto} style={[styles.searchBox]}>
                        {/* <View><Image source={} style={} /></View> */}
                        {!isEmpty(locationSearchText)
                          ? <View style={[AtomicCss.flexRow]}>
                              <View style={[AtomicCss.marginRight10]}><Text style={styles.searchText}>{locationSearchText}</Text></View>
                              <TouchableOpacity onPress={()=> refreshListing()}>
                              <View>
                                  <Image source={iconClose} style={styles.iconClose}/>
                              </View>
                              </TouchableOpacity>
                          </View>
                          :
                          <View style={[AtomicCss.flexRow]}>
                          <Image source={icSearch} style={styles.icSearchStyle}/>
                          <Text style={styles.searchText}>Search by Hotel Name or Location</Text>
                          </View>
                        }
                    </TouchableOpacity>
                </View>

                {!isEmpty(locationSearchText)
                && <View style={[AtomicCss.flexRow, AtomicCss.alignCenter, AtomicCss.marginTop5]}>
                    <View><Text style={[AtomicCss.font12, AtomicCss.greyText, AtomicCss.regularFont]}>Showing {hotelCount} Results
                        for</Text></View>
                    <View style={[styles.searchUnderlineText]}><Text
                      style={[AtomicCss.font11, AtomicCss.defaultText, AtomicCss.boldFont]}>{locationSearchText}</Text></View>
                    <View style={[AtomicCss.marginLeft12]}>
                        <TouchableOpacity onPress={()=> refreshListing()}>
                        <Image source={iconblueCross} style={styles.iconblueCross}/>
                        </TouchableOpacity>
                    </View>
                </View>}

               {/* <View
                    style={[AtomicCss.flexRow, AtomicCss.alignCenter, AtomicCss.marginTop10, AtomicCss.marginBottom10]}>
                    <View style={[AtomicCss.marginRight10]}><Image source={picSiddhi} style={styles.picSiddhi}/></View>
                    <View style={styles.siddhiContent}>
                        <View style={[AtomicCss.flexRow, AtomicCss.alignCenter]}>
                            <View style={AtomicCss.marginRight5}><Image source={icQuotes}
                                                                        style={styles.icQuotes}/></View>
                            <View><Text style={[AtomicCss.font11, AtomicCss.defaultText, styles.italicFont]}>Need Help
                                Selecting a Hotel that’s Best For You? </Text></View>
                        </View>
                        <View style={[AtomicCss.flexRow, AtomicCss.alignCenter, AtomicCss.marginTop5]}>
                            <View style={[styles.stayType, AtomicCss.marginRight10]}>
                                <Text style={[AtomicCss.regularFont, AtomicCss.font11, AtomicCss.defaultText]}><Text
                                    style={[AtomicCss.boldFont]}>5 </Text>Romantic Stays</Text>
                            </View>
                            <View style={[styles.stayType, AtomicCss.marginRight10]}>
                                <Text style={[AtomicCss.regularFont, AtomicCss.font11, AtomicCss.defaultText]}><Text
                                    style={[AtomicCss.boldFont]}>Top </Text>Luxury Stays</Text>
                            </View>
                            <View style={[styles.stayType, AtomicCss.marginRight10]}>
                                <Text style={[AtomicCss.regularFont, AtomicCss.font11, AtomicCss.defaultText]}><Text
                                    style={[AtomicCss.boldFont]}>Best </Text>Budget Stays</Text>
                            </View>
                        </View>
                    </View>
                </View>*/}
            </View>
        </>
    );
};

const styles = StyleSheet.create({
    stayType: {
        borderWidth: 1,
        ...holidayBorderRadius.borderRadius8,
        backgroundColor: holidayColors.white,
        ...paddingStyles.pa16,
    },
    searchBox: {
        backgroundColor: colors.skyBlue6,
        ...holidayBorderRadius.borderRadius8,
        ...paddingStyles.pa6,
        borderWidth: 1,
        borderColor: holidayColors.lightBlue,
        flexDirection: 'row',
        alignItems: 'center',
        width: 250,
        marginTop: 5,
    },
    searchText: {
        ...fontStyles.labelSmallRegular,
        marginLeft: 5,
        color: holidayColors.gray,
    },
    picSiddhi: {
        width: 44,
        height: 55,
        resizeMode: 'cover',
    },
    icQuotes: {
        width: 14,
        height: 10,
        resizeMode: 'cover',
    },
    changeHotelCard: {
        backgroundColor: holidayColors.lightGray2,
        paddingHorizontal: 16,
        paddingTop: 10,
        paddingBottom: 16,

    },
    iconClose: {
        width: 14,
        height: 14,
        resizeMode: 'cover',
    },
    iconblueCross: {
        width: 10,
        height: 10,
        resizeMode: 'cover',
    },
    italicFont: {
        fontFamily: fonts.italic,
    },
    siddhiContent: {
        width: '80%',
        flexWrap: 'wrap',
    },
    searchUnderlineText: {
        borderBottomWidth: 1,
        borderColor: '#008cff',
        borderStyle: 'dashed',
        paddingBottom: 2,
        marginLeft: 5,
    },
    icSearchStyle: {
        width: 16,
        height: 16,
        resizeMode: 'cover',
    },
});

export default ChangeHotel;
