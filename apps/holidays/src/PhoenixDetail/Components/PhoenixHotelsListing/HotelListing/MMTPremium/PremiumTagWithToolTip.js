import React, { useState, useRef } from 'react';
import { View, Text, TouchableOpacity, StyleSheet, Image, Animated, Easing } from 'react-native';
import { marginStyles, paddingStyles } from '../../../../../Styles/Spacing';
import { holidayColors } from '../../../../../Styles/holidayColors';
import { fontStyles } from '../../../../../Styles/holidayFonts';

const PremiumTagWithToolTip = (props) => {
  const [isTooltipVisible, setTooltipVisible] = useState(false);
  const tooltipOpacity = useRef(new Animated.Value(0)).current;
  const triangleOpacity = useRef(new Animated.Value(0)).current;

  const premiumHotelInfo = {
    'imageUrl': 'https://hldak.mmtcdn.com/prod-s3-hld-hpcmsadmin/holidays/images/phonixImages/On Card Tags.png',
    'backgroundColor': '#FDF8F3',
    'tooltipData': {
      'heading': 'MMT Premium',
      'subHeading': 'Handpicked Luxury Hotels',
      'items': [
        {
          'iconUrl': 'https://hldak.mmtcdn.com/prod-s3-hld-hpcmsadmin/holidays/images/phonixImages/Diamond premium.png',
          'description': 'Exclusive handpicked ultra-premium hotels',
        },
        {
          'iconUrl': 'https://hldak.mmtcdn.com/prod-s3-hld-hpcmsadmin/holidays/images/phonixImages/Diamond premium.png',
          'description': 'Extraordinary Signature Amenities tailored for you',
        },
      ],
    },
  };

  const { imageUrl, backgroundColor, tooltipData } = premiumHotelInfo || {};
  const { heading, subHeading, items } = tooltipData || {};

  if (!premiumHotelInfo || !imageUrl) {
    return null;
  }

  const animateTooltip = (toValue, callback) => {
  Animated.parallel([
    Animated.timing(tooltipOpacity, {
      toValue,
      duration: 300,
      easing: Easing.ease,
      useNativeDriver: true,
    }),
    Animated.timing(triangleOpacity, {
      toValue,
      duration: 300,
      easing: Easing.ease,
      useNativeDriver: true,
    }),
  ]).start(callback);
};

const toggleTooltip = () => {
  if (isTooltipVisible) {
    animateTooltip(0, () => setTooltipVisible(false));
  } else {
    setTooltipVisible(true);
    animateTooltip(1);
  }
};

  return (
    <View style={styles.container}>
      <TouchableOpacity onPress={toggleTooltip}>
        <Image source={{ uri: imageUrl }} style={styles.premiumTag} />
      </TouchableOpacity>
      {isTooltipVisible &&
        <Animated.View style={[styles.triangle, { borderBottomColor: backgroundColor, opacity: triangleOpacity }]} />}

      {isTooltipVisible && (
        <Animated.View style={[styles.tooltipContainer, { backgroundColor, opacity: tooltipOpacity }]}>
          <Text style={styles.title}>{heading}</Text>
          <Text style={styles.subtitle}>{subHeading}</Text>

          <View style={styles.separator} />

          {items.map((item, index) => (
            <View key={index}>
              <View style={styles.itemContainer}>
                <Image source={{ uri: item.iconUrl }} style={styles.icon} />
                <Text style={styles.itemText}>{item.description}</Text>
              </View>
              {index < items.length - 1 && <View style={styles.separator} />}
            </View>
          ))}

          <TouchableOpacity style={styles.closeButton} onPress={toggleTooltip}>
            <Text style={styles.closeButtonText}>×</Text>
          </TouchableOpacity>
        </Animated.View>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    zIndex: 1,
  },
  tooltipContainer: {
    position: 'absolute',
    top: 40,
    backgroundColor: holidayColors.lightBeige,
    borderRadius: 16,
    paddingLeft: 15,
    paddingTop: 5,
    ...paddingStyles.pb4,

    // iOS shadow
    shadowColor: holidayColors.black,
    shadowOffset: { width: 0, height: 0 },
    shadowOpacity: 0.4,
    shadowRadius: 2,

    // Android shadow
    elevation: 5,
  },
  title: {
    marginTop: 4,
    ...fontStyles.labelBaseBold,
    color: holidayColors.black,
  },
  subtitle: {
    ...marginStyles.mb4,
    ...fontStyles.labelSmallRegular,
    color: holidayColors.black,
  },
  separator: {
    height: 1,
    backgroundColor: holidayColors.grayBorder,
    marginVertical: 5,
    marginEnd: 16,
  },
  itemContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginEnd: 16,
  },
  icon: {
    width: 20,
    height: 17,
    ...marginStyles.mr12,
    ...marginStyles.mt6,
    ...marginStyles.mb6,
  },
  itemText: {
    marginEnd: 16,
    ...marginStyles.mt6,
    ...marginStyles.mb6,
    ...fontStyles.labelSmallRegular,
    color: holidayColors.black,
  },
  boldText: {
    fontWeight: 'bold',
  },
  closeButton: {
    position: 'absolute',
    right: 10,
  },
  closeButtonText: {
    fontSize: 24,
    color: holidayColors.lightGray,
  },
  premiumTag: {
    width: 107,
    height: 18,
    resizeMode: 'contain',
  },
  triangle: {
    width: 0,
    height: 0,
    backgroundColor: holidayColors.transparent,
    borderStyle: 'solid',
    borderLeftWidth: 10,
    borderRightWidth: 10,
    borderBottomWidth: 15,
    borderLeftColor: holidayColors.transparent,
    borderRightColor: holidayColors.transparent,
    borderBottomColor: holidayColors.lightBeige,
    marginLeft: 36,
    marginTop: -13,
    zIndex: 1,
    top: 20,
    marginBottom: -1,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: -4 },
    shadowOpacity: 0.1,
  },
});

export default PremiumTagWithToolTip;
