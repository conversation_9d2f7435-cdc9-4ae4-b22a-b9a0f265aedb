import React, { useState, useRef, useEffect } from 'react';
import {
  StyleSheet,
  FlatList,
  View,
  TouchableOpacity,
  Image,
  Text,
} from 'react-native';
import LinearGradient from 'react-native-linear-gradient';
import Header from '../Components/Header';
import AtomicCss from '../../../../Styles/AtomicCss';
import { fonts } from '../../../../Styles/globalStyles';
import getPlatformElevation from '../../../../Styles/getPlatformElevation';
import LikeIcon from '../images/icons-like.png';
import DislikeIcon from '../images/icons-dislike.png';

const imageList = [
  'https://images.unsplash.com/photo-1599632740188-8a4f152a8342',
  'https://images.unsplash.com/photo-1593417033920-3c04818ee8a8',
  'https://images.unsplash.com/photo-1491497895121-1334fc14d8c9',
  'https://images.unsplash.com/photo-1599632740188-8a4f152a8342',
  'https://images.unsplash.com/photo-1593417033920-3c04818ee8a8',
  'https://images.unsplash.com/photo-1491497895121-1334fc14d8c9',
  'https://images.unsplash.com/photo-1599632740188-8a4f152a8342',
  'https://images.unsplash.com/photo-1593417033920-3c04818ee8a8',
  'https://images.unsplash.com/photo-1491497895121-1334fc14d8c9',
];

const ReviewCard = () => {

  const renderImage = ({ item }) => {
    return (
      <View style={styles.reviewImgWrapper}>
        <Image source={{ uri: item }} style={styles.reviewImg} />
      </View>
    );
  };

  return (
    <View style={styles.card}>
      <View style={styles.rating}>
        <Text style={styles.ratingText}>4.5</Text>
      </View>
      <View style={AtomicCss.flex1}>
        <Text style={styles.title}>Very Good</Text>
        <Text style={styles.subTitle}>Madhavi Ranjan, Family Traveller, 24 Apr, 2017</Text>
        <Text style={styles.roomName}>Deluxe Twin Room</Text>
        <Text style={styles.desc}>We got upgraded to the villa. The villas are gorgeous. The staff is super nice and tried to help us with everything. The food in the new cafe was amazing.</Text>
        <View style={styles.imageList}>
          <FlatList
            data={imageList}
            renderItem={renderImage}
            contentContainerStyle={AtomicCss.flexRow}
            horizontal
            showsHorizontalScrollIndicator={false}
          />
        </View>
        <View style={styles.vendorReply}>
          <Text style={styles.vendorReplyDate}>Vendor Reply . 08 JAN 2020</Text>
          <Text style={styles.vendorReplyText}>Dear Mr. Hawaldar, it was a pleasure having you with us and thank you for your valuable feedback. We’ll working to serving you better the next time - hope we will see you again in the near future.</Text>
        </View>
        <View style={[AtomicCss.flexRow, AtomicCss.spaceBetween, AtomicCss.alignCenter, AtomicCss.paddingTop10, AtomicCss.paddingBottom12]}>
          <Text style={styles.footerText}>Was this review helpful?</Text>
          <View style={[AtomicCss.flexRow, AtomicCss.alignCenter]}>
            <View style={[AtomicCss.flexRow, AtomicCss.alignCenter]}>
              <Image source={LikeIcon} style={styles.footerIcon} />
              <Text style={styles.footerText}>24</Text>
            </View>
            <View style={[AtomicCss.flexRow, AtomicCss.alignCenter]}>
              <Image source={DislikeIcon} style={styles.footerIcon} />
              <Text style={styles.footerText}>10</Text>
            </View>
          </View>
        </View>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  card: {
    flexDirection: 'row',
    marginHorizontal: -15,
    paddingHorizontal: 15,
    borderBottomColor: '#f2f2f2',
    borderBottomWidth: 1,
    paddingTop: 12,
  },
  rating: {
    backgroundColor: '#249995',
    borderRadius: 3,
    width: 30,
    height: 20,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 12,
  },
  ratingText: {
    fontFamily: fonts.bold,
    fontSize: 14,
    color: '#ffffff',
  },
  title: {
    fontFamily: fonts.black,
    fontSize: 14,
    color: '#4a4a4a',
  },
  subTitle: {
    fontFamily: fonts.medium,
    fontSize: 10,
    color: '#9b9b9b',
    marginTop: 3,
    marginBottom: 10,
  },
  roomName: {
    fontFamily: fonts.bold,
    fontSize: 10,
    color: '#9b9b9b',
    marginBottom: 3,
  },
  desc: {
    fontFamily: fonts.regular,
    fontSize: 11,
    color: '#4a4a4a',
    marginBottom: 10,
  },
  vendorReply: {
    backgroundColor: '#f2f2f2',
    borderRadius: 4,
    padding: 10,
  },
  vendorReplyDate: {
    fontFamily: fonts.regular,
    fontSize: 10,
    color: '#9b9b9b',
    marginBottom: 7,
  },
  vendorReplyText: {
    fontFamily: fonts.regular,
    fontSize: 11,
    color: '#4a4a4a',
  },
  footerText: {
    fontFamily: fonts.regular,
    fontSize: 10,
    color: '#9b9b9b',
  },
  footerIcon: {
    width: 18,
    height: 16,
    marginRight: 2,
    marginLeft: 10,
  },
  imageList: {
    marginRight: -15,
    marginBottom: 10,
  },
  reviewImgWrapper: {
    height: 54,
    width: 54,
    backgroundColor: '#f00000',
    borderRadius: 4,
    overflow: 'hidden',
    marginRight: 7,
  },
  reviewImg: {
    height: '100%',
    resizeMode: 'cover',
  },
});

export default ReviewCard;
