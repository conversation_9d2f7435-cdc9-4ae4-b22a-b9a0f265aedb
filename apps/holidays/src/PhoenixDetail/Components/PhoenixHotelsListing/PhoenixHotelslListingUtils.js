import {getDateTextForBlackStrip, getDurationText} from '../HotelDetailPageFullScreen/HotelDetailPageFullScreenUtil';
import { trackPhoenixDetailLocalClickEvent } from '../../Utils/PhoenixDetailTracking';
import {isEmpty} from 'lodash';
import { HOLIDAYS_HOTEL_OVERLAY_LISTING } from '../../Utils/PheonixDetailPageConstants';
import { logPhoenixDetailPDTEvents } from 'mobile-holidays-react-native/src/utils/PhoenixDetailPDTTrackingUtils';
import { PDT_EVENT_TYPES } from 'mobile-holidays-react-native/src/utils/HolidayPDTConstants';

export const getUpdatedBlackStripObject = (hotel, packageDetailDTO, showUpdateButtonOnHeader, discountedFactor) => {
  const {price} = packageDetailDTO || {};
  const packagePrice = Math.ceil((price * discountedFactor));
  return {
    perPersonPrice : packagePrice,
    priceDiff: hotel.price,
    showUpdateButtonOnHeader,
    durationText : getDurationText(hotel),
    name : hotel.name,
    packageDetailDTO,
    dateText: getDateTextForBlackStrip(hotel),
    selectedHotel: hotel,
  };
};

const captureClickEvents = ({eventName = '', suffix = '', prop1 = ''}) =>{
  const value = eventName + suffix;
  logPhoenixDetailPDTEvents({
    actionType: PDT_EVENT_TYPES.buttonClicked,
    value: value.replace(/_/g, '|'),
    subPageName: prop1,
  })
  trackPhoenixDetailLocalClickEvent({
    eventName,
    suffix,
    prop1,
  })

}

export const trackAppliedFilterAndSorter = appliedFilterData => {
  try {
    let filterData = [];
    const {
      appliedStarRatingList, appliedPopularList,
      appliedPropertyTypeList, appliedAmenitiesList,
      appliedPriceRange, appliedLocationList, appliedSortingList, appliedURating,
    } = appliedFilterData || {};

    const {min, max} = appliedPriceRange || {};
    if (min && max) {
      filterData.push('MinPrice_' + min);
      filterData.push('MaxPrice_' + max);
    }

    if (!isEmpty(appliedURating)) {
      filterData.push('UserRating_' + appliedURating);
    }

    appliedStarRatingList.forEach(item=> filterData.push('StarRating_' + item));
    appliedPopularList.forEach(item=> filterData.push('Popular_' + item));
    appliedPropertyTypeList.forEach(item=> filterData.push('PropertyType_' + item));
    appliedAmenitiesList.forEach(item=> filterData.push('Amenities_' + item));
    appliedLocationList.forEach(item=> filterData.push('Location_' + item));

    const filterTrackingData = filterData.reduce((acc, item) => item + ':' + acc, '');

    if (!isEmpty(filterTrackingData)){
      captureClickEvents({
        eventName: 'filter_',
        suffix: filterTrackingData,
        prop1: HOLIDAYS_HOTEL_OVERLAY_LISTING,
      });
    }

    if (!isEmpty(appliedSortingList)){
      captureClickEvents({
        eventName: 'sorter_ ',
        suffix: appliedSortingList.replace(/ /g,'_'),
        prop1: HOLIDAYS_HOTEL_OVERLAY_LISTING,
      });
    }
  } catch (e) {
    console.log(e);
  }
};
