import React from 'react';
import { StyleSheet } from 'react-native';
import DestDuration from '../../DestDuration';
import { connect } from 'react-redux';
import PageHeader from '../../../../Common/Components/PageHeader';
import { paddingStyles } from '../../../../Styles/Spacing';

const Header = (props) => {
  const { onBackPressed, detailData } = props;
  const { packageDetail } = detailData || {};

  const renderSubTitle = () => <DestDuration containerStyles={paddingStyles.ph0} />;
  return (
    <PageHeader
      title={packageDetail.name}
      showShadow
      showBackBtn
      SubTitleComponent={renderSubTitle}
      onBackPressed={onBackPressed}
      containerStyles={styles.container}
    />
  );
};

const styles = StyleSheet.create({
  container: {
    ...paddingStyles.pa16,
  },
});

const mapStateToProps = (state) => {
  const { detailData } = state.holidaysDetail || {};
  return { detailData };
};

export default connect(mapStateToProps, null)(Header);
