import React, {useEffect, useRef, useState} from 'react';
import {Dimensions, FlatList, Platform, StatusBar, StyleSheet, Text, TouchableOpacity, View} from 'react-native';
import Header from './Header';
import {connect} from 'react-redux';
import {cloneDeep, isEmpty} from 'lodash';
import PhoenixVideoSection from '../HolidaysVideoPlayer/PhoenixVideoSection';
import {getVideoUrl} from '../../Utils/HolidayDetailUtils';
import PlaceholderImageView from '@mmt/legacy-commons/Common/Components/PlaceholderImageView';
import {trackPhoenixDetailLocalClickEvent} from '../../Utils/PhoenixDetailTracking';
import { hideOverlays, showOverlay } from '../DetailOverlays/Redux/DetailOverlaysActions';
import { Overlay } from '../DetailOverlays/OverlayConstants';
import { HOLIDAY_ROUTE_KEYS, HolidayNavigation } from '../../../Navigation';
import { holidayColors } from '../../../Styles/holidayColors';
import { fontStyles } from '../../../Styles/holidayFonts';
import { marginStyles } from '../../../Styles/Spacing';
import {isAndroidClient, isMobileClient, isRawClient} from '../../../utils/HolidayUtils';

const screenWidth = Dimensions.get('screen').width;
const ALL_DAYS = 0;
let destMapping = [];

const GridGallery = (props) => {
  const { detailData, showOverlay, hideOverlays } = props || {};
  const [tabActiveIndex, setTabActiveIndex] = useState(ALL_DAYS);

  const { packageDetail } = detailData || {};
  const { imageDetail, destinationDetail, additionalDetail } = packageDetail || {};
  const { destinations } = destinationDetail || {};

  const { usp } = additionalDetail || {};
  const destCity = destinations.find(dest => dest.name);

  const tabsList = getTabs(imageDetail.images, destinations);
  const videoUrl = getVideoUrl(packageDetail);
  const VIDEO_POSITION_INDEX = 1;
  let flatList = useRef();
  const mediaList = getMediaList(imageDetail.images, tabActiveIndex, videoUrl, VIDEO_POSITION_INDEX);

  const onBackPressed = () => {
    isMobileClient() ? HolidayNavigation.pop() : hideOverlays([Overlay.GRID_GALLERY]);
  };

  const handleImageClicked = (imageList, destCity, usp, name, index) => {
    const obj = {imageList, destCity, usp, name, index};
    isMobileClient()
        ? HolidayNavigation.push(HOLIDAY_ROUTE_KEYS.DETAIL_GRID_GALLERY_FULL_PAGE, obj)
        : showOverlay(Overlay.FULL_PAGE_GALLERY, obj);

    trackPhoenixDetailLocalClickEvent({
      eventName: 'gallery_photo_clicked_',
      suffix: index,
    });
  };

  const GalleryImage = ({ url, index, style }) => {
    const imageList = getImagesForDay(imageDetail.images, tabActiveIndex);
    const { name } = packageDetail || {};
    return (
      <TouchableOpacity
        activeOpacity={0.8}
        onPress={() => handleImageClicked(imageList, destCity, usp, name, index)}>
        <PlaceholderImageView
          style={style}
          url={url} />
      </TouchableOpacity>
    );
  };

  const GalleryVideo = () => {
    return (
      <View style={{
        marginBottom: 5, borderRadius: 4,
        borderWidth: 1,
        borderColor: '#fff',
      }}>
        <PhoenixVideoSection
          togglePopup={() => {
          }}
          videoUrl={videoUrl}
          videoPaused={false}
        /></View>
    );
  };


  const renderListItem = (item) => {
    if (item.length === 1) {
      const { type, url, index } = item[0];
      if (type === 'VIDEO' && url) {
        return (<View style={{ marginLeft: 10, marginRight: 10 }}><GalleryVideo /></View>);
      } else if (type === 'IMAGE' && url) {
        return (<View style={styles.row}><GalleryImage url={url} index={index} style={styles.image1} /></View>);
      }
    } else if (item.length === 2) {
      const { url, index } = item[0];
      const { url: url1, index: index1 } = item[1];
      return (
        <View style={styles.row}>
          <GalleryImage url={url} index={index} style={styles.image2} />
          <GalleryImage url={url1} index={index1} style={styles.image2} />
        </View>
      );
    }
  };


  const handleSelectedTab = index => {
    setTabActiveIndex(index);
    if (index === 0) {
      trackPhoenixDetailLocalClickEvent({
        eventName: 'gallery_all_photos',
        sendGIData: isRawClient(),
      });
    } else {
      trackPhoenixDetailLocalClickEvent({
        eventName: 'gallery_photo_day_' + index,
        sendGIData: isRawClient(),
      });
    }
  };

  const renderListItemDay = (item:[], index: number) => {
    const containerStyle = index === tabActiveIndex
      ? [styles.tabContainer, styles.tabActive]
      : styles.tabContainer;
    const textStyle =
      index === tabActiveIndex
        ? [styles.tabText, styles.tabActiveText]
        : styles.tabText;
    return (
      <TouchableOpacity
        activeOpacity={0.8}
        style={containerStyle}
        onPress={() => handleSelectedTab(index)}>
        <Text style={textStyle}>{item.label}</Text>
      </TouchableOpacity>
    );
  };

  useEffect(() => {
    if (flatList && flatList.current && tabActiveIndex >= 0) {
      flatList.current.scrollToIndex({animated: true, index: tabActiveIndex});
    }
  }, [tabActiveIndex]);


  return (
      <View style={ {flex:1 }}>
      <Header onBackPressed={onBackPressed}/>
        <View>
          <FlatList
            horizontal={true}
            data={tabsList}
            renderItem={({item, index}) => renderListItemDay(item, index)}
            showsVerticalScrollIndicator={false}
            showsHorizontalScrollIndicator={false}
            onScrollToIndexFailed={() => {}}
            snapToAlignment={'center'}
            ref={flatList}
            contentContainerStyle={marginStyles.mt10}
          />
        </View>
      <FlatList
        style={styles.list}
        data={mediaList}
        extraData={this.state}
        keyExtractor={(item, index) => index.toString()}
        renderItem={({ item }) => renderListItem(item)}
      />
      </View>
  );
};

const getImagesForDay = (images, dayNumber) => {
  if (dayNumber === ALL_DAYS ){
    return images.map(i => i.fullPath);
  }

  return images.map(image => {
    const {day} = image;
    if (day === dayNumber) {
      return image.fullPath;
    }
    return '';
  }).filter(d => !isEmpty(d));
};

const getMediaList = (images, dayNumber, videoUrl, VIDEO_POSITION_INDEX) => {
  let mediaList = [];

  // Handling when nothing is available.
  if (!images && !videoUrl){
    return mediaList;
  }

  let tempList = [];
  let imageChunkSize = 2;

  // Handle case when only video is available.
  if (images.length === 0 && videoUrl) {
    mediaList.push([{type: 'VIDEO', url: videoUrl}]);
    return mediaList;
  }

  // Handle case  0;
 let indexItem = 0;
  images.forEach((image, index) => {
    // Put Video at VIDEO_POSITION_INDEX of then list.
    if (dayNumber === 0  && videoUrl && mediaList.length === VIDEO_POSITION_INDEX) {
      mediaList.push([{type: 'VIDEO', url: videoUrl}]);
      imageChunkSize = 2;
    }

    const {day} = image;
    if (dayNumber === 0 || day === dayNumber) {
      tempList.push({type: 'IMAGE', url: image.fullPath, day, index : indexItem});
      indexItem = indexItem + 1;
      if (tempList.length === imageChunkSize) {
        mediaList.push(cloneDeep(tempList));
        tempList = [];
        imageChunkSize = imageChunkSize === 2 ? 1 : 2;
      }
    }
  });

  // Push Last Item from tempList to MediaList.
  if (tempList.length > 0) {
    mediaList.push(cloneDeep(tempList));
  }
  return mediaList;
};

const getTabs = (images, destinations) => {
  const newTab = [{label: 'All Days'}];
  images.forEach(image => {
    const {day, fullPath} = image;
    const destinationByDay = getDestinationByDay(day , destinations) || {};
    const {name} =  destinationByDay || {};
    if (!newTab.some(tab => tab.label === 'Day ' + day + ' - ' + name)) {
      // Check if day has a valid Image URL
      if (!isEmpty(fullPath)) {
        newTab.push({label: 'Day ' + day + ' - ' + name});
      }
    }
  });
  return newTab;
};

const getDestinationByDay = (day, destinations) => {
  if (destMapping[`day${day}`]) {
    return destinations[destMapping[`day${day}`]];
  }
  for (let i = 0; i < destinations.length; i++) {
    for (let j = destinations[i].startDay; j <= destinations[i].endDay; j++) {
      destMapping[`day${j}`] = i;
    }
  }
  return destinations[destMapping[`day${day}`]];
};

const styles = StyleSheet.create({
  row: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    marginBottom: 6,
    marginLeft:10,
    marginRight: 10,
  },
  image2: {
    width: (screenWidth - 30) / 2,
    height: 102,
    borderRadius: 4,
  },
  image1: {
    width: screenWidth - 30,
    height: 174,
    borderRadius: 4,
  },
  container: {
    backgroundColor: holidayColors.white,
    paddingHorizontal: 15,
  },
  tabs: {
    marginBottom: 10,
    marginLeft: 5,
  },
  tabContainer: {
    marginLeft: 5,
    backgroundColor: holidayColors.white,
    marginRight: 10,
    borderRadius: 4,
    ...marginStyles.mb10,
    paddingVertical: 6,
    paddingHorizontal: 7,
    shadowColor: 'rgba(0, 0, 0, 0.4)',
    shadowOpacity: 0.4,
    shadowRadius: 2,
    shadowOffset: {
      width: 0,
      height: 0,
    },
    elevation: 2,
  },
  tabText: {
    paddingBottom: 2,
    ...fontStyles.labelSmallRegular,
    color: holidayColors.gray,
  },
  tabActive: {
    shadowOpacity: 0,
    borderColor: holidayColors.primaryBlue,
    borderWidth: 1,
  },
  tabActiveText: {
    color: holidayColors.primaryBlue,
    ...fontStyles.labelSmallBold,
  },
});

const mapStateToProps = (state) => {
  const {detailData} = state.holidaysDetail || {};
  return {detailData};
};

const mapDispatchToProps = dispatch => ({
  showOverlay: (key, data) => dispatch(showOverlay(key, data)),
  hideOverlays: (keys) => dispatch(hideOverlays(keys)),
});

export default connect(mapStateToProps, mapDispatchToProps)(GridGallery);
