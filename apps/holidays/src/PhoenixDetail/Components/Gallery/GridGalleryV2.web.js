import React, {useCallback, useEffect, useRef, useState} from 'react';
import {
  Dimensions,
  FlatList,
  Platform,
  SafeAreaView,
  SectionList,
  StatusBar,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
} from 'react-native';
import { connect } from 'react-redux';
import { statusBarBootomHeightForIphone } from '@mmt/legacy-commons/Styles/globalStyles';
import { trackPhoenixDetailLocalClickEvent } from '../../Utils/PhoenixDetailTracking';
import { HOLIDAY_ROUTE_KEYS, HolidayNavigation } from '../../../Navigation';
import { getGridData, getImagesForDay, getOptimisedUrl, subTypes } from './GalleryUtils';
import AtomicCss from '@mmt/legacy-commons/Styles/AtomicCss';
import { holidayColors } from 'apps/holidays/src/Styles/holidayColors';
import { fontStyles } from 'apps/holidays/src/Styles/holidayFonts';
import { paddingStyles } from 'apps/holidays/src/Styles/Spacing';
import { isAndroidClient, isRawClient } from '../../../utils/HolidayUtils';
import { hideOverlays, showOverlay } from '../DetailOverlays/Redux/DetailOverlaysActions';

/* Components */
import CloseBtn from '@mmt/legacy-assets/src/holidays/iconCross.webp';
import LinearGradient from 'react-native-linear-gradient';
import PageHeader from 'apps/holidays/src/Common/Components/PageHeader';
import { logPhoenixDetailPDTEvents } from 'apps/holidays/src/utils/PhoenixDetailPDTTrackingUtils';
import { PDT_EVENT_TYPES } from 'apps/holidays/src/utils/HolidayPDTConstants';
import { isEmpty } from 'lodash';
import PhoenixVideoSection from '../HolidaysVideoPlayer/PhoenixVideoSection';
import PlaceholderImageWithText from '@mmt/holidays/src/Common/Components/HolImageComponent/PlaceholderImageWithText';
import PropertyPhotos from './PropertyPhotos';
import { Overlay } from '../DetailOverlays/OverlayConstants';
import HolidayDataHolder from '../../../../src/utils/HolidayDataHolder';
import { SUB_PAGE_NAMES } from '../../../../src/HolidayConstants';

const ALL_DAYS = 0;

const screenHeight = Dimensions.get('screen').height;
const screenWidth = Dimensions.get('screen').width;
function GridGalleryV2(props) {
  const [tabActiveIndex, setTabActiveIndex] = useState(
    props?.activeTabIndex ? props?.activeTabIndex : ALL_DAYS,
  );
  const [tabActiveCityIndex, setTabActiveCityIndex] = useState(ALL_DAYS);
  let flatList = useRef();
  let citiesFlatList = useRef();
  let dataRef = useRef();
  const isSelfScroll = useRef({
    value: false,
  });

  const { detailData, closeOverlay } = props || {};
  const { packageDetail } = detailData || {};
  const { imageDetail, name, videoDetail } = packageDetail || {};
  const { packageVideo = {} } = videoDetail || {};
  const { tabs = [], cityData = [] } =
    getGridData(
      imageDetail?.gallery?.[0], //first component is used to check type
      imageDetail?.gallery,
      tabActiveIndex,
      tabActiveCityIndex,
    ) || {};

  const captureClickEvents = ({ eventName = '', suffix = '', value = '', actionType = {} }) => {
    trackPhoenixDetailLocalClickEvent({
      eventName,
      suffix,
    });
    logPhoenixDetailPDTEvents({
      actionType: !isEmpty(actionType) ? actionType : PDT_EVENT_TYPES.buttonClicked,
      value: value || eventName,
      subPageName:SUB_PAGE_NAMES.GALLERY
    });
  };

  const onPressCategoryTab = ({ item, index }) => {
    captureClickEvents({
      eventName: `Component_${item.label}_`,
      suffix: index,
      value:  `Component|${item.label}_${index}`
    })
    setTabActiveIndex(index);
    setTabActiveCityIndex(ALL_DAYS);
    flatList?.current?.scrollToIndex({ index: index, itemIndex: 0, viewPosition: 0.5 });
    dataRef?.scrollToLocation({ sectionIndex: 0, itemIndex: 0 });
  };
  const onPressCities = ({ item, index }) => {
    setTabActiveCityIndex(index);
    captureClickEvents({
      eventName: `City_${item.name}_`,
      suffix: index,
      value:  `City|${item.name}_${index}`
    })
    isSelfScroll.current.value = true;
    citiesFlatList?.current?.scrollToIndex({ index: index, animated: true, viewPosition: 0.5 });
    dataRef?.scrollToLocation({ sectionIndex: index, itemIndex: 0 });
  };

  const renderListItemDay = (item, index) => {
    const containerStyle =
      index === tabActiveIndex
        ? [styles.tabContainer, styles.tabActive]
        : [styles.tabContainer, styles.tabInactive];
    const textStyle =
      index === tabActiveIndex ? [styles.tabText, styles.tabActiveText] : [styles.tabText];
    return (
      <TouchableOpacity
        activeOpacity={0.8}
        style={containerStyle}
        onPress={() => onPressCategoryTab({ item, index })}
      >
        {!!item.label && <Text style={textStyle}>{item.label}</Text>}
        <View style={index === tabActiveIndex ? styles.activeTabBorder : []} />
      </TouchableOpacity>
    );
  };

  const renderCities = (item, index) => {
    const containerStyle =
      index === tabActiveCityIndex
        ? [styles.tabContainer, styles.tabCityContainer, styles.tabCityActive]
        : [styles.tabContainer, styles.tabCityContainer];
    const textStyle =
      index === tabActiveCityIndex
        ? [styles.tabText, styles.tabActiveText, styles.tabCityText]
        : [styles.tabText, styles.tabCityText];
    return (
      <TouchableOpacity
        activeOpacity={0.8}
        style={containerStyle}
        onPress={() => onPressCities({ item, index })}
      >
        {!!item.name && <Text style={textStyle}>{item.name}</Text>}
      </TouchableOpacity>
    );
  };

  const scrollTop = ({ index, sectionIndex }) => {
    dataRef?.scrollToLocation({
      itemIndex: index + 1,
      sectionIndex: sectionIndex,
      animated: true,
      viewPosition: 0,
    });
  };
  const renderProperty = (data) => {
    return (
      <PropertyPhotos
        {...data}
        GalleryImage={GalleryImage}
        GalleryVideo={GalleryVideo}
        scrollTop={scrollTop}
      />
    );
  };
  const onBackPressed = () => {
    captureClickEvents({
      eventName: 'Gallery_close ',
    })
    flatList?.current?.scrollToIndex({ index: 0, itemIndex: 0, viewPosition: 0.5 });
    if (isRawClient()) {
      props.hideOverlays([Overlay.GRID_GALLERY_V2]);
    } else {
      HolidayNavigation.pop();
    }
  };

  const onViewableItemsChanged = useCallback(({ viewableItems, changed }) => {
    if (isSelfScroll.current.value) {
      return;
    }
    let sectionIndexToScroll = -1;
    let sectionIndexHeaderToScroll = -1;

    for (let index = 0; index < viewableItems?.length; index++) {
      const el = viewableItems[index];
      if (el?.isViewable) {
        const {
          section: { index: sectionIndex },
        } = el;
        sectionIndexToScroll = sectionIndex;
        break;
      }
    }
    if (sectionIndexHeaderToScroll > -1) {
      sectionIndexToScroll = sectionIndexHeaderToScroll;
    }
    if (sectionIndexToScroll >= 0) {
      citiesFlatList?.current?.scrollToIndex({
        index: sectionIndexToScroll,
        animated: true,
        viewPosition: 0.5,
      });
      setTabActiveCityIndex(sectionIndexToScroll);
    }
  }, []);

  const back = () => {
    captureClickEvents({
      eventName: 'Gallery_image_close',
    })
  };
  const handleImageClicked = (imageList, destCity, usp, name, index) => {
    const pageProps = {
      imageList,
      destCity,
      usp,
      name,
      index,
      isGalleryV2: true,
      back,
    };
    if (isRawClient()) {
      props.showOverlay(Overlay.FULL_PAGE_GALLERY, pageProps);
    } else {
      HolidayNavigation.push(HOLIDAY_ROUTE_KEYS.DETAIL_GRID_GALLERY_FULL_PAGE, pageProps);
    }

    captureClickEvents({
      eventName: `imageClick_${tabs?.[tabActiveIndex].label}_${destCity}_`,
      suffix: index,
      actionType: PDT_EVENT_TYPES.contentClicked,
      value: `imageClick|${tabs?.[tabActiveIndex].label}_${destCity}_${index}`,
    });
  };

  const GalleryImage = ({ imageList, url, index, imageDescription, imageStyle, usps, city }) => {
    const imageClick = () => {
      handleImageClicked(imageList, city, usps, tabs?.[tabActiveIndex]?.label, index);
    };

    return (
      <TouchableOpacity activeOpacity={0.8} style={styles.imageWrapper} onPress={imageClick}>
        <PlaceholderImageWithText
          style={[styles.image1, imageStyle]}
          url={getOptimisedUrl({ url, width: imageStyle.width, height: 200 })}
          description={imageDescription}
          fontStyle={styles.fontOverImage}
          imageStyle={styles.borderRadiusImage}
          wrapperStyle={styles.wrapperWidth}
        />
      </TouchableOpacity>
    );
  };

  const GalleryVideo = (url) => {
    let mediaurl = url;
    if(isRawClient() && !isEmpty(packageVideo?.mpdUrl)) {
      mediaurl = packageVideo?.mpdUrl;
    }
    return (
      <View style={styles.videoWrapper}>
        <PhoenixVideoSection togglePopup={() => {}} videoUrl={mediaurl} videoPaused={false} repeat={true} width={screenWidth - 30} height={170} />
      </View>
    );
  };

  const getSectionData = (list) => {
    const toReturn = [];
    let index = 0;
    list?.filter((el) => {
      if (el?.components?.length > 0) {
        let { usps, mediaList } = getImagesForDay(el?.components);
        toReturn.push({
          title: el.title,
          data: el.components,
          index,
          usps,
          mediaList,
          type: 'section',
        });
        index = index + 1;
      }
    });
    return toReturn;
  };
  const renderContent = ({ item, index, section }) => {
    if (!item) {
      return [];
    }
    const { type } = item;
    const { fullPath, imageDescription, city, hlsUrl } = item || {};
    switch (type) {
      case subTypes.IMAGE:
        return GalleryImage({
          imageList: section.mediaList,
          url: fullPath,
          usps: section.usps,
          index,
          imageDescription,
          imageStyle: {
            width: screenWidth - 30,
            marginTop: 5,
            marginBottom: 5,
          },
          city,
        });
      case subTypes.VIDEO:
        return GalleryVideo(hlsUrl);
      case subTypes.PROPERTY:
        return renderProperty({ ...item, index, sectionIndex: section.index });
      default:
        return [];
    }
  };

  const currentCategory = imageDetail?.gallery?.[tabActiveIndex];
  const sectionData = getSectionData(currentCategory?.components);
  const scrollFailForDayFlatlist = (e) => {
    setTimeout(() => {
      flatList?.current?.scrollToIndex({
        index: tabActiveIndex,
        itemIndex: tabActiveIndex,
        viewPosition: 1,
      });
    }, 100);
  };
  const renderSectionHeader = ({ section }) => {
    const { title, index } = section;
    if (section?.data?.[0]?.type == subTypes.PROPERTY) {
      return;
    }
    const category = tabs?.[tabActiveIndex]?.label;
    return (
      <View style={[styles.cityRow, index != 0 ? AtomicCss.marginTop20 : []]}>
        <Text numberOfLines={1}>
          {!!category && <Text style={styles.cityHeading}> {category}</Text>}
        </Text>
        {!!title && <Text style={styles.cityName}> {title}</Text>}
      </View>
    );
  };

  return (
    <SafeAreaView containerStyle={{ flex: 1 }}>
      <PageHeader
        title={'Photos and Videos'}
        subTitle={name}
        showBackBtn
        iconSource={CloseBtn}
        containerStyles={styles.galleryHeader}
        onBackPressed={onBackPressed}
      />
      <View
        style={styles.mainTab}
        onLayout={() => {
          if (flatList.current && tabs && tabs.length > 0 && tabActiveIndex < tabs.length) {
            flatList.current.scrollToIndex({ animated: false, index: tabActiveIndex });
          }
        }}
      >
        <FlatList
          horizontal
          data={tabs}
          renderItem={({ item, index }) => renderListItemDay(item, index)}
          showsVerticalScrollIndicator={false}
          showsHorizontalScrollIndicator={false}
          onScrollToIndexFailed={scrollFailForDayFlatlist}
          snapToAlignment={'center'}
          ref={flatList}
          bounces={false}
          initialNumToRender={5}
        />
        <LinearGradient
          style={styles.shadow}
          start={{ x: 0, y: 0 }}
          end={{ x: 0, y: 1 }}
          colors={['rgba(0, 0, 0, 0.2)', 'rgba(0, 0, 0, 0)']}
        />
      </View>
      <View style={styles.cityFlatListWrapper}>
        {cityData?.length > 1 && (
          <View>
            <FlatList
              horizontal={true}
              data={cityData}
              contentContainerStyle={{ paddingRight: 20, overflow: 'hidden' }}
              bounces={false}
              style={styles.cityFlatList}
              renderItem={({ item, index }) => renderCities(item, index)}
              showsVerticalScrollIndicator={false}
              showsHorizontalScrollIndicator={false}
              initialScrollIndex={0}
              initialNumToRender={30}
              onScrollToIndexFailed={() => {}}
              ref={citiesFlatList}
            />
          </View>
        )}
        <View style={styles.separatorLine} />
        <SectionList
          sections={sectionData}
          style={[
            isAndroidClient() ? AtomicCss.marginBottom48 : AtomicCss.marginBottom30,
            AtomicCss.paddingHz15,
          ]}
          keyExtractor={(item, index) => item + index}
          viewabilityConfig={{ viewAreaCoveragePercentThreshold: 40 }}
          onViewableItemsChanged={onViewableItemsChanged}
          renderItem={renderContent}
          showsVerticalScrollIndicator={false}
          ref={(e) => {
            dataRef = e;
          }}
          onMomentumScrollEnd={() => {
            isSelfScroll.current.value = false;
          }}
          onScrollToIndexFailed={() => {}}
          renderSectionHeader={renderSectionHeader}
          stickySectionHeadersEnabled={false}
          initialScrollIndex={0}
          initialNumToRender={30}
          bounces={false}
          alwaysBounceVertical={false}
        />
      </View>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  tabContainer: {
    backgroundColor: holidayColors.white,
    // marginBottom: 10,
    paddingVertical: 6,
    paddingLeft: 7,
    alignSelf: 'flex-start',
  },
  tabText: {
    ...paddingStyles.ph10,
    ...fontStyles.labelBaseRegular,
    color: holidayColors.black,
  },
  tabActive: {
    shadowOpacity: 0,
    paddingVertical: 0,
  },
  tabActiveText: {
    color: holidayColors.black,
    ...fontStyles.labelBaseBold,
  },
  tabCityContainer: {
    borderColor: holidayColors.grayBorder,
    borderWidth: 1,
    borderRadius: 20,
    marginRight: 5,
    paddingHorizontal: 10,
  },
  tabCityText: {
    color: holidayColors.gray,
    ...fontStyles.labelSmallBold,
  },
  tabCityActive: {
    backgroundColor: holidayColors.lightBlueBg,
    borderColor: holidayColors.primaryBlue,
  },
  tabCityActiveText: {
    color: holidayColors.black,
    ...fontStyles.labelBaseBold,
  },
  mainTab: {},
  image1: {
    height: 200,
    borderRadius: 4,
  },
  ratingStarWrapper: {
    flexDirection: 'row',
    marginTop: 3,
    alignItems: 'center',
    marginLeft: 3,
  },
  starRating: {
    width: 9,
    height: 8,
  },
  icon: {
    width: 14,
    height: 14,
  },
  mr13: { marginRight: 13 },
  fontOverImage: {
    ...fontStyles.labelSmallBold,
    color: holidayColors.white,
    display: 'flex',
    alignItems: 'center',
    position: 'absolute',
    zIndex: 10,
  },

  imageWrapper: { display: 'flex', width: '100%' },
  videoWrapper: {
    height: 170,
    marginBottom: 5,
    borderRadius: 4,
    borderWidth: 1,
    backgroundColor: holidayColors.black,
    borderColor: holidayColors.black,
  },
  galleryHeader: {
  },
  headerText: { fontSize: 18, color: holidayColors.gray, fontWeight: '600' },
  packageName: { color: holidayColors.lightGray, fontSize: 12 },
  cityFlatListWrapper: { height: screenHeight - 150 },
  cityFlatList: { flex: 0, maxHeight: 42, minHeight: 42, paddingHorizontal: 15 },
  activeTabBorder: {
    borderColor: holidayColors.primaryBlue,
    width: 'auto',
    borderBottomWidth: 4,
    marginTop: 8,
  },
  cityName: {
    ...fontStyles.labelSmallBold,
    opacity: 0.5,
    color: holidayColors.black,
  },
  cityHeading: {
    ...fontStyles.headingBase,
    color: holidayColors.black,
    marginRight: 5,
  },
  cityRow: {
    marginVertical: 5,
    display: 'flex',
    flexDirection: 'column',
  },
  tabInactive: { alignItems: 'flex-start', paddingTop: 0, paddingBottom: 12 },
  crossIcon: { tintColor: holidayColors.lightGray },
  separatorLine: {
    borderWidth: 1,
    width: '100%',
    borderColor: holidayColors.grayBorder,
    marginBottom: 10,
  },
  borderRadiusImage: { borderRadius: 5 },
  shadow: { height: 10, width: '100%', marginBottom: 5 },
  wrapperWidth: { width: 90 },
  textWrapper: { width: '100%', paddingRight: 20 },
});

const mapStateToProps = (state) => {
  const { detailData } = state.holidaysDetail || {};
  return { detailData };
};

const mapDispatchToProps = (dispatch) => ({
  showOverlay: (key, data) => dispatch(showOverlay(key, data)),
  hideOverlays: (keys) => dispatch(hideOverlays(keys)),
});

export default connect(mapStateToProps, mapDispatchToProps)(GridGalleryV2);
