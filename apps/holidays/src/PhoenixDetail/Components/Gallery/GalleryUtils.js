const CATEGORY = 'CATEGORY';
export const subTypes = {
  IMAGE: 'IMAGE',
  VIDEO: 'VIDEO',
  CITY: 'CITY',
  PROPERTY: 'PROPERTY',
};
export const getImagesForDay = (images, selectedOfficialMedia) => {
  const filteredImages = images?.filter(media => {
    const isSameMediaCategory = selectedOfficialMedia ? media.travellerMedia === selectedOfficialMedia : true;
    return media?.type === subTypes.IMAGE && isSameMediaCategory;
  });
  const mediaList = filteredImages?.map(media => media.fullPath);
  const usps = filteredImages?.map(media => media.imageDescription);
  return { mediaList, usps };
};

export const getOptimisedUrl = ({url,width,height})=>{
  const width1 = width * 3;
  const height1 = height * 3;
  return url ? `${url}?downsize=${width1}:${height1}` : null;
};

const getPropertyInfo = (components, propertyInfo) => {
  const propertyData = components?.map(el => getGridData(el, el));
  return { isProperty: true, propertyData, ...propertyInfo };
};

const getCityTabs = (components) => {
  return {
    cityTabList: components
        .filter(el => el?.components?.length > 0)
        .map((el, index) => ({ name: el?.title, cityIndex: index })),
  };
};

export const getGridData = (firstElement, images, tabActiveIndex, tabActiveCityIndex) => {
  if (firstElement?.type === CATEGORY) {
    {
      /* to check for category tabs*/
    }
    const tabs = [];
    images?.map((el) => {
      tabs.push({ label: el.title });
    });

    const components = images?.[tabActiveIndex]?.components;
    if (components?.[0]?.type === subTypes.CITY) {
      /* hardcoded 0th index,because 0th index will be used to check type */
      const { cityTabList: cityData } = getCityTabs(components);
      return { tabs, cityData };
    }

    return { tabs };
  } else if (firstElement?.type === subTypes.IMAGE) {
    return {
      url: images?.fullPath,
      index: null,
      styles: { height: 174, borderRadius: 4 },
      imageDescription: images?.imageDescription,
      travellerMedia: images?.travellerMedia,
      type: images?.type,
    };
  } else if (firstElement?.type === subTypes.VIDEO) {
    return { videoUrl: images?.hlsUrl, travellerMedia: images?.travellerMedia, type: images?.type };
  } else if (firstElement?.type === subTypes.PROPERTY) {
    const propertyInfo = getPropertyInfo(firstElement.components, { ...firstElement });
    propertyInfo.hotelName = propertyInfo?.name;
    return propertyInfo;
  }
};
