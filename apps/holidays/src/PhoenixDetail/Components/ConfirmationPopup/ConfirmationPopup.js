import React, { useEffect, useRef, useState } from 'react';
import { StyleSheet, Text, TouchableOpacity, View, Animated } from 'react-native';
import AtomicCss from '@mmt/legacy-commons/Styles/AtomicCss';
import PrimaryButton from 'mobile-holidays-react-native/src/Common/Components/Buttons/PrimaryButton';
import { marginStyles, paddingStyles } from 'mobile-holidays-react-native/src/Styles/Spacing';
import { holidayColors } from 'mobile-holidays-react-native/src/Styles/holidayColors';
import { fontStyles } from 'mobile-holidays-react-native/src/Styles/holidayFonts';
import { borderRadiusValues } from 'mobile-holidays-react-native/src/Styles/holidayBorderRadius';
import TextButton from '../../../Common/Components/Buttons/TextButton';
import BottomSheetOverlay from 'mobile-holidays-react-native/src/Common/Components/BottomSheetOverlay';

const ConfirmationPopup = (props) => {
  const {
    confirmDialogVisibility,
    onUpdatePackageClickFromPopup,
    onNotNowClicked,
    onShowMeClicked,
    showMeButtonVisibility = true,
    headingText = 'You have done some changes in the Holidays package.',
    primaryButtonText = 'UPDATE PACKAGE',
  } = props || {};

  const ANIMATION_DURATION = 200;
  const BACKGROUND_TRANSPARENCY = 0.3;
  const [renderComponent, setRenderComponent] = useState(false);
  const slideAnim = useRef(
    new Animated.Value(confirmDialogVisibility ? 0 : ANIMATION_DURATION),
  ).current;
  const backgroundColorAnim = useRef(
    new Animated.Value(confirmDialogVisibility ? BACKGROUND_TRANSPARENCY : 0),
  ).current;

  const interpolatedBackgroundColor = backgroundColorAnim.interpolate({
    inputRange: [0, BACKGROUND_TRANSPARENCY],
    outputRange: ['rgba(0, 0, 0, 0)', 'rgba(0, 0, 0, 0.3)'],
  });

  useEffect(() => {
    if (confirmDialogVisibility) {
      setRenderComponent(true);
      // Slide up
      Animated.sequence([
        Animated.timing(slideAnim, {
          toValue: 0,
          duration: ANIMATION_DURATION,
          useNativeDriver: false,
        }),
        // Background Color Animation
        Animated.timing(backgroundColorAnim, {
          toValue: confirmDialogVisibility ? BACKGROUND_TRANSPARENCY : 0,
          duration: ANIMATION_DURATION,
          useNativeDriver: false,
        }),
      ]).start();
    } else if (renderComponent) {
      // Slide down
      Animated.sequence([
        Animated.timing(backgroundColorAnim, {
          toValue: confirmDialogVisibility ? BACKGROUND_TRANSPARENCY : 0,
          duration: ANIMATION_DURATION,
          useNativeDriver: false,
        }),
        Animated.timing(slideAnim, {
          toValue: 300,
          duration: ANIMATION_DURATION,
          useNativeDriver: false,
        }),
      ]).start(() => {
        setRenderComponent(false);
      });
    }
  }, [confirmDialogVisibility]);

  return (
    <>
      {(renderComponent && (
        <Animated.View
          style={[
            styles.popOverlay,
            {
              transform: [{ translateY: slideAnim }],
              backgroundColor: interpolatedBackgroundColor,
            },
          ]}
        >
          <View style={styles.popContent}>
            <View>
              <Text style={styles.headingText}>{headingText}</Text>
            </View>
            <View style={[AtomicCss.marginTop5, AtomicCss.marginBottom10]}>
              <Text style={styles.subHeadingText}>Do you want to update?</Text>
            </View>
            <View style={[AtomicCss.flexRow, AtomicCss.spaceBetween]}>
              <TouchableOpacity onPress={() => onNotNowClicked()}>
                <View style={[AtomicCss.paddingLeft10, AtomicCss.paddingTop16]}>
                  <Text style={styles.leftBtn}>NOT NOW</Text>
                </View>
              </TouchableOpacity>

              <View style={[AtomicCss.alignCenter]}>
                <PrimaryButton
                  buttonText={primaryButtonText}
                  handleClick={() => onUpdatePackageClickFromPopup(true)}
                  btnContainerStyles={styles.updateButton}
                />
                {showMeButtonVisibility && (
                  <TouchableOpacity onPress={onShowMeClicked}>
                    <View style={marginStyles.mt16}>
                      <Text style={styles.showSelection}>SHOW SELECTION</Text>
                    </View>
                  </TouchableOpacity>
                )}
              </View>
            </View>
          </View>
        </Animated.View>
      )) ||
        null}
    </>
  );
};

const styles = StyleSheet.create({
  popOverlay: {
    position: 'absolute',
    left: 0,
    right: 0,
    top: 0,
    bottom: 0,
    justifyContent: 'flex-end',
    zIndex: 5,
  },
  popContent: {
    backgroundColor: holidayColors.white,
    borderTopLeftRadius: borderRadiusValues.br16,
    borderTopRightRadius: borderRadiusValues.br16,
    ...paddingStyles.ph16,
    ...paddingStyles.pv30,
  },
  headingText: {
    ...fontStyles.labelMediumBlack,
    color: holidayColors.black,
  },
  subHeadingText: {
    ...fontStyles.labelMediumRegular,
    color: holidayColors.gray,
  },
  leftBtn: {
    ...fontStyles.labelMediumRegular,
    color: holidayColors.primaryBlue,
  },
  showSelection: {
    ...fontStyles.labelBaseBold,
    color: holidayColors.gray,
  },
  updateButton: {
    ...paddingStyles.ph20,
  },
});

export default ConfirmationPopup;
