import React from 'react';
import { Text, View, StyleSheet } from 'react-native';
import { NO_TRANSPORT_OPTION_INCLUDED } from '../ComboConstats';
import { holidayColors } from '../../../../Styles/holidayColors';
import { marginStyles } from '../../../../Styles/Spacing';
import { fontStyles } from '../../../../Styles/holidayFonts';

const NoTransportOption = ({ journeyType }) => (
    <>
        <View style={styles.boarderNoTransport}/>
        <Text style={styles.noTransportOptionText}>
            {journeyType} :
            <Text style={styles.noTransportOptionSubText}>
                {NO_TRANSPORT_OPTION_INCLUDED}
            </Text>
        </Text>
    </>
);

const styles = StyleSheet.create({
    boarderNoTransport: {
        height: 1,
        backgroundColor: holidayColors.grayBorder,
        width: '100%',
        ...marginStyles.mb16,
    },
    noTransportOptionText: {
        ...fontStyles.labelSmallBold,
        ...marginStyles.ml16,
        ...marginStyles.mb16,
        color: holidayColors.gray,
    },
    noTransportOptionSubText: {
        ...fontStyles.labelSmallRegular,
        color: holidayColors.gray,
    },
});
export default NoTransportOption;
