import React from 'react';
import {StyleSheet, View} from 'react-native';
import {holidayColors} from '../../../../Styles/holidayColors';
import ComboShortListPriceSection from './ShortlistPriceSection';
import ComboJourneySection from './ComboJourneySection';

const ComboShortListCard = (props) => {
    const { selectedItem  } = props || {};
    if (!selectedItem){
        return [];
    }
    return (
        <View style={styles.container}>
            <ComboJourneySection {...props} />
            <ComboShortListPriceSection {...props}/>
        </View>
    );
};

const styles = StyleSheet.create({
    container: {
        flexDirection: 'row',
        backgroundColor: holidayColors.fadedYellow,
    },
});

export default ComboShortListCard;
