import React from 'react';
import { Image, StyleSheet, Text, View } from 'react-native';
import { JOURNEY_TYPE, TRANSPORT_TYPE } from '../ComboConstats';
import { fontStyles } from '../../../../Styles/holidayFonts';
import { getImageUrl, IMAGE_ICON_KEYS } from '../../../../Common/Components/HolidayImageUrls';
import ComboTransportSection from './ComboTransportSection';
import { holidayColors } from '../../../../Styles/holidayColors';
import {
  checkCombo,
  extractSourceAndDestination,
  findCarItineraryTimeBySellableId,
  getCommuteDataForSellableId,
  getFlightArrivalDepartureTimeForHeader,
  getFlightDataForSellableId,
  getIsFlightDummy,
  getSellableId,
} from '../Utils/ComboUtils';
import { getAirlineIconUrl } from '../../FlightDetailPage/FlightListing/FlightsUtils';
import { marginStyles, paddingStyles } from '../../../../Styles/Spacing';

const ComboJourneySection = (props) => {
  const { selectedItem, sourceCity, DestinationCity } = props || {};
  const type = checkCombo(selectedItem);
  const { departureDetail, returnDetail, flightDetail, carItineraryDetail, title } =
    selectedItem || {};
  let DepartureTime,
    arrivalTime,
    leftIconUrl,
    rightIconUrl,
    transportTitle = title,
    isDummy = false;

  if (type === TRANSPORT_TYPE.FLIGHT) {
    const flightDepartureData = getFlightDataForSellableId(
      flightDetail,
      getSellableId(departureDetail, TRANSPORT_TYPE.FLIGHT),
    );
    const flightReturnData = getFlightDataForSellableId(
      flightDetail,
      getSellableId(returnDetail, TRANSPORT_TYPE.FLIGHT),
    );

    DepartureTime = getFlightArrivalDepartureTimeForHeader(flightDepartureData);
    arrivalTime = getFlightArrivalDepartureTimeForHeader(flightReturnData);

    leftIconUrl = getAirlineIconUrl(flightDepartureData?.airlineCode);
    rightIconUrl = getAirlineIconUrl(flightReturnData?.airlineCode);
    isDummy = getIsFlightDummy(flightDetail);
    transportTitle = 'Flight';
  } else if (type === TRANSPORT_TYPE.CABS) {
    const carItineraries = carItineraryDetail?.carItineraries;
    const carDepartureData = getCommuteDataForSellableId(
      carItineraries,
      getSellableId(departureDetail, TRANSPORT_TYPE.CABS),
    );
    const carReturnData = getCommuteDataForSellableId(
      carItineraries,
      getSellableId(returnDetail, TRANSPORT_TYPE.CABS),
    );

    leftIconUrl = carDepartureData?.vehicleInfo?.imageUrl;
    rightIconUrl = carReturnData?.vehicleInfo?.imageUrl;

    DepartureTime = findCarItineraryTimeBySellableId(
      carItineraryDetail,
      getSellableId(departureDetail, TRANSPORT_TYPE.CABS),
      JOURNEY_TYPE.DEPARTURE,
    );
    arrivalTime = findCarItineraryTimeBySellableId(
      carItineraryDetail,
      getSellableId(returnDetail, TRANSPORT_TYPE.CABS),
      JOURNEY_TYPE.RETURN,
    );
    transportTitle = 'Cab';
  }

  const getCabsOrFlight = () => {
    return (
      <>
        <ComboTransportSection
          sectionTitle={`Departure ${transportTitle}`}
          time={DepartureTime}
          imageUrl={leftIconUrl}
          type={type}
          isDummy={isDummy}
        />
        <ComboTransportSection
          sectionTitle={`Return ${transportTitle}`}
          time={arrivalTime}
          imageUrl={rightIconUrl}
          type={type}
          isDummy={isDummy}
        />
      </>
    );
  };

  const renderTransportIcon = (iconKey, style) => (
    <Image source={{ uri: getImageUrl(iconKey) }} style={style} />
  );

  const renderTransportRoute = (transportType, fromCity, toCity) => {
    const srcDest = `${fromCity} → ${toCity}`;
    const MAX_TEXT_LENGTH = 18;
    const formattedSrcDest =
      srcDest.length < MAX_TEXT_LENGTH
        ? `${srcDest}`
        : `${srcDest.substring(0, MAX_TEXT_LENGTH)}...`;
    return (
      <View style={styles.routeRow}>
        <Text style={[marginStyles.mb4, fontStyles.labelSmallBold]}>{transportType} :</Text>
        <View style={{ flexDirection: 'row', ...marginStyles.mb4 }}>
          {transportType === 'Return' &&
            renderTransportIcon(IMAGE_ICON_KEYS.CAB_ICON_WHITE, styles.cabIcon)}
          {renderTransportIcon(IMAGE_ICON_KEYS.FLIGHT_ICON_WHITE, styles.flightIcon)}
          {transportType === 'Departure' &&
            renderTransportIcon(IMAGE_ICON_KEYS.CAB_ICON_WHITE, styles.cabIcon)}
        </View>
        <Text maxLength={2} style={[fontStyles.labelSmallRegular]}>
          {formattedSrcDest}
        </Text>
      </View>
    );
  };

  const getCombo = () => {
    const {
      source: departureSource,
      destination: departureDestination,
      transports: deparutreTransports,
    } = extractSourceAndDestination(departureDetail?.transports[0]?.cities);
    const {
      source: returnSource,
      destination: returnDestination,
      transports: returnTransports,
    } = extractSourceAndDestination(returnDetail?.transports[0]?.cities);

    return (
      <View style={[styles.section, { flexDirection: 'row' }]}>
        <>
          {renderTransportRoute('Departure', departureSource, departureDestination)}
          <View style={{ width: 18 }} />
          {renderTransportRoute('Return', returnSource, returnDestination)}
        </>
      </View>
    );
  };

  const getEmptyView = () => (
    <View style={styles.nto}>
      <Text style={styles.ntoText}>Reach {DestinationCity} on your own</Text>
    </View>
  );

  switch (type) {
    case TRANSPORT_TYPE.CABS:
    case TRANSPORT_TYPE.FLIGHT:
      return getCabsOrFlight();
    case TRANSPORT_TYPE.COMBO:
      return getCombo();
    case TRANSPORT_TYPE.NONE:
      return getEmptyView();
    default:
      return [];
  }
};

export default ComboJourneySection;

const styles = StyleSheet.create({
  section: {
    flex: 1,
    ...paddingStyles.pl16,
    ...paddingStyles.pr16,
    ...paddingStyles.pt8,
    ...paddingStyles.pb16,
  },
  image: {
    width: 24,
    height: 24,
    resizeMode: 'cover',
    borderRadius: 5,
    marginHorizontal: 2,
    ...marginStyles.mr10,
  },
  text: {
    color: holidayColors.lightGray,
    ...fontStyles.labelSmallRegular,
  },
  cabIcon: {
    width: 16,
    height: 16,
    marginHorizontal: 2,
  },
  flightIcon: {
    width: 16,
    height: 16,
    ...marginStyles.mh2,
  },
  row: {
    flexDirection: 'row',
  },
  routeRow: {
    ...marginStyles.mt6,
  },
  nto: {
    justifyContent: 'center',
    flex: 1,
    alignItems: 'center',
  },
  ntoText: {
    color: holidayColors.black,
    ...fontStyles.labelMediumBlack,
    ...marginStyles.ml16,
  },
});
