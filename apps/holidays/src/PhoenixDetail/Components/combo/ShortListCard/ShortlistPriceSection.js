import React from 'react';
import {View, Text, StyleSheet} from 'react-native';
import {fontStyles} from '../../../../Styles/holidayFonts';
import { formatAmount } from '@mmt/legacy-commons/Helpers/currencyUtils';
import PrimaryButton from '../../../../Common/Components/Buttons/PrimaryButton';
import SelectedTag from '../../../../Common/Components/Tags/SelectedTag';
import {holidayColors} from '../../../../Styles/holidayColors';
import { marginStyles, paddingStyles } from '../../../../Styles/Spacing';

const ComboShortListPriceSection = (props) => {
    const {selectedItem, onUpdateClick, showUpdate} = props || {};


    const { priceDiff } = selectedItem || {};

    if (showUpdate) {
        return (<View style={styles.priceSection}>
            <View style={styles.rowContainer}>
                <Text style={styles.text}>{formatAmount(priceDiff)}</Text>
                <Text style={styles.subText}>Per Person</Text>
                <PrimaryButton
                    buttonText="UPDATE"
                    btnContainerStyles={styles.update}
                    handleClick={onUpdateClick}
                />
            </View>
        </View>);
    }

    return <View style={styles.selectedButton}><SelectedTag/></View>;
};

export default ComboShortListPriceSection;

const styles = StyleSheet.create({

    selectedButton:{
        justifyContent: 'center',
        alignItems: 'center',
        ...marginStyles.mr16,
        ...marginStyles.mt16,
        ...marginStyles.mb16,
    },
    priceSection: {
        flex: 1,
        ...paddingStyles.pl16,
        ...paddingStyles.pr16,
        ...paddingStyles.pt8,
        ...paddingStyles.pb8,
        justifyContent: 'center',
        alignItems: 'flex-end',
    },
    text: {
        color: holidayColors.black,
        ...marginStyles.ml2,
        ...fontStyles.labelMediumBold,
    },
    subText: {
        color: holidayColors.gray,
        ...marginStyles.ml2,
        ...marginStyles.mb4,
        ...fontStyles.labelSmallRegular,
    },
    rowContainer: {
        alignItems: 'flex-start',
    },
    update: {
        ...paddingStyles.pv6,
        ...paddingStyles.ph12,
    },
});
