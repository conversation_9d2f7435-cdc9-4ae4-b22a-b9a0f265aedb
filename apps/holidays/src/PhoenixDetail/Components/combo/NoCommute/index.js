import { StyleSheet, Text, View } from 'react-native';
import React from 'react';
import {holidayColors} from '../../../../Styles/holidayColors';
import {fontStyles} from '../../../../Styles/holidayFonts';
import {PLEASE_NOTE_TEXT} from '../ComboConstats';
import {marginStyles} from '../../../../Styles/Spacing';

const NoCommute = (props) => {
    const { showBottomDivider = false, subText, removedText, type = ''} = props || {};
    const backgroundColor = showBottomDivider ?  holidayColors.grayBorder : holidayColors.white;
    return (<View style={styles.container}>
        <View style={{ ...marginStyles.mr16}}>
            <Text style={styles.type}>{type}</Text>
        <Text style={styles.arriveText}>{subText}</Text>
        <Text style={styles.longMessage}>{PLEASE_NOTE_TEXT}<Text style={styles.longMessageSubText}>{removedText}</Text></Text>
            {<View style={[styles.divider, {backgroundColor}]}/>}
        </View>

    </View>);
};

const styles = StyleSheet.create({
    container:{
      backgroundColor: holidayColors.white,
        ...marginStyles.ml16,
        ...marginStyles.mr8,
    },
    arrivalContainer:{
        flexDirection: 'row',
        ...marginStyles.ml10,
        ...marginStyles.mb16,
        ...marginStyles.mt16,
    },
    arriveText:{
        color:holidayColors.black,
        ...fontStyles.labelBaseRegular,
    },
    type:{
        ...fontStyles.labelLargeBlack,
        color:holidayColors.black,
        ...marginStyles.mb8,
    },
    divider:{
        height:1,
        ...marginStyles.mt16,
    },
    longMessage:{
        color: holidayColors.red,
        ...fontStyles.labelMediumBold,
        ...marginStyles.mt10,
    },
    longMessageSubText:{
        color: holidayColors.red,
        ...fontStyles.labelBaseRegular,
    },
    viewOptions:{
        marginLeft: 42,
        ...marginStyles.mr16,
        ...marginStyles.mb16,
    },
    iconStyle:{
        width: 20,
        height: 20,
        resizeMode: 'cover',
    },
});
export default NoCommute;
