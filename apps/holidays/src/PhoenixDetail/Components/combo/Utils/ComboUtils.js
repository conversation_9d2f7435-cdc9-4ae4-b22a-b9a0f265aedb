import {isEmpty, isEqual} from 'lodash';
import {JOURNEY_TYPE, TRANSPORT_TYPE} from '../ComboConstats';
import {FLIGHT_TYPE} from '../../../../MimaPreSales/CompareQuotes/compareConstants';
import fecha from 'fecha';

export const getFlightStartDateBySellableId = (flightDetail, sellableId) => {
  const { flightDetailType, obtFlightGroup, flightGroup, departureFlight, returnFlight } = flightDetail || {};

  let startDate = '';
  if (flightDetailType === FLIGHT_TYPE.DOM_ONWARDS) {
    const flightData = flightGroup?.flights.find((flight) => flight.sellableId === sellableId);
    startDate = flightData?.departure;
  } else if (flightDetailType === FLIGHT_TYPE.OBT) {
    const flightData = obtFlightGroup?.flights.find((flight) => flight.sellableId === sellableId);
    startDate = flightData?.departure;
  }  else {
    const isDepartureFlight = departureFlight?.sellableId === sellableId;
    const isReturnFlight = returnFlight?.sellableId === sellableId;
    startDate = isDepartureFlight
      ? departureFlight?.departure
      : isReturnFlight
      ? returnFlight?.departure
      : undefined;
  }
  return fecha.format(new Date(startDate), 'ddd, DD MMM');
};

export const findCarItineraryStartDateBySellableId = (
  carItineraryDetail,
  sellableId,
  journeyType,
) => {
  if (!carItineraryDetail?.carItineraries) {
    return '';
  }

  for (let itinerary of carItineraryDetail.carItineraries) {
    const commute = itinerary?.commuteDetails?.find((item) => item.sellableId === sellableId);
    if (commute) {
      switch (journeyType) {
        case JOURNEY_TYPE.DEPARTURE:
          return fecha.format(new Date(commute.startDate), 'ddd, DD MMM');
        case JOURNEY_TYPE.RETURN:
          return fecha.format(new Date(commute.endDate), 'ddd, DD MMM');
        default:
          return '';
      }
    }
  }
  return '';
};

export const getDefaultSelectedItem = (response) => {
    const { variants } = response || {};
    if (variants) {
        const selectedKey = Object.keys(variants).find(key => variants[key].selected);
        return selectedKey ? variants[selectedKey] : null;
    }
    return null;
};

export const getSelectedItemIndex = ({ selectedItem, data }) => {
    return data?.findIndex((item) => item?.variantId === selectedItem?.variantId);
  };


export const isSelectedItemDefault = (selectedItem, response) => {

    const defaultItem = getDefaultSelectedItem(response);
    return isEqual(selectedItem, defaultItem);
};

export const getJourneyOrderData = ({ journey, journeyType, item } = {}) => {
    const { transports = [] } = journey || {};
    const { flightDetail = {}, carItineraryDetail = {} } = item || {};
    if (transports.length === 0) {
      return null;
    }
    let citiesListInOrder = [];
    const legObj = transports.map((item) => {
      const { type, cities = [], sellableId } = item || {};
      citiesListInOrder = [...citiesListInOrder, ...cities];
      return {
        type,
        sellableId,
      };
    });
  
    let startDate = '';
    const transportObj = transports?.[0];
    const type = transportObj?.type;
    if (type === 'Flights') {
      startDate = getFlightStartDateBySellableId(flightDetail, transportObj.sellableId);
    } else if (type === 'Cabs') {
      startDate = findCarItineraryStartDateBySellableId(
        carItineraryDetail,
        transportObj.sellableId,
        journeyType,
      );
    }

    const [fromCity] = citiesListInOrder;
    const toCity = citiesListInOrder.slice(-1)[0];
    const viaCities = citiesListInOrder
      .slice(1, -1)
      .filter((city, index, self) => self.indexOf(city) === index); // Remove duplicates while maintaining order

    let journeyLegString = [];
    journeyLegString.push(fromCity + ' → ');
    journeyLegString.push(...viaCities.map((city) => city + ' → ')); // Using spread operator to push elements of viaCities into journeyLegString
    journeyLegString.push(toCity);

    return {
      fromCity,
      toCity,
      viaCities,
      legObj,
      journeyType,
      journeyLegString,
      journey,
      startDate,
    };
  };

export const checkCombo = item => {
    const { variantType, carItineraryDetail } = item || {};
    const departureTransports = item?.departureDetail ? item.departureDetail?.transports : [];
    const returnTransports = item?.returnDetail ? item?.returnDetail?.transports : [];


    if (isEmpty(carItineraryDetail)) {
        if (departureTransports.length === 0 && returnTransports.length === 0) {
            return TRANSPORT_TYPE.NONE;
        } else if (variantType === TRANSPORT_TYPE.NMC) {
            return TRANSPORT_TYPE.NONE;
        }
    }

    if (departureTransports.length > 1 || returnTransports.length > 1) {
        return TRANSPORT_TYPE.COMBO;
    }

    if (departureTransports.length === 1 && returnTransports.length === 1) {
        if (departureTransports[0].type === returnTransports[0].type) {
            return departureTransports[0]?.type?.toUpperCase();
        }
    }

    if (carItineraryDetail) {
        return TRANSPORT_TYPE.NMC;
    }
    return TRANSPORT_TYPE.COMBO;
};

const convertMinInHrFormat = (timeInMin) => {
    if (timeInMin) {
        const h = Math.floor(timeInMin / 60);
        const min = timeInMin % 60;
        return `${h}h ${min > 0 ? `${min}m` : ''}`;
    }
    return '';
};

export const getIsFlightDummy = (flightDetail) => {
    const { flightDetailType,  obtFlightGroup: flightGroup, departureFlight } = flightDetail || {};
    const isDomOnwardsOrObt = flightDetailType === FLIGHT_TYPE.DOM_ONWARDS || flightDetailType === FLIGHT_TYPE.OBT;
let isDummy = false;
    if (isDomOnwardsOrObt) {
        const flightData = flightGroup?.flights?.[0];
        isDummy = flightData?.flightMetadataDetail?.isDummy;
    } else {
        isDummy = departureFlight?.flightMetadataDetail?.isDummy;
    }

    return isDummy;
};

const getFlightTimeBySellableId = (flightDetail, sellableId) => {
    const { flightDetailType, flightGroup, departureFlight, returnFlight } = flightDetail || {};

    let timeInMin;

    const isDomOnwardsOrObt = flightDetailType === FLIGHT_TYPE.DOM_ONWARDS || flightDetailType === FLIGHT_TYPE.OBT;

    if (isDomOnwardsOrObt) {
        const flightData = flightGroup?.flights.find(flight => flight.sellableId === sellableId);
        timeInMin = flightData?.duration;
    } else {
        const isDepartureFlight = departureFlight?.sellableId === sellableId;
        const isReturnFlight = returnFlight?.sellableId === sellableId;
        timeInMin = isDepartureFlight ? departureFlight?.duration :
            isReturnFlight ? returnFlight?.duration : undefined;
    }

    return convertMinInHrFormat(timeInMin);
};

export const findCarItineraryTimeBySellableId = (carItineraryDetail, sellableId, journeyType) => {
    if (!carItineraryDetail?.carItineraries) {
        return '';
    }

    for (let itinerary of carItineraryDetail.carItineraries) {
        const commute = itinerary?.commuteDetails?.find(item => item.sellableId === sellableId);
        if (commute) {
            switch (journeyType) {
                case JOURNEY_TYPE.DEPARTURE:
                    return commute.pickupDuration;
                case JOURNEY_TYPE.RETURN:
                    return commute.dropDuration;
                default:
                    return '';
            }
        }
    }
    return '';
};

export const getJourneyTimeBySellableId = (type, sellableId, flightDetail, carItineraryDetail, journeyType) => {
    const lookup = {
        [TRANSPORT_TYPE.FLIGHT]: () => getFlightTimeBySellableId(flightDetail, sellableId),
        [TRANSPORT_TYPE.CABS]: () => findCarItineraryTimeBySellableId(carItineraryDetail, sellableId, journeyType),
    };
    return lookup[type] ? lookup[type]() : '';
};

export const getFlightDataForSellableId = (flightDetail, sellableId) => {
    if (!flightDetail || !sellableId) {
        return {};
    }

    const {flightDetailType, flightGroup, departureFlight, returnFlight, obtFlightGroup} = flightDetail;

    if (flightDetailType === FLIGHT_TYPE.DOM_ONWARDS) {
        return flightGroup?.flights?.find(item => item.sellableId === sellableId) || {};
    }

    if (flightDetailType === FLIGHT_TYPE.OBT) {
        return obtFlightGroup?.flights?.find(item => item.sellableId === sellableId) || {};
    }

    if (departureFlight?.sellableId === sellableId) {
        return departureFlight;
    }

    if (returnFlight?.sellableId === sellableId) {
        return returnFlight;
    }

    return {};
};

export const getCommuteDataForSellableId = (carItineraries = [], sellableId) => {
    const [carItinerary] = carItineraries || {};
    return carItinerary?.commuteDetails?.find(item => item?.sellableId === sellableId);
};

export const formatComboTime = (timestamp) => {
    const date = new Date(timestamp);

    const hours = String(date.getHours()).padStart(2, '0');
    const minutes = String(date.getMinutes()).padStart(2, '0');

    return `${hours}:${minutes}`;
};

export const getTimeDiffForCombo = (timestamp1, timestamp2) => {
    const date1 = new Date(timestamp1);
    const date2 = new Date(timestamp2);
    const differenceInMilliseconds = date2 - date1;
    const hours = Math.floor(differenceInMilliseconds / (1000 * 60 * 60));
    const minutes = Math.floor((differenceInMilliseconds % (1000 * 60 * 60)) / (1000 * 60));
    return `${hours}h ${minutes > 0 ? `${minutes}m` : ''}`;
};


export const getFlightArrivalDepartureTimeForHeader = (flightData) => {
    const arrivalHours = String(new Date(flightData.arrival).getHours()).padStart(2, '0');
    const arrivalMinutes = String(new Date(flightData.arrival).getMinutes()).padStart(2, '0');

    const departureHours = String(new Date(flightData.departure).getHours()).padStart(2, '0');
    const departureMinutes = String(new Date(flightData.departure).getMinutes()).padStart(2, '0');

    return `${departureHours}:${departureMinutes} - ${arrivalHours}:${arrivalMinutes}`;

};

export const getSellableId = (detail, transportType) => {
    const {transports = []} = detail || {};

    const transportItem = transports.find(item =>
        item?.type?.toUpperCase() === transportType
    );

    return transportItem?.sellableId;
};

const getTransportDetails = (transports) => {
    let flightDetails = {};
    let cabDetails = {};

    for (let transport of transports) {
        const {type = '', cities} = transport || {};
        if (type.toUpperCase() === TRANSPORT_TYPE.FLIGHT) {
            const [departureCity, arrivalCity] = cities;
            flightDetails = { departureCity, arrivalCity };
        } else if (type.toUpperCase() === TRANSPORT_TYPE.CABS) {
            const [pickupCity, dropoffCity] = cities;
            cabDetails = { pickupCity, dropoffCity };
        }
    }

    return {
        flight: flightDetails,
        cab: cabDetails,
    };
};

export function extractTravelDetails(response) {
    const {departureDetail, returnDetail} = response || {};
    const departureTransports = departureDetail.transports;
    const returnTransports = returnDetail.transports;

    return {
        departure: getTransportDetails(departureTransports),
        return: getTransportDetails(returnTransports),
    };
}

export const constructCabsDataForCommuteListing = item => {
    return [
        {
            id: 1,
            journeyType: JOURNEY_TYPE.DEPARTURE,
            journeyDetails: item?.departureDetail,
        },
        {
            id: 2,
            journeyType: JOURNEY_TYPE.RETURN,
            journeyDetails: item?.returnDetail,
        },
    ];
};

export const getBaggageText = (baggage, type) => baggage?.limit ? `${type}: ${baggage?.limit} ${baggage?.unit}` : '';

export const getTripReturnDateFromDestinationDetail = destinationDetail => {
    const {destinations = []} = destinationDetail || {};
    if (destinations.length > 0) {
        return destinations[destinations.length - 1]?.end;
    }
    return '';
};

export const extractSourceAndDestination = cities => {
    if (Array.isArray(cities) && cities.length > 0) {
        const source = cities[0];
        const destination = cities[cities.length - 1];
        return {source, destination};
    }
    return {source: '', destination: ''};
};

