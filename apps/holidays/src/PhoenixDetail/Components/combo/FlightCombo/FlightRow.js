import React from 'react';
import { Image, StyleSheet, Text, View } from 'react-native';
import cStyles from '@mmt/legacy-commons/Styles/AtomicCss';
import { formatComboTime, getTimeDiffForCombo } from '../Utils/ComboUtils';
import { getAirlineIconUrl } from '../../FlightDetailPage/FlightListing/FlightsUtils';
import HolidayImageHolder from '../../../../Common/Components/HolidayImageHolder';
import { isEmpty } from 'lodash';
import { getImageUrl, IMAGE_ICON_KEYS } from '../../../../Common/Components/HolidayImageUrls';
import { holidayColors } from '../../../../Styles/holidayColors';
import { fontStyles } from '../../../../Styles/holidayFonts';
import fecha from 'fecha';
import { marginStyles } from '../../../../Styles/Spacing';

const FlightRow = (props) => {
  const { flightCard = 'Departure', flightDetails } = props;
  if (isEmpty(flightDetails)) {
    return [];
  }

  const {
    arrival,
    departure,
    fromAirport,
    toAirport,
    airlineName,
    flightKey,
    airlineCode,
    stops,
    flightMetadataDetail,
  } = flightDetails || {};
  const isDummy = flightMetadataDetail?.isDummy;
  const arrivalTime = formatComboTime(arrival);
  const departTime = formatComboTime(departure);
  const from = fromAirport?.airportCity;
  const to = toAirport?.airportCity;
  const flightType = stops === 0 ? 'Non Stop' : `${stops} Stop`;
  const duration = getTimeDiffForCombo(departure, arrival);
  const startDate = fecha.format(new Date(departure), 'ddd, DD MMM');

  return !isDummy ? (
    <View style={styles.flightComboRow}>
      <View style={styles.rowHeader}>
        <View>
          <Text style={styles.journeyType}>
            {flightCard} : {startDate}
          </Text>
          <Text style={styles.journeyDetail}>
            <Text style={cStyles.blackFont}>
              {from} - {to}
            </Text>
            | {airlineName}
          </Text>
        </View>
        <Image
          source={{ uri: getImageUrl(IMAGE_ICON_KEYS.BAGGAGE_ICON_WHITE) }}
          style={styles.baggageIcon}
        />
      </View>
      <View style={[cStyles.alignCenter, cStyles.flexRow]}>
        <View style={[cStyles.marginRight5]}>
          <HolidayImageHolder
            imageUrl={getAirlineIconUrl(airlineCode)}
            style={styles.airlineLogo}
          />
        </View>
        <View style={styles.cardContentRow}>
          <View style={[cStyles.marginRight10, cStyles.flex1]}>
            <Text style={[cStyles.font16, cStyles.blackFont, cStyles.blackText]}>{departTime}</Text>
            <Text
              style={[fontStyles.labelSmallRegular, { color: holidayColors.gray }]}
              numberOfLines={1}
              ellipsizeMode="tail"
            >
              {from}
            </Text>
          </View>
          <View style={[cStyles.alignCenter, cStyles.flex1]}>
            <View>
              <Text style={[fontStyles.labelSmallBold, { color: holidayColors.black }]}>
                {duration}
              </Text>
            </View>
            <View style={styles.durationDivider}>
              <View style={styles.stopPointer} />
            </View>
            <View>
              <Text style={[fontStyles.labelSmallBold, { color: holidayColors.gray }]}>
                {flightType}
              </Text>
            </View>
          </View>
          <View style={[cStyles.marginLeft12, cStyles.flex1]}>
            <Text
              style={[cStyles.font16, cStyles.blackFont, cStyles.blackText, cStyles.alignRight]}
            >
              {arrivalTime}
            </Text>
            <Text
              style={[
                fontStyles.labelSmallRegular,
                cStyles.alignRight,
                { color: holidayColors.gray },
              ]}
              numberOfLines={1}
              ellipsizeMode="tail"
            >
              {to}
            </Text>
          </View>
        </View>
      </View>
    </View>
  ) : (
    <View style={styles.flightComboRow}>
      <View style={styles.rowHeader}>
        <View>
          <Text style={styles.journeyType}>
            {flightCard} : {startDate}
          </Text>
          <Text style={styles.jouryneyDetail}>
            <Text style={cStyles.blackFont}>
              {from} - {to}
            </Text>{' '}
            | Tentative Flight
          </Text>
        </View>
      </View>
      <View style={[cStyles.alignCenter, cStyles.flexRow]}>
        <View style={[cStyles.marginRight5]}>
          <HolidayImageHolder imageUrl={getImageUrl(IMAGE_ICON_KEYS.AIRPLANE_ICON)} style={styles.dummyAirlineLogo} />
        </View>
        <View style={styles.cardContentRow}>
          <View style={[cStyles.marginRight10, cStyles.flex1]}>
            <Text
              style={[fontStyles.labelSmallRegular, cStyles.blackFont, cStyles.blackText]}
              numberOfLines={1}
              ellipsizeMode="tail"
            >
              {from}
            </Text>
          </View>
          <View style={[cStyles.alignCenter, cStyles.flex1]}>
            <View style={styles.durationDivider}>
              <View style={styles.stopPointer} />
            </View>
          </View>
          <View style={[cStyles.marginLeft12, cStyles.flex1]}>
            <Text
              style={[
                fontStyles.labelSmallRegular,
                cStyles.blackFont,
                cStyles.blackText,
                cStyles.alignRight,
              ]}
              numberOfLines={1}
              ellipsizeMode="tail"
            >
              {to}
            </Text>
          </View>
        </View>
      </View>
    </View>
  );
};
const styles = StyleSheet.create({
  cardContentRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    flex: 1,
    alignItems: 'center',
  },
  journeyType: {
    color: holidayColors.gray,
    ...fontStyles.labelSmallBlack,
    ...marginStyles.mb4,
  },
  jouryneyDetail: {
    ...fontStyles.labelSmallBold,
    color: holidayColors.gray,
  },
  flightComboRow: {
    borderTopWidth: 1,
    borderColor: '#d8d8d8',
    paddingHorizontal: 16,
    paddingVertical: 11,
  },
  rowHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-end',
    marginBottom: 17,
  },
  durationDivider: {
    borderWidth: 0.75,
    borderColor: '#50E3C2',
    position: 'relative',
    marginTop: 6,
    marginBottom: 3,
    width: 51,
  },
  airlineLogo: {
    width: 32,
    height: 32,
    resizeMode: 'cover',
    marginRight: 15,
    borderRadius: 8,
  },
  dummyAirlineLogo: {
    width: 16,
    height: 16,
    resizeMode: 'cover',
    marginRight:15,
    borderRadius:8,
},
  availableSlotTags: {
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.22,
    shadowRadius: 2.22,
    elevation: 3,
    backgroundColor: 'white',
    padding: 2,
    borderRadius: 25,
    flexDirection: 'row',
    alignItems: 'center',
    minWidth: 75,
    overflow: 'hidden',
  },
  iconAirlineLogo: {
    width: 24,
    height: 24,
    resizeMode: 'cover',
  },
  baggageIcon: {
    width: 31,
    height: 20,
    resizeMode: 'cover',
  },
});
export default FlightRow;
