import React from 'react';
import {StyleSheet, View} from 'react-native';
import cStyles from '@mmt/legacy-commons/Styles/AtomicCss';
import FlightRow from './FlightRow';
import {isEqual} from 'lodash';
import {getFlightDataForSellableId, getIsFlightDummy, getSellableId} from '../Utils/ComboUtils';
import {holidayColors} from '../../../../Styles/holidayColors';
import {TRANSPORT_TYPE} from '../ComboConstats';
import ComboHeader from '../comboHeader';

const FlightCombo = (props) => {
    const {item, setSelectedItem, selectedItem, onViewDetailsClick, subtitleData} = props || {};

    const isSelected = isEqual(item, selectedItem);

    const {departureDetail, returnDetail, title, priceDiff, flightDetail} = item || {};
    const flightDepartureData = getFlightDataForSellableId(
        flightDetail,
        getSellableId(departureDetail, TRANSPORT_TYPE.FLIGHT),
      );
      const flightReturnData = getFlightDataForSellableId(
        flightDetail,
        getSellableId(returnDetail, TRANSPORT_TYPE.FLIGHT),
      );

      const flightData = [
        { ...flightDepartureData, type: 'Departure' },
        { ...flightReturnData, type: 'Return' },
      ];
      const isDummy = getIsFlightDummy(flightDetail);

      const toggleSelected = () => {
        setSelectedItem(item);
    };

    const handleOnViewDetailsClick = () => {
        onViewDetailsClick(item);
    };

    return (
      <>
        <View style={styles.flightComboWrap}>
          <View
            style={[styles.flightComboBox, isSelected ? styles.activeTouchable : cStyles.whiteBg]}
          >
            <ComboHeader
              title={title}
              priceDiff={priceDiff}
              subTitle={'View Details'}
              showSubTitle={!isDummy}
              isSelected={isSelected}
              toggleSelected={toggleSelected}
              handleOnViewDetailsClick={handleOnViewDetailsClick}
            />
            {flightData.map((flightDetails, index) => (
              <FlightRow
                key={index}
                flightDetails={flightDetails}
                flightCard={flightDetails.type}
              />
            ))}
          </View>
        </View>
      </>
    );
};
const styles = StyleSheet.create({
    flightComboWrap: {
        marginBottom: 16,
        paddingHorizontal: 16,
    },
    flightComboBox: {
        borderRadius: 16,
        borderWidth:1,
        borderColor:holidayColors.grayBorder,
        backgroundColor:holidayColors.white,
        // Android shadow (elevation)
        elevation: 6, // Adjust the value as needed
        // iOS shadow
        shadowColor: 'rgba(74, 74, 74, 0.10)',
        shadowOffset: { width: 0, height: 4 },
        shadowOpacity: 0.5,
        shadowRadius: 8,
        zIndex:100,
    },
    activeTouchable: {
        backgroundColor: holidayColors.lightBlueBg,
        borderWidth:1,
        borderColor:holidayColors.primaryBlue,
    },
    selectedButton:{
        flexDirection:'row',
        paddingHorizontal:8,
        paddingVertical:2,
        borderRadius:16,
        alignItems:'center',
        justifyContent:'center',
        alignSelf:'flex-start',
    },
    whiteCircle:{
        width:16,
        height:16,
        borderRadius: 20,
        backgroundColor:'#97d6ec',
        alignItems:'center',
        justifyContent:'center',
        marginRight:5,
    },
    arrowWhite:{
        width:9,
        height:7,
    },
    flightComboHeader:{
        flexDirection:'row',
        alignItems:'center',
        justifyContent:'space-between',
        paddingHorizontal:16,
        paddingTop:16,
        paddingBottom:8,
    },
    moreFlightInfoContainer:{
        backgroundColor:holidayColors.fadedGreen,
        paddingHorizontal:16,
        paddingBottom:10,
        paddingTop:24,
        marginTop:-10,
        borderBottomLeftRadius:16,
        borderBottomRightRadius:16,
    },
});

export default FlightCombo;
