import React from 'react';
import { Image, StyleSheet, Text, View } from 'react-native';
import cStyles from '@mmt/legacy-commons/Styles/AtomicCss';
import { getImageUrl, IMAGE_ICON_KEYS } from '../../../../Common/Components/HolidayImageUrls';
import {
  findCarItineraryStartDateBySellableId,
  findCarItineraryTimeBySellableId,
} from '../Utils/ComboUtils';
import { holidayColors } from '../../../../Styles/holidayColors';
import { marginStyles, paddingStyles } from '../../../../Styles/Spacing';
import { fontStyles } from '../../../../Styles/holidayFonts';

const CabRow = (props) => {
  const { journeyType, journeyDetails, cabType, carItineraryDetail, isSelected } = props || {};
  const { transports } = journeyDetails || {};
  const [transport] = transports || {};
  const { cities = [], sellableId = '' } = transport || {};
  const [from = '', to = ''] = cities || {};
  const iconBackgroundColor = isSelected ? holidayColors.lightBlueBg : holidayColors.white;

  const journeyTime = findCarItineraryTimeBySellableId(carItineraryDetail, sellableId, journeyType);
  const startDate = findCarItineraryStartDateBySellableId(
    carItineraryDetail,
    sellableId,
    journeyType,
  );
  return (
    <View style={styles.cabComboRow}>
      <Text style={styles.journeyType}>
        {journeyType} : {startDate}
      </Text>
      <View style={styles.cardContentRow}>
        <View style={styles.destinationFrom}>
          <Text style={styles.cityText} numberOfLines={1} ellipsizeMode="tail">
            {from}
          </Text>
        </View>
        <View style={[cStyles.alignCenter, cStyles.flex1]}>
          <View>
            <Text style={fontStyles.labelSmallBold}>{journeyTime}</Text>
          </View>
          <View style={styles.durationDivider}>
            <View style={[styles.iconContainer, { backgroundColor: iconBackgroundColor }]}>
              <Image
                source={{ uri: getImageUrl(IMAGE_ICON_KEYS.CAB_ICON_TRANSPARENT) }}
                style={styles.cabIcon}
              />
            </View>
          </View>
          <View>
            <Text style={fontStyles.labelSmallBold}>{cabType}</Text>
          </View>
        </View>
        <View style={styles.destinationTo}>
          <Text style={styles.cityText} numberOfLines={1} ellipsizeMode="tail">
            {to}
          </Text>
        </View>
      </View>
    </View>
  );
};
const styles = StyleSheet.create({
  cabComboRow: {
    borderTopWidth: 1,
    borderColor: holidayColors.grayBorder,
    ...paddingStyles.ph16,
    ...paddingStyles.pv10,
  },
  journeyType: {
    color: holidayColors.gray,
    ...fontStyles.labelSmallBlack,
    ...marginStyles.mb10,
  },
  cityText: {
    ...fontStyles.labelMediumBlack,
    color: holidayColors.black,
  },
  cardContentRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    flex: 1,
    alignItems: 'center',
  },
  cabComboRowData: {
    flexDirection: 'row',
  },
  durationDivider: {
    borderWidth: 0.75,
    borderColor: holidayColors.green,
    position: 'relative',
    ...marginStyles.mt8,
    ...marginStyles.mb6,
    width: 100,
    justifyContent: 'center',
    alignItems: 'center',
  },
  cabIcon: {
    width: '100%',
    height: '100%',
    top: '50%',
    transform: [{ translateY: -8 }],
  },
  availableSlotTags: {
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.22,
    shadowRadius: 2.22,
    elevation: 3,
    backgroundColor: 'white',
    padding: 2,
    borderRadius: 25,
    flexDirection: 'row',
    alignItems: 'center',
    minWidth: 75,
    overflow: 'hidden',
  },
  destinationFrom: {
    flex: 1,
    ...paddingStyles.pr20,
  },
  destinationTo: {
    flex: 1,
    flexDirection: 'row',
    justifyContent: 'flex-end',
    ...paddingStyles.pl20,
  },
  iconContainer: {
    width: 16,
    height: 16,
    position: 'absolute',
    paddingLeft: 2,
    paddingRight: 2,
  },
});
export default CabRow;
