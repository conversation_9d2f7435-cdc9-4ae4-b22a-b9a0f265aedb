import React from 'react';
import {StyleSheet, View} from 'react-native';
import cStyles from '@mmt/legacy-commons/Styles/AtomicCss';
import CabRow from './CabRow';
import {isEqual} from 'lodash';
import { constructCabsDataForCommuteListing } from '../Utils/ComboUtils';
import ComboHeader from '../comboHeader';

const CabCombo = ({ item = {}, setSelectedItem, selectedItem, onViewDetailsClick }) => {
    const isSelected = isEqual(item, selectedItem);
    const { title, priceDiff, carItineraryDetail = {} } = item;

    const handleOnViewDetailsClick = () => onViewDetailsClick(item);
    const toggleSelected = () => setSelectedItem(item);
    const cabData = constructCabsDataForCommuteListing(item);

    return (
        <View style={styles.cabComboWrap}>
            <View style={[styles.cabComboBox, isSelected ? styles.activeTouchable : cStyles.whiteBg]}>
                <ComboHeader
                    title={title}
                    priceDiff={priceDiff}
                    isSelected={isSelected}
                    subTitle={'View Details'}
                    toggleSelected={toggleSelected}
                    handleOnViewDetailsClick={handleOnViewDetailsClick}
                />
                {cabData.map(({ id, journeyType, journeyDetails }) => (
                    <CabRow
                        key={id}
                        journeyType={journeyType}
                        journeyDetails={journeyDetails}
                        cabType="Non Stop"
                        carItineraryDetail={carItineraryDetail}
                        isSelected={isSelected}
                    />
                ))}
            </View>
        </View>
    );
};

const styles = StyleSheet.create({
    cabComboWrap: {
        marginBottom: 16,
        paddingHorizontal: 16,
    },
    cabComboBox: {
        borderRadius: 16,
        borderWidth: 1,
        borderColor: '#d8d8d8',
        backgroundColor: '#ffffff',
        elevation: 6,
        shadowColor: 'rgba(74, 74, 74, 0.10)',
        shadowOffset: { width: 0, height: 4 },
        shadowOpacity: 0.5,
        shadowRadius: 8,
        zIndex: 100,
    },
    activeTouchable: {
        backgroundColor: '#eaf5ff',
        borderWidth: 1,
        borderColor: '#008cff',
    },
    cabComboHeader: {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-between',
        paddingHorizontal: 16,
        paddingTop: 16,
        paddingBottom: 8,
    },
});

export default CabCombo;
