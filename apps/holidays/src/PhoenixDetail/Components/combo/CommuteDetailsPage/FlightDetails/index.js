import React from 'react';
import {StyleSheet, Text, View} from 'react-native';
import cStyles from '@mmt/legacy-commons/Styles/AtomicCss';
import {formatComboTime, getTimeDiffForCombo} from '../../Utils/ComboUtils';
import {getAirlineIconUrl} from '../../../FlightDetailPage/FlightListing/FlightsUtils';
import HolidayImageHolder from '../../../../../Common/Components/HolidayImageHolder';
import {isEmpty} from 'lodash';
import {holidayColors} from '../../../../../Styles/holidayColors';
import {marginStyles, paddingStyles} from '../../../../../Styles/Spacing';
import FlightLayover from '../../../DayPlan/Flight/FlightLayover';
import {formatDate} from '../../../../../utils/HolidayDateUtils';
import BaggageDetails from '../BaggageDetails';
import {OvernightFlightPill} from '../../../../../Common/Components/OvernightFlightComponents';
import HolidaysMessageStrip from '../../../../../Common/Components/HolidaysMessageStrip';
import AlertIcon from '@mmt/legacy-assets/src/alert_cream.webp';
import {fontStyles} from "../../../../../Styles/holidayFonts";
import ItineraryUnitExtraInfoMessages from '../../../ItineraryUnitExtraInfoMessages';

const FlightDetails = (props) => {
    const { flightsData, baggageData = [] } = props || {};
    if (isEmpty(flightsData)){
        return [];
    }

    const { flightLegs, stops, overnightLabel,overnightMessage, isOvernight, flightMetadataDetail } = flightsData || {};

    const renderItem = (item, index) => {
        const {flightId, fromAirport, toAirport, oprAirlineCode, arrival, departure, oprAirlineName} = item || {};
        const flightType = 'Non Stop';
        const {layoverDuration , fromAirport : layoverAirport = ''} = flightLegs[index + 1] || {};

        const arrivalTime = formatComboTime(arrival);
        const departTime = formatComboTime(departure);
        const duration = getTimeDiffForCombo(departure, arrival);

        const arrivalDate = formatDate(arrival, { day: '2-digit', weekday: 'short', month: 'short' });
        const departureDate = formatDate(departure, { day: '2-digit', weekday: 'short', month: 'short'  });
        const baggageInfo = baggageData?.hasOwnProperty(flightId) ? baggageData[flightId] : null;

        return (
            <View style={styles.itemContainer} key={flightId + fromAirport + toAirport + arrival}>
            <View style={[cStyles.flexRow, cStyles.alignCenter, cStyles.marginBottom10]}>
                <HolidayImageHolder imageUrl={getAirlineIconUrl(oprAirlineCode)} style={styles.airlineLogo}/>
                <View style={styles.marginLeft10}>
                    <Text style={[cStyles.font12, cStyles.boldFont, cStyles.blackText]}>{oprAirlineName}</Text>
                    <Text style={[cStyles.font12, cStyles.defaultText]}>{flightId}</Text>
                </View>
            </View>
            <View style={[cStyles.alignCenter, cStyles.flexRow, cStyles.marginBottom10]}>
                <View style={styles.cardContentRow}>
                    <View style={[cStyles.marginRight10, cStyles.flex1]}>
                        <Text
                            style={[cStyles.font16, cStyles.blackFont, cStyles.blackText, cStyles.marginBottom2]}>{departTime}</Text>
                        <Text style={[fontStyles.labelSmallRegular, {color: holidayColors.gray}, cStyles.marginBottom2]}>{departureDate}</Text>
                        <Text
                            style={[fontStyles.labelSmallRegular, {color: holidayColors.gray}, cStyles.marginBottom2]}>{fromAirport?.airportCity}</Text>
                        {/*<Text style={[cStyles.font12, cStyles.midGreyText]}>MUM</Text>*/}
                    </View>
                    <View style={[cStyles.alignCenter, cStyles.flex1]}>
                        <View><Text style={[cStyles.font12, cStyles.boldFont, cStyles.blackText]}>{duration}</Text></View>
                        <View style={styles.durationDivider}><View style={styles.stopPointer}/></View>
                        <View><Text style={[cStyles.font12, cStyles.boldFont, cStyles.defaultText, {color: holidayColors.gray}]}>{flightType}</Text></View>
                    </View>
                    <View style={[cStyles.marginLeft12, cStyles.flex1]}>
                        <Text
                            style={[cStyles.font16, cStyles.blackFont, cStyles.blackText, cStyles.alignRight, cStyles.marginBottom2]}>{arrivalTime}</Text>
                        <Text style={[fontStyles.labelSmallRegular, {color: holidayColors.gray}, cStyles.alignRight, cStyles.marginBottom2]}>{arrivalDate}</Text>
                        <Text
                            style={[fontStyles.labelSmallRegular, {color: holidayColors.gray}, cStyles.alignRight, marginStyles.mb2]}>{toAirport?.airportCity}</Text>
                    </View>
                </View>
            </View>
                <BaggageDetails baggageInfo={baggageInfo}/>
                {layoverDuration > 0 &&
                    <FlightLayover
                    duration={layoverDuration}
                    airport={layoverAirport?.airportCity}
                    airportCode={layoverAirport?.airportCode}
                    flightChange={false}
                    style={marginStyles.mb0}
                />}
            </View>
            );
    };

    return (<View>
        {flightLegs.map(renderItem)}
        <ItineraryUnitExtraInfoMessages extraInfo={flightMetadataDetail?.flightExtraInfo || []}/>
    </View>);
};
const styles = StyleSheet.create({
    cardContentRow: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        flex: 1,
        alignItems: 'center',
    },
    durationDivider: {
        borderWidth: 2,
        borderColor: holidayColors.lightGreen,
        position: 'relative',
        ...marginStyles.mt6,
        ...marginStyles.mb4,
        width: 50,
        borderRadius: 6,
    },
    departureContainer: {
        borderTopWidth: 1,
        borderColor: holidayColors.grayBorder,
        ...paddingStyles.pt10,
    },
    airlineLogo: {
        width: 26,
        height: 26,
        borderRadius: 4,
        resizeMode: 'cover',
    },
    marginLeft10: {
        ...marginStyles.ml10,
    },
    container: {
        flex: 1,
        backgroundColor: holidayColors.gray,
    },
    itemContainer: {
        ...paddingStyles.pb16,
    },
    separator: {
        height: 1,
        backgroundColor: holidayColors.gray,
    },
    messageStrip:{
        ...marginStyles.mb16,
        ...marginStyles.mt12,
    },
});
export default FlightDetails;
