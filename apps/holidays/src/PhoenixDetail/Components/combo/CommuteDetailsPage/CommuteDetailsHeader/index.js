import React from 'react';
import {View, Text, StyleSheet, TouchableOpacity, Image, Platform, StatusBar} from 'react-native';
import cStyles from '@mmt/legacy-commons/Styles/AtomicCss';
import iconArrowLeft from '../../../images/ic_arrowLeftTail.png';
import {HolidayNavigation} from '../../../../../Navigation';
import { trackPhoenixDetailLocalClickEvent } from '../../../../Utils/PhoenixDetailTracking';
import { logPhoenixDetailPDTEvents } from 'mobile-holidays-react-native/src/utils/PhoenixDetailPDTTrackingUtils';
import { PDT_EVENT_TYPES } from 'mobile-holidays-react-native/src/utils/HolidayPDTConstants';
import { holidayNavigationPop } from '../../../../Utils/DetailPageNavigationUtils';
import { isMobileClient } from '../../../../../utils/HolidayUtils';

const CommuteDetailsHeader = (props) => {
    const { subtitleData, overlayKey, hideOverlays } = props || {};
    const captureClickEvents = () =>{
        logPhoenixDetailPDTEvents({
            actionType: PDT_EVENT_TYPES.buttonClicked,
            value: 'back',
            subPageName: 'details:detailmoretransportoptions',
        })
        trackPhoenixDetailLocalClickEvent({
          eventName: 'back',
          prop1: 'details:detailmoretransportoptions',
        });
    }
    const onBackPressed = () => {
        captureClickEvents();
        if (isMobileClient()) {
            HolidayNavigation.pop();
        } else {
            holidayNavigationPop({
                overlayKeys: [overlayKey],
                hideOverlays,
            })
        }
        holidayNavigationPop({
            overlayKeys: [overlayKey],
            hideOverlays,
        })
    };

    return (
        <View style={styles.modeTransportHeader}>
            <TouchableOpacity onPress={onBackPressed}>
                <View style={cStyles.paddingAll20}>
                    <Image source={iconArrowLeft} style={styles.iconArrowLeft} />
                </View>
            </TouchableOpacity>
            <View>
                <Text style={[cStyles.font16, cStyles.boldFont, cStyles.blackText, cStyles.marginBottom3]}>Change Mode of Transport</Text>
                <Text style={[cStyles.font12, cStyles.defaultText]}>{subtitleData}</Text>
            </View>
        </View>
    );
};
const styles = StyleSheet.create({
    modeTransportHeader: {
        shadowColor: '#000',
        shadowOffset: {
            width: 0,
            height: 1,
        },
        shadowOpacity: 0.20,
        shadowRadius: 1.41,
        elevation: 3,
        flexDirection: 'row',
        alignItems: 'center',
        paddingVertical: 3,
    },
    iconArrowLeft: {
        width: 16,
        height: 16,
        resizeMode: 'cover',
    },
});
export default CommuteDetailsHeader;
