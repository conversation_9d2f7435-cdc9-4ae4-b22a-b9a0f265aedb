import React from 'react';
import {StyleSheet, Text, View} from 'react-native';
import {actionSeperator} from '../../../DayPlan/dayPlanStyles';
import {fontStyles} from '../../../../../Styles/holidayFonts';
import {holidayColors} from '../../../../../Styles/holidayColors';
import HolidayImageHolder from '../../../../../Common/Components/HolidayImageHolder';
import {isEmpty} from 'lodash';
import {marginStyles, paddingStyles} from '../../../../../Styles/Spacing';
import { JOURNEY_TYPE } from '../../ComboConstats';

const CabDetails = ({ carItineraryDetail = {}, cabPickUpAirport = '', cabType = '', journeyType = ''}) => {
    if (isEmpty(carItineraryDetail)) {
        return [];
    }

    const {carItineraries = []} = carItineraryDetail;
    const {commuteDetails = []} = carItineraries[0] || {};
    const {vehicleInfo = {}} = commuteDetails[0] || {};
    const {vehicleName, facilities, imageUrl} = vehicleInfo;

    const Facilities = ({facilities}) => {
        const MAX_FACILITY_COUNT = 3;
        if (!facilities || facilities.length === 0) {
            return null;
        }

        return (
            <View style={styles.transferMajorDetails}>
                {facilities.slice(0, MAX_FACILITY_COUNT).map((facility, index) => {
                    const {title} = facility || {};
                    return title ? (
                        <Text key={index} style={styles.detailText}>
                            {title}
                            {(index === MAX_FACILITY_COUNT - 1 || index === facilities.length - 1) ? '' :
                                <Text style={actionSeperator}> | </Text>}
                        </Text>
                    ) : [];
                })}
            </View>
        );
    };

    return (
        <View>
            {!isEmpty(cabPickUpAirport) && (journeyType === JOURNEY_TYPE.DEPARTURE) && <View style={styles.cabOffer}>
                <Text style={fontStyles.labelSmallRegular}>Cab {cabType} {cabPickUpAirport} Airport</Text>
            </View>}
            <View>
                {!isEmpty(cabPickUpAirport) && <Text style={[fontStyles.labelSmallRegular, marginStyles.mb6]}>{cabType} Airport in {cabPickUpAirport}</Text> }
                <View style={styles.cabContainer}>
                    <HolidayImageHolder imageUrl={imageUrl} style={styles.carImgStyle} resizeMode="contain"/>
                    <View style={marginStyles.mt4}>
                        <Text style={[fontStyles.labelBaseRegular, marginStyles.mb6]}>{vehicleName}</Text>
                        <Facilities facilities={facilities}/>
                    </View>
                </View>
            </View>
            {!isEmpty(cabPickUpAirport) && (journeyType === JOURNEY_TYPE.RETURN) && <View style={styles.cabOfferReturn}>
                <Text style={fontStyles.labelSmallRegular}>Cab {cabType} {cabPickUpAirport} Airport</Text>
            </View>}
        </View>
    );
};

const styles = StyleSheet.create({
    cabContainer: {
        flexDirection: 'row',
    },
    cabOffer: {
        backgroundColor: holidayColors.lightBlueBg,
        borderWidth: 1,
        borderColor: holidayColors.grayBorder,
        ...paddingStyles.ph14,
        ...paddingStyles.pv4,
        borderRadius: 50,
        alignSelf: 'flex-start',
        ...marginStyles.mb18,
    },
    cabOfferReturn: {
        backgroundColor: holidayColors.lightBlueBg,
        borderWidth: 1,
        borderColor: holidayColors.grayBorder,
        ...paddingStyles.ph14,
        ...paddingStyles.pv4,
        borderRadius: 50,
        alignSelf: 'flex-start',
        ...marginStyles.mb18,
        marginTop:14,
    },
    carImgStyle: {
        width: 86,
        height: 61,
        ...marginStyles.mr12,
        borderWidth: 1,
        borderColor: holidayColors.grayBorder,
        borderRadius: 10,
    },
    transferMajorDetails: {
        flex: 1,
        flexDirection: 'row',
    },
    detailText: {
        ...fontStyles.labelBaseBold,
        color: holidayColors.gray,
    },
});

export default CabDetails;
