import React, {useEffect, useState} from 'react';
import {FlatList, StyleSheet, View} from 'react-native';
import cStyles from '@mmt/legacy-commons/Styles/AtomicCss';
import CommuteDetailsHeader from './CommuteDetailsHeader';
import CommuteDetailLeg from './CommuteDetailLeg';
import {fetchReviewContent} from '../../../../utils/HolidayNetworkUtils';
import {JOURNEY_TYPE, VIEW_STATE} from '../ComboConstats';
import HolidayDetailLoader from '../../HolidayDetailLoader';
import {marginStyles, paddingStyles} from '../../../../Styles/Spacing';
import {holidayColors} from '../../../../Styles/holidayColors';
import { Overlay } from '../../DetailOverlays/OverlayConstants';

const CommuteDetailPage = (props) => {
    const { item, dynamicPackageId, packageSrcDestData, hideOverlays } = props || {};
    const [viewState, setViewState] = useState(VIEW_STATE.LOADING);
    const [baggageData, setBaggageData ] = useState(null);

    const { sourceCity, DestinationCity } = packageSrcDestData || {};

    const  subtitleData = [ `${sourceCity} - ${DestinationCity} |  ${DestinationCity} - ${sourceCity} `];
    const fetchBaggageDataAndUpdateState = async () => {
        const response = await fetchReviewContent(dynamicPackageId);
        if (response && response.success) {
            const {flightContent} = response || {};
            const {baggageInfoMap} = flightContent || {};
            if (baggageInfoMap){
                setBaggageData(baggageInfoMap);
            }
        }
        setViewState(VIEW_STATE.SUCCESS);
    };

    useEffect(() => {
        fetchBaggageDataAndUpdateState(dynamicPackageId);
    }, [dynamicPackageId]);


    const renderProgressView = () =>
        <HolidayDetailLoader
            openingSavedPackage
            showDateText={false}
            changeAction
            loadingText="Loading Commute Details ..."
        />;

    const renderItem = ({ item: journeyType }) => <CommuteDetailLeg
            item={item}
            baggageData={baggageData}
            packageSrcDestData={packageSrcDestData}
            type={journeyType}
        />;

    const renderSeparator = () => <View style={styles.separator} />;

    const renderSuccess = () =>
        <View style={cStyles.flex1}>
            <CommuteDetailsHeader
                subtitleData={subtitleData}
                item={item} 
                overlayKey={Overlay.COMMUTE_DETAIL_PAGE}
                hideOverlays={hideOverlays}
            />
            <FlatList
                style={styles.commuteContainer}
                data={[JOURNEY_TYPE.DEPARTURE, JOURNEY_TYPE.RETURN]}
                keyExtractor={item => item}
                renderItem={renderItem}
                ItemSeparatorComponent={renderSeparator}
            />
        </View>;

    switch (viewState) {
        case VIEW_STATE.LOADING:
            return renderProgressView();
        case VIEW_STATE.SUCCESS:
            return renderSuccess();
        default:
            return [];
    }
};
const styles = StyleSheet.create({
    commuteContainer: {
        ...paddingStyles.ph16,
        backgroundColor: holidayColors.white,
    },
    separator: {
        height: 1,
        backgroundColor: holidayColors.grayBorder,
        ...marginStyles.mt16,
        ...marginStyles.mb16,
    },
});
export default CommuteDetailPage;
