import React from 'react';
import { StyleSheet, Text, View } from 'react-native';
import cStyles from '@mmt/legacy-commons/Styles/AtomicCss';
import { holidayColors } from '../../../../Styles/holidayColors';
import { isEmpty } from 'lodash';
import { getBaggageText } from '../Utils/ComboUtils';
import { paddingStyles } from '../../../../Styles/Spacing';
import { fontStyles } from '../../../../Styles/holidayFonts';

const BaggageDetails = ({ baggageInfo }) => {
  if (isEmpty(baggageInfo)) {
    return [];
  }

  const ADULT = baggageInfo?.paxTypeBaggageInfoMap?.ADULT || {};
  const cabinBaggageText = getBaggageText(ADULT.cabinBaggage || {}, 'Cabin');
  const checkinBaggageText = getBaggageText(ADULT.checkInBaggage || {}, 'Check-in');

  const baggageInfoText = [cabinBaggageText, checkinBaggageText].filter(Boolean).join('  ●  ');

  return (
    <View style={styles.baggageContainer}>
      <Text style={[fontStyles.labelSmallBold, cStyles.blackText, cStyles.marginBottom2]}>
        Baggage
      </Text>
      {baggageInfoText && (
        <Text style={[fontStyles.labelSmallRegular, { color: holidayColors.gray }]}>
          {cabinBaggageText} {checkinBaggageText ? `●  ${checkinBaggageText}` : ''}
        </Text>
      )}
    </View>
  );
};
const styles = StyleSheet.create({
  baggageContainer: {
    borderTopWidth: 1,
    borderTopColor: holidayColors.grayBorder,
    ...paddingStyles.pt10,
  },
});
export default BaggageDetails;
