import React from 'react';
import {StyleSheet, Text, View} from 'react-native';
import FlightDetails from '../FlightDetails';
import cStyles from '@mmt/legacy-commons/Styles/AtomicCss';
import {colors} from '@mmt/legacy-commons/Styles/globalStyles';
import CabDetails from '../CabDetails';
import {extractTravelDetails, getFlightDataForSellableId, getSellableId} from '../../Utils/ComboUtils';
import {JOURNEY_TYPE, TRANSPORT_TYPE} from '../../ComboConstats';
import {isEmpty} from 'lodash';
import {formatDate} from '../../../../../utils/HolidayDateUtils';
import HolidayImageHolder from '../../../../../Common/Components/HolidayImageHolder';
import {getImageUrl, IMAGE_ICON_KEYS} from '../../../../../Common/Components/HolidayImageUrls';
import {marginStyles, paddingStyles} from '../../../../../Styles/Spacing';
import {holidayColors} from '../../../../../Styles/holidayColors';
import {fontStyles} from '../../../../../Styles/holidayFonts';

const CommuteDetailLeg = ({ item, baggageData = [], packageSrcDestData, type = '' }) => {
    const { flightDetail, carItineraryDetail } = item || {};
    const legDetail = type === JOURNEY_TYPE.RETURN ? item?.returnDetail : item?.departureDetail;
    const flightSellableId = getSellableId(legDetail, TRANSPORT_TYPE.FLIGHT);
    const flightDepartureData = getFlightDataForSellableId(flightDetail, flightSellableId);

    const travelItinerary = extractTravelDetails(item);
    const {departure} = travelItinerary || {};

    const {sourceCity, DestinationCity} = packageSrcDestData || {};
    const departureDate = formatDate(packageSrcDestData?.departureDate, { weekday: 'long', day: '2-digit', month: 'short' });
    const returnDate = formatDate(packageSrcDestData?.returnDate, { weekday: 'long', day: '2-digit', month: 'short' });

    // cabPickUpAirport should only be populated when we have flight.
    const cabPickUpAirport = !isEmpty(flightDepartureData) ? departure?.cab?.pickupCity : undefined;

    const getLegInfo = () => {
        if (type === JOURNEY_TYPE.RETURN) {
            return `${DestinationCity} → ${sourceCity}  ●  ${returnDate}`;
        } else if (type === JOURNEY_TYPE.DEPARTURE) {
            return `${sourceCity} → ${DestinationCity}  ●  ${departureDate}`;
        }
        return [];
    };

    const getTrackerRowColor = index => {
        return legDetail.transports.length > index + 1 ? {borderColor: colors.lightTextColor} : {borderColor: colors.white};
    };

    return (
        <View>
            <View style={[cStyles.flexRow, marginStyles.mb16]}>
                <Text style={[cStyles.blackFont, cStyles.blackText, cStyles.marginRight10]}>{type}</Text>
                <Text style={[fontStyles.labelSmallBold, {marginTop: -2, color: holidayColors.gray}]}>
                    {getLegInfo()}
                </Text>

            </View>
            <View style={styles.trackerContainer}>
                {legDetail.transports.map((transport, index) => {
                    if (transport?.type?.toUpperCase() === TRANSPORT_TYPE.FLIGHT && !isEmpty(flightDepartureData) ) {
                        return <View style={[styles.trackerRow, getTrackerRowColor(index)]}>
                            <View style={styles.trackerSpace}>
                                <HolidayImageHolder imageUrl={getImageUrl(IMAGE_ICON_KEYS.FLIGHT_ICON_WHITE)} style={styles.Icon20} resizeMode="contain"/>
                            </View>
                            <View style={[styles.contentContainer]}>
                                <FlightDetails flightsData={flightDepartureData} baggageData={baggageData}/>
                            </View>
                        </View>;
                    }
                    else if (transport?.type?.toUpperCase() === TRANSPORT_TYPE.CABS && !isEmpty(departure?.cab)) {
                        return <View style={[styles.trackerRow, getTrackerRowColor(index)]}>
                            <View style={[styles.trackerSpace]}>
                                <HolidayImageHolder imageUrl={getImageUrl(IMAGE_ICON_KEYS.CAB_ICON_WHITE)} style={styles.Icon18} resizeMode="contain"/>
                            </View>
                            <View style={[styles.contentContainer]}>
                                <CabDetails carItineraryDetail={carItineraryDetail} cabPickUpAirport={cabPickUpAirport} cabType={type === JOURNEY_TYPE.RETURN ? 'Drop to' : 'Pick Up from'} journeyType={type}/>
                            </View>
                        </View>;
                    }
                    else {
                        return [];
                    }
                } )}
                {/*<View style={styles.trackerRow}>*/}
                {/*    <View style={styles.trackerCircle}/>*/}
                {/*</View>*/}
            </View>
        </View>
    );
};
const styles = StyleSheet.create({
    trackerContainer: {
        flex:1,
    },
    trackerRow: {
        borderLeftWidth: 1,
        borderColor: colors.lightTextColor,
        flexDirection: 'row',
        ...marginStyles.ml4,
        ...paddingStyles.pl4,
    },
    trackerSpace: {
        marginLeft: -12,
    },
    contentContainer: {
        marginHorizontal:20,
        flex:1,
    },
    Icon20:{
        width:20,
        height:20,
        resizeMode: 'cover',
    },
    Icon18:{
        width:18,
        height:18,
        resizeMode: 'cover',
    },
    trackerCircle: {
        borderRadius: 50,
        width: 8,
        height: 8,
        backgroundColor: colors.defaultTextColor,
        marginLeft: -9,
    },
});
export default CommuteDetailLeg;
