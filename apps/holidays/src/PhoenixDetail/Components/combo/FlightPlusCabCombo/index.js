import React from 'react';
import {StyleSheet, View} from 'react-native';
import AtomicCss from '@mmt/legacy-commons/Styles/AtomicCss';
import FlightPlusCabRow from './FlightPlusCabRow';
import {isEqual} from 'lodash';
import {checkCombo, getJourneyOrderData} from '../Utils/ComboUtils';
import {JOURNEY_TYPE, TRANSPORT_TYPE} from '../ComboConstats';
import {holidayColors} from '../../../../Styles/holidayColors';
import {marginStyles, paddingStyles} from '../../../../Styles/Spacing';
import {fontStyles} from '../../../../Styles/holidayFonts';
import NoTransportOption from '../NoTransportOption/NoTransportOption';
import ComboHeader from '../comboHeader';

const FlightPlusCabCombo = (props) => {
    const {item, setSelectedItem, selectedItem, onViewDetailsClick} = props || {};
    const type = checkCombo(item);
    const isSelected = isEqual(item, selectedItem);
    const {departureDetail, returnDetail, title, priceDiff, flightDetail, carItineraryDetail} = item || {};

    const flightCabDepartureData = getJourneyOrderData({
        journey: departureDetail,
        journeyType: JOURNEY_TYPE.DEPARTURE,
        item,
      });
      const flightCabReturnData = getJourneyOrderData({
        journey: returnDetail,
        journeyType: JOURNEY_TYPE.RETURN,
        item,
      });

    const flightCabComboData = [flightCabDepartureData, flightCabReturnData];
    const hasFlightCabData = flightCabDepartureData || flightCabReturnData;
    const { flightMetadataDetail = {} } = flightDetail?.departureFlight || {};
    const { isDummy = false } = flightMetadataDetail || {};

    const toggleSelected = () => setSelectedItem(item);
    const handleOnViewDetailsClick = () => onViewDetailsClick(item);


    const renderFlightCabRows = (isFlightDummy) => {
        return flightCabComboData.map((orderData, index) => {
            const key = orderData.fromCity || index;
            return (
                <FlightPlusCabRow
                    key={key}
                    isFlightDummy={isFlightDummy}
                    isSelected={isSelected}
                    orderData={orderData}
                    flightDetail={flightDetail}
                    carItineraryDetail={carItineraryDetail}
                />
            );
        });
    };

    return (
        <>
            <View style={styles.cabComboWrap}>
                <View style={[styles.cabComboBox, isSelected ? styles.activeTouchable : AtomicCss.whiteBg]}>
                    <ComboHeader
                        title={title}
                        priceDiff={priceDiff}
                        subTitle={'View Details'}
                        isSelected={isSelected}
                        showSubTitle={type !== TRANSPORT_TYPE.NONE && !isDummy}
                        toggleSelected={toggleSelected}
                        handleOnViewDetailsClick={handleOnViewDetailsClick}
                    />
                    {hasFlightCabData && renderFlightCabRows(isDummy)}
                    {type === TRANSPORT_TYPE.NONE &&
                        <>
                            <NoTransportOption journeyType={JOURNEY_TYPE.DEPARTURE}/>
                            <NoTransportOption journeyType={JOURNEY_TYPE.RETURN}/>
                        </>
                    }
                </View>
            </View>
        </>
    );
};
const styles = StyleSheet.create({
    cabComboWrap: {
        ...marginStyles.mb16,
        ...paddingStyles.ph16,
    },
    cabComboBox: {
        borderRadius: 16,
        borderWidth: 1,
        borderColor: holidayColors.grayBorder,
        backgroundColor: holidayColors.white,
        // Android shadow (elevation)
        elevation: 6, // Adjust the value as needed
        // iOS shadow
        shadowColor: 'rgba(74, 74, 74, 0.10)',
        shadowOffset: {width: 0, height: 4},
        shadowOpacity: 0.5,
        shadowRadius: 8,
    },
    activeTouchable: {
        backgroundColor: holidayColors.lightBlueBg,
        borderWidth: 1,
        borderColor: holidayColors.primaryBlue,
    },
    cabComboHeader: {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-between',
        ...paddingStyles.ph16,
        ...paddingStyles.pt16,
        ...paddingStyles.pb8,
    },
    titleText:{
        ...fontStyles.labelMediumBlack,
        ...marginStyles.mb4,
        ...marginStyles.mt10,
        color: holidayColors.black,
    },
    currency :{
        ...fontStyles.labelLargeBlack,
        ...marginStyles.mb2,
        color: holidayColors.black,
        paddingTop:1,
    },
    selectText:{
        ...fontStyles.labelSmallBlack,
        ...AtomicCss.azure,
        ...AtomicCss.alignRight,
    },
});
export default FlightPlusCabCombo;
