import React from 'react';
import { Image, StyleSheet, Text, View } from 'react-native';
import AtomicCss from '@mmt/legacy-commons/Styles/AtomicCss';
import { TRANSPORT_TYPE } from '../ComboConstats';
import { getJourneyTimeBySellableId } from '../Utils/ComboUtils';
import { holidayColors } from '../../../../Styles/holidayColors';
import { isEmpty } from 'lodash';
import { getImageUrl, IMAGE_ICON_KEYS } from '../../../../Common/Components/HolidayImageUrls';
import { fontStyles } from '../../../../Styles/holidayFonts';
import { marginStyles } from '../../../../Styles/Spacing';

const FlightPlusCabRow = (props) => {
  const { isSelected, orderData, flightDetail, carItineraryDetail } = props;
  // NO need to render anything if both flight and car are empty.
  if (isEmpty(flightDetail) && isEmpty(carItineraryDetail)) {
    return [];
  }
  const { legObj = [], fromCity = '', toCity, viaCities = [], journeyType, journeyLegString, startDate } =
    orderData || {};

  const getTransportLeg = (type, duration) => {
    const iconUrl =
      type === TRANSPORT_TYPE.FLIGHT
        ? getImageUrl(IMAGE_ICON_KEYS.FLIGHT_ICON_TRANSPARENT)
        : getImageUrl(IMAGE_ICON_KEYS.CAB_ICON_TRANSPARENT);
    const iconBackgroundColor = isSelected ? holidayColors.lightBlueBg : holidayColors.white;
    return (
      <View style={styles.transportLegContainer}>
        <Text style={styles.transportLegText}>{duration}</Text>
        <View style={[AtomicCss.flexRow, AtomicCss.flex1, AtomicCss.alignCenter]}>
          <View style={styles.outerIconContainer}>
            <View style={[styles.iconContainer, { backgroundColor: iconBackgroundColor }]}>
              <Image source={{ uri: iconUrl }} style={styles.transportIcon} />
            </View>
          </View>
          <View style={[styles.durationDivider, { borderColor: holidayColors.lightGreen }]} />
        </View>
      </View>
    );
  };

  const CompleteLegUi = () => {
    const mappedLegs = legObj.map((leg) => {
      const { type, sellableId } = leg || {};
      const duration = getJourneyTimeBySellableId(
        type?.toUpperCase(),
        sellableId,
        flightDetail,
        carItineraryDetail,
        journeyType,
      );
      return getTransportLeg(type?.toUpperCase(), duration);
    });

    return mappedLegs.reduce((acc, currentLeg, index) => {
      // Push the current leg
      acc.push(currentLeg);

      // Add the black dot between legs, but not after the last leg
      if (index !== mappedLegs.length - 1) {
        acc.push(<View key={`stopPointer_${index}`} style={[styles.stopPointer]} />);
      }
      return acc;
    }, []);
  };

  const Stops = () => {
    return viaCities.map((city) => (
      <Text key={city} style={styles.stopText}>
        {city}
      </Text>
    ));
  };

  return (
    <View style={styles.flightCabComboRow}>
      <Text style={styles.journeyTypeText}>{journeyType} : {startDate}</Text>
      <Text style={styles.journeyLegText}>{journeyLegString}</Text>
      <View style={[styles.cardContentRow]}>
        <View style={[styles.destinationFrom]}>
          <Text
            style={[fontStyles.labelMediumBold, { color: holidayColors.black }]}
            numberOfLines={1}
            ellipsizeMode="tail"
          >
            {fromCity}
          </Text>
        </View>
        <View style={styles.legContainer}>
          <View style={styles.leg}>
            <CompleteLegUi />
          </View>
          <View style={[AtomicCss.flexRow]}>
            <Stops />
          </View>
        </View>
        <View style={styles.destinationTo}>
          <Text
            style={[
              fontStyles.labelMediumBold,
              AtomicCss.alignRight,
              { color: holidayColors.black },
            ]}
            numberOfLines={1}
            ellipsizeMode="tail"
          >
            {toCity}
          </Text>
        </View>
      </View>
    </View>
  );
};
const styles = StyleSheet.create({
  flightCabComboRow: {
    borderTopWidth: 1,
    borderColor: '#d8d8d8',
    paddingHorizontal: 16,
    paddingVertical: 11,
  },
  journeyTypeText: {
    color: holidayColors.gray,
    ...fontStyles.labelSmallBlack,
    ...marginStyles.mb2,
  },
  journeyLegText: {
    ...fontStyles.labelSmallBold,
    color: holidayColors.gray,
    ...marginStyles.mb8,
  },
  cardContentRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  durationDivider: {
    borderWidth: 1,
    position: 'relative',
    marginTop: 9,
    marginBottom: 10,
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: 8,
    ...AtomicCss.flex1,
  },
  outerIconContainer: {
    width: '100%',
    position: 'absolute',
    alignItems: 'center',
    justifyContent: 'center',
    left: 0,
    top: 2,
    zIndex: 1,
  },
  iconContainer: {
    width: 20,
    height: 16,
    alignItems: 'center',
    justifyContent: 'center',
    paddingLeft: 2,
    paddingRight: 2,
  },
  transportIcon: {
    width: '100%',
    height: '100%',
  },
  stopPointer: {
    width: 5,
    height: 5,
    backgroundColor: holidayColors.gray,
    borderRadius: 50,
    marginHorizontal: 3,
    marginTop: 15,
  },
  destinationFrom: {
    marginRight: 10,
    width: '27%',
  },
  destinationTo: {
    marginLeft: 10,
    width: '27%',
  },
  legContainer: {
    ...AtomicCss.alignCenter,
    ...AtomicCss.flex1,
    width: '46%',
  },
  leg: {
    ...AtomicCss.flexRow,
    ...AtomicCss.flex1,
    ...AtomicCss.alignCenter,
    ...marginStyles.ml10,
    ...marginStyles.mr10,
  },
  transportLegContainer: {
    flex: 1,
    minWidth: 'fit-content',
  },
  transportLegText: {
    flex: 1,
    textAlign: 'center',
    ...fontStyles.labelSmallRegular,
    color: holidayColors.black,
  },
  stopText: {
    ...fontStyles.labelSmallRegular,
    ...marginStyles.mr4,
  },
});
export default FlightPlusCabRow;
