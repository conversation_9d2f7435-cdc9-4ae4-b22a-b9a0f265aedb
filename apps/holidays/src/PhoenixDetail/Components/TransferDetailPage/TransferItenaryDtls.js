import React from 'react';
import PropTypes from 'prop-types';
import {
  View,
  Text,
  Image,
  StyleSheet,
  TouchableOpacity,
} from 'react-native';
import AtomicCss from '@mmt/legacy-commons/Styles/AtomicCss';
import {fonts} from '@mmt/legacy-commons/Styles/globalStyles';
import {getInclusionMap, getInclusionsAndExclusions, getMySafetyText} from '../../Utils/ActivityUtils';
import {getTransferHeading, getVehicleType} from '../../Utils/TransferUtils';
import {
  INCLUSIONS,
  EXCLUSIONS,
  PRIVATE,
  VEHICLE_TYPE,
  TRANSFER_TYPE,
  GROUP_TRANSFER,
  PRIVATE_TRANSFER, DAY,
} from '../../DetailConstants';
import {fetchPricePersDate} from '../../Utils/HolidayDetailUtils';
import HolidaySafe from '../../../Common/Components/CovidSafety/HolidaySafe';

const cabImg = require('@mmt/legacy-assets/src/CabImg.webp');
const busImg = require('@mmt/legacy-assets/src/BusImage.webp');


class TransferItenaryDtls extends React.Component {
  constructor(props) {
    super(props);
    this.inclusionMap = getInclusionMap();
  }

  static navigationOptions = {header: null};

  render() {
    const {transferObj} = this.props;
    const transferHeading = getTransferHeading(transferObj);
    const vehicleType = getVehicleType(transferObj);
    const transferDetailObj = transferObj.defaultSelection === PRIVATE ? transferObj.privateTransfer : transferObj.groupTransfer;
    const transferType = transferObj.defaultSelection === PRIVATE ? PRIVATE_TRANSFER : GROUP_TRANSFER;
    const transferImgStyle = transferObj.defaultSelection === PRIVATE ? 'CabImg' : 'BusImg'; //Just for style
    const exclusionsList = getInclusionsAndExclusions(transferDetailObj, false, this.inclusionMap);
    const inclusionsList = getInclusionsAndExclusions(transferDetailObj, true, this.inclusionMap);
    let transferLabel = 'Change Transfer';

    if (!transferObj.carItinerary) {
      if (transferObj.privateTransfer && transferObj.groupTransfer) {
        transferLabel = 'Change Transfer';
      } else {
        transferLabel = 'Remove Transfer';
      }
    }

    const mySafety = getMySafetyText(transferObj, this.props.branch);

    return (

      <View style={styles.transferWrapper}>
        <View style={styles.transferDuration}>
          <Text
            style={[AtomicCss.font20, AtomicCss.regularFont, AtomicCss.defaultText]}>{DAY} {this.props.day}.</Text>
          <Text
            style={styles.transferDurationTxt}> {transferObj.date ? fetchPricePersDate(transferObj.date) : ''}</Text>
        </View>
        <Text style={styles.transferHeading}>{transferHeading}</Text>
        {mySafety.isSafe &&
          <HolidaySafe
            safeData={mySafety.safeData}
            sizeLarge
            style={AtomicCss.marginBottom16}
          />
        }
        <View style={[AtomicCss.flexRow, AtomicCss.marginBottom15]}>
          <View style={styles.transferImgWrapper}>
            <Image style={styles[transferImgStyle]}
                   source={transferObj.defaultSelection === PRIVATE ? cabImg : busImg}/>
          </View>
          <View>
            <View style={AtomicCss.marginBottom10}>
              <Text style={styles.transferTypeHeading}>{TRANSFER_TYPE}</Text>
              <Text style={styles.transferTypeDtls}>{transferType}</Text>
            </View>
            <View>
              <Text style={styles.transferTypeHeading}>{VEHICLE_TYPE}</Text>
              <Text style={styles.transferTypeDtls}>{vehicleType}</Text>
            </View>
          </View>
        </View>
        <View style={styles.transferInclusionWrapper}>
          {inclusionsList && inclusionsList.length > 0 &&
          <View>
            <Text style={styles.roomInfoHeading}>{INCLUSIONS}</Text>
            {inclusionsList}
          </View>}
          {exclusionsList && exclusionsList.length > 0 &&
          <View>
            <Text style={styles.roomInfoHeading}>{EXCLUSIONS}</Text>
            {exclusionsList}
          </View>}
        </View>
        <TouchableOpacity onPress={() => {
            this.props.changeTransferListing(transferObj);
        }}>
          <Text style={styles.changeTransfer}>{transferLabel}</Text>
        </TouchableOpacity>
      </View>


    );
  }
}

TransferItenaryDtls.propTypes = {
  transferObj: PropTypes.object.isRequired,
  changeTransferListing: PropTypes.func.isRequired,
  day: PropTypes.oneOfType([
    PropTypes.string,
    PropTypes.number,
  ]).isRequired,
  branch: PropTypes.string.isRequired,
};

const styles = StyleSheet.create({
  transferWrapper: {
    paddingHorizontal: 15,
    paddingBottom: 10,
    marginBottom: 7,
    backgroundColor: '#fff',
  },
  transferDuration: {
    borderBottomWidth: 1,
    borderBottomColor: '#e7e7e7',
    paddingVertical: 13,
    flexDirection: 'row',
    alignItems: 'center',

  },
  transferDurationTxt: {
    fontSize: 16,
    fontFamily: fonts.bold,
    color: '#000',
    alignSelf: 'flex-end',
  },

  transferHeading: {
    paddingVertical: 14,
    color: '#000',
    fontSize: 16,
    fontFamily: fonts.bold,
  },
  changeTransfer: {
    paddingVertical: 3,
    color: '#008cff',
    fontSize: 14,
    fontFamily: fonts.bold,
  },
  transferImgWrapper: {
    borderWidth: 1,
    borderRadius: 2,
    borderColor: '#bababa',
    width: 103,
    height: 80,
    marginRight: 14,
    justifyContent: 'center',
    alignItems: 'center',
  },
  transferTypeHeading: {
    fontSize: 10,
    fontFamily: fonts.bold,
    color: '#9b9b9b',
    marginBottom: 4,
  },
  transferTypeDtls: {
    fontSize: 14,
    fontFamily: fonts.regular,
    color: '#000',
  },
  CabImg: {
    width: 79,
    height: 26,
  },
  BusImg: {
    width: 67,
    height: 28,
  },
  tickImg: {
    width: 11,
    height: 7,
    marginRight: 5,
    marginTop: 5,
  },
  crossImg: {
    width: 10,
    height: 11,
    marginRight: 5,
    marginTop: 5,
  },
  transferInclusionWrapper: {
    justifyContent: 'space-between',
    flexDirection: 'row',
  },
  inclusionDtls: {
    fontSize: 14,
    fontFamily: fonts.regular,
    color: '#000',
  },
  lastItemStyle: {
    borderBottomWidth: 0,
    borderBottomColor: 'transparent',
  },
  roomInfoHeading: {
    marginBottom: 6,
    fontSize: 10,
    color: '#9b9b9b',
    fontFamily: fonts.bold,
    lineHeight: 12,
  },
});
export default TransferItenaryDtls;
