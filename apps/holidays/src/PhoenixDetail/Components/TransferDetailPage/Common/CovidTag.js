import React from 'react';
import {Image, StyleSheet, Text, View} from 'react-native';
import AtomicCss from '@mmt/legacy-commons/Styles/AtomicCss';
import covidCard from '@mmt/legacy-assets/src/covidCard.webp';

const CovidTag = ({subTitle}) => {
  return (
    <View style={[AtomicCss.flexRow, styles.covidTag]}>
      <Image style={styles.covidTagIcon} source={covidCard}/>
      <View style={[AtomicCss.flexRow, AtomicCss.alignCenter]}>
        <Text style={[AtomicCss.font11, AtomicCss.blackFont, AtomicCss.blackText]}><Text
          style={AtomicCss.azure}>My</Text>Safety
          {subTitle ? <Text> - </Text> : null}
        </Text>
        {subTitle ?
          <Text style={[AtomicCss.font11, AtomicCss.regularFont, AtomicCss.blackText]}>{subTitle}</Text>
          : null}
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  covidTagIcon: {width: 16, height: 16, marginRight: 4, marginLeft: -1},
  covidTag: {
    alignItems: 'center',
    borderWidth: 1,
    borderColor: '#0874ce',
    alignSelf: 'flex-start',
    borderRadius: 3,
    paddingRight: 4,
    backgroundColor: '#edf7ff',
  },
});
export default CovidTag;
