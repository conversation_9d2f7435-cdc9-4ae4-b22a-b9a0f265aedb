import React from 'react';
import {ActivityIndicator, Dimensions, StyleSheet, Text, View} from 'react-native';
import { packageMealsPromise, packageVariantsPromise, packageVisaPromise } from '../../../utils/HolidayNetworkUtils';
import { trackPhoenixDetailLocalClickEvent } from '../../Utils/PhoenixDetailTracking';
import AtomicCss from '@mmt/legacy-commons/Styles/AtomicCss';

/* Components */
import MealPlan from '../../../Common/Components/MealPlan/MealPlan';
import BottomSheetOverlay from '../../../Common/Components/BottomSheetOverlay';
import {isMobileClient} from "../../../utils/HolidayUtils";
import FeatureList from '../FDFeatureEdit/FeatureList';
import BasePage from '@mmt/legacy-commons/Common/navigation/BasePage';
import SelectedFeatureOption from './SelectedFeatureOption';
import SelectableMealOption from './SelectableMealOption';
import SelectableVisaOption from './SelectableVisaOption';
import SelectablePackageVariant from './SelectablePackageVariant';

const screenWidth = Dimensions.get('screen').width;

const title = {
  'EDIT_FEATURES':'Edit Features',
  'VIEW_DETAILS':'View Details',
};

const Carousel = isMobileClient()
    ? require('react-native-snap-carousel').default
    : require('../../../Common/Components/Carousel').default;

export default class FDFeatureEditOverlay extends BasePage {

    fromReview=false;
   constructor (props) {
     super(props);
     this.state = {
       loading: true,
       packageVariants: [],
       packageMeals: [],
       packageVisa: [],

     };
     if (props.isReview){
       this.fromReview = true;
     }
   }

  fetchCarousalPages = async (dynamicPackageId) => {
    try {
      const [packageVariantResponse, packageMealResponse, packageVisaResponse] = await Promise.all([
          packageVariantsPromise(dynamicPackageId),
          packageMealsPromise(dynamicPackageId),
          packageVisaPromise(dynamicPackageId),
        ]);
      this.setCarousalPagesOnState([await packageVariantResponse.json(), await packageMealResponse.json(), await packageVisaResponse.json()]);
    } catch (e) {
      this.setCarousalPagesOnState([]);
    }
  };
  fetchCarouselPagesReview =() => {
   this.setState({
      loading: false,
      packageVariants: [],
      packageMeals: [],
      packageVisa: [],
    });
  };

  setCarousalPagesOnState = (results = []) => {
    if (results && results.length > 0) {
      this.setState({
        loading: false,
        packageVariants: results[0],
        packageMeals: results[1],
        packageVisa: results[2],
      });
    } else {
      this.state = {
        loading: true,
        packageVariants: [],
        packageMeals: [],
        packageVisa: [],
      };
    }
  }


   async componentDidMount () {
     super.componentDidMount();
     if (this.fromReview){
      this.fetchCarouselPagesReview();
     } else {this.fetchCarousalPages(this.props.dynamicPackageId);}
   }


   updatePackage = (packageId, oldPackageId) => {
    trackPhoenixDetailLocalClickEvent({eventName: 'change_VARIANT_', suffix: `${oldPackageId}_${packageId}`});
   this.props.updatePackage(packageId);
   }

   updateMeal = (mealCode) => {
     trackPhoenixDetailLocalClickEvent({eventName: 'change_MEAL_', suffix: `${this.props.selectedMealCode}_${mealCode}`});
     this.props.updateMeal(mealCode);
   }

  updateVisa = (sellableId, visaIncluded) => {
    trackPhoenixDetailLocalClickEvent({eventName: 'change_Visa_', suffix: `${sellableId}`});
    this.props.updateVisa(visaIncluded);
  }

  renderProgressView = () => (
    <View style={{width: '100%', height: 200, backgroundColor: '#ffffffd9', alignItems: 'center', justifyContent: 'center'}}>
      <ActivityIndicator styleAttr="Inverse" size="large" color="#008cff" />
      <Text style={[AtomicCss.darkGreyText, AtomicCss.boldFont]}> {this.state.loaderText}
      </Text>
    </View>
  )


    render() {
        return (
            <BottomSheetOverlay
                toggleModal={() => this.props.toggleFDFeatureBottomSheet(false)}
                title={!this.fromReview ? title.EDIT_FEATURES : title.VIEW_DETAILS}
                containerStyles={styles.bottomSheetContainer}
                visible={this.props.toggleFDFeatureBottomSheet}>
                {this.state.loading && this.renderProgressView()}
                {!this.state.loading && this.props.packageFeatures && <FeatureList packageFeatures={this.props.packageFeatures} isOverlay = {true} swipeCarousal = {this.swipeCarousal} activeIndex={this.props.activeIndex} />}
                {!this.state.loading && this.props.packageFeatures && <Carousel
                    ref={(c) => {
                        this._carousel = c;
                    }}
                    data={this.props.packageFeatures}
                    firstItem={this.props.activeIndex}
                    initialNumToRender={this.props.packageFeatures.length}
                    renderItem={(item, index) => this.fromReview ? this.getViewReview(item) : this.getView(item, index)}
                    itemWidth={screenWidth - 25}
                    keyExtractor={(index) => index}
                    scrollEnabled = {false}
                    sliderWidth={screenWidth - 25}
                />}
            </BottomSheetOverlay>
        );
    }

  swipeCarousal = (index) => {
    if (this._carousel) {
      this._carousel.snapToItem(index);
      const item = this.props.packageFeatures[index];
      trackPhoenixDetailLocalClickEvent({eventName: 'expand_', suffix: `${item.type}`});
    }
  }

    getViewReview = (i) => {
        const item = isMobileClient() && i.item || i;
        return <SelectedFeatureOption
            options={this.state.packageVariants.variants}
            heading={item?.subtitle}
            description={item?.description}
            subHeading={item?.description}
        />;
    }
  getView = (i) => {
      const item = isMobileClient() && i.item || i;
    switch (item.type) {
      case 'VARIANT': {
        if (this.state.packageVariants && this.state.packageVariants.variants) {
          return <SelectablePackageVariant
            options={this.state.packageVariants.variants}
            heading={item.subtitle}
            description={item.description}
            subHeading={item.description}
            updatePackage={this.updatePackage}
          />;
        } else {
          return [];
        }
      }
      case 'MEAL': {
        if (this.state.packageMeals && this.state.packageMeals.mealListingData && this.state.packageMeals.mealListingData.meals && this.state.packageMeals.mealListingData.meals.length > 0) {
          return <SelectableMealOption
            options={this.state.packageMeals.mealListingData.meals}
            discountedFactor = {this.state.packageMeals.mealListingData.discountedFactor}
            packagePriceMap = {this.state.packageMeals.mealListingData.packagePriceMap}
            heading={item.subtitle}
            description = {item.description}
            subHeading = {item.description}
            selectedMealCode = {this.props.selectedMealCode}
            updateMeal = {this.updateMeal}
          />;
        } else {
          return [];
        }
      }
      case 'MEAL_DAY_WISE': {
          return this.state.packageMeals
              && <MealPlan packageMeals={this.state.packageMeals} />;
      }
      case 'VISA_INCLUDED':
      case 'VISA': {
        if (this.state.packageVisa && this.state.packageVisa.visaListingData && this.state.packageVisa.visaListingData.availableVisas && this.state.packageVisa.visaListingData.availableVisas.length > 0) {
           return <SelectableVisaOption
            options={this.state.packageVisa.visaListingData.availableVisas}
            discountedFactor = {this.state.packageVisa.visaListingData.discountedFactor}
            packagePriceMap = {this.state.packageVisa.visaListingData.packagePriceMap}
            heading={item.subtitle}
            description = {item.description}
            subHeading = {item.description}
            updateVisa = {this.updateVisa}
          />;
         }
        return [];
      }
      default : {
        return <SelectedFeatureOption
          options={this.state.packageVariants.variants}
          heading={item.subtitle}
          description = {item.description}
          subHeading = {item.description}
        />;
      }
    }
  }
}

const styles = StyleSheet.create({
    bottomSheetContainer: {
      borderTopLeftRadius: 20,
      borderTopRightRadius: 20,
      maxHeight: '100%',
      padding: 16,
      paddingBottom:40
    },
});
