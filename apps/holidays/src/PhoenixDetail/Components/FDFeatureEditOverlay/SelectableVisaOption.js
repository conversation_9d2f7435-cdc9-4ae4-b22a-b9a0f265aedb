import React from 'react';
import { View, Text, StyleSheet, Image, TouchableOpacity } from 'react-native';
import { rupeeFormatter } from '@mmt/legacy-commons/Helpers/currencyUtils';
import AtomicCss from '@mmt/legacy-commons/Styles/AtomicCss';
import BasePage from '@mmt/legacy-commons/Common/navigation/BasePage';

export default class SelectableVisaOption extends BasePage {

  constructor (props) {
    super(props);
    this.state = {
      showSelection: props.options.findIndex(item => item.selected ),
      selectedVisaPrice : props.packagePriceMap[props.options.find(item => item.selected )?.sellableId],
    };
  }
  toggleSelection = (index ,sellableId,toggleAction) => {
    if (index !== this.state.showSelection){
        this.setState({
            showSelection:  index,
            selectedVisaPrice:this.props.packagePriceMap[sellableId],
        });
        this.props.updateVisa(sellableId,toggleAction);
    }

  };

  getPriceDiff = (sellableId ) => {
    const visaPrice = this.props.packagePriceMap[sellableId];
    return Math.ceil((visaPrice - this.state.selectedVisaPrice) * this.props.discountedFactor);
  };

  render() {
    return (
      <View style={styles.container}>
        <View style={styles.mb10}>
          {!!this.props.heading && <Text style={styles.heading}>{this.props.heading}</Text>}
          {!!this.props.subHeading && <Text style={styles.subHeading}>{this.props.subHeading}</Text>}
        </View>
        {this.props.options.map((option, index) =>
          <View style={[AtomicCss.marginBottom26]}>
            <TouchableOpacity
              activeOpacity={0.8}
              onPress={() => this.toggleSelection(index,option.sellableId,option.toggleAction)}
            >
              <View style={index === this.state.showSelection ? [styles.optionContainer, styles.activeOptionContainer] : [styles.optionContainer]}>
                <View style={{flexDirection: 'row'}}>
                  {index === this.state.showSelection && <Image style={styles.tick} source={require('../images/tick-blue-circular.png')}/>}
                  <Text style={styles.optionText}>{option.name}</Text>
                </View>
                <View>
                  {index === this.state.showSelection
                    ?
                    <View>
                      <Text style={[styles.selectedText]}>SELECTED
                      </Text>
                    </View>
                    :
                    <View>
                      <Text style={[styles.price, { color: this.getPriceDiff(option.sellableId) >= 0 ? '#eb2026' : '#1a7971' }]}>
                        {this.getPriceDiff(option.sellableId) >= 0 ? '+ ' : '- ' }{'\u20B9'}{ rupeeFormatter(Math.abs((this.getPriceDiff(option.sellableId))))}/p
                      </Text>
                    </View>
                    }
                </View>
              </View>
            </TouchableOpacity>

          </View>
        )}
      </View>
    );
  }
}

const styles = StyleSheet.create({
  description: {
    marginVertical: 11,
    fontFamily: 'Lato',
    fontSize: 12,
    color: '#4a4a4a',
    marginHorizontal: 50,
  },
  tick: {
    width: 14,
    height: 14,
    marginRight: 8,
  },
  selectedText: {
    color: '#008cff',
    fontFamily: 'Lato-Bold',
    fontSize: 10,
  },
  price: {
    fontSize: 12,
    fontFamily: 'Lato',
  },
  optionText: {
    color: '#000',
    fontSize: 14,
    fontFamily: 'Lato-Bold',
  },
  optionContainer: {
    width: '100%',
    backgroundColor: '#fff',
    paddingVertical: 13,
    paddingLeft: 37,
    paddingRight: 16,
    flexDirection: 'row',
    justifyContent: 'space-between',
    borderRadius: 4,
    shadowColor: 'rgba(0, 0, 0, 0.4)',
    shadowOpacity: 0.4,
    shadowRadius: 2,
    elevation: 1,
    borderColor: 'rgba(0, 0, 0, 0.1)',
    borderWidth: 1,
  },
  activeOptionContainer: {
    borderColor: '#008cff',
    borderWidth: 1,
    paddingLeft: 15,
  },
  container: {
    paddingHorizontal: 15,
  },
  heading: {
    marginTop: 30,
    fontWeight: 'bold',
    fontFamily: 'Lato-Bold',
    fontSize: 14,
    color: '#000',
  },
  subHeading: {
    marginTop: 4,
    fontFamily: 'Lato',
    fontSize: 12,
    color: '#9b9b9b',
  },
  mb10: { marginBottom: 10 },


});
