import React, {useState} from 'react';
import { View, Text, StyleSheet, Image, TouchableOpacity } from 'react-native';
import { fonts } from '@mmt/legacy-commons/Styles/globalStyles';
import AtomicCss from '@mmt/legacy-commons/Styles/AtomicCss';
import BasePage from '@mmt/legacy-commons/Common/navigation/BasePage';
import { holidayColors } from '../../../Styles/holidayColors';
import { fontStyles } from 'mobile-holidays-react-native/src/Styles/holidayFonts';

export default class SelectableMealOption extends BasePage {

  constructor (props) {
    super(props);
    this.state = {
      showNote: false,
      showSelectBtn: true,
      showSelection: props.options.findIndex(item => item.mealCode === this.props.selectedMealCode),
    };
  }

   toggleNote = (index) => {
    this.setState({
      showNote: index,
      showSelectionBtn: true,
    });
  };

  toggleSelection = (index, mealCode) => {
    this.setState({
      showSelection: this.state.showSelection === index ? null : index,
      showSelectionBtn: false,
    });
    this.props.updateMeal(mealCode);
  };

  getPriceDiff = (mealCode, selectedMealCode) => {
    const selectedMealPrice = this.props.packagePriceMap[selectedMealCode];
    const mealPrice = this.props.packagePriceMap[mealCode];
    return Math.ceil((mealPrice - selectedMealPrice) * this.props.discountedFactor);
  };

  render() {
    return (
      <View style={styles.container}>
        <View style={styles.mb10}>
          {!!this.props.heading && <Text style={styles.heading}>{this.props.heading}</Text>}
          {!!this.props.subHeading && <Text style={styles.subHeading}>{this.props.subHeading}</Text>}
        </View>
        {this.props.options.map((option, index) =>
          <View style={[AtomicCss.marginBottom26]}>
            <TouchableOpacity
              activeOpacity={0.8}
              onPress={() => this.toggleNote(index)}
            >
              <View style={index === this.state.showSelection ? [styles.optionContainer, styles.activeOptionContainer] : [styles.optionContainer]}>
                <View style={{flexDirection: 'row'}}>
                  {index === this.state.showSelection && <Image style={styles.tick} source={require('../images/tick-blue-circular.png')}/>}
                  <Text style={styles.optionText}>{option.mealName}</Text>
                </View>
                <View>
                  {index === this.state.showSelection
                    ?
                    <View>
                      <Text style={[styles.selectedText]}>SELECTED
                      </Text>
                    </View>
                    :
                    <View>
                      <Text style={[styles.price, { color: this.getPriceDiff(option.mealCode, this.props.selectedMealCode) >= 0 ? '#eb2026' : '#1a7971' }]}> {this.getPriceDiff(option.mealCode, this.props.selectedMealCode) >= 0 ? '+' : '-' }{this.getPriceDiff(option.mealCode, this.props.selectedMealCode)}/p</Text>
                    </View>
                    }
                </View>
              </View>
            </TouchableOpacity>
            {index === this.state.showNote && this.state.showSelectBtn &&
                    <View style={styles.noteCategory}>
                      <View style={styles.leftCategoryContent}>

                        <Text style={[styles.noteText, AtomicCss.boldFont]}>Note:</Text>
                        <Text style={styles.noteText}>Changes applied to overall holidays packages & change in price as
                          well.</Text>
                      </View>
                      <TouchableOpacity onPress={() => this.toggleSelection(index,option.mealCode)}>
                        <View style={[styles.selectBtn]}>
                          <Text style={[styles.selectBtnText]}>SELECT</Text>
                        </View>
                      </TouchableOpacity>
                    </View>
                  }
          </View>
        )}
        {/*{this.props.description && <Text style={styles.description}>{this.props.description}</Text>}*/}
      </View>
    );
  }
}

const styles = StyleSheet.create({
  description: {
    marginVertical: 11,
    fontFamily: 'Lato',
    fontSize: 12,
    color: '#4a4a4a',
    marginHorizontal: 50,
  },
  tick: {
    width: 14,
    height: 14,
    marginRight: 8,
  },
  selectedText: {
    color: '#008cff',
    fontFamily: 'Lato-Bold',
    fontSize: 10,
  },
  price: {
    fontSize: 12,
    fontFamily: 'Lato',
  },
  optionText: {
    color: '#000',
    fontSize: 14,
    fontFamily: 'Lato-Bold',
  },
  optionContainer: {
    width: '100%',
    backgroundColor: '#fff',
    paddingVertical: 13,
    paddingLeft: 37,
    paddingRight: 16,
    flexDirection: 'row',
    justifyContent: 'space-between',
    borderRadius: 4,
    shadowColor: 'rgba(0, 0, 0, 0.4)',
    shadowOpacity: 0.4,
    shadowRadius: 2,
    elevation: 1,
  },
  activeOptionContainer: {
    borderColor: '#008cff',
    borderWidth: 1,
    paddingLeft: 15,
  },
  container: {
    paddingHorizontal: 15,
  },
  heading: {
    marginTop: 30,
    ...fontStyles.labelBaseBold,
    color: holidayColors.black,
  },
  subHeading: {
    marginTop: 4,
    ...fontStyles.labelSmallRegular,
    color: holidayColors.lightGray,
  },
  mb10: { marginBottom: 10 },
  noteCategory: {
    backgroundColor: holidayColors.lightGray2,
    borderRadius: 2,
    padding: 15,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  noteText: {
    ...fontStyles.labelSmallRegular,
    color: holidayColors.gray,
    lineHeight: 11 * 1.2,
  },
  leftCategoryContent: {
    width: '60%',

  },
  selectBtn: {
    backgroundColor: holidayColors.primaryBlue,
    paddingVertical: 8,
    paddingHorizontal: 20,
    borderRadius: 25,
  },
  selectBtnText: {
    color: holidayColors.white,
    ...fontStyles.labelBaseBold,
  },
});
