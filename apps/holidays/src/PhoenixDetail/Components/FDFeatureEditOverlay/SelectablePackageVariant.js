import React, {useState} from 'react';
import { View, Text, StyleSheet, Image, TouchableOpacity } from 'react-native';
import { fonts } from '@mmt/legacy-commons/Styles/globalStyles';
import AtomicCss from '@mmt/legacy-commons/Styles/AtomicCss';
import BasePage from '@mmt/legacy-commons/Common/navigation/BasePage';
import { holidayColors } from '../../../Styles/holidayColors';

export default class SelectablePackageVariant extends BasePage {

  constructor (props) {
    super(props);
    this.state = {
      showNote: false,
      showSelectBtn: true,
      showSelection: props.options.findIndex(item => item.selected === true),
    };
  }

   toggleNote = (index) => {
    this.setState({
      showNote: index,
      showSelectionBtn: true,
    });
  };

  toggleSelection = (index,packageId) => {
    const obj = this.props.options.find(item => item.selected === true);
    const oldPackageId = obj ? obj.packageId : null;
    this.props.updatePackage(packageId, oldPackageId);
    this.setState({
      showSelection: this.state.showSelection === index ? null : index,
      showSelectionBtn: false,
    });
  };

  render() {
    return (
      <View style={styles.container}>
        <View style={styles.mb10}>
          {!!this.props.heading && <Text style={styles.heading}>{this.props.heading}</Text>}
          {!!this.props.subHeading && <Text style={styles.subHeading}>{this.props.subHeading}</Text>}
        </View>
        {this.props.options.map((option, index) =>
          <View style={[AtomicCss.marginBottom26]}>
            <TouchableOpacity
              activeOpacity={0.8}
              onPress={() => this.toggleNote(index)}
            >
              <View style={index === this.state.showSelection ? [styles.optionContainer, styles.activeOptionContainer] : [styles.optionContainer]}>
                <View style={{flexDirection: 'row'}}>
                  {index === this.state.showSelection && <Image style={styles.tick} source={require('../images/tick-blue-circular.png')}/>}
                  <Text style={styles.optionText}>{option.variantType}</Text>
                </View>
                <View>
                  {index === this.state.showSelection
                    ?
                    <View>
                      <Text style={[styles.selectedText]}>SELECTED
                      </Text>
                    </View>
                    :
                    <View>
                      <Text style={[styles.price, { color: option.priceDelta >= 0 ? '#eb2026' : '#1a7971' }]}>{option.priceDelta >= 0 ? '+' : '-'}{option.priceDelta}/p</Text>
                    </View>
                    }
                </View>
              </View>
            </TouchableOpacity>
            {index === this.state.showNote && this.state.showSelectBtn &&
                    <View style={styles.noteCategory}>
                      <View style={styles.leftCategoryContent}>
                        <Text style={[styles.noteText, AtomicCss.boldFont]}>Note:</Text>
                        <Text style={styles.noteText}>Changes applied to overall holidays packages & change in price as
                          well.</Text>
                      </View>
                      <TouchableOpacity onPress={() => this.toggleSelection(index,option.packageId)}>
                        <View style={[styles.selectBtn]}>
                          <Text style={[styles.selectBtnText]}>SELECT</Text>
                        </View>
                      </TouchableOpacity>
                    </View>
                  }
          </View>
        )}
        {/*{this.props.description && <Text style={styles.description}>{this.props.description}</Text>}*/}
      </View>
    );
  }
}

const styles = StyleSheet.create({
  description: {
    marginVertical: 11,
    fontFamily: 'Lato',
    fontSize: 12,
    color: '#4a4a4a',
    marginHorizontal: 50,
  },
  tick: {
    width: 14,
    height: 14,
    marginRight: 8,
  },
  selectedText: {
    color: '#008cff',
    fontFamily: 'Lato-Bold',
    fontSize: 10,
  },
  price: {
    fontSize: 12,
    fontFamily: 'Lato',
  },
  optionText: {
    color: '#000',
    fontSize: 14,
    fontFamily: 'Lato-Bold',
  },
  optionContainer: {
    width: '100%',
    backgroundColor: '#fff',
    paddingVertical: 13,
    paddingLeft: 37,
    paddingRight: 16,
    flexDirection: 'row',
    justifyContent: 'space-between',
    borderRadius: 4,
    shadowColor: 'rgba(0, 0, 0, 0.4)',
    shadowOpacity: 0.4,
    shadowRadius: 2,
    elevation: 1,
  },
  activeOptionContainer: {
    borderColor: '#008cff',
    borderWidth: 1,
    paddingLeft: 15,
  },
  container: {
    paddingHorizontal: 15,
  },
  heading: {
    marginTop: 30,
    fontWeight: 'bold',
    fontFamily: 'Lato-Bold',
    fontSize: 14,
    color: '#000',
  },
  subHeading: {
    marginTop: 4,
    fontFamily: 'Lato',
    fontSize: 12,
    color: '#9b9b9b',
  },
  mb10: { marginBottom: 10 },
  noteCategory: {
    backgroundColor: '#f2f2f2',
    borderRadius: 2,
    padding: 15,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  noteText: {
    fontSize: 11,
    color: '#4a4a4a',
    fontFamily: fonts.italic,
    lineHeight: 11 * 1.2,
  },
  leftCategoryContent: {
    width: '60%',
  },
  selectBtn: {
    backgroundColor: holidayColors.primaryBlue,
    paddingVertical: 8,
    paddingHorizontal: 20,
    borderRadius: 25,
  },
  selectBtnText: {
    color: 'white',
    fontFamily: fonts.bold,
    fontSize: 14,
  },
});
