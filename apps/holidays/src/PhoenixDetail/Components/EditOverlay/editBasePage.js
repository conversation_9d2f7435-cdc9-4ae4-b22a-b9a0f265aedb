import React from 'react';
import fecha from 'fecha';
import PropTypes from 'prop-types';
import {
  View,
  Text,
  Image,
  StyleSheet,
  ScrollView,
  BackHandler,
  TouchableOpacity,
  ActivityIndicator,
} from 'react-native';
import {
  addDays,
  getFormattedDate,
  today,
  convertDateObject,
} from '@mmt/legacy-commons/Helpers/dateHelpers';
import {
  DATE_FORMAT,
  NEW_DATE_FORMAT,
  ONLY_YEAR_DATE_FORMAT,
  CALENDAR_SELECTED_DATE_DIFF,
  HARDWARE_BACK_PRESS,
  FROM,
} from '../../../SearchWidget/SearchWidgetConstants';


import HolidayCalendar from '../../../Calender/MmtHolidayCalender';
import TravellerPage from './TravellerPage/TravellerPage';

import AtomicCss from '@mmt/legacy-commons/Styles/AtomicCss';
import {
  createRoomDataFromRoomDetailsPhoenix,
  createRoomDetailsFromRoomDataForPhoenix,
  getPaxCount,
  getPaxCountInfoForTotalRooms,
  getRoomDataForPdt,
} from '../../Utils/HolidayDetailUtils';
import {PDTConstants} from '../../DetailConstants';
import {NO_DEPARTURE_CITY} from '../../../HolidayConstants';
import {getPackageExcludedDates} from '../../../utils/HolidayNetworkUtils';
import {getAPWindow, getNearByCityDataForPdt} from '../../../utils/HolidayUtils';
import DepartureCities from '../../../SearchWidget/Components/getDepartureCities';
import {getCitySearchType} from '../../../LandingNew/Utils/DestinationDepartureCityUtils';
import {
  getDataFromStorage,
  KEY_USER_CITY_LOCUS_ID,
  KEY_USER_CITY_SEARCH_TYPE,
  KEY_USER_CITY_SELECTION_TYPE,
  KEY_USER_DEP_CITY, removeDataFromStorage,
  setDataInStorage,
} from '@mmt/legacy-commons/AppState/LocalStorage';
import isEmpty from 'lodash/isEmpty';
import { getEnableGeoLoc } from '../../../utils/HolidaysPokusUtils';
import PageHeader from 'mobile-holidays-react-native/src/Common/Components/PageHeader';
import { holidayColors } from 'mobile-holidays-react-native/src/Styles/holidayColors';
import { marginStyles, paddingStyles } from 'mobile-holidays-react-native/src/Styles/Spacing';
import { borderRadiusValues, holidayBorderRadius } from 'mobile-holidays-react-native/src/Styles/holidayBorderRadius';
import { fontStyles } from 'mobile-holidays-react-native/src/Styles/holidayFonts';
import { actionStyle } from '../DayPlan/dayPlanStyles';
import PrimaryButton from 'mobile-holidays-react-native/src/Common/Components/Buttons/PrimaryButton';
import { logPhoenixDetailPDTEvents } from 'mobile-holidays-react-native/src/utils/PhoenixDetailPDTTrackingUtils';
import { PDT_EVENT_TYPES } from 'mobile-holidays-react-native/src/utils/HolidayPDTConstants';
import Spinner from '@Frontend_Ui_Lib_App/Spinner';
import withBackHandler from '../../../hooks/withBackHandler';
const nativeImg = require('@mmt/legacy-assets/src/navigateImg.webp');
const backArrowAndroid = require('@mmt/legacy-assets/src/backArrowAndroid.webp');
const errorMsg = 'Starting city of this package cannot be changed.';
class EditOverlay extends React.Component {
  constructor(props) {
    super(props);
    const { departureDate, departureCity, cityId, roomDetails, checkoutDate, childAgeArray,allowChangeHub, destinationCity } = this.props;
    this.state = {
      correctDate: getFormattedDate(departureDate, DATE_FORMAT, NEW_DATE_FORMAT),
      correctDay: getFormattedDate(departureDate, DATE_FORMAT, ONLY_YEAR_DATE_FORMAT),
      selectedDate: getFormattedDate(departureDate, DATE_FORMAT, DATE_FORMAT),
      departureDate: getFormattedDate(departureDate, DATE_FORMAT, DATE_FORMAT),
      correctCheckoutDate: getFormattedDate(checkoutDate, DATE_FORMAT, NEW_DATE_FORMAT),
      correctCheckoutDay: getFormattedDate(checkoutDate, DATE_FORMAT, ONLY_YEAR_DATE_FORMAT),
      selectedCheckoutDate: getFormattedDate(checkoutDate, DATE_FORMAT, DATE_FORMAT),
      checkoutDate: getFormattedDate(checkoutDate, DATE_FORMAT, DATE_FORMAT),
      departureCity,
      destinationCity,
      departureCityId: cityId,
      adult: getPaxCount(roomDetails, 'noOfAdults'),
      child: getPaxCount(roomDetails, 'noOfChildrenWB') +  getPaxCount(roomDetails, 'noOfChildrenWOB'),
      noOfRooms: roomDetails.length,
      roomData: createRoomDataFromRoomDetailsPhoenix(roomDetails, childAgeArray),
      infantCount: getPaxCount(roomDetails, 'noOfInfants'),
      excludedDates: [],
      showCalendar: false,
      showDepartureCityPopup: false,
      showTravellerPopup: false,
      showLoader: false,
      dateCalendarType: null,
      showMsg:false,
      showToastMsg:!allowChangeHub,
    };

    getDataFromStorage(KEY_USER_CITY_SEARCH_TYPE).then(d => this.setState({citySearchType: d ? d : 'Airport'}));
    getDataFromStorage(KEY_USER_CITY_SELECTION_TYPE).then(d => this.setState({citySelectionType: d ? d : 'Auto-Detect'}));

    if (!allowChangeHub) {
      setTimeout(()=>{
          this.setState({
            showToastMsg:false,
          });
      },5000);
    }
  }

  showMsg=()=>{
    this.setState({
      showMsg:true,
    });
  }
  static navigationOptions = { header: null };

  onBackClick = ()=> {
    return this.handleBackPress();
  }

  componentDidMount() {
    if (this.props.packageId) {
      this.fetchPackageExcludedDates();
    }
  }

  async fetchPackageExcludedDates() {
    this.setState({
      showLoader: true,
    });
    const response = await getPackageExcludedDates(
      this.state.departureCityId,
      this.props.packageId,
      this.props.categoryId,
      this.props?.roomDetails || []
    );
    this.setState({
      excludedDates: response,
      showLoader: false,
    });
  }
  onBackHub = () => {
    this.captureClickEvents({ eventName: 'back_hub' });
    this.setState({
      showDepartureCityPopup: false,
    });
  };
  onBackTravellerPage = () => {
    this.captureClickEvents({ eventName: 'back_pax'});
    this.setState({
      showTravellerPopup: false,
    });
  };
  handleBackPress = () => {
    if (this.state.showCalendar) {
      this.onCalendarBack();
    } else if (this.state.showTravellerPopup) {
      this.onBackTravellerPage();
    } else if (this.state.showDepartureCityPopup) {
      this.onBackHub();
    } else {
      this.props.onBackPressed();
    }
    return true;
  };

  render() {
    const { startDateTxt, endDateTxt, hideFromCity, packagePaxDetail,allowChangeHub} = this.props;
    const navigateImg = nativeImg;
    const { correctCheckoutDate, dateCalendarType, minDate } = this.state;
    const totalChildCount = this.state.child + this.state.infantCount;

    const enableNewGeoLocation = getEnableGeoLoc();
    let DepartureCities;
    if (enableNewGeoLocation) {
      DepartureCities = require('../../../SearchWidget/Components/DepartureDestinationSelector').default;
    } else {
      DepartureCities = require('../../../SearchWidget/Components/getDepartureCities').default;
    }

    const childText =
      totalChildCount > 1
        ? `, ${totalChildCount} Children `
        : totalChildCount > 0
        ? `, ${totalChildCount} Child `
        : '';
    const isWG = this.props.isWG;
    return (
      <View style={styles.overlayContainer}>
        {this.state.showLoader && (
          <View
            style={{
              flex: 1,
              justifyContent: 'center',
              alignItems: 'center',
              backgroundColor: '#f2f2f2',
            }}
          >
                <Spinner
                size={36}
                strokeWidth={4}
                progressPercent={85}
                speed={1.5}
                color={holidayColors.primaryBlue}
                />
          </View>
        )}

        {this.state.showTravellerPopup && (
          <TravellerPage
            flightCount={this.props.flightCount}
            roomData={this.state.roomData}
            handleTravellerChange={this.handleTravellerChange}
            selectedDate={this.state.selectedDate}
            packageId={this.props.packageId}
            packagePaxDetail={packagePaxDetail}
            trackClickEvent={this.props.trackLocalClickEvent}
            trackPDTV3Event={logPhoenixDetailPDTEvents}
          />
        )}

        {this.state.showDepartureCityPopup && (
          <DepartureCities
            packageId={this.props.packageId}
            handleFromChangeSelection={this.handleFromChangeSelection}
            onBack={this.handleBackPress}
            autoCompleteObj={{ label: FROM }}
            searchType={getCitySearchType(FROM)}
            citySelect={this.handleFromChangeSelection}
            showGetCurrLocation={true}
            trackClickEvent={this.props.trackLocalClickEvent}
            trackPDTV3Event={logPhoenixDetailPDTEvents}
          />
        )}
        {this.state.showCalendar && (
          <HolidayCalendar
            headerTitle={dateCalendarType === 'checkoutDate' ? 'Return Date' : 'Start Date'}
            selectedDate={this.state.currentPackageDate}
            onDone={this.onDateSelect}
            availableDates={this.state.excludedDates}
            onCalendarBack={this.onCalendarBack}
            minDate={minDate}
            onDayClicked={this.onDayClicked}
          />
        )}

        {!this.state.showCalendar &&
          !this.state.showDepartureCityPopup &&
          !this.state.showTravellerPopup &&
          !this.state.showLoader && (
            <View style={{ flex: 1 }}>
              <View style={[
                styles.overlayContent, 
                { bottom: 0 }
              ]}>
                <PageHeader
                  showBackBtn
                  showShadow
                  title={'Select Booking Details'}
                  onBackPressed={this.onBackButtonPressed}
                  containerStyles={styles.overlayHeader}
                />
                <ScrollView style={styles.cardList}>
                  {this.state.departureCity !== NO_DEPARTURE_CITY && !hideFromCity && (
                    <View>
                      <View style={[styles.bookingDtlsWrapper]}>
                        <Text
                            style={[
                              styles.bookingDtlLabel,
                              marginStyles.mb10,
                            ]}
                          >
                            FROM
                          </Text>
                          <View style={styles.bookingDtlsSection}>
                            <View style={AtomicCss.flexRow}>
                              <Text style={styles.bookingDtlsTxt}>{this.state.departureCity}</Text>
                              <Image style={styles.navigateImg} source={navigateImg} />
                            </View>
                            {!isWG && (
                              <TouchableOpacity>
                                {allowChangeHub ? (
                                  <Text
                                    style={actionStyle}
                                    onPress={this.changeDepartureCity}
                                  >
                                    Change
                                  </Text>
                                ) : (
                                  <Text
                                    style={[
                                      actionStyle,
                                      { opacity: 0.5 },
                                    ]}
                                    onPress={this.showMsg}
                                  >
                                    Change
                                  </Text>
                                )}
                              </TouchableOpacity>
                            )}
                        </View>
                      </View>
                      {this.state.showMsg && (
                        <View style={styles.msg}>
                          <Text style={styles.msgStyle}>
                            This Package is starting from {this.state.departureCity}
                          </Text>
                          <Text style={styles.msgStyle}>
                            Starting city of this package cannot be changed.
                          </Text>
                        </View>
                      )}
                    </View>
                  )}

                  <View style={[styles.bookingDtlsWrapper]}>
                    <Text style={[styles.bookingDtlLabel, marginStyles.mb10]}>
                      {startDateTxt || 'STARTING ON'}
                    </Text>
                    <View style={styles.bookingDtlsSection}>
                      <View style={AtomicCss.flexRow}>
                        <Text style={styles.bookingDtlsTxt}>{this.state.correctDate}</Text>
                        <Text style={styles.bookingDtlsDaysTxt}>{this.state.correctDay}</Text>
                      </View>
                      <TouchableOpacity>
                        <Text
                          style={actionStyle}
                          onPress={() => this.onSpecificDateClick('departureDate')}
                        >
                          Change
                        </Text>
                      </TouchableOpacity>
                    </View>
                  </View>

                  {correctCheckoutDate ? (
                    <View style={[styles.bookingDtlsWrapper]}>
                      <Text style={[styles.bookingDtlLabel, marginStyles.mb10]}>
                        {endDateTxt || 'ENDING ON'}
                      </Text>
                      <View style={styles.bookingDtlsSection}>
                        <View style={AtomicCss.flexRow}>
                          <Text style={styles.bookingDtlsTxt}>{correctCheckoutDate}</Text>
                          <Text style={styles.bookingDtlsDaysTxt}>
                            {this.state.correctCheckoutDay}
                          </Text>
                        </View>
                        <TouchableOpacity>
                          <Text
                            style={actionStyle}
                            onPress={() => this.onSpecificDateClick('checkoutDate')}
                          >
                            Change
                          </Text>
                        </TouchableOpacity>
                      </View>
                    </View>
                  ) : null}

                  <View style={[styles.bookingDtlsWrapper]}>
                    <Text style={[styles.bookingDtlLabel, marginStyles.mb10]}>
                      ROOMS &amp; GUESTS
                    </Text>
                    <View style={styles.bookingDtlsSection}>
                      <View style={AtomicCss.flexRow}>
                        <Text style={styles.bookingDtlsTxt}>
                          {this.state.adult > 1
                            ? `${this.state.adult} Adults `
                            : `${this.state.adult} Adult `}
                          {childText}
                          <Text style={styles.bookingDtlsTxt}>in</Text>
                          {this.state.noOfRooms > 1
                            ? ` ${this.state.noOfRooms} Rooms`
                            : ` ${this.state.noOfRooms} Room`}
                        </Text>
                      </View>
                      <TouchableOpacity>
                        <Text
                          style={actionStyle}
                          onPress={this.openTraveller}
                        >
                          Change
                        </Text>
                      </TouchableOpacity>
                    </View>
                  </View>
                </ScrollView>
                {this.state.showToastMsg && (
                  <View style={styles.errorMsg}>
                    <Text style={AtomicCss.whiteText}>{errorMsg}</Text>
                  </View>
                )}
                <View style={marginStyles.ma16}>
                  <PrimaryButton buttonText={'Apply'} handleClick={this.reloadTravellerContent} />
                </View>
              </View>
            </View>
          )}
      </View>
    );
  }
  captureClickEvents = ({
    eventName = '',
    value = '',
    suffix = '',
    prop1 = '',
    omniData = {},
    pdtExtraData = {},
  }) => {
    logPhoenixDetailPDTEvents({
      actionType: PDT_EVENT_TYPES.buttonClicked,
      value: value || eventName,
    });
    this.props.trackLocalClickEvent(eventName, suffix, { prop1, omniData, pdtExtraData });
  };
  reloadTravellerContent = () => {
    const {departureCity, selectedDate, roomData, packagePaxDetail, departureDate, destinationCity, citySelectionType, citySearchType} = this.state || {};
    const {adult, child, infantCount } = this.state || {};

    this.storeDataInLocalStorage();
    this.props.refreshFetchContent(selectedDate, departureCity, roomData, packagePaxDetail);

    //Tracking
    const m_v98 = `${citySelectionType} | ${citySearchType}`;
    const TOTAL_PAX = adult + child + infantCount;
    const AP_WINDOW = getAPWindow(departureDate);
    const FILTER_FLAG = 'N';
    const DATE_DATA = ` Date: ${departureDate}`;
    const SRC_DEST_DATA = `FromCity: ${departureCity}, Dest: ${destinationCity}`;
    const prop1 = `SearchCriteria_${departureCity}_${destinationCity}_${AP_WINDOW}_${TOTAL_PAX}_${FILTER_FLAG}`;
    const pdtExtraData = {
      search_criteria: `${SRC_DEST_DATA},Pax:${getPaxCountInfoForTotalRooms(this.props.roomDetails)},${DATE_DATA},Filters:${FILTER_FLAG}`,
    };
    this.captureClickEvents({
      eventName: 'Search',
      value: 'Search|' + prop1,
      prop1,
      omniData: { m_v98 },
      pdtExtraData,
    });
  };

  onBackButtonPressed = () => {
    this.props.togglePopup('');
  };

  openTraveller = () => {
    this.captureClickEvents({ eventName: PDTConstants.CHANGE_PAX });
    this.setState({
      showTravellerPopup: true,
    });
  };

  changeDepartureCity = () => {
    this.captureClickEvents({ eventName: PDTConstants.CHANGE_FROM_CITY });
    this.setState({
      showDepartureCityPopup: true,
    });
  };

  handleTravellerChange = (roomData, packagePaxDetail, isBack) => {
    const roomDetails = createRoomDetailsFromRoomDataForPhoenix(roomData, packagePaxDetail);
    const adult = getPaxCount(roomDetails, 'noOfAdults');
    const child = getPaxCount(roomDetails, 'noOfChildrenWB') + getPaxCount(roomDetails, 'noOfChildrenWOB');
    const noOfRooms = roomDetails.length;
    const infantCount = getPaxCount(roomDetails, 'noOfInfants');

    this.setState({
      adult,
      child,
      noOfRooms,
      roomData,
      infantCount,
      showTravellerPopup: false,
    });

    // Tracking
    const eventName = `changed_pax_adult_${adult}_child_${child}_infant_${infantCount}_noOfRooms_${noOfRooms}`;
    const prop1 = `changed_adult:${adult}_children:${child}_infant:${infantCount}`;
    const pdtExtraData = {
      pax_selected: `${adult}|${child}|${infantCount}|${adult + child + infantCount}`,
      pax_room_selected: getRoomDataForPdt(roomDetails),
    };
    const value = isBack ? 'back_pax' : eventName;
    this.captureClickEvents({ eventName, value, prop1, pdtExtraData });
  };

  handleFromChangeSelection = (city, branch, locusId, data ) => {
    const {citySearchType, citySelectionType, cityId, id, primaryCityObj = {}, nearByCities = []} = data || {};
    const eventName = `${PDTConstants.SELECT_FROM_CITY}_${city}`;
    const {primaryCityName} = primaryCityObj || {};
    const nearByCityDataForPdt = getNearByCityDataForPdt(nearByCities);


    this.setState({
      departureCity: city,
      departureCityId: cityId ? cityId : id,
      showDepartureCityPopup: false,
      citySearchType,
      citySelectionType,
      locusId,
      excludedDates: this.fetchPackageExcludedDates(),
    });

    //Tracking
    const pdtExtraData = {
      'fromcity_selected': city,
      'fromcity_selected_type': citySelectionType,
      'fromcity_search_type': citySearchType,
      'fromcity_selected_parent': primaryCityName ? primaryCityName : city,
      'fromcity_suggestions': nearByCityDataForPdt ? nearByCityDataForPdt : '',
    };
    const prop1 = `${city} | ${citySearchType}`;
    const value = `${eventName}|${prop1}`;
    this.captureClickEvents({ eventName, value, pdtExtraData, prop1 });
  };

  storeDataInLocalStorage() {
    const {departureCity, citySearchType, citySelectionType, locusId} = this.state || {};
    setDataInStorage(KEY_USER_DEP_CITY, departureCity);
    if (!isEmpty(citySearchType)) {
      setDataInStorage(KEY_USER_CITY_SEARCH_TYPE, citySearchType);
    }

    if (!isEmpty(citySelectionType)) {
      setDataInStorage(KEY_USER_CITY_SELECTION_TYPE, citySelectionType);
    }

    if (!isEmpty(locusId)) {
      setDataInStorage(KEY_USER_CITY_LOCUS_ID, locusId);
    }
  }

  getDateChangeEvent = (isFPHPage, dateType) => {
    if (isFPHPage) {
      return dateType === 'checkoutDate' ? 'change_returndate' : 'change_startdate';
    }
    return PDTConstants.CHANGE_DATE;
  };

  onSpecificDateClick = async (dateType) => {
    const { correctCheckoutDate } = this.state;
    const eventName = this.getDateChangeEvent(correctCheckoutDate, dateType);
    this.captureClickEvents({ eventName });
    let minDate = null;
    if (correctCheckoutDate) {
      minDate =
        dateType === 'departureDate'
          ? null
          : fecha.format(
              addDays(fecha.parse(this.state.departureDate, DATE_FORMAT), 1),
              DATE_FORMAT,
            );
    }
    const currentPackageDate = this.state[dateType]
      ? convertDateObject(this.state[dateType])
      : addDays(today(), CALENDAR_SELECTED_DATE_DIFF);
    this.setState({
      showCalendar: true,
      dateCalendarType: dateType,
      currentPackageDate,
      minDate,
    });
  };

  onCalendarBack = () => {
    this.captureClickEvents({ eventName: 'back_calendar' });
    this.setState({
      showCalendar: false,
    });
  };

  onDayClicked = (day) => {
    const eventName = 'click_calendar_date';
    const value = `click_calendar_date|${day?.dateString}`;
    this.captureClickEvents({ eventName, value });
  };

  onDateSelect = (selectedDate) => {
    let formattedDate = fecha.format(selectedDate, NEW_DATE_FORMAT);
    let formattedDay = fecha.format(selectedDate, ONLY_YEAR_DATE_FORMAT);
    let newSelectedDate = fecha.format(selectedDate, DATE_FORMAT);
    const { dateCalendarType } = this.state;

    //Tracking
    const pdtExtraData = { ap_window: getAPWindow(selectedDate) };
    const eventName = 'select_date';
    const value = `${eventName}|${newSelectedDate}`;
    this.captureClickEvents({ eventName, value, pdtExtraData });

    if (dateCalendarType === 'checkoutDate') {
      this.setState({
        correctCheckoutDate: formattedDate,
        correctCheckoutDay: formattedDay,
        selectedCheckoutDate: newSelectedDate,
        checkoutDate: newSelectedDate,
        showCalendar: false,
        dateCalendarType: null,
      });
    } else {
      this.setState(
        {
          correctDate: formattedDate,
          correctDay: formattedDay,
          selectedDate: newSelectedDate,
          departureDate: newSelectedDate,
          showCalendar: false,
          dateCalendarType: null,
        },
        () => {
          if (this.state.correctCheckoutDate) {
            const { checkoutDate, departureDate } = this.state;
            const formattedCheckoutDate = fecha.parse(checkoutDate, DATE_FORMAT);
            const formattedDepartureDate = fecha.parse(departureDate, DATE_FORMAT);
            if (formattedCheckoutDate <= formattedDepartureDate) {
              const newDate = addDays(formattedDepartureDate, 1);
              formattedDate = fecha.format(newDate, NEW_DATE_FORMAT);
              formattedDay = fecha.format(newDate, ONLY_YEAR_DATE_FORMAT);
              newSelectedDate = fecha.format(newDate, DATE_FORMAT);
              this.setState(
                {
                  correctCheckoutDate: formattedDate,
                  correctCheckoutDay: formattedDay,
                  selectedCheckoutDate: newSelectedDate,
                  checkoutDate: newSelectedDate,
                },
                () => {
                  this.onSpecificDateClick('checkoutDate');
                },
              );
            } else {
              this.onSpecificDateClick('checkoutDate');
            }
          }
        },
      );
    }
  };
}

const styles = StyleSheet.create({
  overlayContainer: {
    justifyContent: 'flex-end',
    position: 'absolute',
    bottom: 0,
    left: 0,
    height: '100%',
    width: '100%',
    zIndex: 15,
    elevation: 15,
  },
  errorMsg:{
    paddingHorizontal:5,
    backgroundColor:'#4A4A4A',
    height:50,
    justifyContent:'center',
    marginHorizontal:10,
    borderRadius:5,
    alignItems: 'center',
  },
  msg:{
    ...paddingStyles.pt18,
    ...paddingStyles.pb10,
    ...paddingStyles.pl10,
    ...marginStyles.mh16,
    backgroundColor:holidayColors.lightBlueBg,
   top: -10,
  },
  msgStyle: {
    ...fontStyles.labelSmallRegular,
    color: holidayColors.primaryBlue,
  },
  backWrapper: {
    paddingRight: 10,
    paddingLeft: 10,
    paddingVertical: 15,
  },
  overlayIconBack: {
    height: 24,
    width: 24,
  },
  overlayTitle: {
    flex: 5,
    color: '#000000',
    fontFamily: 'Lato-Bold',
    letterSpacing: 0.3,
    fontSize: 18,
  },
  overlayHeader: {
    backgroundColor: holidayColors.white,
    zIndex: 4,
  },
  cardList: {
    ...paddingStyles.pt14,
  },
  overlayBg: {
    backgroundColor: 'rgba(0,0,0,.5)',
    position: 'absolute',
    top: 0,
    left: 0,
    height: '100%',
    width: '100%',
    zIndex: 3,
    elevation: 3,
  },
  overlayContent: {
    backgroundColor: holidayColors.white,
    shadowColor: '#330000',
    shadowOpacity: 0.3,
    shadowRadius: 2,
    elevation: 4,
    zIndex: 4,
    position: 'absolute',
    bottom: 0,
    width: '100%',
    shadowOffset: {
      width: 1,
      height: 5,
    },
    height: '100%',
    flex: 1,
  },
  bookingDtlsWrapper: {
    borderColor: holidayColors.grayBorder,
    borderWidth: 1,
    backgroundColor: holidayColors.lightGray2,
    paddingVertical: 20,
    paddingHorizontal: 16,
    ...marginStyles.mt8,
    ...marginStyles.mh16,
    ...holidayBorderRadius.borderRadius16,
    zIndex: 1,
  },
  bookingDtlsSection: {
    justifyContent: 'space-between',
    flexDirection: 'row',
    alignItems: 'center',
  },
  bookingDtlLabel: {
    ...fontStyles.labelSmallBold,
    color: holidayColors.black,
    marginRight: 10,
  },
  bookingDtlsTxt: {
    ...fontStyles.labelMediumBlack,
    color: holidayColors.black,
  },
  navigateImg: {
    width: 15,
    height: 16,
    marginTop: 4,
    marginLeft: 10,
  },
  bookingDtlsDaysTxt: {
    ...fontStyles.labelMediumRegular,
    color: holidayColors.black,
    marginLeft: 10,
  },
});

EditOverlay.defaultProps = {
  checkoutDate: '',
  startDateTxt: '',
  endDateTxt: '',
};

EditOverlay.propTypes = {
  departureCity: PropTypes.string.isRequired,
  departureDate: PropTypes.string.isRequired,
  checkoutDate: PropTypes.string,
  startDateTxt: PropTypes.string,
  endDateTxt: PropTypes.string,
  roomDetails: PropTypes.array.isRequired,
  packageId: PropTypes.number.isRequired,
  flightCount: PropTypes.number.isRequired,
  categoryId: PropTypes.number.isRequired,
  cityId: PropTypes.number.isRequired,
  onBackPressed: PropTypes.func.isRequired,
  isWG: PropTypes.bool.isRequired,
};
export default withBackHandler(EditOverlay);