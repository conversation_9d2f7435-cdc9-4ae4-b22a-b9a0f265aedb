import React from 'react';
import { View, Text, Image, StyleSheet } from 'react-native';
import { fontStyles } from 'mobile-holidays-react-native/src/Styles/holidayFonts';
import { holidayColors } from 'mobile-holidays-react-native/src/Styles/holidayColors';
import { paddingStyles } from 'mobile-holidays-react-native/src/Styles/Spacing';

const ChildPolicy = (props) => {
  const { paxDetail } = props || {};

  const { intro, paxGuidelines } = paxDetail || {};
  return (
    <View>
      <Text style={styles.heading}>Child Policies</Text>
      <Text style={styles.description}>{intro}</Text>
      <Text style={styles.subHeading}>Age Guidelines</Text>
      <View style={styles.wrapper}>
        {paxGuidelines?.map((data) => {
          const { type, subHeader, text, iconUrl, bgColor, textColor } = data || {};
          return (
            <View style={styles.column}>
              <View style={[styles.upperBox, { backgroundColor: bgColor }]}>
                <Image source={{ uri: iconUrl }} style={styles.icon} />
                <Text style={[styles.headingText, { color: textColor }]}>{type}</Text>
              </View>
              <View style={styles.info}>
                <Text style={styles.infoHeading}>{subHeader}</Text>
                <Text style={styles.infoData}>{text}</Text>
              </View>
            </View>
          );
        })}
      </View>
    </View>
  );
};
const styles = StyleSheet.create({
  heading: {
    ...fontStyles.headingMedium,
    color: holidayColors.black,
  },
  description: { color: holidayColors.lightGray, paddingVertical: 10 },
  subHeading: {
    ...fontStyles.labelLargeBold,
    color: holidayColors.black,
    paddingVertical: 10,
  },
  wrapper: { display: 'flex', flexDirection: 'row', marginBottom: 100, marginTop: 10 },
  column: {
    flex: 1,
    borderRadius: 4,
    marginHorizontal: 5,
    shadowColor: holidayColors.black,
    shadowOffset: { width: 1, height: 1 },
    shadowOpacity: 0.2,
    elevation: 10,
  },
  upperBox: {
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
    height: 70,
    borderRadius: 4,
  },
  icon: { width: 20, height: 12, opacity: 0.5 , marginBottom: 5},
  headingText: {
    ...fontStyles.labelLargeBold,
  },
  info: {
    display: 'flex',
    alignItems: 'center',
    paddingHorizontal: 5,
    height: 185,
    backgroundColor: holidayColors.white,
  },
  infoHeading: {
    ...fontStyles.labelSmallBold,
    ...paddingStyles.pv14,
  },
  infoData: {
    textAlign: 'center',
    ...fontStyles.labelSmallRegular,
    color: holidayColors.gray,
  },
});


export default ChildPolicy;
