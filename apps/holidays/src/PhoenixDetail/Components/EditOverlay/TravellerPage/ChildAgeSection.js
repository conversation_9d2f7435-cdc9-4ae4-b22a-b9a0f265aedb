import React from 'react';
import {View, StyleSheet, Image, FlatList,Text, ScrollView, TouchableOpacity} from 'react-native';
import PropTypes from 'prop-types';


import ModalDropdown from 'react-native-modal-dropdown';
import downIcon from '@mmt/legacy-assets/src/down_arrow_blue.webp';
import {fonts} from '@mmt/legacy-commons/Styles/globalStyles';

export default class ChildAgeSection extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      currentChildIndex: this.props.index,
      activeSorter: this.props.activeSorter,
    };
  }

  pad = n => (n < 10 ? (`0${n}`) : n);

  handleChange = (value) => {
    const { maximumChildAge, packagePaxDetail = {} } = this.props || {};
    const { childWithBed = false } = packagePaxDetail || {}
    const selectedChildAge = parseInt(value);
    const bedRequired = childWithBed
      ? selectedChildAge >= maximumChildAge
        ? true
        : this.props.bedRequired
      : false; // bed to required true if age is more than max age (getting from backend as minChildAgeForBedCompulsory)
    this.setState(() =>
      this.props.handleChange(this.state.currentChildIndex, selectedChildAge, bedRequired),
    );
  };;

  getDefaultIndex = () => (this.props.activeSorter && this.props.activeSorter > 0) ? this.props.activeSorter - 1 : -1;

  render() {
    const options = ['01', '02', '03', '04', '05', '06', '07', '08', '09', '10', '11'];

    return (
      <View style={styles.container}>
        <ScrollView horizontal showsHorizontalScrollIndicator={false}>
          {options?.map((item, index) => {
            return (
              <TouchableOpacity
                onPress={() => {
                  this.handleChange(item);
                }}
              >
                <View
                  style={[
                    styles.childlist,
                    this.props.activeSorter - 1 == index ? { backgroundColor: '#008CFF' } : [],
                  ]}
                >
                  <Text
                    style={[
                      { textAlign: 'center', alignItems: 'center' },
                      this.props.activeSorter - 1 == index ? { color: 'white' } : [],
                    ]}
                  >
                    {item}
                  </Text>
                </View>
              </TouchableOpacity>
            );
          })}
        </ScrollView>
      </View>
    );
  }
}

ChildAgeSection.propTypes = {
  index: PropTypes.number.isRequired,
  handleChange: PropTypes.func.isRequired,
  activeSorter: PropTypes.number,
};

ChildAgeSection.defaultProps = {
  activeSorter: null,
};

const styles = StyleSheet.create({

  description: {
    fontFamily: 'Lato-Italic',
    fontSize: 12,
    fontStyle: 'italic',
    letterSpacing: 0.3,
    color: '#9b9b9b',
    marginBottom: 5,
  },
  bar: {
    flexDirection: 'column',
    flexWrap: 'wrap',
  },
  container:{
    display:'flex',
    flexDirection:'row',
    width:'100%',
    height:45,
  },
  buttonContainer: {
    borderRadius: 4,
    marginRight: 5,
    backgroundColor: '#fff',
    width: 120,
    height: 40,
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: '#BEBEBE',
  },
  icon:{
    height: 18,
    width:18,
    marginTop:4,
    marginStart: 16,
    resizeMode : 'contain',
  },
  defaultText:{
    width: 44,
    height: 19,
    fontSize: 16,
    fontFamily: fonts.bold,
    marginStart: 10,
    color: '#9b9b9b',
  },
  dropdownTextHighlight:{
    backgroundColor: '#EFEFEF',
  },
  dropdownText:{
    fontSize: 22,
    fontFamily: fonts.bold,
    paddingLeft: 50,
    color: '#000000',
  },
  dropdown:{
    borderColor: '#BEBEBE',
    borderWidth: 1,
    borderRadius: 4,
  },
  text:{
    fontSize: 22,
    fontFamily: fonts.bold,
    marginStart: 10,
    color: '#000000',
  },
  childlist:{
    height:40,
    width:40,
    marginRight:5,
    marginLeft:2,
    marginTop:2,
    backgroundColor:'white',
    justifyContent:'center',
    borderRadius:4,
    shadowColor: '#000',
  shadowOffset: { width: 0, height: 1 },
  shadowOpacity: 0.2,
  elevation: 10},
});
