import React from 'react';
import {View} from 'react-native';
import PropTypes from 'prop-types';
import OpenCard from './OpenCard';
import ClosedCard from './ClosedCard';


export default class RoomCards extends React.Component {
  render() {
    const {
      adultCount, childCount, index, active,
      removeRoom, closedCardClick,
      totalTravellerCount, childAgeArray, packagePaxDetail,trackClickEvent,roomCount,trackPDTV3Event = () => {}
    } = this.props;

    return (
      <View>
        {active !== index &&
        <ClosedCard
          adultCount={adultCount}
          childCount={childCount}
          removeRoom={removeRoom}
          closedCardClick={closedCardClick}
          index={index}
          totalTravellerCount={totalTravellerCount}
          childAgeArray={childAgeArray}
        />}
        {active === index &&
        <OpenCard
          adultCount={adultCount}
          childCount={childCount}
          index={index}
          removeRoom={removeRoom}
          changeCallbacks={this.props.changeCallbacks}
          childAgeArray={childAgeArray}
          packagePaxDetail={packagePaxDetail}
          trackClickEvent={trackClickEvent}
          roomCount={roomCount}
          trackPDTV3Event={trackPDTV3Event}
        />}
      </View>
    );
  }
}

RoomCards.propTypes = {
  childAgeArray: PropTypes.array.isRequired,
  childCount: PropTypes.number.isRequired,
  adultCount: PropTypes.number.isRequired,
  totalTravellerCount: PropTypes.number.isRequired,
  index: PropTypes.number.isRequired,
  active: PropTypes.number.isRequired,
  removeRoom: PropTypes.func.isRequired,
  changeCallbacks: PropTypes.object.isRequired,
  closedCardClick: PropTypes.func.isRequired,
  roomCount:PropTypes.number.isRequired,
};
