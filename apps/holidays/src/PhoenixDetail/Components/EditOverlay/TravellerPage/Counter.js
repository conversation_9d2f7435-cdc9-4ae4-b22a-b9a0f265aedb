import React, {Component} from 'react';
import {View, Text, TouchableOpacity, StyleSheet} from 'react-native';
import PropTypes from 'prop-types';


class Counter extends Component {
  constructor(props) {
    super(props);

    this.state = {
      value: this.props.value,
    };
  }

  componentWillReceiveProps(nextProps) {
    this.setState({
      value: nextProps.value,
    });
  }

  pad = n => (n < 10 ? (`0${n}`) : n);

  render() {
    const activeBtnOpacity = 0.7;
    const plusStyle = [styles.btnText];
    const minusStyle = [styles.btnText];
    const containerStyle = [styles.button];
    const {value} = this.state;

    return (
      <View style={styles.container}>
        <TouchableOpacity
          style={containerStyle}
          onPress={this.props.onDecrease}
          activeOpacity={activeBtnOpacity}
        >
          <Text style={minusStyle}>-</Text>
        </TouchableOpacity>
        <Text style={styles.value}>{this.pad(value)}</Text>
        <TouchableOpacity
          style={containerStyle}
          onPress={this.props.onIncrease}
          activeOpacity={activeBtnOpacity}
        >
          <Text style={plusStyle}>+</Text>
        </TouchableOpacity>
      </View>
    );
  }
}

Counter.propTypes = {
  value: PropTypes.number.isRequired,
  onIncrease: PropTypes.func.isRequired,
  onDecrease: PropTypes.func.isRequired,
};

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    alignItems: 'center',
    borderRadius: 4,
    borderStyle: 'solid',
    borderWidth: 0.9,
    borderColor: '#979797',
    paddingVertical: 2,
    paddingHorizontal: 2,
  },
  value: {
    marginHorizontal: 0,
    fontFamily: 'Lato-Bold',
    fontSize: 22,
    fontWeight: 'bold',
    fontStyle: 'normal',
    letterSpacing: 0,
    textAlign: 'center',
    color: '#040404',
    minWidth: 27,
  },
  btnText: {
    color: '#4a4a4a',
    fontFamily: 'Lato-Bold',
    fontSize: 20,
    fontStyle: 'normal',
    fontWeight: 'bold',
    letterSpacing: 0,
    textAlign: 'center',
    paddingHorizontal: 17,
    paddingVertical: 5,
  },
  disableBtnText: {
    opacity: 0.3,
  },
});

export default Counter;
