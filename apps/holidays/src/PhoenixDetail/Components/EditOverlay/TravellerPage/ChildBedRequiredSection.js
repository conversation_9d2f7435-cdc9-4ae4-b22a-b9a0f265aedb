import React from 'react';
import {View, StyleSheet, Text, TouchableOpacity} from 'react-native';
import PropTypes from 'prop-types';

import AtomicCss from '@mmt/legacy-commons/Styles/AtomicCss';
import Radio from '@mmt/ui/components/radio';
import { fontStyles } from '../../../../Styles/holidayFonts';
import { holidayColors } from '../../../../Styles/holidayColors';
import { marginStyles } from '../../../../Styles/Spacing';

export default class ChildBedRequiredSection extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      currentChildIndex: this.props.index,
      bedRequired: this.props.bedRequired,
      //activeSorter is the current selected age of child
      activeSorter: this.props.activeSorter,
    };
  }

  handleChange = (bedRequired) => {
    const { index: currentChildIndex, activeSorter } = this.props || {};
    this.setState(() => this.props.handleChange(currentChildIndex, activeSorter, bedRequired));
  };

  render() {
    return (
      <View style={styles.container}>
        <View style={[AtomicCss.flexRow]}>

          {/*Yes Button*/}
          <TouchableOpacity onPress={() => {
            if (!this.state.bedRequired) {
              this.setState({bedRequired: true});
              this.handleChange(true);
            }
          }}>
          <View style={styles.radioContainer}>
            <Radio
              style={styles.radio}
              isSelected={this.state.bedRequired}/>
            <Text style={styles.radioText}>Yes</Text>
          </View>
          </TouchableOpacity>

          {/*No button*/}
          <TouchableOpacity onPress={() => {
            if (this.state.bedRequired) {
              this.setState({bedRequired: false});
              this.handleChange(false);
            }
          }}>
          <View style={styles.radioContainer}>
            <Radio
              style={styles.radio}
              isSelected={!this.state.bedRequired}/>
            <Text style={styles.radioText}>No</Text>
          </View>
          </TouchableOpacity>

        </View>
      </View>
    );
  }
}

ChildBedRequiredSection.propTypes = {
  index: PropTypes.number.isRequired,
  handleChange: PropTypes.func.isRequired,
  activeSorter: PropTypes.number,
};

ChildBedRequiredSection.defaultProps = {
  activeSorter: null,
};

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    ...marginStyles.mv16,
    marginLeft: 65,
  },
  radio: {
    ...marginStyles.mr6,
    width: 20,
    height: 20,
  },
  radioContainer: {
    flexDirection: 'row',
    ...marginStyles.mr16,
    alignItems: 'center',
  },
  radioText: {
    ...fontStyles.labelSmallBold,
    color: holidayColors.black,
  },
  disabledText: {
    color: holidayColors.red,
  },
});
