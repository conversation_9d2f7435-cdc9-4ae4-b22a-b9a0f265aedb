import React, {Component} from 'react';
import {FlatList, Text, TouchableOpacity, View} from 'react-native';
import {times} from 'lodash';
import styles from './horizontalCalendarCss';
import {TABS} from '../DayPlan/Sorter';
import LinearGradient from 'react-native-linear-gradient';
import { trackPhoenixDetailLocalClickEvent } from '../../Utils/PhoenixDetailTracking';
import { holidayColors } from '../../../Styles/holidayColors';
import { MONTHS } from './Calendar/utilities';
import { logPhoenixDetailPDTEvents } from 'mobile-holidays-react-native/src/utils/PhoenixDetailPDTTrackingUtils';
import { PDT_EVENT_TYPES } from 'mobile-holidays-react-native/src/utils/HolidayPDTConstants';

const days = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'];

class HorizontalCalendar extends Component {
  constructor(props) {
    super(props);
    this.state = {
      startIndex: 0,
      selectedDate: props.calenderDates[0].currentDate,
      monthLabelDate: props.calenderDates[0].currentDate,
      selectedTab:props.currentActivePlanOnItinerary,
      ...this._getDatesState(),
    };
  }

  _updateSelectedDate = (index) => {
    if (this.props.calenderDates && index >= 0 && index < this.props.calenderDates.length) {
      this.setState({
        selectedDate: this.props.calenderDates[index].currentDate,
      });
      if (this._calendarScroll) {
        // Ensure the index is within valid bounds before scrolling
        const maxIndex = this.props.calenderDates.length - 1;
        const validIndex = Math.min(Math.max(0, index), maxIndex);
        this._calendarScroll.scrollToIndex({ index: validIndex, animated: true });
      }
    }
  }

  _getDatesState() {
    const {calenderDates} = this.props;
    if (calenderDates && calenderDates.length > 0) {
      const currentDate = this.state && this.state.selectedDate || calenderDates[1].currentDate;
      let startIndex = 0;
      const dates = times(calenderDates.length, (i) => {
        var currentD = calenderDates[i].currentDate;
        if (currentD.getTime() === currentDate.getTime()) {
          startIndex = i;
        }
        return ({
          date: currentD.getDate(),
          day: days[currentD.getDay()],
          month: currentD.getMonth(),
          year: currentD.getFullYear(),
          ts: currentD.getTime(),
        });
      });
      return {
        dates,
        startIndex,
      };
    }
    return {};
  }

  captureClickEvents = ({ eventName, suffix }) => {
    logPhoenixDetailPDTEvents({
      actionType: PDT_EVENT_TYPES.buttonClicked,
      value: eventName + suffix,
    });
    trackPhoenixDetailLocalClickEvent({
      eventName,
      suffix,
    });
  };


  _getItemLayout = (data, index) => ({length: 70, offset: 70 * index, index});

  _onDateSelected = (date) => {
    if (date.ts === this.state.selectedDate.getTime()) {
      return;
    }
    const dayNumber = this.state.dates.findIndex((item) => item.ts === date.ts) + 1;
    this.captureClickEvents({
      eventName: 'navigation_daywise_',
      suffix:`${dayNumber}`,
    });
    this.props.onPressHandler(this.props.index + dayNumber);
    this.setState({selectedDate: new Date(date.ts)});
  };

  _renderDate = ({item, index}) => {
    const {currentActivePlanOnItinerary, calenderDates} = this.props || {};

    // Handle Tab change. Reset selected tabs and dates.
    if (this.state.selectedTab !== currentActivePlanOnItinerary) {
      this.setState({selectedTab: currentActivePlanOnItinerary});

      // set first date of the calender as default selected day switched tab is DAY.
      if (currentActivePlanOnItinerary === TABS.DAY) {
        this.setState({selectedDate: calenderDates[0].currentDate});
      }
    }

    // Make the first date of the calender as selected when current TAB is DAY.
    // Other dates will not be highlighted if selected TAB is not DAY.
    // For rest of the tabs {@link #highLightItem(item)} method determines if a particular day in calender need to be highlighted or not.
    let isSelected;
    if (currentActivePlanOnItinerary === TABS.DAY) {
      isSelected = new Date(item.ts).getDate() === new Date(this.state.selectedDate.getTime()).getDate();
    }

    // init isSelectedText string.
    // This string will be used to select CSS based on highlighted date.
    let isSelectedText = (isSelected || this.highLightItem(item)) ? 'Selected' : '';

    // Calender item should be clickable only i case if DAY or it has be already highlighted.
    const enablePress = isSelectedText === 'Selected' || currentActivePlanOnItinerary === TABS.DAY;

    const isFirstDay = item.date === 1;
    let dateContainerStyle = [styles.dateContainer];
    let dayTextStyle = [styles.dayText];
    let dateTextStyle = [styles.dateText];

    if (isSelectedText === 'Selected') {
      dateContainerStyle.push(styles.activeDateContainer);
      dateTextStyle.push(styles.primaryBlueText);
      dayTextStyle.push(styles.primaryBlueText);
    }

    return (
      <>
        {isFirstDay &&
          index !== 0 &&
          MONTHS[item.month] !== MONTHS[this.state.monthLabelDate.getMonth()] && (
            <View style={{
              zIndex: 1,
              left: 5,
            }}>
                <MonthLabel month={MONTHS[item.month]} />
            </View>         
        )}
        <TouchableOpacity
          style={dateContainerStyle}
          activeOpacity={0.8}
          disabled={!enablePress}
          onPress={() => this._onDateSelected(item)}
        >
          <Text style={dayTextStyle}>{item.day}</Text>
          <Text style={dateTextStyle}>{item.date}</Text>
        </TouchableOpacity>
      </>
    );
  }

  handleViewableItemsChanged = ({ viewableItems, changed }) => {
    const firstViewItem = viewableItems[0];
    if (this.state.monthLabelDate.getMonth() !== firstViewItem.item.month) {
      this.setState({
        monthLabelDate: new Date(firstViewItem.item.ts),
      });
    }
  }

  render() {
    const {dates} = this.state;
    if (dates && dates.length > 0) {
      return (
        <View style={styles.container}>
          <View style={styles.monthLabelContainer}>
            <MonthLabel month={MONTHS[this.state.monthLabelDate.getMonth()]} />
          </View>
          <View style={styles.listContainer}>
            <FlatList
              ref={(e) => {
                this._calendarScroll = e;
              }}
              showsHorizontalScrollIndicator={false}
              horizontal
              onScrollToIndexFailed={info => {
                const wait = new Promise(resolve => setTimeout(resolve, 500));
                wait.then(() => {
                  this._calendarScroll.scrollToIndex({ index: 0, animated: true });
                });
              }}
              data={dates}
              extraData={this.state.selectedDate}
              renderItem={this._renderDate}
              keyExtractor={i => `${i.ts}`}
              onViewableItemsChanged={this.handleViewableItemsChanged}
              removeClippedSubviews
              scrollEventThrottle={16}
            />
          </View>
        </View>
      );
    }
    return [];
  }

  // This functions returns an array of dates for currently selected tab
  // ex: if seleted tab is FLIGH then returned array will contain all the dates for which FLIGHTS are available.
  // returns an empty array of no dates are found.
  getDatesForType = () => {
    const {currentActivePlanOnItinerary, calenderDates} = this.props || {};
    let retData = [];
    calenderDates.forEach(date => {
      const {currentDate, types} = date || {};
      if (currentActivePlanOnItinerary === TABS.DAY) {
        retData.push(currentDate);
      } else if (types && types.some(d => d === currentActivePlanOnItinerary)) {
        retData.push(currentDate);
      }
    });
  return retData;
  };

  // check and returns if a particular date on the calender view needs be highlighted or not.
  // This method put a check tabs other than day tab.
  highLightItem = (inputDate) => {
    const {currentActivePlanOnItinerary} = this.props || {};
    // if current tab is DAY the no highlight is required.
    if (currentActivePlanOnItinerary === TABS.DAY) {
      return false;
    }
    // get all the dates of selected tab.
    const dates = this.getDatesForType();
    // check of input date exist in the list.
    return dates.some(d => new Date(d).getDate() === inputDate.date);
  };
}

const MonthLabel = ({ month }) => {
  return (
    <LinearGradient
      start={{ x: 0.0, y: 1.0 }}
      end={{ x: 0.0, y: 0.0 }}
      colors={[holidayColors.gray, holidayColors.black]}
      style={styles.labelContainer}
    >
      <Text style={styles.labelText}>{month}</Text>
    </LinearGradient>
  );
};


export default HorizontalCalendar;
