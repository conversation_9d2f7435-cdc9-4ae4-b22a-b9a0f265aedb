const dateStrToObj = (dateStr) => {
  // Format: YYYY-MM-DD
  const arr = dateStr.split('-').map((str) => Number(str));
  const date = new Date(arr[0], arr[1] - 1, arr[2]);

  return date;
};

const dateToStr = (date) => {
  const year = date.getFullYear();
  let month = date.getMonth() + 1;
  if (month < 10) {
    month = `0${month}`;
  }
  let dateStr = date.getDate();
  if (dateStr < 10) {
    dateStr = `0${dateStr}`;
  }

  return `${year}-${month}-${dateStr}`;
};

export const DAYS = ['SUN', 'MON', 'TUE', 'WED', 'THU', 'FRI', 'SAT'];
export const MONTHS = [
  'JAN',
  'FEB',
  'MAR',
  'APR',
  'MAY',
  'JUN',
  'JUL',
  'AUG',
  'SEP',
  'OCT',
  'NOV',
  'DEC',
];

const getDay = (date) => DAYS[date.getDay()];
const getMonth = (date) => MONTHS[date.getMonth()];
const getDate = (date) => {
  const number = date.getDate();

  return number < 10 ? `0${number}` : number;
};

export {
  dateStrToObj,
  getDay,
  getMonth,
  getDate,
  dateToStr,
};
