import { holidayBorderRadius } from '../../../Styles/holidayBorderRadius';
import { holidayColors } from '../../../Styles/holidayColors';
import { fontStyles } from '../../../Styles/holidayFonts';
import { widthPixel } from '../../../Styles/holidayNormaliseSize';
import { marginStyles } from '../../../Styles/Spacing';

const DATE_CONTAINER_HEIGHT = 48;
const MONTH_CONTAINER_HEIGHT = DATE_CONTAINER_HEIGHT;
const styles = {
  whiteText: {
    color: holidayColors.white,
    opacity: 1,
  },
  primaryBlueText: {
    color: holidayColors.primaryBlue,
    opacity: 1,
  },
  container: {
    paddingHorizontal: 8,
    paddingBottom: 10,
  },
  monthLabelContainer: {
    position: 'absolute',
    zIndex: 1,
    left: 15,
  },
  listContainer: {
    left: 12,
    ...marginStyles.ml12,
  },
  dateContainer: {
    justifyContent: 'space-between',
    alignItems: 'center',
    height: DATE_CONTAINER_HEIGHT,
    ...holidayBorderRadius.borderRadius8,
    backgroundColor: holidayColors.white,
    borderColor: holidayColors.grayBorder,
    borderWidth: 1,
    paddingHorizontal: 15,
    paddingTop: 6,
    paddingBottom: 6,
    marginRight: 4,
  },
  activeDateContainer: {
    backgroundColor: holidayColors.white,
    borderColor: holidayColors.primaryBlue,
    borderWidth: 1,
  },
  dayText: {
    ...fontStyles.labelSmallRegular,
    color: holidayColors.lightGray,
  },
  dateText: {
    ...fontStyles.labelBaseBlack,
    color: holidayColors.black,
  },
  labelContainer: {
    // marginTop:1,
    height: MONTH_CONTAINER_HEIGHT,
    width: widthPixel(28),
    justifyContent: 'center',
    alignItems: 'center',
    borderTopLeftRadius: 4,
    borderBottomLeftRadius: 4,
  },
  labelText: {
    ...fontStyles.labelSmallRegular,
    color: holidayColors.white,
    transform: [{ rotate: '270deg' }],
  },
};

export default styles;
