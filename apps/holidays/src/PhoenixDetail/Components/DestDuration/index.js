import React from 'react';
import {StyleSheet, Text, View} from 'react-native';
import {connect} from 'react-redux';
import { holidayColors } from '../../../Styles/holidayColors';
import { fontStyles } from '../../../Styles/holidayFonts';

const container = (props) => {
  const {detailData, containerStyles} = props || {};
  const {packageDetail} = detailData || {};
  const {destinationDetail} = packageDetail || {};
  const {destinations} = destinationDetail || {};
  return (
    <View style={[styles.container, containerStyles]}>
      {destinations.map((destination, index) => (
        <>
          <Text style={styles.text}>{destination.duration}N {destination.name} </Text>
          {index !== destinations.length - 1 && (
            <View style={styles.separator}/>
          )}
        </>
      ))}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    alignItems: 'center',
    flexWrap: 'wrap',
    width: '90%',
    paddingHorizontal: 15,
    marginTop: 4,
  },
  separator: {
    backgroundColor: '#26b5a9',
    borderRadius: 100,
    width: 6,
    height: 6,
    marginHorizontal: 5,
    lineHeight: 16,
  },
  text: {
    ...fontStyles.labelBaseRegular,
    color: holidayColors.gray,
    lineHeight: 16,
  },
});

const mapStateToProps = (state) => {
  const {detailData} = state.holidaysDetail || {};
  return {detailData};
};

export default connect(mapStateToProps, null)(container);
