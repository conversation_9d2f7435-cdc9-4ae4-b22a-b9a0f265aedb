import React, {useState} from 'react';
import {Image, LayoutAnimation, StyleSheet, Text, TouchableOpacity, View} from 'react-native';

import DownArrow from '../../images/ic_downArrow.png';
import cStyles from '@mmt/legacy-commons/Styles/AtomicCss';
import { fontStyles } from 'mobile-holidays-react-native/src/Styles/holidayFonts';
import { holidayColors } from 'mobile-holidays-react-native/src/Styles/holidayColors';
import { paddingStyles } from 'mobile-holidays-react-native/src/Styles/Spacing';

const HouseRulesAccordian = (props) => {
  const {item, index, clickedItemIndex, onItemClick} = props || {};
  const [showData, setAccordian] = useState(index === clickedItemIndex);

  const accordianClick = () => {
    LayoutAnimation.configureNext(
      LayoutAnimation.create(
        400,
        LayoutAnimation.Types.easeInEaseOut,
        LayoutAnimation.Properties.opacity
      )
    );
    setAccordian(!showData);
    if (!showData){
      onItemClick(item, index);
    }
  };

  return (
    <View style={[styles.accordianWrap, cStyles.marginBottom15]}>
      <TouchableOpacity onPress={accordianClick} style={styles.accordianHead}>
        <View style={{flexDirection: 'row', flex:1}}>
          <View style={{flex: 10}}>
        <Text style={styles.heading}>{item.cardHdr}</Text>
          </View>
          <View style={{flex:1}}>
            <Image style={[showData ? styles.downArrow : {}, styles.arrow]} source={DownArrow}/>
          </View>
        </View>
      </TouchableOpacity>
      {showData &&
      <View style={styles.accordianContent}>
        {item.cardContent.map(content => (
          <View style={[cStyles.flexRow, cStyles.marginBottom10]}><View style={styles.contentDots}/><Text
            style={styles.content}>{content}</Text></View>
        ))}
      </View>
      }
    </View>
  );
};

const styles = StyleSheet.create({

  accordianHead: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    ...paddingStyles.pa16,
  },
  heading: {
    ...fontStyles.labelBaseBlack,
    color: holidayColors.black,
  },
  content: {
    ...fontStyles.labelSmallRegular,
    color: holidayColors.gray,
  },
  arrow: {
    width: 24,
    height: 25,
  },
  downArrow: {
    transform: [
      {rotate: '180deg'},
    ],
  },
  contentDots: {
    width: 4,
    height: 4,
    backgroundColor: holidayColors.gray,
    borderRadius: 10,
    marginTop: 6,
    marginRight: 5,
  },
  accordianWrap: {
    backgroundColor: holidayColors.white,
    borderBottomColor: holidayColors.grayBorder,
    borderBottomWidth: 1,
  },
  accordianContent: {
    ...paddingStyles.ph16,
  },
});

export default HouseRulesAccordian;
