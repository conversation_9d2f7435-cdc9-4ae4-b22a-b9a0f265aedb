import React, {Component} from 'react';
import {BackHandler, Platform, StyleSheet, View} from 'react-native';
import {exitLocalNotification, isIosClient} from '../../../../utils/HolidayUtils';
import {
  DETAIL_LOCAL_NOTIFICATION_PAGE_NAME,
  PDTConstants,
} from '../../../DetailConstants';
import {PROVIDER_GOOGLE} from 'react-native-maps';
import MapView, {Marker} from 'react-native-maps';
import { HolidayNavigation } from '../../../../Navigation';
import { trackPhoenixDetailLocalClickEvent } from '../../../Utils/PhoenixDetailTracking';
import PageHeader from 'mobile-holidays-react-native/src/Common/Components/PageHeader';
import { paddingStyles } from 'mobile-holidays-react-native/src/Styles/Spacing';
import withBackHandler from '../../../../hooks/withBackHandler';
import HolidayDataHolder from '../../../../../src/utils/HolidayDataHolder';
import { SUB_PAGE_NAMES } from '../../../../../src/HolidayConstants';

const HARDWARE_BACK_PRESS = 'hardwareBackPress';

class HolidayDetailMapPage extends Component {
  MARKER_COLOR = '#7EACFD';
  constructor(props) {
    super(props);
  }

  onBackClick = ()=> {
    return this.onBackPressed();
     HolidayDataHolder.getInstance().clearSubPageName()
  }

  onBackPressed = () => {
    HolidayNavigation.pop();
    exitLocalNotification(DETAIL_LOCAL_NOTIFICATION_PAGE_NAME);
    this.trackLocalClickEvent(PDTConstants.BACK, '');
    return true;
  };

  trackLocalClickEvent = (eventName, suffix) => {
    trackPhoenixDetailLocalClickEvent({
      eventName: eventName + suffix,
      prop1: this.props?.pageName || '',
    });
  };

  render() {
    const {name, markers} = this.props || {};
    let lat, long = 0.0;

    try {
      if (markers && markers.length > 0) {
        lat = markers[0].coordinates.latitude;
        long = markers[0].coordinates.longitude;
      }
    } catch (e) {
      console.error(e);
    }

    return (
      <View>
        <PageHeader
          showBackBtn
          showShadow
          title={name}
          onBackPressed={this.onBackPressed}
        />
        <MapView
          style={styles.map}
          provider={PROVIDER_GOOGLE}
          title={name}
          initialRegion={{
            latitude: lat,
            longitude: long,
            latitudeDelta: 0.0922,
            longitudeDelta: 0.0421,
          }}>
          {markers && markers.length > 0 && markers.map(marker => (
            <Marker
              coordinate={marker.coordinates}
              pinColor={this.MARKER_COLOR}
            />
          ))}
        </MapView>
      </View>
    );
  }
}

const styles = StyleSheet.create({
  map: {
    height: '100%',
    width: '100%',
  },
});

export default withBackHandler(HolidayDetailMapPage)
