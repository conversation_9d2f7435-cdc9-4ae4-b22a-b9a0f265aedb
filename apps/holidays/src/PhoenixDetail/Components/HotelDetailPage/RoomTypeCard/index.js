import React, { useState } from 'react';
import { View, Text, StyleSheet, Image, TouchableOpacity, LayoutAnimation, UIManager, Platform } from 'react-native';
import cStyles from '@mmt/legacy-commons/Styles/AtomicCss';
import iconAdult from '../../images/ic_adult.png';
import iconChild from '../../images/ic_child.png';
import iconDownArrow from '../../images/ic_downArrow.png';
import iconUpArrow from '../../images/ic_upArrow.png';
import BottomSheetOverlay from '../../../../Common/Components/BottomSheetOverlay';
import TickIcon from '../../images/greenTick.png';
import { getAllInclusions, getFormattedRoomTypeList } from '../../../Utils/PhoenixDetailUtils';
import { has } from 'lodash';
import { getPriceDiff, getSelectedRoomIndex } from '../../HotelDetailPageFullScreen/HotelDetailPageFullScreenUtil';
import { HOLIDAY_ROUTE_KEYS, HolidayNavigation } from '../../../../Navigation';
import { fontStyles } from '../../../../Styles/holidayFonts';
import { holidayColors } from '../../../../Styles/holidayColors';
import { holidayBorderRadius } from '../../../../Styles/holidayBorderRadius';
import { paddingStyles, marginStyles } from '../../../../Styles/Spacing/index';
import RoomDetails from 'mobile-holidays-react-native/src/DetailMimaComponents/Hotel/HotelDetail/RoomDetails';
import RoomAmenities from 'mobile-holidays-react-native/src/DetailMimaComponents/Hotel/HotelDetail/RoomAmenities';
import SelectedTag from '../../../../Common/Components/Tags/SelectedTag';
import TextButton from 'mobile-holidays-react-native/src/Common/Components/Buttons/TextButton';
import { logPhoenixDetailPDTEvents } from 'mobile-holidays-react-native/src/utils/PhoenixDetailPDTTrackingUtils';
import { PDT_EVENT_TYPES } from 'mobile-holidays-react-native/src/utils/HolidayPDTConstants';
import {isAndroidClient} from "../../../../utils/HolidayUtils";

if (isAndroidClient()) {
  UIManager.setLayoutAnimationEnabledExperimental(true);
}

// pricingDetail node is for Package level Price
const RoomTypeCard = ({ hotelDetailData, pricingDetail, packageDetailDTO, onSelectRoomClick, selectedRoom, changeHotelRestricted, trackOmniture, bundled, defaultRoom = {} }) => {
  if (bundled) {
    return [];
  }
  const { hotel, roomPriceMap, discountedFactor } = hotelDetailData || {};
  const { roomTypes } = hotel || {};
  const roomTypeList = getFormattedRoomTypeList(roomTypes);
  const [showCardIndex, setShowCardIndex] = useState(getSelectedRoomIndex(roomTypeList, selectedRoom));

  const toggleCard = (index) => {
    LayoutAnimation.configureNext(LayoutAnimation.Presets.easeInEaseOut);
    setShowCardIndex(showCardIndex === index ? null : index);
  };

  if (!roomTypes || roomTypes === 'undefined') {
    return [];
  }

  const retArray = [];
  roomTypeList.map((item, index) => {
    const singleRoom = item[0];
    const { name, imageDataList, code } = singleRoom || {};
    const roomSelected = isRoomSelected(selectedRoom, code);

    if (changeHotelRestricted && !roomSelected) {
      // if Change room is not allowed and item is not a default selected room.
      // do nothing
      return [];
    }

    const openImageGalleryForRoom = () => {
      if (!imageDataList || imageDataList.length === 0) {
        return;
      }

      const imageList = imageDataList.reduce((acc, item) => {
        const { images } = item || {};
        if (images && images.length > 0) {
          images.forEach(image => {
            if (image && image.path) {
              acc.push(image.path);
            }
          });
        }
        return acc;
      }, []);

      if (imageList.length > 0) {
        HolidayNavigation.push(HOLIDAY_ROUTE_KEYS.DETAIL_GRID_GALLERY_FULL_PAGE, { imageList, name });
      }
    };

    retArray.push(
      <View style={[styles.roomTypeCard, roomSelected ? styles.backgroundBlue : styles.backgroundYellow]} key={index}>
        <RoomDetails hotelDetailData={hotelDetailData}>
          <TouchableOpacity onPress={() => toggleCard(index)}>
            <View style={styles.roomTypeHdr}>
              <Text style={styles.roomTypeName}>{name}</Text>
              <Image
                source={showCardIndex === index ? iconUpArrow : iconDownArrow}
                style={styles.iconArrow}
              />
            </View>
          </TouchableOpacity>
        </RoomDetails>
        {(showCardIndex === index && (
          <View style={[paddingStyles.ph16, paddingStyles.pt12]}>
            <RoomAmenities
              enableHotelClick={true}
              roomData={singleRoom}
              trackOmniture={trackOmniture}
              handlehotelClick={openImageGalleryForRoom}
            />
            {item && item.map((room, index) => <IndividualRoomInfo
              index={index}
              room={room}
              roomPriceMap={roomPriceMap}
              pricingDetail={pricingDetail}
              discountedFactor={discountedFactor}
              onSelectRoomClick={onSelectRoomClick}
              selectedRoom={selectedRoom}
              changeHotelRestricted={changeHotelRestricted}
              trackOmniture={trackOmniture}
              defaultRoom={defaultRoom}
            />)}
          </View>
        ) || null)}
      </View>);

  });
  return retArray;
};

const IndividualRoomInfo = ({ index, room, roomPriceMap, pricingDetail, discountedFactor, onSelectRoomClick, selectedRoom, changeHotelRestricted, trackOmniture, defaultRoom = {} }) => {
  const [roomInclusionsModalVisibility, setRoomInclusionsModalVisibility] = useState(false);
  if (room) {
    const { ratePlan, roomInformation, code: roomTypeCode } = room || {};
    const { refundable, inclusions, inclusionsHighlighted } = roomInformation || {};
    const allInclusions = getAllInclusions(inclusionsHighlighted, inclusions);
    const { mealName, code: ratePlanCode } = ratePlan || {};

    const { ratePlan: defaultRatePlan } = defaultRoom || {};
    const { code: defaultRatePlanCode } = defaultRatePlan || {};


    // pricingDetail node is for Package level Price
    const { price } = pricingDetail.categoryPrices[0] || {};
    let priceDifference = getPriceDiff(roomPriceMap, price, roomTypeCode, ratePlanCode, discountedFactor);

    if (defaultRatePlanCode === ratePlanCode) {
      priceDifference = 0;
    }
    const isRoomSelected = isRoomPlanSelected(selectedRoom, room);
    const MAX_INCLUSION_TO_SHOW = 2;

    // if Change room is not allowed and item is not a default selected room, return.
    if (changeHotelRestricted && !isRoomSelected) {
      return [];
    }

    const captureClickEvents = ({eventName = '', value = ''}) => {
      logPhoenixDetailPDTEvents({
        actionType: PDT_EVENT_TYPES.buttonClicked,
        value: value || eventName.replace(/_/g,'|'),
      })
      trackOmniture(eventName);
    }

    const onViewAllInclusionsClicked = () => {
      setRoomInclusionsModalVisibility(true);
      captureClickEvents({eventName: 'View_All_Inclusions',value : 'View|All_Inclusions'});
    };

    return (<View style={styles.roomTypeRowWrap} key={index}>
      <View style={styles.roomTypeRow}>
        <View style={styles.roomTypeLeft}>
          <View style={[cStyles.flexRow, cStyles.alignCenter, marginStyles.mb6]}>
            <Text style={[styles.mealText]}>{mealName}</Text>
            <View style={[marginStyles.ml6]}>
              <Text style={[fontStyles.labelSmallRegular, refundable ? cStyles.greenText : cStyles.redText, cStyles.regularFont]}>{''}</Text>
            </View>
          </View>
          <View style={[marginStyles.mb6]}>
            {allInclusions
              && allInclusions.length > 0
              && allInclusions
                .slice(0, MAX_INCLUSION_TO_SHOW)
                .map(({ description, color }, index) => (
                  <View style={[cStyles.flexRow, marginStyles.mb6]} key={index}>
                    <Image style={[styles.tick, { tintColor: color }]} source={TickIcon} />
                    <View style={{width:'100%'}}>
                      <Text style={[styles.descText, { color }]}>
                        {description}
                      </Text>
                    </View>
                  </View>
                ))
            }
          </View>
        </View>
        <View style={styles.roomTypeRight}>
          <View style={[cStyles.flexRow, cStyles.spaceBetween, marginStyles.mb2]}>
            <View style={marginStyles.mr6}><Text
              style={[styles.bfPersonText]}>for</Text></View>
            <View style={[cStyles.flexRow, cStyles.alignCenter]}>
              <View><Image source={iconAdult} style={styles.iconAdult} /></View>
              <View><Image source={iconAdult} style={styles.iconAdult} /></View>
              <View><Image source={iconChild} style={styles.iconChild} /></View>
            </View>
          </View>
          {(priceDifference >= 0 || priceDifference < -1) &&
            <View style={{ alignItems: 'flex-end' }}>
              <View><Text
                style={[styles.priceText, marginStyles.mb2]}>₹ {priceDifference > 0 ? '+' + priceDifference : priceDifference}</Text></View>
              <View><Text style={[styles.bfPersonText]}>per person</Text></View>
            </View>
          }
        </View>
      </View>

      <View style={[{ flex: 1, flexDirection: 'row' }, marginStyles.mt6]}>
        {allInclusions && allInclusions.length > MAX_INCLUSION_TO_SHOW ?
          <TextButton
            buttonText="View All Inclusions"
            handleClick={onViewAllInclusionsClicked}
            btnTextStyle={styles.selectRoomLink}
          />
          : []
        }

        <View style={{ marginLeft: 'auto' }}>
          {!isRoomSelected ? (
            <TextButton
              buttonText="+ Select Room"
              handleClick={() => onSelectRoomClick(priceDifference, room, index)}
              btnTextStyle={styles.selectRoomLink}
            />
          ) : (
            <View style={[{ alignItems: 'flex-end' }, marginStyles.mt6]}>
              <SelectedTag />
            </View>
          )}
        </View>
      </View>

      {roomInclusionsModalVisibility ?
        <BottomSheetOverlay
          containerStyles={styles.containerStyles}
          headingContainerStyles={styles.headingContainerStyles}
          title={'Room Inclusions'}
          toggleModal={() => setRoomInclusionsModalVisibility(!roomInclusionsModalVisibility)}
          visible={roomInclusionsModalVisibility}
        >
          {allInclusions && allInclusions.length > 0 && allInclusions.map(item =>
            <View style={[cStyles.flexRow, marginStyles.mb4]} key={index}>
              <Image style={[styles.tick, { tintColor: item.color }, marginStyles.mt6]} source={TickIcon} />
              <Text style={[styles.inclusion, { color: item.color }]}>{item.description}</Text>
            </View>,
          )}
        </BottomSheetOverlay> : []
      }
    </View>);
  } else {
    return [];
  }
};

function isRoomSelected(selectedRoom, code) {
  const { code: selectedRoomCode } = selectedRoom || {};
  return selectedRoomCode === code;
}

function isRoomPlanSelected(selectedRoom, room) {
  const { ratePlan: selectedRoomRatePlan } = selectedRoom || {};
  const { code: selectedRoomCode } = selectedRoomRatePlan || {};
  const { ratePlan } = room || {};
  const { code } = ratePlan || {};
  return selectedRoomCode === code;
}

const styles = StyleSheet.create({
  roomTypeCard: {
    ...holidayBorderRadius.borderRadius16,
    borderWidth: 1,
    borderColor: holidayColors.grayBorder,
    ...paddingStyles.pv12,
    ...marginStyles.mt16,
  },
  backgroundYellow: {
    backgroundColor: holidayColors.white,
  },
  backgroundBlue: {
    backgroundColor: holidayColors.lightBlueBg,
    borderColor: holidayColors.primaryBlue,
    borderWidth: 1,
  },
  roomTypeRowWrap: {
    ...paddingStyles.pt14,
    borderTopWidth: 1,
    borderColor: holidayColors.grayBorder,
    ...marginStyles.mt12,
    ...paddingStyles.pb6,
  },
  roomTypeRow: {
    flexDirection: 'row',
  },
  roomTypeLeft: {
    width: '70%',
  },
  roomTypeRight: {
    width: '30%',
    justifyContent: 'flex-end',
    alignItems: 'flex-end',
  },
  selectRoomLink: {
    color: holidayColors.primaryBlue,
    ...fontStyles.labelBaseBold,
    textAlign: 'right',
    ...marginStyles.mt10
  },
  iconAdult: {
    width: 9,
    height: 12,
    resizeMode: 'cover',
  },
  iconChild: {
    width: 7,
    height: 9,
    resizeMode: 'cover',
  },
  lastRoomType: {
    borderRightWidth: 0,
  },
  iconArrow: {
    width: 20,
    height: 20,
    resizeMode: 'cover',
  },
  roomTypeHdr: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    ...paddingStyles.ph16,
    ...paddingStyles.pb12,
  },
  roomTypeName: {
    ...fontStyles.labelMediumBlack,
    color: holidayColors.black,
  },
  selectedTag: {
    backgroundColor: holidayColors.red,
    ...holidayBorderRadius.borderRadius16,
    ...paddingStyles.pv4,
    ...paddingStyles.ph6,
    flexDirection: 'row',
    alignItems: 'center',
  },
  rightContent: {
    alignItems: 'center',
    ...paddingStyles.pa10,
  },
  selectedIcon: {
    width: 11,
    height: 11,
    resizeMode: 'cover',
  },
  selectedText: {
    ...fontStyles.labelSmallBold,
    color: holidayColors.white,
  },
  tick: {
    width: 14,
    height: 14,
    ...marginStyles.mr10,
    ...marginStyles.mt2,
    tintColor: holidayColors.gray,
  },
  freeCancellation: {
    ...fontStyles.labelSmallBold,
    ...marginStyles.mt2,
    ...marginStyles.mr16,
    color: holidayColors.green,
  },
  inclusion: {
    ...fontStyles.labelBaseRegular,
    color: holidayColors.gray,
    ...marginStyles.mt4,
    ...marginStyles.mr30,
  },
  bfPersonText: {
    ...fontStyles.labelSmallRegular,
    color: holidayColors.lightGray,
  },
  descText: {
    ...fontStyles.labelSmallRegular,
    color: holidayColors.gray,
    maxWidth:'100%',
  },
  mealText: {
    ...fontStyles.labelBaseBold,
    color: holidayColors.black,
  },
  priceText: {
    ...fontStyles.labelMediumBlack,
    color: holidayColors.black,
  },
  containerStyles: {
    ...paddingStyles.pa16,
    ...paddingStyles.pb40,
  },
  headingContainerStyles: {
    marginBottom: 10
  }
});

export default RoomTypeCard;
