import React, { useState } from 'react';
import { FlatList, Image, StyleSheet, Text, View, ScrollView, Dimensions } from 'react-native';
import cStyles from '@mmt/legacy-commons/Styles/AtomicCss';
import icondrink from '../../images/ic_drink.png';
import iconWifi from '../../images/ic_wifi.png';
import iconArrowDown from '../../images/ic_downArrow.png';
import BottomSheetOverlay from '../.././../../Common/Components/BottomSheetOverlay';
import TickIcon from '../../images/greenTick.png';
import { holidayColors } from '../../../../Styles/holidayColors';
import { fontStyles } from '../../../../Styles/holidayFonts';
import { paddingStyles, marginStyles } from '../../../../Styles/Spacing/index';
import TextButton from 'mobile-holidays-react-native/src/Common/Components/Buttons/TextButton';
import { logPhoenixDetailPDTEvents } from 'mobile-holidays-react-native/src/utils/PhoenixDetailPDTTrackingUtils';
import { PDT_EVENT_TYPES } from 'mobile-holidays-react-native/src/utils/HolidayPDTConstants';
import { SUB_PAGE_NAMES } from '../../../../../src/HolidayConstants';

const Facilities = ({ hotelDetailData, trackOmniture }) => {
  const [modalVisibility, setModalVisibility] = useState(false);
  const { hotel } = hotelDetailData || {};
  const { hotelInformation } = hotel || {};
  const { hotelFacilities } = hotelInformation || {};

  const captureClickEvents = (eventName = '') => {
    logPhoenixDetailPDTEvents({
      actionType: PDT_EVENT_TYPES.buttonClicked,
      value: eventName.replace(/_/g, '|'),
      subPageName:SUB_PAGE_NAMES.ACTIVITY_DETAIL
    })
    trackOmniture(eventName);
  }
  if (hotelFacilities) {
    return (
      <View style={[paddingStyles.ph16, paddingStyles.pb16]}>
        <View style={[marginStyles.mb10, marginStyles.mt6]}>
          <Text style={[styles.facilitiesTitle]}>Facilities</Text>
        </View>
        <View style={styles.facilitiesWrap}>
          <View style={[cStyles.flexRow, cStyles.alignCenter]}>
            <View style={[marginStyles.mr6]}><Image source={iconWifi} style={styles.iconImage} /></View>
            <View><Text style={[styles.freeWifiText]}>Free Wifi</Text></View>
          </View>
          <View style={[cStyles.flexRow, cStyles.alignCenter]}>
            <View style={[marginStyles.mr6]}><Image source={icondrink} style={styles.iconImage} /></View>
            <View><Text style={[styles.freeWifiText]}>Hotel Bar</Text></View>
          </View>
          <TextButton
            buttonText="All Facilities"
            handleClick={() => {
              setModalVisibility(true);
              captureClickEvents('view_facilities');
            }}
            btnTextStyle={styles.facilitiesLink}
            endIcon={iconArrowDown}
          />
          {modalVisibility ? (
            <BottomSheetOverlay
              title={'Facilities'}
              toggleModal={() => setModalVisibility(!modalVisibility)}
              containerStyles={styles.modalOverlay}
              childStyle={{
                flex: 1,
              }}
              bottomSheetStyle={{ flex:1 }}
              visible={modalVisibility}
            >
              {renderModalFacilities(hotelFacilities)}
            </BottomSheetOverlay>
          ) : null}
        </View>
      </View>
    );
  } else {
    return [];
  }
};

const renderModalFacilities = (amenitiesList) => {
  const renderFacilityItem = ({ item, index }) => {
    return (
      <View style={styles.modalFacility}>
        <Image source={TickIcon} style={styles.tickIcon} />
        <Text key={item} style={styles.modalFacilityName}>
          {item}
        </Text>
      </View>
    );
  };
  return (
    <ScrollView>
      <FlatList
        data={amenitiesList}
        numColumns={4}
        horizontal={false}
        renderItem={renderFacilityItem}
        contentContainerStyle={styles.modalFacilitiesList}
      />
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  iconImage: {
    width: 20,
    height: 20,
  },
  facilitiesWrap: {
    backgroundColor: holidayColors.white,
    ...paddingStyles.pv2,
    ...paddingStyles.ph6,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  facilitiesTitle: {
    ...fontStyles.labelLargeBlack,
    color: holidayColors.gray,
  },
  freeWifiText: {
    ...fontStyles.labelBaseRegular,
    color: holidayColors.gray,
  },
  facilitiesLink: {
    color: holidayColors.primaryBlue,
    ...fontStyles.labelSmallBold,
  },
  modalOverlay: {
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    padding: 16,
    paddingBottom: 40,
    maxHeight: Dimensions.get('window').height - 100,
    flex: 1,
  },
  modalFacilitiesList: {
    ...paddingStyles.pt10,
    height: '100%',
    ...marginStyles.mb40,
  },
  modalFacility: {
    width: '25%',
    alignItems: 'center',
    ...marginStyles.mb30,
  },
  tickIcon: {
    width: 16,
    height: 8,
    ...marginStyles.mb6,
  },
  modalFacilityName: {
    color: holidayColors.gray,
    ...fontStyles.labelSmallRegular,
    textAlign: 'center',
  },
});

export default Facilities;
