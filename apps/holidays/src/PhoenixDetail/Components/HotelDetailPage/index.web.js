import React from 'react';
import { ScrollView, StyleSheet, Text, TouchableOpacity, View} from 'react-native';
import HotelGalleryCard from './HotelGallaryCard';
import AboutHotel from './AboutHotel';
import EmptySlide from '../PhoneixDetailOverlay/emptySlide';
import {HOLIDAYS_HOTEL_OVERLAY, HOTEL_DETAIL_FULL_PAGE_TRANSITION_SOURCE} from '../../Utils/PheonixDetailPageConstants';
import { openChangeHotelFromPhoenixPage} from '../../Utils/HolidayDetailUtils';
import { trackPhoenixDetailLocalClickEvent } from '../../Utils/PhoenixDetailTracking';
import { HOLIDAY_ROUTE_KEYS, HolidayNavigation } from '../../../Navigation';
import { fontStyles } from 'apps/holidays/src/Styles/holidayFonts';
import { holidayColors } from 'apps/holidays/src/Styles/holidayColors';
import { holidayBorderRadius } from 'apps/holidays/src/Styles/holidayBorderRadius';
import { paddingStyles, marginStyles } from '../../../Styles/Spacing/index';
import RoomDetails from '@mmt/holidays/src/DetailMimaComponents/Hotel/HotelDetail/RoomDetails';
import RoomHeader from '@mmt/holidays/src/DetailMimaComponents/Hotel/HotelDetail/RoomHeader';
import RoomAmenities from '@mmt/holidays/src/DetailMimaComponents/Hotel/HotelDetail/RoomAmenities';
import PrimaryButton from '../../../Common/Components/Buttons/PrimaryButton';
import {isMobileClient, isRawClient} from '../../../utils/HolidayUtils';
import {Overlay} from '../DetailOverlays/OverlayConstants';
import { logPhoenixDetailPDTEvents } from 'apps/holidays/src/utils/PhoenixDetailPDTTrackingUtils';
import { PDT_EVENT_TYPES } from 'apps/holidays/src/utils/HolidayPDTConstants';

const HotelDetailPage = (props) => {
  const {
    failedHotels,
    item,
    roomDetails,
    packageDetailDTO,
    onComponentChange,
    accessRestriction,
    lastPageName,
    fromPage,
    showOverlay,
    hideOverlays,
    detailData,
  } = props || {};

  if (item && item.data){
  const hotelDetailData = {
    hotel : item.data,
  };
  const {hotel} = hotelDetailData || {};
  const {branch} =  packageDetailDTO || {};
  const { roomTypes, checkInDate, bundled, similarHotels, sellableId } =  hotel || {};
  const changeHotelRestricted = accessRestriction ? accessRestriction.changeHotelRestricted : false;

  let isHotelUnavailable = false;
  for (const element of failedHotels) {
    if (element.sellableId === sellableId) {
      isHotelUnavailable = true;
      break;
    }
  }

    const openHotelListingPage = () => {
      openChangeHotelFromPhoenixPage(
          hotel,
          packageDetailDTO,
          roomDetails,
          onComponentChange,
          lastPageName,
          isRawClient() ? fromPage : HOTEL_DETAIL_FULL_PAGE_TRANSITION_SOURCE.OVERLAY_PAGE,
          isHotelUnavailable ? sellableId : null,
          showOverlay,
          hideOverlays,
          detailData,
      );
    };

  const openHotelDetailPage = (scrollToRoomType = false) => {
    const hotelProps={
      hotel,
      roomDetails,
      fromPage: HOTEL_DETAIL_FULL_PAGE_TRANSITION_SOURCE.OVERLAY_PAGE,
      onComponentChange,
      changeHotelRestricted,
      /*This prop is required to handle a case when user lands from hotel listing to detail page by selecting a different
  hotel. The room type code of new hotel room will be compared with the room type code of preselected hotel in order to
  show update button on price bar. In case when user lands to this page from detail or overlay page, both the props
  hotel and preselected hotel can be same.*/
      preSelectedHotel : hotel,
      scrollToRoomType,
      lastPageName,
      packageDetailDTO,
      bundled,
      hideOverlays,
      showOverlay,
      failedHotelSellableId: isHotelUnavailable ? sellableId : null,
      detailData,
    };
    isMobileClient()
        ? HolidayNavigation.push(HOLIDAY_ROUTE_KEYS.HOTEL_DETAIL, hotelProps)
        : showOverlay(Overlay.HOTEL_DETAIL_PAGE_FULL_SCREEN, hotelProps);
  };

  const trackOmniture = (eventName = '') => {
    logPhoenixDetailPDTEvents({
      actionType: PDT_EVENT_TYPES.buttonClicked,
      value: eventName.replace(/_/g, '|'),
      subPageName: HOLIDAYS_HOTEL_OVERLAY,
    });
    trackPhoenixDetailLocalClickEvent({
      eventName,
      prop1:HOLIDAYS_HOTEL_OVERLAY,
    });
  };

  const handleMoreRoomDetails = () => {
    openHotelDetailPage(true);
    trackOmniture('read_room');
  };

  return (
    <View style={styles.pageWrap}>
      <ScrollView>
        <HotelGalleryCard hotelDetailData={hotelDetailData}
            fromPage={fromPage}
            changeHotelRestricted={changeHotelRestricted}
            openHotelListingPage={openHotelListingPage}
            trackOmniture={trackOmniture}
            packageDetailDTO={packageDetailDTO}
            bundled={similarHotels && similarHotels.length > 0 && bundled}
        />
        <AboutHotel hotelDetailData={hotelDetailData}
            branch={branch}
            fromOverlay={true}
            onOverlayReadMoreClicked={openHotelDetailPage}
            trackOmniture={trackOmniture}
        />
        <View style={styles.blkWrapper}>
          <RoomHeader roomDetails={roomDetails} checkInDate={checkInDate} />
          {!bundled &&  <View style={styles.roomTypeCard}>
            <RoomDetails hotelDetailData={hotelDetailData} />
            <View style={{ padding: 16 }}>
              <RoomAmenities roomData={roomTypes[0]} trackOmniture={trackOmniture} />
              <TouchableOpacity onPress={handleMoreRoomDetails}>
                <Text style={styles.moreRoomDetails}>More about Room</Text>
              </TouchableOpacity>
            </View>
          </View>}
          <View>
            {!bundled && !changeHotelRestricted && (
              <TouchableOpacity
                onPress={() => {
                  openHotelDetailPage(true);
                  trackOmniture('view_rooms');
                }}
              >
                <Text style={[styles.moreOptions]}>MORE ROOM OPTIONS</Text>
              </TouchableOpacity>
            )}
          </View>
        </View>
        <View style={styles.btnWrapper}>
        <PrimaryButton
        buttonText={'SEE MORE'}
        handleClick={() => {
          openHotelDetailPage();
          trackOmniture('see_hotel');
        }}
        />
        </View>
      </ScrollView>
    </View>
  );}
return <EmptySlide/>;
};

/**
 * Return image urls for the room.
 * This image will be displayed in the accordian of the hotel detail page.
 * Empty string is returned if no image URL is found.
 * @param imageDataList
 */


const styles = StyleSheet.create({
  blkWrapper: {
    ...paddingStyles.ph16,
    ...paddingStyles.pt12,
    ...paddingStyles.pb6,
    borderColor: holidayColors.grayBorder,
    borderBottomWidth: 1,
    borderTopWidth: 1,
  },

  btnWrapper: {
    ...paddingStyles.ph16,
    ...marginStyles.mv16,
  },

  pageWrap: {
    flex: 1,
    backgroundColor:holidayColors.white,
  },

  roomTypeCard: {
    ...holidayBorderRadius.borderRadius16,
    backgroundColor: holidayColors.lightBlueBg,
    borderWidth: 1,
    borderColor: holidayColors.grayBorder,
    ...marginStyles.mt6,
  },

  moreRoomDetails: {
    ...fontStyles.labelBaseBold,
    color: holidayColors.primaryBlue,
    ...marginStyles.mt8,
  },

  moreOptions: {
    ...fontStyles.labelSmallBold,
    color: holidayColors.primaryBlue,
    ...marginStyles.mv12,
    ...marginStyles.ml6,
  },

  tickIcon: {
    width: 16,
    height: 8,
    ...marginStyles.mb6,
  },

  dateStyle: {
    ...fontStyles.labelSmallRegular,
    color: holidayColors.gray,
  },

  roomTextStyle: {
    ...fontStyles.labelSmallRegular,
    color: holidayColors.lightGray,
  },

});

export default HotelDetailPage;
