import React from 'react';
import {StyleSheet, Text, TouchableOpacity, View} from 'react-native';
import {connect} from 'react-redux';
import { getPaxCount } from '../../Utils/HolidayDetailUtils';
import {NO_DEPARTURE_CITY} from '../../../HolidayConstants';
import DynamicCoachMark from '@mmt/legacy-commons/Common/Components/CoachMarks/DynamicCoachMark';
import { createDateFromItinerary } from '../../Utils/PhoenixDetailUtils';
import { fontStyles } from 'mobile-holidays-react-native/src/Styles/holidayFonts';
import { holidayColors } from '../../../Styles/holidayColors';
import { marginStyles } from 'mobile-holidays-react-native/src/Styles/Spacing';

const ModifySearch = (props) => {
  const { editTravelDetails, roomDetails, detailData, showNewGallery } =
    props || {};
  const { departureDetail, destinationDetail } = detailData.holidayDetailData || {};
  const { itineraryDetail = {} } = detailData?.packageDetail || {};
  const { departureCity, departureDate } = departureDetail || {};
  const { duration } = destinationDetail || {};
  const dateText = createDateFromItinerary(itineraryDetail);
  const adultCount = getPaxCount(roomDetails, 'noOfAdults');
  const childCount =
    getPaxCount(roomDetails, 'noOfChildrenWB') + getPaxCount(roomDetails, 'noOfChildrenWOB');
  const infantCount = getPaxCount(roomDetails, 'noOfInfants');

  const travellerCount = adultCount + childCount + infantCount;
  const travellerText = travellerCount + (travellerCount > 1 ? ' Travellers' : ' Traveller');

  const modifyContentData = {
    text: 'MODIFY',
    textStyle: styles.newTextStyle,
    seperatorColor: holidayColors.lightGray,
  };
  return (
    <View style={styles.container}>
      <View style={styles.row}>
        {/*Departure city name*/}
        {departureCity && departureCity !== NO_DEPARTURE_CITY && (
          <View style={{ flexDirection: 'row', alignItems: 'center' }}>
            <Text style={[styles.text, modifyContentData.textStyle]}>{departureCity}</Text>
            <View
              style={[styles.separator, { backgroundColor: modifyContentData.seperatorColor }]}
            />
          </View>
        )}

        <Text style={[styles.text, modifyContentData.textStyle]}>{travellerText}</Text>
        <View style={[styles.separator, { backgroundColor: modifyContentData.seperatorColor }]} />
        <Text style={[styles.text, modifyContentData.textStyle]}>{dateText}</Text>
      </View>
      <TouchableOpacity>
        <DynamicCoachMark
          cueStepKey={'editSearch'}
          offsetHeight={showNewGallery ? 25 : 30}
          offsetWidth={showNewGallery ? 15 : 25}
          isSetCustomShape
          shapeObject={{ radius: 40 }}
        >
          <Text
            style={styles.link}
            onPress={(e) => {
              e.preventDefault();
              editTravelDetails();
            }}
          >
            MODIFY
          </Text>
        </DynamicCoachMark>
      </TouchableOpacity>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    ...marginStyles.mh2,
  },
  text: {
    ...fontStyles.labelSmallBold,
    color: holidayColors.black,
  },
  separator: {
    width: 3,
    height: 3,
    borderRadius: 100,
    ...marginStyles.mh6,
  },
  link: {
    color: holidayColors.primaryBlue,
    ...fontStyles.labelSmallBlack,
    marginTop: 1,
  },
  newTextStyle: {
    ...fontStyles.labelSmallBold,
    color: holidayColors.black,
  },
  row: {
    flexDirection: 'row',
    alignItems: 'center',
  },
});

const mapStateToProps = (state) => {
  const {detailData} = state.holidaysDetail || {};
  return {detailData};
};

export default connect(mapStateToProps, null)(ModifySearch);
