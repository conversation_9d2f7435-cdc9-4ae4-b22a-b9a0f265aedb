import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity, Image } from 'react-native';
import getPlatformElevation from '@mmt/legacy-commons/Styles/getPlatformElevation';
import cStyles from '@mmt/legacy-commons/Styles/AtomicCss';
import {getBackIcon} from '../../../Utils/PhoenixDetailUtils';
import { HolidayNavigation } from '../../../../Navigation';

const Header = ({ title, subTitle, navigation }) => {

  if ((!title || !title.length) && (!subTitle || !subTitle.length)) {
    return null;
  }

  const onBackPress = () => {
    HolidayNavigation.pop();
  };

  return (
    <View style={styles.header}>
      <TouchableOpacity onPress={onBackPress} style={styles.backBtn}>
        <Image source={getBackIcon()} style={styles.iconArrowLeft} />
      </TouchableOpacity>
      <View style={cStyles.flex1}>
        {title && title.length ?
          <Text style={[
            cStyles.font16,
            cStyles.boldFont,
            cStyles.defaultText,
          ]}>
            {title}
          </Text> : null}
        {subTitle && subTitle.length ?
          <Text style={[
            cStyles.font12,
            cStyles.blackText,
            cStyles.marginTop5,
            cStyles.lightTextColor,
            cStyles.regularFont,
          ]}>
            {subTitle}
          </Text> : null}
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  header: {
    ...getPlatformElevation(4),
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 15,
    backgroundColor: '#ffffff',
  },
  backBtn: {
    marginRight: 13,
  },
  iconArrowLeft: {
    width: 16,
    height: 16,
    resizeMode: 'cover',
  },
});

export default Header;
