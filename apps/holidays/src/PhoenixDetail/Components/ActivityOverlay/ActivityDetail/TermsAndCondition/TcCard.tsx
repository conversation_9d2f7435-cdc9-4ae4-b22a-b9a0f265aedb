import React, {useState} from 'react';
import { View, StyleSheet, Text, TouchableOpacity, Image } from 'react-native';
import AtomicCss from '@mmt/legacy-commons/Styles/AtomicCss';
import iconDownArrow from '../../../images/ic_downArrow.png';
import iconUpArrow from '../../../images/ic_upArrow.png';
import CommonModal from '../Popups';
import TermsConditionPopup from '../Popups/TermsConditions';

const TcCard = ({tcData}) => {
  const [showCard, setShowCard] = useState(true);
  const toggleCard = () => {setShowCard(!showCard);};
  const [modalVisible, setModalVisible] = useState(false);
  return (
    <>
      <View style={styles.tcCard}>
        <TouchableOpacity onPress={() => toggleCard()}>
          <View style={[AtomicCss.flexRow, AtomicCss.alignCenter, AtomicCss.spaceBetween]}>
            <Text style={[AtomicCss.font16, AtomicCss.azure, AtomicCss.boldFont]}>Terms & Conditions</Text>
            <View><Image source={showCard ? iconUpArrow : iconDownArrow} style={styles.toggleArrow} /></View>
          </View>
        </TouchableOpacity>
        {showCard &&
        <View style={[AtomicCss.marginTop10]}>
          <View style={[AtomicCss.marginTop10]}>
            {tcData.map((item, index) => {
                return (
                  <View key={index} style={[AtomicCss.flexRow, AtomicCss.alignCenter, AtomicCss.marginBottom10]}>
                    <View style={[AtomicCss.marginRight10]}>
                      <Text style={[AtomicCss.boldFont, AtomicCss.defaultText, AtomicCss.font9]}>&#8226;</Text>
                    </View>
                    <View><Text style={[AtomicCss.defaultText, AtomicCss.font12, AtomicCss.regularFont]}>{item}</Text></View>
                  </View>
                );
              }
            )}
          </View>
          <TouchableOpacity onPress={() => setModalVisible(true)}>
            <View style={[AtomicCss.marginTop10]}>
              <Text style={[AtomicCss.azure, AtomicCss.boldFont, AtomicCss.font12, AtomicCss.textUpper]}>Read More</Text>
            </View>
          </TouchableOpacity>
        </View>
        }
      </View>
      <CommonModal
        modalVisible={modalVisible}
        handlePopup={() => setModalVisible(!modalVisible)}
      >
        <TermsConditionPopup data={tcData} handleClose={() => setModalVisible(!modalVisible)} />
      </CommonModal>
    </>
  );
};

const styles = StyleSheet.create({

  toggleArrow: {
    width: 20,
    height: 20,
    resizeMode: 'cover',
  },
  tcCard: {
    borderBottomWidth: 1,
    borderColor: '#e5e5e5',
    paddingBottom: 15,
  },
});

export default TcCard;
