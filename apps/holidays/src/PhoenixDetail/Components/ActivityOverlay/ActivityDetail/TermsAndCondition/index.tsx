import React from 'react';
import {StyleSheet, View} from 'react-native';
import TcCard from './TcCard';
import FaqCard from './FaqCard';
import CancellationPolicyCard from './CancellationPolicyCard';

const TermsAndCondition = (props) => {
  return (
    <View style={styles.toggleCards}>
      <TcCard tcData = {props.termsAndConditions} />
      <FaqCard />
      <CancellationPolicyCard />
    </View>
  );
};

const styles = StyleSheet.create({
  toggleCards: {
    backgroundColor: 'white',
    padding: 15,
    marginBottom: 10,
  },
  toggleArrow: {
    width: 15,
    height: 15,
    resizeMode: 'cover',
  },
});

export default TermsAndCondition;
