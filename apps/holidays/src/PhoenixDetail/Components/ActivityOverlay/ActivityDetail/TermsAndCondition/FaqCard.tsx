import React, {useState} from 'react';
import { View, StyleSheet, Text, TouchableOpacity, Image } from 'react-native';
import AtomicCss from '@mmt/legacy-commons/Styles/AtomicCss';
import iconDownArrow from '../../../images/ic_downArrow.png';
import iconUpArrow from '../../../images/ic_upArrow.png';
import CommonModal from '../Popups';
import FaqModal from '../Popups/faq';

const faqData = [
  {
    que: 'How can I earn more rewards?',
    ans: 'In case of cancellation of bookings made through MyWallet, the refunds if any after deducting cancellation charges will be processed into the MyWallet account only. However, initially made.',
  },
  {
    que: 'How can I earn more rewards and what to do when text is in 2 lines?',
    ans: 'In case of cancellation of bookings made through MyWallet, the refunds if any after deducting cancellation charges will be processed into the MyWallet account only. However, initially made.',
  },
];



const FaqCard = () => {
  const [showCard, setShowCard] = useState(true);
  const toggleCard = () => {setShowCard(!showCard);};

  const [modalVisible, setModalVisible] = useState(false);

  return (
    <>
      <View style={styles.faqCard}>
        <TouchableOpacity onPress={() => toggleCard()}>
          <View style={[AtomicCss.flexRow, AtomicCss.alignCenter, AtomicCss.spaceBetween]}>
            <Text style={[AtomicCss.font16, AtomicCss.azure, AtomicCss.boldFont]}>Frequently Asked Questions</Text>
            <View><Image source={showCard ? iconUpArrow : iconDownArrow} style={styles.toggleArrow} /></View>
          </View>
        </TouchableOpacity>
        {showCard &&
        <View style={[AtomicCss.marginTop10]}>
          <View>
            {faqData.map((item, i) =>
              <View style={styles.faqList} key={i}>
                <View style={[AtomicCss.flexRow]}>
                  <Text  style={[AtomicCss.font14, AtomicCss.lightGreyText, AtomicCss.boldFont]}>Q: </Text>
                  <Text style={[AtomicCss.font14, AtomicCss.blackText, AtomicCss.boldFont]}>{item.que}</Text>
                </View>
                <View style={[AtomicCss.flexRow, AtomicCss.marginTop5, AtomicCss.paddingLeft5]}>
                  <View style={[AtomicCss.marginTop5]}><Text style={[AtomicCss.font9, AtomicCss.defaultText, AtomicCss.boldFont]}>&#8226;</Text></View>
                  <Text style={[AtomicCss.font12, AtomicCss.defaultText, AtomicCss.paddingLeft5]}>{item.ans}</Text>
                </View>
              </View>
            )}
          </View>
          <TouchableOpacity onPress={() => setModalVisible(true)}>
            <View style={[AtomicCss.marginTop10]}>
              <Text style={[AtomicCss.azure, AtomicCss.boldFont, AtomicCss.font12, AtomicCss.textUpper]}>Read More</Text>
            </View>
          </TouchableOpacity>
        </View>
        }
      </View>
      <CommonModal
        modalVisible={modalVisible}
        handlePopup={() => setModalVisible(!modalVisible)}
      >
        <FaqModal data={faqData} handleClose={() => setModalVisible(!modalVisible)} />
      </CommonModal>
    </>
  );
};

const styles = StyleSheet.create({

  toggleArrow: {
    width: 20,
    height: 20,
    resizeMode: 'cover',
  },
  faqCard: {
    borderBottomWidth: 1,
    borderColor: '#e5e5e5',
    paddingVertical: 15,
  },
  faqList: {

    paddingVertical: 10,
    width: '95%',
  },
});

export default FaqCard;
