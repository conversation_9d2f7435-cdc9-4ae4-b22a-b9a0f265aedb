import React, {useState} from 'react';
import { View, StyleSheet, Text, TouchableOpacity, Image } from 'react-native';
import AtomicCss from '@mmt/legacy-commons/Styles/AtomicCss';
import { colors, fonts } from '@mmt/legacy-commons/Styles/globalStyles';
import iconDownArrow from '../../../images/ic_downArrow.png';
import iconUpArrow from '../../../images/ic_upArrow.png';





const CancellationPolicyCard = () => {
  const [showCard, setShowCard] = useState(true);
  const toggleCard = () => {setShowCard(!showCard);};

  return (

    <View style={styles.cancelCard}>
      <TouchableOpacity onPress={() => toggleCard()}>
        <View style={[AtomicCss.flexRow, AtomicCss.alignCenter, AtomicCss.spaceBetween]}>
          <Text style={[AtomicCss.font16, AtomicCss.azure, AtomicCss.boldFont]}>Cancellation Policy</Text>
          <View><Image source={showCard ? iconUpArrow : iconDownArrow} style={styles.toggleArrow} /></View>
        </View>
      </TouchableOpacity>
      {showCard &&
      <View style={[AtomicCss.marginTop10]}>
        <View />
      </View>
      }
    </View>

  );
};

const styles = StyleSheet.create({

  toggleArrow: {
    width: 20,
    height: 20,
    resizeMode: 'cover',
  },
  cancelCard: {

    paddingVertical: 15,
  },
  faqList: {

    paddingVertical: 10,
    width: '95%',
  },
});

export default CancellationPolicyCard;
