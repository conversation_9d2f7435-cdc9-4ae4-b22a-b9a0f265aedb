import React from 'react';
import { View, StyleSheet, Text, Image, TouchableOpacity } from 'react-native';
import iconClose from '../../../../images/ic_cross.webp';
import AtomicCss from '@mmt/legacy-commons/Styles/AtomicCss';



const FaqModal = (props) => {
    const {data, handleClose} = props;
    return (
        <View style={styles.faqModalContainer}>
            <View style={styles.faqHeader}>
                <TouchableOpacity onPress={handleClose}>
                    <View><Image source={iconClose} style={styles.iconClose} /></View>
                </TouchableOpacity>

                <View style={[AtomicCss.justifyCenter, AtomicCss.alignCenter, AtomicCss.flex1]}><Text style={[AtomicCss.font14, AtomicCss.boldFont, AtomicCss.blackText, AtomicCss.textCenter]}>FAQs</Text></View>
            </View>
            <View style={styles.faqContent}>
                {data.map((item, i) =>
                    <View style={styles.faqList} key={i}>
                        <View style={[AtomicCss.flexRow]}>
                            <Text  style={[AtomicCss.font14, AtomicCss.lightGreyText, AtomicCss.boldFont]}>Q: </Text>
                            <Text style={[AtomicCss.font14, AtomicCss.blackText, AtomicCss.boldFont]}>{item.que}</Text>
                        </View>
                        <View style={[AtomicCss.flexRow, AtomicCss.marginTop5, AtomicCss.paddingLeft5]}>
                            <View style={[AtomicCss.marginTop5]}><Text style={[AtomicCss.font9, AtomicCss.defaultText, AtomicCss.boldFont]}>&#8226;</Text></View>
                            <Text style={[AtomicCss.font12, AtomicCss.defaultText, AtomicCss.paddingLeft5]}>{item.ans}</Text>
                        </View>
                    </View>
                )}
            </View>
        </View>
    );
};

const styles = StyleSheet.create({
    faqModalContainer: {
        minHeight: '80%',
        backgroundColor: 'white',
        borderTopLeftRadius: 10,
        borderTopRightRadius: 10,
        width: '100%',
        overflow: 'hidden',
    },
    faqHeader: {
        flexDirection: 'row',
        alignItems: 'center',
        shadowColor: '#000',
        shadowOffset: {
            width: 0,
            height: 1,
        },
        shadowOpacity: 0.20,
        shadowRadius: 1.41,
        elevation: 2,
        backgroundColor: 'white',
        padding:15,

    },
    faqList: {
        paddingVertical: 10,
        width: '90%',
    },
    iconClose: {
        width: 15,
        height: 15,
        resizeMode: 'cover',
    },
    faqContent: {
        padding: 15,
    },
});

export default FaqModal;
