import React from 'react';
import { View, StyleSheet, Text, Image, TouchableOpacity } from 'react-native';
import iconClose from '../../../../images/ic_cross.webp';
import AtomicCss from '@mmt/legacy-commons/Styles/AtomicCss';

const TermsConditionPopup = (props) => {
    const {data, handleClose} = props;
    return (
        <View style={styles.modalContainer}>
            <View style={styles.header}>
                <TouchableOpacity onPress={handleClose}>
                    <View>
                        <Image source={iconClose} style={styles.iconClose} />
                    </View>
                </TouchableOpacity>

                <View style={[AtomicCss.justifyCenter, AtomicCss.alignCenter, AtomicCss.flex1]}><Text style={[AtomicCss.font14, AtomicCss.boldFont, AtomicCss.blackText, AtomicCss.textCenter]}>Term & Condition</Text></View>
            </View>
            <View style={[styles.tcContent]}>
                {data.map((item, i) =>
                    <View key={i} style={[AtomicCss.flexRow, AtomicCss.alignCenter, AtomicCss.marginBottom10]}>
                        <View style={[AtomicCss.marginRight10]}>
                            <Text style={[AtomicCss.boldFont, AtomicCss.defaultText, AtomicCss.font9]}>&#8226;</Text>
                        </View>
                        <View><Text style={[AtomicCss.defaultText, AtomicCss.font12, AtomicCss.regularFont]}>{item}</Text></View>
                    </View>
                )}
            </View>
        </View>
    );
};

const styles = StyleSheet.create({
    modalContainer: {
        minHeight: '80%',
        backgroundColor: 'white',
        borderTopLeftRadius: 10,
        borderTopRightRadius: 10,
        width: '100%',
        overflow: 'hidden',
    },
    header: {
        flexDirection: 'row',
        alignItems: 'center',
        shadowColor: '#000',
        shadowOffset: {
            width: 0,
            height: 1,
        },
        shadowOpacity: 0.20,
        shadowRadius: 1.41,
        elevation: 2,
        backgroundColor: 'white',
        padding:15,

    },
    faqList: {
        paddingVertical: 10,
        width: '90%',
    },
    iconClose: {
        width: 15,
        height: 15,
        resizeMode: 'cover',
    },
    tcContent: {
        padding: 20,
    },
});

export default TermsConditionPopup;
