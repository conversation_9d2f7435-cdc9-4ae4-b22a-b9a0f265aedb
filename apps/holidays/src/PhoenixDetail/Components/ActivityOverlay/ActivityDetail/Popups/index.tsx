import React from 'react';
import { View, StyleSheet, Modal, TouchableOpacity } from 'react-native';


const CommonModal = (props) => {
    const {children, modalVisible, handlePopup} = props;
    return (
        <Modal
            animationType="slide"
            transparent={true}
            visible={modalVisible}
            onRequestClose={handlePopup}
        >
            <TouchableOpacity style={styles.modalTransparent}  onPress={handlePopup} />
            <View style={styles.modalContent}>{children}</View>
        </Modal>
    );
};

const styles = StyleSheet.create({
    modalTransparent: {
        width: '100%',
        height: '100%',
        position: 'absolute',
        left: 0,
        right: 0,
        top: 0,
        bottom: 0,
        backgroundColor: 'rgba(0, 0, 0, 0.5)',
    },
    modalContent: {
        width: '100%',
        justifyContent: 'flex-end',
        height: '100%',
    },
});

export default CommonModal;
