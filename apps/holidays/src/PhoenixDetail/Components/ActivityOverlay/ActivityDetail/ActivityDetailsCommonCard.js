import React from 'react';
import { View, StyleSheet, Text } from 'react-native';
import AtomicCss from '@mmt/legacy-commons/Styles/AtomicCss';
import UserRatingCommon from 'mobile-holidays-react-native/src/Common/Components/UserRatingCommon';
import AboutActivity from './ToggleActivityCards/AboutActivity';
import ToggleActivityCards from './ToggleActivityCards';
import HolidayImageHolder from '../../../../Common/Components/HolidayImageHolder';
import FreeActivityPersuasion from '../FreeActivityPersuasion';
import HolidaySafe from '../../../../Common/Components/CovidSafety/HolidaySafe';
import { HTML_CODES, MONTH_ARR_CAMEL } from '../../../../HolidayConstants';
import { getNewDate } from '@mmt/legacy-commons/Common/utils/DateUtils';
import { fontStyles } from 'mobile-holidays-react-native/src/Styles/holidayFonts';
import { holidayColors } from 'mobile-holidays-react-native/src/Styles/holidayColors';
import { borderRadiusValues, holidayBorderRadius } from 'mobile-holidays-react-native/src/Styles/holidayBorderRadius';
import { paddingStyles, marginStyles } from 'mobile-holidays-react-native/src/Styles/Spacing/index';
import { actionStyle } from '../../DayPlan/dayPlanStyles';
import TextButton from '../../../../Common/Components/Buttons/TextButton';

const  ActivityDetailsCommonCard = ({ activity, onRemovePress, onChangePress, selected, safetyDetails, trackClick,restriction = false }) => {
  let day = null;
  let date = null;
  if (activity.day && activity.date) {
    day = activity.day;
    date = activity.date;
  } else {
    day = activity.activityAvailabilityInfoList?.[0].day;
    date = activity.activityAvailabilityInfoList?.[0].date;
  }
  if (date) {
    const dd = getNewDate(date);
    date = dd.getDate() + ' ' + MONTH_ARR_CAMEL[dd.getMonth()];
  }
  const {metaData = {}, imageDetail, additionalData = {}} = activity;
  const {locked = false, freebie, name = '', userRating = '', cityName = '', duration = ''} = metaData || {};
  const { description  = ''} = additionalData || {};
  const {images = []} = imageDetail || {};

  return (
    <View style={styles.ActivityCardWrap}>
      <View
        style={[
          styles.headingContainer,
          !restriction ? marginStyles.mb6 : {},
        ]}
      >
      <View style={[{alignSelf:'flex-end'}, marginStyles.mb4]}>
          {!locked && selected && !freebie && !restriction &&
            <View style={styles.actionContainer}>
              <TextButton
                buttonText="Remove"
                handleClick={() => {
                  onRemovePress();
                  trackClick('activity_remove');
                }}
                btnTextStyle={actionStyle}
              />
              <View style= {styles.separator} />
              <TextButton
                buttonText="Change"
                handleClick={() => {
                  onChangePress();
                  trackClick('activity_change');
                }}
                btnTextStyle={actionStyle}
              />
            </View>}
      </View>
      {!restriction && (
          <View style={[locked ? styles.cardHdrWrapLocked : styles.cardHdrWrap]}>
            <Text style={styles.cardTitle} numberOfLines={3}>
              {name}
            </Text>
          </View>
        )}
      </View>


      <View style={marginStyles.mb10}>
        {freebie && <FreeActivityPersuasion />}
      </View>
      <View style={AtomicCss.makeRelative}>
        <HolidayImageHolder
          imageUrl={images && images.length ? images?.[0].path : ''}
          style={styles.picImageWrap}
        />
        <View style={styles.ratingContainer}>
          <UserRatingCommon ratingValue = {userRating} containerStyle={styles.ratingTag}/>
        </View>
      </View>
      {safetyDetails && safetyDetails.isSafe && (
        <View style={marginStyles.mb10}>
          <HolidaySafe
            safeData={safetyDetails.safeData}
            style={styles.holidaySafe}
          />
        </View>
      )}
      {!!cityName &&
        <View style={styles.locationContainer}>
            <Text style={styles.itemTitle}>Location: </Text>
            <Text style={styles.itemValue}>{cityName}</Text>
        </View>
      }
      <View style={styles.detailContainer}>
        {!!day && !!date &&
          <View style={[AtomicCss.flexRow, AtomicCss.alignCenter]}>
            <Text style={styles.itemTitle}>{`Day ${day}: `}</Text>
            <Text style={styles.itemValue}>{date}</Text>
          </View>}
        {!!day && !!date && !!duration &&
          <View>
            <Text style={styles.bullet}> {HTML_CODES.BULLET} </Text>
          </View>}
        {!!duration &&
          <View style={[AtomicCss.flexRow, AtomicCss.alignCenter]}>
            <Text style={styles.itemTitle}>Duration: </Text>
            <Text style={styles.itemValue}>{duration}</Text>
          </View>}
      </View>
      <ToggleActivityCards marginLeft = {0} title="About the Activity" trackClick={trackClick}>
        <AboutActivity description={description} trackClick={trackClick} />
      </ToggleActivityCards>
    </View>
  );
};

const styles = StyleSheet.create({
  ActivityCardWrap: {
    ...paddingStyles.pa16,
    backgroundColor: holidayColors.white,
  },
  picImageWrap: {
    width: '100%',
    height: 200,
    ...marginStyles.mb0,
    ...holidayBorderRadius.borderRadius16,
    borderWidth:1,
    borderColor:holidayColors.grayBorder,
  },
  selectedTag: {
    backgroundColor: holidayColors.red,
    ...holidayBorderRadius.borderRadius16,
    ...paddingStyles.pv4,
    ...paddingStyles.ph6,
    flexDirection: 'row',
    alignItems: 'center',
  },
  selectedIcon: {
    width: 11,
    height: 11,
    resizeMode: 'cover',
  },
  headingContainer : {
  },
  cardHdrWrap: {
    flexWrap: 'wrap',
    flexDirection: 'row',
  },
  cardHdrWrapLocked: {
    width: '100%',
    flexWrap: 'wrap',
    flexDirection: 'row',
  },
  cardTitle: {
    ...fontStyles.headingBase,
    color: holidayColors.black,
  },
  ratingContainer: {
    position: 'absolute',
    bottom: 0,
  },
  ratingTag: {
    ...paddingStyles.pa10,
    borderBottomLeftRadius: borderRadiusValues.br16,
    borderTopRightRadius: borderRadiusValues.br16,
    borderTopLeftRadius: 0,
    borderBottomRightRadius: 0,
  },
  holidaySafe: {
    ...marginStyles.mb8,
    ...marginStyles.mt4,
  },
  actionContainer: {
    ...AtomicCss.flexRow,
    ...AtomicCss.alignCenter,
  },
  detailContainer: {
    flexDirection: 'row',
    alignItems:'center',
    borderBottomWidth: 1.5,
    borderBottomColor: holidayColors.grayBorder,
    ...paddingStyles.pb12,
  },
  locationContainer: {
    ...AtomicCss.flexRow,
    ...AtomicCss.alignCenter,
    ...AtomicCss.marginBottom10,
    borderBottomWidth: 1.5,
    borderBottomColor: holidayColors.grayBorder,
    ...paddingStyles.pv12,
  },
  itemTitle: {
    ...fontStyles.labelBaseRegular,
    color: holidayColors.lightGray,
  },
  itemValue: {
    ...fontStyles.labelBaseBold,
    color: holidayColors.black,
  },
  separator: {
    ...marginStyles.mh6,
    backgroundColor: holidayColors.grayBorder,
    width: 1,
    height: 10,
  },
  bullet: {
    ...fontStyles.labelBaseRegular,
    color: holidayColors.lightGray,
    ...marginStyles.mh6,
  },
});

export default ActivityDetailsCommonCard;
