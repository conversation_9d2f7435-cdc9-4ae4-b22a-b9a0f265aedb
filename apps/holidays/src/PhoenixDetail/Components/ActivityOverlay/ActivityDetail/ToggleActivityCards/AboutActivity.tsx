import React, {useState} from 'react';
import { View, StyleSheet, Text } from 'react-native';
import { PHOENIX_ACTIVITY_DETAIL } from '../../../../Utils/PheonixDetailPageConstants';
import { trackLocalClickEvent } from '../../../../Utils/PhoenixDetailTracking';
import { fontStyles } from '../../../../../Styles/holidayFonts';
import { holidayColors } from '../../../../../Styles/holidayColors';
import { marginStyles } from '../../../../../Styles/Spacing';
import TextButton from '../../../../../Common/Components/Buttons/TextButton';
import { logPhoenixDetailPDTEvents } from 'mobile-holidays-react-native/src/utils/PhoenixDetailPDTTrackingUtils';
import { PDT_EVENT_TYPES } from 'mobile-holidays-react-native/src/utils/HolidayPDTConstants';

const AboutActivity = (props: { description?: any; trackClick?: any; }) => {
  let description = props.description;
  const { trackClick = null } = props;
  const [textShown, setTextShown] = useState(false);
  
  const captureClickEvents = (eventName = '')=>{
    logPhoenixDetailPDTEvents({
      actionType: PDT_EVENT_TYPES.buttonClicked,
      value: eventName,
      subPageName: PHOENIX_ACTIVITY_DETAIL
    })
    trackLocalClickEvent(eventName,'',PHOENIX_ACTIVITY_DETAIL);
  }

  const toggleTextShown = () => {
    const temp = textShown;
    setTextShown(!textShown);
    if (!temp) {
        if (trackClick) {
            trackClick('read_details');
        } else {
          captureClickEvents('read_details');
        }
    }
  };
  if (!description) {
    return [];
  }
  const content = textShown ? description : description.slice(0, 150);
  return (
    <View>
      <View style={[marginStyles.mt10]}>
        <Text style={styles.content}>{content}</Text>
      </View>
      {description.length > 150 && 
      <TextButton
        buttonText={textShown ? 'Read Less' : 'Read More'}
        handleClick={() => toggleTextShown()}
        btnTextStyle={styles.readMore}
      />
      }
      {/*<MySafetyCard />*/}
    </View>
  );
};

const styles = StyleSheet.create({
  content: {
    ...fontStyles.labelBaseRegular,
    color: holidayColors.gray,
    lineHeight:19,
  },
  readMore: {
    ...fontStyles.labelBaseBold,
    color: holidayColors.primaryBlue,
    ...marginStyles.mt8
  },
});

export default AboutActivity;
