import React, {useState} from 'react';
import {StyleSheet, Text, TouchableOpacity, View, Image} from 'react-native';
import AtomicCss from '@mmt/legacy-commons/Styles/AtomicCss';
import {fonts} from '@mmt/legacy-commons/Styles/globalStyles';
import { holidayColors } from '../../../../../Styles/holidayColors';
import { fontStyles } from '../../../../../Styles/holidayFonts';
import { paddingStyles, marginStyles } from '../../../../../Styles/Spacing/index';

const CardContent = (props) => {
  const {data, listType, bulletList} = props;
  const [textShown, setTextShown] = useState(false);
  const toggleTextShown = () => setTextShown(!textShown);
  if (!data) {
    return [];
  }
  let dataNew = textShown ? data : data.slice(0,3);
  return (
    <View style={[marginStyles.mt10]}>
      <View style={[marginStyles.mt6]}>
        {dataNew.map((item, index) => {
            return (
              <View key={index} style={styles.listContainer}>
                <View style={[marginStyles.mr10, paddingStyles.pt2]}>
                {bulletList === 'tickmark' ? (
                    <Image source={listType} style={styles.bulletListTick} resizeMode={'contain'} />
                  ) : (
                    <Text style={styles.bulletListDot}>{listType}</Text>
                  )}
                </View>
                <View style={[AtomicCss.flex1, AtomicCss.flexRow, marginStyles.mr10]}>
                  <Text style={[styles.itemText]}>{item}</Text>
                </View>
              </View>
            );
          }
        )}
      </View>
      {(data?.length > 3) && <TouchableOpacity onPress={() => toggleTextShown()}>
        <View style={[AtomicCss.marginTop10]}>
          <Text
            style={[styles.readMore]}>{textShown ? 'Read Less' : 'Read More'}</Text>
        </View>
      </TouchableOpacity>
      }
    </View>
  );
};

const styles = StyleSheet.create({
  listContainer: {
    ...AtomicCss.flexRow,
    ...AtomicCss.alignSelfStart,
    ...marginStyles.mb6,
    ...AtomicCss.flex1,
  },
  readMore: {
    color:holidayColors.primaryBlue,
    ...fontStyles.labelSmallBold,
  },
   bulletListTick: {
    width: 15,
    height: 15,
  },
  bulletListDot: {
    color: holidayColors.red,
    fontSize: 6,
    fontFamily: fonts.bold,
  },
  itemText:{
    ...fontStyles.labelBaseRegular,
    color:holidayColors.gray,
  },
});

export default CardContent;
