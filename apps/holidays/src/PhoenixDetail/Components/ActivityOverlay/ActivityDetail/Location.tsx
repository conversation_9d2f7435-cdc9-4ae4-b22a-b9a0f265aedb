import React from 'react';
import { View, Text, StyleSheet, Image } from 'react-native';
import AtomicCss from '@mmt/legacy-commons/Styles/AtomicCss';
import {getGoogleAPIKeyForAllPlarforms, getStaticMapUriForCoordinatesListV2} from '../../../../utils/HolidayUtils';
import { ACTIVITY_MAP_PARAMS } from '../../../Utils/PheonixDetailPageConstants';
import { isNumber } from 'lodash';
import BasePage from '@mmt/legacy-commons/Common/navigation/BasePage';

export default class Location extends BasePage {
  private googleAPIKey: string;

  constructor ({props}: { props: any }) {
    super(props);
    this.googleAPIKey = '';
  }

  async componentDidMount() {
    this.googleAPIKey = await getGoogleAPIKeyForAllPlarforms();
  }

  render() {
    const {showMap, venueAddress, latitude, longitude} = this.props.venueDetails || {};
    if (venueAddress && showMap) {
      const map = isNumber(latitude) && isNumber(longitude);
      const coordinatesList = [{
        lat: latitude,
        lon: longitude,
      }];

      return (
          <View style={styles.locationWrap}>
            <View style={[AtomicCss.marginBottom10]}><Text style={[AtomicCss.font14, AtomicCss.blackFont, AtomicCss.defaultText]}>Location</Text></View>
            <View style={styles.locContent}>
              <View style={styles.locInnerWrap}>
                <Text style={styles.address}>{venueAddress}</Text>
              </View>
              {map &&
              <View style={styles.locInnerWrap}>
                <Image source={{uri: getStaticMapUriForCoordinatesListV2(coordinatesList, ACTIVITY_MAP_PARAMS, this.googleAPIKey)}} style={styles.mapImage}/>
              </View>}
            </View>
          </View>
      );
    }
    return [];
  }
}

const styles = StyleSheet.create({
  address: {
    fontSize: 10,
    color: '#4a4a4a',
    lineHeight: 10 * 1.7,
  },
  locationWrap: {
    marginBottom: 10,
    backgroundColor: 'white',
    padding: 15,
  },
  locInnerWrap: {
    width: '50%',
    flexWrap: 'wrap',
    flexDirection: 'row',
  },
  mapImage: {
    width: '100%',
    height: 93,
    resizeMode: 'contain',
    backgroundColor: '#fff',
    borderRadius: 3,
  },
  locContent: {
    borderTopWidth: 1,
    borderBottomWidth: 1,
    borderColor: '#e5e5e5',
    paddingVertical: 10,
    marginBottom: 16,
    flexDirection: 'row',
    alignItems: 'center',
    width: '100%',
  },
});
