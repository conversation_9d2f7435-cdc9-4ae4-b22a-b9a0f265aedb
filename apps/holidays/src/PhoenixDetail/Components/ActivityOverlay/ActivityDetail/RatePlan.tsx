import React from 'react';
import {StyleSheet, Text, View} from 'react-native';
import AtomicCss from '@mmt/legacy-commons/Styles/AtomicCss';
import TourGradeCard from './TourGradeCard';
import { getPriceDiff } from '../../../../utils/HolidayUtils';
import { MONTH_ARR_CAMEL } from '../../../../HolidayConstants';
import { checkAndChangePriceToZero } from '../../../Utils/PhoenixDetailUtils';
import { getNewDate } from '@mmt/legacy-commons/Common/utils/DateUtils';
import { fontStyles } from '../../../../Styles/holidayFonts';
import { holidayColors } from '../../../../Styles/holidayColors';
import { paddingStyles, marginStyles } from '../../../../Styles/Spacing/index';

const RatePlan = (props) => {
  let tourGrades = [];
  if (props.activity.activityOptions) {
    tourGrades = props.activity.activityOptions;
  } else {
    const tourGrade = props.activity.tourGrade;
    tourGrade.selected = true;
    tourGrades.push(tourGrade);
  }

  let day = null;
  let date = null;
  if (props.activity.day && props.activity.date) {
    day = props.activity.day;
    date = props.activity.date;
  } else {
    day = props.activity.activityAvailabilityInfoList[0].day;
    date = props.activity.activityAvailabilityInfoList[0].date;
  }
  if (date) {
    const dd = getNewDate(date);
    date = dd.getDate() + ' ' + MONTH_ARR_CAMEL[dd.getMonth()];
  }
  const getPrice = (key) => {
    const {activity, packagePrice} = props;
    const {packagePriceMap = {}, discountedFactor} = activity || {};
    return checkAndChangePriceToZero(getPriceDiff(packagePrice, packagePriceMap[key], discountedFactor));
  };

  const renderTourGrade = (item, index) => {
    return (
      <TourGradeCard
        editable={props.editable}
        key={index}
        activityName={item.name}
        description={item.description}
        recheckKey={item.recheckKey}
        activityTourGrade={item.activityTourGrade}
        selectedReCheckKey={props.reCheckKey}
        updateRecheckKey={props.updateRecheckKey}
        openDetailPage={props.openDetailPage}
        trackClick={props.trackClick}
        getPrice={getPrice}
        showOnlyDefault={props.showOnlyDefault}
      />
    );
  };

  return (
    <View style={styles.ratePlanWrap}>
      <View style={[marginStyles.mb6]}>
        <Text style={styles.heading}>
          Rate Plan - Package types
        </Text>
      </View>
      {!!day && !!date && (
        <View style={[AtomicCss.flexRow, AtomicCss.alignCenter, marginStyles.mb16]}>
          <Text
            style={styles.dayText}
          >
            DAY {day}
          </Text>
          <Text
            style={styles.dateText}
          >{` | ${date}`}</Text>
        </View>
      )}
      <View>{tourGrades.map((item, index) => renderTourGrade(item, index))}</View>
    </View>
  );
};

const styles = StyleSheet.create({
  ratePlanWrap: {
    borderColor: holidayColors.grayBorder,
    borderTopWidth: 1,
    ...paddingStyles.ph16,
    borderBottomWidth: 1,
    ...paddingStyles.pb20,
    ...paddingStyles.pt16,
  },
  heading: {
    ...fontStyles.headingBase,
    color: holidayColors.black,
  },
  dayText: {
    ...fontStyles.labelBaseBold,
    color: holidayColors.lightGray,
  },
  dateText: {
    ...fontStyles.labelBaseBold,
    color: holidayColors.black,
  },

});

export default RatePlan;
