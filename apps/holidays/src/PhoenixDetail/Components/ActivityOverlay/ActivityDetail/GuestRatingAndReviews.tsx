import React from 'react';
import LinearGradient from 'react-native-linear-gradient';
import { View, Text, StyleSheet, TouchableOpacity } from 'react-native';
import AtomicCss from '@mmt/legacy-commons/Styles/AtomicCss';
import { fonts } from '@mmt/legacy-commons/Styles/globalStyles';

const GuestRatingAndReviews = () => {
  return (
    <View style={styles.rnrWrap}>
      <View style={[AtomicCss.marginBottom10]}>
        <Text style={[AtomicCss.font16, AtomicCss.blackFont, AtomicCss.blackText]}>Guest Rating & Review</Text>
      </View>
      <LinearGradient start={{x: 0, y: 0}} end={{x: 1, y: 0}} colors={['#26bd99', '#219393']} style={[styles.ratingTag]}>
        <View><Text style={[styles.ratingNumber]}>4.2</Text></View>
        <View style={AtomicCss.marginLeft5}>
          <View><Text style={[AtomicCss.whiteText, AtomicCss.regularFont, AtomicCss.font11, AtomicCss.textUpper]}>143 Ratings</Text></View>
          <View><Text style={[AtomicCss.whiteText, AtomicCss.regularFont, AtomicCss.font11, AtomicCss.textUpper]}>1214 Reviews</Text></View>
        </View>

      </LinearGradient>
      <View style={styles.reviewBlk}>
        <View style={[AtomicCss.flexRow, AtomicCss.alignCenter, AtomicCss.spaceBetween]}>
          <View>
            <View><Text style={[styles.reviewBlkHdr]}>Best budget stay near beach</Text></View>
            <View style={[AtomicCss.marginTop5]}><Text style={[AtomicCss.font11, AtomicCss.greyText, AtomicCss.boldFont]}>Madhavi Ranjan, Family Traveller 24 Apr, 2017</Text></View>
          </View>
          <View style={styles.ratingSmall}><Text style={[AtomicCss.whiteText, AtomicCss.boldFont, AtomicCss.font14]}>4.2</Text></View>
        </View>
        <View style={[AtomicCss.marginTop10, AtomicCss.flexWrap, AtomicCss.marginBottom10, {width: '100%'} ]}>
          <Text style={[AtomicCss.font12, AtomicCss.defaultText, AtomicCss.regularFont]}>We got upgraded to the villa. The villas are gorgeous. The staff is super nice and tried to help us.</Text>
        </View>
      </View>
      <TouchableOpacity style={[AtomicCss.marginTop10]}>
        <View><Text style={[AtomicCss.font12, AtomicCss.azure, AtomicCss.boldFont]}>18 + MORE REVIEWS</Text></View>
      </TouchableOpacity>
    </View>
  );
};

const styles = StyleSheet.create({
  rnrWrap: {
    backgroundColor: 'white',
    padding: 15,
    marginBottom: 10,
  },
  reviewBlk: {
    borderRadius: 4,
    padding: 10,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.20,
    shadowRadius: 1.41,
    elevation: 2,
    flexWrap: 'wrap',
    marginTop: 10,
  },
  reviewBlkHdr: {
    color: '#197971',
    fontSize: 16,
    fontFamily: fonts.black,
  },
  ratingTag: {
    alignSelf: 'flex-start',
    borderRadius: 4,
    paddingVertical: 4,
    paddingHorizontal: 15,
    flexDirection: 'row',
    alignItems: 'center',
  },
  ratingNumber: {
    fontSize: 36,
    fontFamily: fonts.black,
    color: 'white',
  },
  ratingSmall: {
    backgroundColor: '#22a095',
    borderRadius: 2,
    paddingVertical: 10,
    paddingHorizontal: 8,
  },
});

export default GuestRatingAndReviews;
