import React from 'react';
import { View, Text, StyleSheet} from 'react-native';
import cStyles from '@mmt/legacy-commons/Styles/AtomicCss';
import { fontStyles } from '../../../../Styles/holidayFonts';
import { holidayColors } from '../../../../Styles/holidayColors';
import { borderRadiusValues } from '../../../../Styles/holidayBorderRadius';

const UserRating = (props) => {
  const {rating} = props;
  const getBackgroundColor = () => {
    let color;
    if (rating === 0) {
      color = '';
    } else if (rating >= 1 && rating < 2) {
      color = holidayColors.darkBlue;
    } else if (rating > 2 && rating < 3) {
      color = holidayColors.darkBlue;
    } else if (rating > 3 && rating < 4) {
      color = holidayColors.darkBlue;
    } else if (rating > 4) {
      color = holidayColors.darkBlue;
    }
    return color;
  };
  return (
    <View style={[styles.ratingTag, cStyles.marginRight5, {backgroundColor: getBackgroundColor()}]}>
      <Text style={styles.ratingStyle}>{rating}/</Text>
      <Text style={styles.outOfRatingStyle}>5</Text>
    </View>
  );
};

const styles = StyleSheet.create({
  ratingTag: {
    padding:12,
    paddingHorizontal: 10,
    borderBottomLeftRadius: borderRadiusValues.br16,
    borderTopRightRadius: borderRadiusValues.br16,
    flexDirection: 'row',
    alignItems: 'center',
  },
  ratingStyle: {
    ...fontStyles.labelSmallBlack,
    color: holidayColors.white,
  },
  outOfRatingStyle: {
    ...fontStyles.labelSmallRegular,
    color: holidayColors.white,
  },
});

export default UserRating;
