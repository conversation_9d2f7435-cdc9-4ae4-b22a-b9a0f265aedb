import React from 'react';
import { Image, StyleSheet, Text, View } from 'react-native';
import LinearGradient from 'react-native-linear-gradient';
import iconSelect from '@mmt/legacy-assets/src/holidays/ic_tickPink.webp';
import AtomicCss from '@mmt/legacy-commons/Styles/AtomicCss';
import { holidayColors } from '../../../../Styles/holidayColors';
import { fontStyles } from '../../../../Styles/holidayFonts';
import { borderRadiusValues, holidayBorderRadius } from '../../../../Styles/holidayBorderRadius';
import { paddingStyles, marginStyles } from '../../../../Styles/Spacing/index';

const SelectTag = () => {
  return (
      <LinearGradient
        colors={[holidayColors.primaryBlue, holidayColors.primaryBlue]}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 0 }}
        style={styles.selectTag}
      >
        <View style={[AtomicCss.flexRow, AtomicCss.alignCenter]}>
          <View style={styles.iconSelectWrap}>
            <Image source={iconSelect} style={styles.iconSelect} />
          </View>
          <Text style={[styles.selectedText]}>SELECTED</Text>
        </View>
      </LinearGradient>
  );
};

const styles = StyleSheet.create({
  selectTag: {
    ...holidayBorderRadius.borderRadius16,
    ...paddingStyles.pv6,
    ...paddingStyles.ph10,
    alignSelf: 'flex-start',
    left: 16,
    minWidth: 108,
  },
  iconSelectWrap: {
    width: 15,
    height: 15,
    borderRadius: 20,
    backgroundColor: holidayColors.white,
    justifyContent: 'center',
    alignItems: 'center',
    ...marginStyles.mr10,
  },
  iconSelect: {
    width: 9,
    height: 7,
    resizeMode: 'cover',
    tintColor: holidayColors.primaryBlue,
  },
  selectedText:{
    ...fontStyles.labelSmallBold,
    color:holidayColors.white,
  },
});

export default SelectTag;
