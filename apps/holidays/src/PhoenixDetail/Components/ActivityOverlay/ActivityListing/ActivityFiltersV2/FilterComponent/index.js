import React from "react";
import RangeFilter from './RangeFilter';
import SingleFilterSelection from './SingleFilterSelection';

const FilterComponent = ({ filterData, updateByFilter, appliedFilterData }) => {
  const renderContent = (item) => {
    const { type } = item;
    const commonProps = { ...item, updateByFilter, appliedFilterData };

    switch (type) {
      // case 'range':
      //   return <RangeFilter {...commonProps} />;
      case 'Dropdown_Multiselect':
        return <SingleFilterSelection  {...commonProps} />;
      default:
        return null;
    }
  };

  return filterData.map(renderContent);
};

export default FilterComponent;
