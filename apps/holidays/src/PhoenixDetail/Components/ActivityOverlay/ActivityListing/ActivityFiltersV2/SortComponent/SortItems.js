import React from "react";
import { Image, StyleSheet, Text, TouchableOpacity } from "react-native";
import { holidayColors } from "mobile-holidays-react-native/src/Styles/holidayColors";
import { fontStyles } from "mobile-holidays-react-native/src/Styles/holidayFonts";
import { widthPixel } from "mobile-holidays-react-native/src/Styles/holidayNormaliseSize";
import { marginStyles } from "mobile-holidays-react-native/src/Styles/Spacing";
import { holidayBorderRadius } from "mobile-holidays-react-native/src/Styles/holidayBorderRadius";
import { getImageUrl } from "mobile-holidays-react-native/src/Common/Components/HolidayImageUrls";
import starIcon from '.././../../../../../../src/FilterHotels/FilterHotelList/Images/star.webp';
import rupeeLowIcon from '.././../../../../../../src/FilterHotels/FilterHotelList/Images/ruppeLowIcon.webp';
import rupeeHighIcon from '.././../../../../../../src/FilterHotels/FilterHotelList/Images/ruppeHighIcon.webp';
import { SortByTypes } from "mobile-holidays-react-native/src/PhoenixDetail/Utils/ActivityOverlayUtils";
const IconMap = {
  [SortByTypes.POPULARITY]: starIcon,
  [SortByTypes.PRICE_LOW_TO_HIGH]: rupeeLowIcon,
  [SortByTypes.PRICE_HIGH_TO_LOW]: rupeeHighIcon,
};
const SortItems = (props) => {
    const {item,index,updateBySort,filterData} = props;
    return (
        <TouchableOpacity
        key={index}
        style={[styles.optionContainer, filterData?.sorter[0]?.id===item?.id && styles.optionContainerSelected]}
        activeOpacity={0.5}
        onPress={() => updateBySort(item)}>
        <Image style={styles.icon} source={IconMap[item.type]}/>
        <Text style={[styles.value, false && styles.selectedValue]}>{item.title}</Text>
        <Text style={styles.count}>{item.subTitle}</Text >
      </TouchableOpacity>
    );
}
export default SortItems;
const styles = StyleSheet.create({
    optionContainer: {
      marginEnd: 8,
      width: widthPixel(84),
      height: 80,
      alignItems: 'center',
      justifyContent: 'center',
      ...holidayBorderRadius.borderRadius8,
      backgroundColor: holidayColors.white,
      borderWidth: 1,
      borderColor: holidayColors.grayBorder,
    },
    optionContainerSelected: {
      backgroundColor: holidayColors.lightBlueBg,
      borderColor: holidayColors.primaryBlue,
    },
    selectedValue: {
      color: holidayColors.primaryBlue,
    },
    value: {
      color: holidayColors.gray,
      ...fontStyles.labelSmallBold,
    },
    count: {
      color: holidayColors.gray,
      ...fontStyles.labelSmallRegular,
    },
    icon: {
      ...marginStyles.mb10,
      width: 18,
      height: 18,
      resizeMode: 'contain',
      tintColor: holidayColors.black,
    },
  })
