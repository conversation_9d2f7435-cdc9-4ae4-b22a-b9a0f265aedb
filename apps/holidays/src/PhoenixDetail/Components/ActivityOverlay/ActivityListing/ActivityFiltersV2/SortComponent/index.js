import React from "react";
import { holidayColors } from "mobile-holidays-react-native/src/Styles/holidayColors";
import { fontStyles } from "mobile-holidays-react-native/src/Styles/holidayFonts";
import { marginStyles } from "mobile-holidays-react-native/src/Styles/Spacing";
import { ScrollView, StyleSheet, Text, View } from "react-native";
import { FILTER_FLAT_LIST_ITEM_PADDING } from "mobile-holidays-react-native/src/SearchWidget/Components/PhoenixSearchPage/style";
import SortItems from "./SortItems";
const SortComponent = ({ sortData, updateBySort, filterData }) => (
  <View style={styles.container}>
    <Text style={styles.headingText}>Sort By</Text>
    <ScrollView style={styles.optionsContainer} horizontal showsHorizontalScrollIndicator={false}>
      {sortData.map((item, index) => (
        <SortItems key={index} item={item} updateBySort={updateBySort} filterData={filterData} />
      ))}
    </ScrollView>
  </View>
);
export default SortComponent;
const styles = StyleSheet.create({
  container: {
    paddingVertical: FILTER_FLAT_LIST_ITEM_PADDING.vertical,
  },
  optionsContainer: {
    ...marginStyles.mt12,
  },
  headingText: {
    ...fontStyles.labelBaseBlack,
    color: holidayColors.gray,
    ...marginStyles.mb12,
  },
})
