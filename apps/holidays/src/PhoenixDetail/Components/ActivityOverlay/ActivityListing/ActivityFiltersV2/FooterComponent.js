import React from 'react';
import { marginStyles, paddingStyles } from '../../../../../Styles/Spacing/index';
import { fontStyles } from 'mobile-holidays-react-native/src/Styles/holidayFonts';
import Spinner from '@Frontend_Ui_Lib_App/Spinner';
import { Text, View } from 'react-native';

const FooterComponent = ({ loadMore, activityList = [], noMoreItems = false }) => {
    // Show loading only when actually loading more items and more items are available
    if (loadMore && !noMoreItems) {
        return (
            <View style={{
                alignItems: 'center',
                justifyContent: 'center',
                ...paddingStyles.pv30,
                ...marginStyles.mb20,
            }}>
                <Spinner
                    size={25}
                    strokeWidth={2}
                    progressPercent={90}
                    speed={1.5}
                    color='#008CFF'
                />
                <Text style={{
                    ...fontStyles.labelSmallRegular,
                    color: '#4A4A4A',
                    ...marginStyles.mt8,
                    lineHeight: 12,
                }}>
                    Loading More...
                </Text>
            </View>
        );
    }

    // Show end message when we have items but no more available
    if (activityList.length > 0 && noMoreItems) {
        return (
            <View style={{
                alignItems: 'center',
                ...paddingStyles.pv30,
                ...marginStyles.mb20,
            }}>
                <Text style={{
                    ...fontStyles.labelSmallRegular,
                    color: '#4A4A4A',
                }}>
                    That's all for now
                </Text>
            </View>
        );
    }

    return null;
};

export default FooterComponent;
