import React from 'react';
import MultiSlider from '@ptomasroos/react-native-multi-slider';
import { Platform, StyleSheet, Text, View } from "react-native";
import { getScreenWidth } from "mobile-holidays-react-native/src/utils/HolidayUtils";
import { fontStyles } from 'mobile-holidays-react-native/src/Styles/holidayFonts';
import { holidayColors } from 'mobile-holidays-react-native/src/Styles/holidayColors';
import { marginStyles } from 'mobile-holidays-react-native/src/Styles/Spacing';
import { rupeeFormatter } from '@mmt/legacy-commons/Helpers/currencyUtils';

const RangeFilter = ({ name, filterOptions, updateByFilter, type, appliedFilterData,id }) => {
  const { filterText } = filterOptions[0] || {};
  const priceRange = filterText.split('_').map(Number);
  const selectedFilter = appliedFilterData?.filter?.find(filter => filter.id === id);
  const selectedValues = selectedFilter ? selectedFilter.values[0].split('_').map(Number) : priceRange;
  const updateValue = (value) => {
    updateByFilter(value.join("_"), type,id);
  };
  return (
    <View style={styles.container}>
      <Text style={styles.name}>{name}</Text>
      <View style={styles.headingContainer}>
        <Text style={styles.heading}>
          {`\u20B9 ${rupeeFormatter(selectedValues[0])} - \u20B9 ${rupeeFormatter(selectedValues[1])}`}
        </Text>
      </View>
      <MultiSlider
        values={selectedValues}
        sliderLength={getScreenWidth() - 50}
        trackStyle={styles.trackStyle}
        selectedStyle={styles.selectedStyle}
        onValuesChange={updateValue}
        min={priceRange[0]}
        max={priceRange[1]}
        step={1}
        allowOverlap={false}
        snapped
        markerStyle={styles.markerStyle}
        pressedMarkerStyle={styles.pressedMarkerStyle}
      />
    </View>
  );
};

export default RangeFilter;

const styles = StyleSheet.create({
  container: {
    alignItems: 'center'
  },
  name: {
    ...fontStyles.headingBase,
    color: holidayColors.black,
    alignSelf: 'flex-start'
  },
  headingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    ...marginStyles.mv10,
  },
  heading: {
    flex: 1,
    color: holidayColors.gray,
    ...fontStyles.labelBaseBold
  },
  trackStyle: {
    height: 6,
    backgroundColor: '#e6e6e6',
  },
  selectedStyle: {
    backgroundColor: '#008cff',
  },
  pressedMarkerStyle: {
    height: 30,
    width: 30,
    borderRadius: 30,
    backgroundColor: '#fff',
    elevation: 3,
    borderWidth: 1,
    borderColor: '#ccc',
    ...Platform.select({
      ios: {
        marginTop: 0,
      },
      android: {
        marginTop: 5,
      },
    }),
  },
  markerStyle: {
    height: 30,
    width: 30,
    borderRadius: 30,
    backgroundColor: '#fff',
    elevation: 3,
    borderWidth: 1,
    borderColor: '#ccc',
    ...Platform.select({
      ios: {
        marginTop: 0,
      },
      android: {
        marginTop: 5,
      },
    }),
  }
});
