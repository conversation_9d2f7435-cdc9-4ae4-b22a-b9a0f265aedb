import React, { useEffect, useMemo, useState } from 'react';
import { FlatList, Modal, StyleSheet, View } from 'react-native';
import AtomicCss from '@mmt/legacy-commons/Styles/AtomicCss';
import FilterTag from './FilterTag';
import { createFilterJson, getPackagePrice } from '../../../Utils/ActivityOverlayUtils';
import { getPriceDiff } from '../../../../utils/HolidayUtils';
import { getActivitySafetyDetails } from '../../../Utils/ActivityUtils';
import HolidayDataHolder from '../../../../utils/HolidayDataHolder';
import { HOLIDAYS_ACTIVITY_OVERLAY_LISTING } from '../../../Utils/PheonixDetailPageConstants';
import { trackPhoenixDetailLocalClickEvent } from '../../../Utils/PhoenixDetailTracking';
import { checkAndChangePriceToZero } from '../../../Utils/PhoenixDetailUtils';
import { holidayColors } from '../../../../Styles/holidayColors';
import { paddingStyles } from '../../../../Styles/Spacing/index';
import { initializeMap } from '../ActivityUtils';
import { createStaySeqData, getFilterTagBottomMargin } from '../../ItineraryV2/TravelTidbits/TidBitsUtils';
import ActivityListCardV2 from '../../ItineraryV2/Activity/ActivityListingPage/ActivityListCardV2';
import { connect } from 'react-redux';
import { logPhoenixDetailPDTEvents } from 'mobile-holidays-react-native/src/utils/PhoenixDetailPDTTrackingUtils';
import { PDT_EVENT_TYPES } from 'mobile-holidays-react-native/src/utils/HolidayPDTConstants';
import ActivityFiltersV2 from './ActivityFiltersV2';
import FooterComponent from './ActivityFiltersV2/FooterComponent';
import { isEmpty } from 'lodash';
import TidbitsErrorView from '../../../Components/ItineraryV2/TravelTidbitsV2/TidbitsErrorView';

export const HOLIDAYS_ACTIVITY_INITIAL_LIST = -1;
export const DEFAULT_SELECTED_STAY_SEQUENCE = 1;

const ActivityListingPage = (props) => {
  if (!props.pageData){
    return [];
  }
  const {
    activityReqParams,
    pricingDetail,
    pageData,
    activityType,
    removeActivity,
    selectActivity,
    searchedText,
    selectedActivities,
    openActivityDetailPage,
    selectedActivitiesStripData,
    selectedActivitiesInfoStripState,
    filterListData,
    appliedFilters,
    applyFilter,
    onEndReached,
    clearFilter,
    isLoadMore,
   //childLoader,
    productType,
  } = props || {};
  const { noMoreItems } = pageData || {};
  const filterTagBottomMargin = getFilterTagBottomMargin(selectedActivitiesInfoStripState, selectedActivitiesStripData);
  const staySequences = createStaySeqData(activityReqParams);
  const [selectedStaySequence, setSelectedStaySequence] = useState(staySequences ? staySequences[0].staySequence : DEFAULT_SELECTED_STAY_SEQUENCE);
  const [baseActivities, setBaseActivities] = useState(new Map());
  const [filteredActivities, setFilteredActivities] = useState(new Map());
  const [packagePriceMap, setPackagePriceMap] = useState(new Map());
  const [discountedFactor, setDiscountedFactor] = useState(new Map());
  const [showSortByAndFilters, setShowSortByAndFilters] = useState(false);
  const [filterData, setFilterData] = useState(new Map());
  const [initialActivityList, setInitialActivityList] = useState(HOLIDAYS_ACTIVITY_INITIAL_LIST);

  const packagePrice = useMemo(() => getPackagePrice(pricingDetail), [pricingDetail]);

  // const SHOW_FILTER =  pageData?.listingActivities.length >= MINIMUM_ACTIVITY_COUNT_TO_SHOW_FILTER;
  HolidayDataHolder.getInstance().setCurrentPage('phoenixActivityListing');
  const { day } = props || {};

  useEffect(() => {
    const { listingActivities, packagePriceMap, discountedFactor  ,noMoreItems } = pageData || {};
    const filterData = createFilterJson(listingActivities, null, packagePrice, packagePriceMap, discountedFactor);
    const staySeq = staySequences[0].staySequence;

    setBaseActivities(initializeMap(staySeq, listingActivities || []));
    setPackagePriceMap(initializeMap(staySeq, packagePriceMap || {}));
    setFilterData(initializeMap(staySeq, filterData));
    setDiscountedFactor(initializeMap(staySeq, discountedFactor || {}));
    setFilteredActivities(initializeMap(staySeq, listingActivities || []));

  }, [pageData]);


  const captureClickEvents = ({ eventName = '', suffix = ''}) => {
    const value = eventName + suffix;
    logPhoenixDetailPDTEvents({
      actionType: PDT_EVENT_TYPES.buttonClicked,
      value: value.replace(/_/g, '|'),
      subPageName: HOLIDAYS_ACTIVITY_OVERLAY_LISTING,
    });
    trackPhoenixDetailLocalClickEvent({
      eventName,
      suffix,
      prop1: HOLIDAYS_ACTIVITY_OVERLAY_LISTING,
    });
  };
  const updateActivities = async (add, activityCode, name, recheckKey, acmeType, acmeSubType) => {
    setInitialActivityList(HOLIDAYS_ACTIVITY_INITIAL_LIST);
    const selectedActivity = selectedActivities.find((activity) => activity.code === activityCode);
    if (add) {
      selectActivity(activityCode, recheckKey, name, acmeType, acmeSubType);
      if (!selectedActivity) {
        captureClickEvents({
          eventName: 'add_',
          suffix: activityCode,
        });
      }
    } else {
      removeActivity(activityCode);
      captureClickEvents({
        eventName: 'remove_',
        suffix: activityCode,
      });
    }
  };


  const getActivityPriceDiff = (code, recheckKey) => {

    const PriceMap = packagePriceMap.get(selectedStaySequence);
    const discFactor = discountedFactor.get(selectedStaySequence);
    const activityPrice = PriceMap[recheckKey] || PriceMap[code];
    return checkAndChangePriceToZero(getPriceDiff(packagePrice, activityPrice, discFactor));
  };

  const renderActivityCard = ({ item, index }) => {
    const { metaData, recheckKey } = item;
    const { code  } = metaData || {};
    const price = getActivityPriceDiff(code, recheckKey);
    const safetyDetails = getActivitySafetyDetails(item, props.branch);
    return (
      <View style={[AtomicCss.marginBottom4, AtomicCss.marginTop10]}>
        <ActivityListCardV2
          key={index}
          item={item}
          index={index}
          price={price}
          openActivityDetailPage={openActivityDetailPage}
          updateActivities={updateActivities}
          safetyDetails={safetyDetails}
          activityType={activityType}
        />
      </View>
    );
  };

  const openSortByAndFilter = (prefix) => {
    captureClickEvents({ eventName: prefix });
    setShowSortByAndFilters(true);
  };

const handleSort = () => {  openSortByAndFilter('sorter');};
  const handleFilter = () => {  openSortByAndFilter('filter');};

  const applyClick = (appliedFilterData) => {
    applyFilter(appliedFilterData, activityType);
    setShowSortByAndFilters(false);
  };

  const resetFilter = () => {
    const resetFilterData = {
      sorter: [],
      filter: [],
      searchText:'',
    };
    applyFilter(resetFilterData, activityType);
    setShowSortByAndFilters(false);
  };

const onEndReachedCallback = () => {
  // Simple check - if no more items available or already loading, don't fetch
  if (noMoreItems || isLoadMore) {
    return;
  }

  // Trigger pagination with updated offset
  onEndReached({
    ...appliedFilters,
  }, activityType);
};

  const renderContent = () => {
  const activityList = initialActivityList === HOLIDAYS_ACTIVITY_INITIAL_LIST ? filteredActivities.get(selectedStaySequence) : initialActivityList;
    return (
      <View style={{ flex: 1, backgroundColor: holidayColors.lightGray2 }}>
        {isEmpty(pageData?.listingActivities) ?
        <View style={{flex:1}}>
          <TidbitsErrorView
          isHeaderShown={false}
          productType={productType}
          departureDetail={props?.departureDetail || {}}
          day={day}
          appliedFilters={appliedFilters}
          resetFilters={resetFilter}
          type={activityType}
        />
           </View>
          : <FlatList
            data={activityList}
            renderItem={renderActivityCard}
            style={styles.activityPageInnerWrap}
            contentContainerStyle={{
              paddingBottom: filterTagBottomMargin + 180, // Increased to account for filter tag (70) + its height (50) + pagination loader (60)
              horizontalPadding: 7,
            }}
            keyExtractor={(item, index) => `${item.recheckKey} ${index}`}
            onEndReached={onEndReachedCallback}
            onEndReachedThreshold={0.1}
            ListFooterComponent={()=>{
              return <FooterComponent loadMore={isLoadMore} activityList={activityList} noMoreItems={noMoreItems} />;
            }}
            // Prevent multiple rapid calls
            scrollEventThrottle={16}
          />
         }
        <Modal
          onRequestClose={() => setShowSortByAndFilters(false)}
          animationType="slide"
          transparent={true}
          visible={showSortByAndFilters}
          propagateSwipe={true}
        >
          {showSortByAndFilters && filterListData &&
            <ActivityFiltersV2
              setShowSortByAndFilters={setShowSortByAndFilters}
              filterListData={filterListData}
              appliedFilters={appliedFilters}
              applyFilters={applyFilter}
              activityType={activityType}
              clearFilter={clearFilter}
              applyClick={applyClick}
            />
            }
        </Modal>
       {!isEmpty(filterListData) &&
        <View style={{bottom:filterTagBottomMargin}}>
        <FilterTag
          handleSort={handleSort}
          handleFilter={handleFilter}
        />
        </View>}
      </View>
    );
  };
 /* if(childLoader === VIEW_STATE.LOADING){
   return <TidbitsProgressView  loadingText='Updating Activities...'/>
  }*/
  // if (!activityList){
  //  return null;
  // }

  /*if(childLoader === VIEW_STATE.ERROR){
    return (
      <>
      <TidbitsErrorView
          isHeaderShown={false}
          productType={productType}
          departureDetail={props?.departureDetail || {}}
          day={day}
        />
      </>
    )
  }*/


  return (
    <View style={styles.activityPageWrap}>
      {renderContent()}
    </View>
  );
};

const styles = StyleSheet.create({
  activityPageWrap: {
    flex: 1,
    backgroundColor: holidayColors.white,
  },
  activityPageInnerWrap: {
    ...paddingStyles.ph8,
  },
});

const  mapStateToProps = state => {
  const { selectedActivitiesStripData,  selectedActivitiesInfoStripState} = state.travelTidbitsReducer;
  return { selectedActivitiesStripData, selectedActivitiesInfoStripState };
};

export default connect(mapStateToProps, null)(ActivityListingPage);
