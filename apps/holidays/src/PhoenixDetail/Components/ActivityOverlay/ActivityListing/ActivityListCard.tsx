import React, {useState} from 'react';
import { View, Text, StyleSheet, TouchableOpacity } from 'react-native';

// @ts-ignore
import { colors } from '@mmt/legacy-commons/Styles/globalStyles';
// @ts-ignore
import AtomicCss from '@mmt/legacy-commons/Styles/AtomicCss';
import SelectTag from './SelectTag';
import HolidayImageHolder from '../../../../Common/Components/HolidayImageHolder';
import { ListingActivity, ActivityImage } from '../../../Types/PhoenixActivityApiTypes';
import FreeActivityPersuasion from '../FreeActivityPersuasion';
import { getPriceText } from '../../FlightDetailPage/FlightListing/FlightsUtils';
import { isNumber } from 'lodash';
import HolidaySafe from '../../../../Common/Components/CovidSafety/HolidaySafe';
import { holidayColors } from '../../../../Styles/holidayColors';
import { fontStyles } from '../../../../Styles/holidayFonts';
import { borderRadiusValues, holidayBorderRadius } from '../../../../Styles/holidayBorderRadius';
import { paddingStyles, marginStyles } from '../../../../Styles/Spacing';
import SecondaryButton from '../../../../Common/Components/Buttons/SecondaryButton';
import SelectedTag from '../../../../Common/Components/Tags/SelectedTag';
import DetailListingSelectButton from '../../DetailListingSelectButton';
interface ActivityListCardInterface {
  item: ListingActivity;
  index: number;
  price: number;
  openActivityDetailPage: (code: string, safe: boolean) => void;
  updateActivities: (add: boolean, activityCode: string, name: string) => boolean;
  safetyDetails: any;
}

const ActivityListCard = ({item, index, price, openActivityDetailPage, updateActivities, safetyDetails}: ActivityListCardInterface) => {
  const {metaData, imageDetail, additionalData}: ListingActivity = item;

  const {
    name = '',
    code = '',
    freebie = false,
    selected = false,
    addonCategory = null,
    themes = [],
    availableOptionsCount = 1,
    hasSlot = false,
  } : MetaData = metaData || {};

  const { shortDescription = '', safe = false } : AdditionalData = additionalData || {};

  const images : ActivityImage[] = imageDetail?.images || [];


  const [trunketString, settrunketString] = useState(true);

  const handleMoreTxt = () => {
    settrunketString(!trunketString);
  };

  const onSelectPress = () => {
    if (availableOptionsCount > 1 || hasSlot) {
      openActivityDetailPage(code, safe);
    } else {
      updateActivities(true, code, name);
    }
  };

  const onRemovePress = () => {
    updateActivities(false, code, name);
  };

  return (
    <View>
      <TouchableOpacity activeOpacity={0.5} onPress={() => openActivityDetailPage(code, safe)}>
        <View style={!selected ? [styles.activityListCard] : [styles.selectActivityListCard]}>
          <View style={[styles.picImageWrap]}>
            <HolidayImageHolder
              imageUrl={images && images.length ? images[0].path : ''}
              style={styles.picImage}
            />
            {selected &&
              <View style={styles.selectTagWrap}>
                <SelectedTag />
              </View>}

          </View>
          <View style={styles.listCardContent}>
            <View>
              <Text numberOfLines={1} style={[styles.titleText]}>{name}</Text>
            </View>
            {!!shortDescription &&
              <View style={[marginStyles.mt6]}>
                <Text style={[styles.descText]}>{ (trunketString && shortDescription.length > 150) ? shortDescription.slice(0, 150) + '...' : shortDescription}
                  {(shortDescription.length > 150) && <Text onPress={() => handleMoreTxt()} style={[styles.readMoreText]}>&nbsp;{trunketString ? 'Read More' : 'Read Less'}</Text>}
                </Text>
              </View>}
            <View style={[{flexDirection: 'row'}, marginStyles.mt16]}>
              <View style={[AtomicCss.flexRow, AtomicCss.alignCenter, AtomicCss.flexWrap, {flex: 2}]}>
                {safetyDetails && safetyDetails.isSafe &&
                  <HolidaySafe
                    safeData={safetyDetails.safeData}
                    style={[marginStyles.mb8, marginStyles.mt4]}
                  />}
                {!!addonCategory &&
                  <View style={styles.familyTag}>
                    <Text style={styles.familyTags}>{addonCategory}</Text>
                  </View>}
                {themes && themes.map((item, index) => (
                  <View style={styles.familyTag} key={item}>
                    <Text style={styles.familyTags}>{item}</Text>
                  </View>
                ))}
              </View>
              <View style={[styles.cardRowContent]}>
                {(!selected && (
                  <View style={styles.rightContent}>
                    {isNumber(price) &&
                      <>
                        <View>
                          <Text style={styles.priceText}>{getPriceText(price)}</Text>
                        </View>
                        <View style={marginStyles.mb10}>
                          <Text style={styles.familyTags}>Price/Person</Text>
                        </View>
                      </>}
                    <DetailListingSelectButton onPress={onSelectPress}/>
                  </View>) || (
                    <View style={[styles.rightContent, marginStyles.mt16]}>
                      <TouchableOpacity onPress = {() => onRemovePress()}>
                        <View style={[ marginStyles.mt16]}>
                          <Text style={styles.removeText}>Remove</Text>
                        </View>
                      </TouchableOpacity>
                    </View>)
                )}
              </View>
            </View>
          </View>
        </View>
      </TouchableOpacity>
      {freebie && <FreeActivityPersuasion />}
    </View>
  );
};

const styles = StyleSheet.create({
  activityListCard: {
    backgroundColor: holidayColors.white,
    borderWidth: 1,
    borderColor: holidayColors.grayBorder,
    ...holidayBorderRadius.borderRadius16,
  },
  selectActivityListCard: {
    backgroundColor: holidayColors.lightBlueBg,
    borderWidth: 1,
    borderColor: holidayColors.primaryBlue,
    ...holidayBorderRadius.borderRadius16,
  },
  titleText: {
    ...fontStyles.labelMediumBlack,
    color:holidayColors.black,
  },
  descText: {
    ...fontStyles.labelBaseRegular,
    lineHeight:19,
    color:holidayColors.gray,
  },
  readMoreText:{
    ...fontStyles.labelBaseBold,
    color:holidayColors.primaryBlue,
  },
  familyTags: {
    ...fontStyles.labelSmallRegular,
    color:holidayColors.gray,
  },
  priceText:{
    ...fontStyles.labelLargeBlack,
    color:holidayColors.black,
  },
  removeText:{
    ...fontStyles.labelBaseBold,
    color:holidayColors.primaryBlue,
  },
  viewActivityBtn: {
    ...paddingStyles.pv6,
    ...paddingStyles.ph20,
  },

  selectBtn: {
    backgroundColor: holidayColors.lightBlueBg,
    borderWidth: 1,
    borderColor: holidayColors.primaryBlue,
    borderRadius: 25,
    ...paddingStyles.pv6,
    ...paddingStyles.ph20,
    alignSelf: 'flex-end',
  },
  picImage: {
    width: '100%',
    height: 165,
    resizeMode: 'cover',
    backgroundColor: holidayColors.lightGray2,
    borderTopLeftRadius: borderRadiusValues.br16,
    borderTopRightRadius: borderRadiusValues.br16,
  },
  picImageWrap: {
    backgroundColor: holidayColors.lightGray2,
    width: '100%',
    height: 165,
    borderTopLeftRadius: borderRadiusValues.br16,
    borderTopRightRadius: borderRadiusValues.br16,
    position: 'relative',
  },
  listCardContent: {
    ...paddingStyles.pa16,
  },
  leftContent: {
    width: '70%',
    flexWrap: 'wrap',
  },
  rightContent: {
    width: '100%',
    alignItems: 'flex-end',
  },
  activityType: {
    flexDirection: 'row',
    alignItems: 'center',
    ...paddingStyles.pv8,
    borderBottomWidth: 1,
    borderColor: holidayColors.grayBorder,
  },
  cardRowContent: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
    alignItems: 'flex-end',
    minHeight: 80,
    flex: 1,
  },
  familyTag: {
    backgroundColor: holidayColors.lightGray2,
    ...holidayBorderRadius.borderRadius8,
    ...paddingStyles.pv6,
    ...paddingStyles.ph10,
    alignSelf: 'flex-start',
    ...marginStyles.mr6,
    ...marginStyles.mb6,
  },
  iconSafety: {
    width: 18,
    height: 18,
    resizeMode: 'cover',
    ...marginStyles.mr6,
  },
  safetytag: {
    borderWidth: 1,
    borderColor: holidayColors.primaryBlue,
    ...holidayBorderRadius.borderRadius8,
    backgroundColor : holidayColors.lightBlueBg,
    flexDirection: 'row',
    alignItems: 'center',
    ...paddingStyles.pr6,
   ...marginStyles.ml16,
  },
  selectTagWrap: {
    position: 'absolute',
    left: 16,
    top: 16,
  },
});

export default ActivityListCard;
