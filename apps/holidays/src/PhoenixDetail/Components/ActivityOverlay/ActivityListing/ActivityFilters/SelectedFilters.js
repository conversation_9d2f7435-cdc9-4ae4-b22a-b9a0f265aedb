import React, { useMemo } from 'react';
import { Image, Text, View, StyleSheet, TouchableOpacity } from 'react-native';
import {fonts} from '@mmt/legacy-commons/Styles/globalStyles';
import { FiltersType } from '../../../../Utils/ActivityOverlayUtils';

const SelectedFilters = ({filterData, onCrossPress}) => {
  const selectedFiltersChip = useMemo(() => {
    const data = [];
    filterData.map((filter, i) => {
      if (filter.type === FiltersType.CATEGORY || filter.type === FiltersType.THEMES) {
        filter.data.map((item, j) => {
          if (item.selected) {
            data.push({
              name: item.name,
              index1: i,
              index2: j,
            });
          }
        });
      }
    });
    return data;
  }, [filterData]);

  return selectedFiltersChip && selectedFiltersChip.length > 0 ? (
    <View style={styles.container}>
      {selectedFiltersChip.map((item) => {
        return (
            <Chip
                key={item?.name}
                item={item}
                onCrossPress={onCrossPress}
            />
        );
      })}
    </View>
  ) : [];
};

const Chip = ({item, onCrossPress}) => {
  return (
    <View style={styles.chip}>
      <Text style={styles.chipText}>{item.name}</Text>
      <TouchableOpacity
        onPress={() => onCrossPress(item.index1, item.index2)}
      >
        <Image
          source={require('@mmt/legacy-assets/src/holidays/cross_blue.webp')}
          style={styles.chipCross}
        />
      </TouchableOpacity>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    paddingHorizontal: 16,
    paddingVertical: 16,
    flexDirection: 'row',
    flexWrap: 'wrap',
  },
  chip: {
    flexDirection: 'row',
    paddingLeft: 10,
    paddingRight: 5,
    paddingVertical: 8,
    marginRight: 7,
    marginBottom: 8,
    justifyContent: 'center',
    borderWidth: 1,
    borderColor: '#008cff',
    borderRadius: 4,
  },
  chipText: {
    fontFamily: fonts.bold,
    fontSize: 12,
    letterSpacing: 0,
    color: '#008cff',
    marginRight: 2,
  },
  chipCross: {
    height: 16,
    width: 16,
  },
});

export default SelectedFilters;
