import React from 'react';
import { Text, View, StyleSheet, TouchableOpacity } from 'react-native';
import { fonts } from '@mmt/legacy-commons/Styles/globalStyles';


const CityFilter = ({staySequences, staySeq, updateStaySeq}) => {
  return (
    <View style={styles.container}>
      <Text style={styles.heading}>Select a city</Text>
      <View style={styles.cityListContainer}>
        {staySequences.map((item, index) => {
          return (
              <CityBox
                  key={index}
                  staySeq={staySeq}
                  item={item}
                  updateStaySeq={updateStaySeq}
              />
          );
        })}
      </View>
    </View>
  );
};

const CityBox = ({item, updateStaySeq, staySeq}) => {
  const boxStyle = staySeq === item.staySequence ? styles.selectedBox : styles.box;
  return (
    <TouchableOpacity
      onPress={() => updateStaySeq(item.staySequence)}
      activeOpacity={0.7}
      disabled={staySeq === item.staySequence}
    >
      <View style={[boxStyle, {marginRight: 8, paddingVertical: 10, paddingHorizontal: 19}]}>
        <Text style={styles.boxText}>{item.name}</Text>
      </View>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  container: {
    paddingVertical: 21,
    paddingHorizontal: 16,
  },
  heading: {
    fontFamily: fonts.black,
    fontSize: 14,
    letterSpacing: 0,
    color: '#4a4a4a',
    marginBottom: 12,
  },
  cityListContainer: {
    flexDirection: 'row',
  },
  selectedBox: {
    backgroundColor: '#eaf5ff',
    borderWidth: 1,
    borderRadius: 4,
    borderColor: '#008cff',
  },
  box: {
    backgroundColor: '#ffffff',
    borderRadius: 4,
    shadowColor: 'rgba(0, 0, 0, 0.2)',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.20,
    shadowRadius: 1.41,

    elevation: 2,
  },
  boxText: {
    fontFamily: fonts.bold,
    fontSize: 12,
    letterSpacing: 0,
    color: '#4a4a4a',
  },
});

export default CityFilter;
