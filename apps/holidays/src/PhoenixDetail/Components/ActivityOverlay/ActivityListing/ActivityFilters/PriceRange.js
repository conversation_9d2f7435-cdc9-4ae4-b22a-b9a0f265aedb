import React from 'react';
import RangeSlider
  from '../../../../../FilterHotels/FilterHotelList/PriceRange/PriceRangeList/BudgetFilter/RangeSlider';
import { Text, View, StyleSheet } from 'react-native';
import { fontStyles } from 'mobile-holidays-react-native/src/Styles/holidayFonts';
import { holidayColors } from 'mobile-holidays-react-native/src/Styles/holidayColors';

const PriceRange = ({priceRange, defaultPriceRange, type, index, updateFilters}) => {

  const updateValue = (value) => {
    updateFilters(type, index, value);
  };

  return (
    <View style={styles.container}>
      <Text style={styles.heading}>Price Range</Text>
      <RangeSlider
        fullPriceRange={defaultPriceRange}
        appliedPriceRange={priceRange}
        updateAppliedPriceRangeFilterData={updateValue}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    paddingHorizontal: 16,
    paddingVertical: 16,
  },
  heading: {
    ...fontStyles.labelBaseBlack,
    color: holidayColors.gray,
  },
});

export default PriceRange;


