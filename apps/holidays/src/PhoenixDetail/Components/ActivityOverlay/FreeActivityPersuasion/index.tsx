import React from 'react';
import { View, StyleSheet, Text } from 'react-native';
import { fonts } from '@mmt/legacy-commons/Styles/globalStyles';
import { holidayColors } from '../../../../Styles/holidayColors';
import { fontStyles } from '../../../../Styles/holidayFonts';
import { borderRadiusValues, holidayBorderRadius } from '../../../../Styles/holidayBorderRadius';
import { paddingStyles, marginStyles } from '../../../../Styles/Spacing/index';

const FreeActivityPersuasion = () => {
  return (
    <View style={styles.infoTag}>
        <Text style={styles.infoText}>Hurray! This activity is free with this package.</Text>
    </View>
  );
};

const styles = StyleSheet.create({
  infoTag: {
    backgroundColor: holidayColors.fadedGreen,
    ...holidayBorderRadius.borderRadius8,
    ...paddingStyles.pv8,
    ...paddingStyles.ph10,
    justifyContent: 'center',
    alignItems: 'center',
  },
  infoText: {
    color: holidayColors.green,
    ...fontStyles.labelSmallRegular,
  },
});

export default FreeActivityPersuasion;
