import { createStaySeqData } from '../ItineraryV2/TravelTidbits/TidBitsUtils';
import { cloneDeep } from 'lodash';
import { trackPhoenixDetailLocalClickEvent } from '../../Utils/PhoenixDetailTracking';
import { ACTIVITY_TYPE, HOLIDAYS_ACTIVITY_OVERLAY_LISTING, TAB_AND_ITINERARY_MAP } from '../../Utils/PheonixDetailPageConstants';
import { FiltersType } from '../../Utils/ActivityOverlayUtils';
import { itineraryUnitTypes } from '../../DetailConstants';
import { logPhoenixDetailPDTEvents } from 'mobile-holidays-react-native/src/utils/PhoenixDetailPDTTrackingUtils';
import { PDT_EVENT_TYPES } from 'mobile-holidays-react-native/src/utils/HolidayPDTConstants';
import { SUB_PAGE_NAMES } from 'mobile-holidays-react-native/src/HolidayConstants';

export const createPreSelectedActivitiesMap = (activityReqParams) => {
  const {activityList = []} = activityReqParams;
 const staySequences = createStaySeqData(activityReqParams);
  const newActivitySeqMap = new Map();
  staySequences.forEach((ss) => {
    newActivitySeqMap.set(ss.staySequence, new Map());
  });
  if (activityList) {
    activityList.forEach((activity) => {
      const {metaData, recheckKey, staySequence} = activity;
      const {code, name, availableOptionsCount, hasSlot} = metaData;
      const actObj = {
        activityCode: code,
        recheckKey: recheckKey,
        name: name,
      };
      if (newActivitySeqMap.has(staySequence)) {
        newActivitySeqMap.set(staySequence, newActivitySeqMap.get(staySequence).set(code, actObj));
      } else {
        const activityMap = new Map();
        activityMap.set(code, actObj);
        newActivitySeqMap.set(staySequence, activityMap);
      }
    });
  }
  return newActivitySeqMap;
}


export const addValueToMap = (mapObj, value, selectedStaySequence) => {
  const newMapObj = cloneDeep(mapObj);
  newMapObj.set(selectedStaySequence, value);
  return newMapObj;
};

export const getValueFromMap = (mapObj, selectedStaySequence) => {
  const newMapObj = mapObj.get(selectedStaySequence);
  return cloneDeep(newMapObj);
};

export const initializeMap = (key, value) => new Map().set(key, value);

export const logSortBy = (data, staySeq,activityType) => {
  const selectedItem = data.find(item => item.selected);
  if (selectedItem) {
    captureClickEvents({
      eventName: 'sorter_',
      suffix: `${staySeq}_${selectedItem.title}_${selectedItem.subTitle}`,
      prop1: HOLIDAYS_ACTIVITY_OVERLAY_LISTING,
      activityType
    });
  }
};

export const logFilters = (type, data, staySeq,activityType) => {
  if (data && data.length > 0) {
    const selectedNames = data.filter(item => item.selected).map(item => item.name);

    if (selectedNames.length > 0) {
      const suffix = `${staySeq}_${type}_${selectedNames.join(':')}`;
      captureClickEvents({
        eventName: 'filter_',
        suffix,
        prop1: HOLIDAYS_ACTIVITY_OVERLAY_LISTING,
        activityType
      });
    }
  }
};

export const logPriceFilter = (priceRange, defaultPriceRange, staySeq,activityType) => {
  const isPriceRangeValid = priceRange && defaultPriceRange;
  const isPriceRangeChanged = priceRange?.min !== defaultPriceRange?.min || priceRange?.max !== defaultPriceRange?.max;

  if (isPriceRangeValid && isPriceRangeChanged) {
    captureClickEvents({
      eventName: 'filter_',
      suffix: `${staySeq}_${FiltersType.PRICE_RANGE}_${priceRange.min},${priceRange.max}`,
      prop1: HOLIDAYS_ACTIVITY_OVERLAY_LISTING,
      activityType
    });
  }
};

export const logSortByAndFilters = async (filterData, staySeq ,activityType) => {
  filterData.forEach((item) => {
    switch (item.type) {
      case FiltersType.SORT_BY:
        logSortBy(item.data, staySeq ,activityType);
        break;
      case FiltersType.THEMES:
      case FiltersType.CATEGORY:
        logFilters(item.type, item.data, staySeq,activityType);
        break;
      case FiltersType.PRICE_RANGE:
        logPriceFilter(item.priceRange, item.defaultPriceRange, staySeq,activityType);
        break;
    }
  });
};

const captureClickEvents = ({eventName = '', suffix = '', prop1 = '',activityType=""}) => {
  const value = eventName + suffix;
  logPhoenixDetailPDTEvents({
    actionType: PDT_EVENT_TYPES.buttonClicked,
    value: value.replace(/_/g, '|'),
    subPageName: getSubPageNameListing(activityType),
  })
  trackPhoenixDetailLocalClickEvent({
    eventName,
    suffix,
    prop1,
  });
}

export const logUpdateEvent = (staySequenceNames, selectedActivityTotalPrice) => {
  const val = staySequenceNames.join(':');
  captureClickEvents({
    eventName: 'update_',
    suffix: `${itineraryUnitTypes.ACTIVITY}_${val}`,
    prop1: HOLIDAYS_ACTIVITY_OVERLAY_LISTING,
  });
};

export const logCloseEvent = () => {
  captureClickEvents({ eventName: 'close', prop1: HOLIDAYS_ACTIVITY_OVERLAY_LISTING });
};

export const getSubPageName = (acmeType) => {
  if (acmeType === TAB_AND_ITINERARY_MAP.TRANSFERS || acmeType === ACTIVITY_TYPE.TRANSFERS) {
    return SUB_PAGE_NAMES.TRANSFER_DETAIL
  }
  else if (acmeType === "MEAL" || acmeType === TAB_AND_ITINERARY_MAP.MEALS || acmeType === ACTIVITY_TYPE.MEALS) {
    return SUB_PAGE_NAMES.MEAL_DETAIL
  }
  else if (acmeType === TAB_AND_ITINERARY_MAP.ACTIVITY || acmeType === ACTIVITY_TYPE.ACTIVITY) {
    return SUB_PAGE_NAMES.ACTIVITY_DETAIL
  }
  return SUB_PAGE_NAMES.ACTIVITY_DETAIL
}

export const getSubPageNameListing = (acmeType) => {
  if (acmeType === TAB_AND_ITINERARY_MAP.TRANSFERS || acmeType === ACTIVITY_TYPE.TRANSFERS) {
    return SUB_PAGE_NAMES.TRANSFER_LISTING
  }
  else if (acmeType === "MEAL" || acmeType === TAB_AND_ITINERARY_MAP.MEALS || acmeType === ACTIVITY_TYPE.MEALS) {
    return SUB_PAGE_NAMES.MEAL_LISTING
  }
  else if (acmeType === TAB_AND_ITINERARY_MAP.ACTIVITY || acmeType === ACTIVITY_TYPE.ACTIVITY) {
    return SUB_PAGE_NAMES.ACTIVITY_LISTING
  }
  return SUB_PAGE_NAMES.ACTIVITY_LISTING
}