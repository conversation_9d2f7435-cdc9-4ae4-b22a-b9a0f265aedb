import React, {useState} from 'react';
import {Image, StyleSheet, Text, TouchableOpacity, View} from 'react-native';
import {getBackIcon} from '../../Utils/PhoenixDetailUtils';

const iconLikeBlack = require('@mmt/legacy-assets/src/iconLikeBlack.webp');
const iconLikeFilled = require('@mmt/legacy-assets/src/gray_heart.webp');

interface DarkHeaderProps {
  heading?: string,
  onBackPress: () => void,
  onSharePress: () => void,
  onFavPress: (isFav: boolean) => void,
  isShortListed: boolean
}

const DarkHeader = ({heading, onBackPress, onSharePress, onFavPress, isShortListed}: DarkHeaderProps) => {
  const [isFav, setFav] = useState<boolean>(isShortListed);

  const onFavSelect = () => {
    onFavPress(!isFav);
    setFav(!isFav);
  };
  return (
    <View style={styles.actions}>
      <View style={[styles.row, {flex: 1}]}>
        <TouchableOpacity onPress={() => onBackPress()}>
          <Image
            style={[styles.icon, styles.mr13]}
            source={getBackIcon()}
          />
        </TouchableOpacity>
        {!!heading && <Text numberOfLines={1} style={styles.heading}>{heading}</Text>}
      </View>
      <View style={styles.row}>
        <TouchableOpacity onPress={() => onSharePress()}>
          <Image
            style={[styles.icon, styles.mr26, styles.ml26]}
            source={require('../images/black-share.png')}
          />
        </TouchableOpacity>
        <TouchableOpacity onPress={() => onFavSelect()}>
          <Image
            style={styles.icon}
            source={isShortListed ? iconLikeFilled : iconLikeBlack}
          />
        </TouchableOpacity>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  actions: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 6,
  },
  icon: {
    width: 16,
    height: 16,
  },
  mr13: {marginRight: 13},
  mr26: {marginRight: 26},
  ml26: {marginLeft: 26},
  ml29: {marginLeft: 29},
  heading: {
    fontFamily: 'Lato-Bold',
    fontSize: 16,
    color: '#4a4a4a',
    marginRight: 7,
  },
  row: {flexDirection: 'row', alignItems: 'center'},
});


export default DarkHeader;
