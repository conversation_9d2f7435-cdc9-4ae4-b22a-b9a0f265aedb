import React from 'react';
import { StyleSheet, Text, TouchableOpacity, View } from 'react-native';
import AtomicCss from '@mmt/legacy-commons/Styles/AtomicCss';
import { rupeeFormatterUtils } from '../../../utils/HolidayUtils';
import { isEmpty } from 'lodash';
import { holidayColors } from 'mobile-holidays-react-native/src/Styles/holidayColors';
import { fontStyles } from 'mobile-holidays-react-native/src/Styles/holidayFonts';
import SelectedTag from 'mobile-holidays-react-native/src/Common/Components/Tags/SelectedTag';
import { paddingStyles } from '../../../Styles/Spacing';
import PrimaryButton from '../../../Common/Components/Buttons/PrimaryButton';

const SelectPriceCard = ({
  perPersonPrice = 0,
  priceDiff = 0,
  showUpdateButtonOnHeader = false,
  packageDetailDTO = {},
  durationText = '',
  onUpdateClickedFromHeader = {},
  name = '',
  dateText = '',
  bundled,
  finalPrice,
  addonPrice=0
}) => {
  const finalPriceToShowOnHeader = (finalPrice?.price || (perPersonPrice + priceDiff)) + addonPrice;
  const isPriceDiff = priceDiff > 1 || priceDiff < -1;

  if (bundled) {
    return [];
  }

  return (
    <View style={styles.activityUpdateContainer}>
      <View style={styles.activityUpdateCard}>
        <View style={[styles.leftContent, isPriceDiff ? {} : styles.pb22]}>
          {!isEmpty(name) && (
            <View style={styles.nameContainer}>
              <Text style={styles.name} numberOfLines={3}>{name}</Text>
            </View>
          )}
          <View style={styles.dateDetails}>
            {!isEmpty(dateText) && <Text style={styles.dateText}>{dateText},</Text>}
            {!isEmpty(durationText) && <Text style={styles.dateText}>{' '}{durationText} </Text>}
          </View>

          {isPriceDiff && (
            <View style={AtomicCss.marginTop3}>
              <Text style={styles.priceDiff}>
                {priceDiff >= 0 ? '+' : '-'} ₹ {Math.abs(priceDiff)}
              </Text>
            </View>
          )}
        </View>
        <View style={styles.rightContent}>
          <View>
            <Text style={styles.priceValue}>
              {rupeeFormatterUtils(Math.abs(finalPriceToShowOnHeader))}
            </Text>
            <Text style={styles.perPersonPrice}>Per Person</Text>
          </View>
          <View style={styles.btnContainer}>
            {(showUpdateButtonOnHeader && (
              <PrimaryButton
                buttonText={'UPDATE'}
                handleClick={onUpdateClickedFromHeader}
                btnContainerStyles={styles.updateBtn}
              />
            )) || (
              <SelectedTag />
            )}
          </View>
        </View>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  activityUpdateContainer: {
    backgroundColor: holidayColors.fadedYellow,
    ...paddingStyles.pa16,
  },
  activityUpdateCard: {
    alignItems: 'flex-start',
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  updateCardContWrap: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingBottom: 10,
  },
  leftContent: {
    flex: 2, 
    flexDirection: 'column',
  },
  nameContainer: {
    flexShrink: 0,
  },
  dateDetails: {
    flexDirection: 'row',
    alignItems: 'center',
    flexWrap: 'wrap',
    marginTop: 3,
  },
  name: {
    ...fontStyles.labelBaseBold,
    color: holidayColors.black,
  },
  dateText: {
    ...fontStyles.labelSmallRegular,
    color: holidayColors.gray,
  },
  priceDiff: {
    ...fontStyles.labelSmallBlack,
    color: holidayColors.black,
  },
  rightContent: {
    justifyContent: 'flex-end',
    flexDirection: 'column',
    alignItems: 'flex-end',
    flex: 1,
  },
  priceValue: {
    ...fontStyles.labelMediumBlack,
    color: holidayColors.black,
  },
  perPersonPrice: {
    ...fontStyles.labelSmallRegular,
    color: holidayColors.gray,
    textAlign:'right',
  },
  btnContainer: {
    marginTop: 10,
  },
  updateBtn: {
    ...paddingStyles.pv6,
    ...paddingStyles.ph12,
  },
  selectedTag: {
    borderRadius: 15,
    paddingVertical: 5,
    paddingHorizontal: 5,
    flexDirection: 'row',
    alignItems: 'center',
    marginLeft: 'auto',
  },
  iconContainer: {
    backgroundColor: holidayColors.midLightBlue,
    padding: 3,
    borderRadius: 100,
    alignItems: 'center',
    marginHorizontal: 3,
  },
  selectedIcon: {
    width: 8,
    height: 8,
    resizeMode: 'cover',
    tintColor: holidayColors.white,
  },
  selectedText: {
    ...fontStyles.labelSmallBlack,
    color: holidayColors.white,
  },
  icondownArrow: {
    width: 18,
    height: 18,
    resizeMode: 'cover',
  },
  pb22: {
      paddingBottom: 22,
  },
});

export default SelectPriceCard;
