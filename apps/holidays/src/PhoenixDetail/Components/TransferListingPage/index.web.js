import React, {useEffect, useRef, useState} from 'react';
import {
    BackHandler,
    Dimensions,
    FlatList,
    Modal,
    Platform,
    StatusBar,
    StyleSheet,
    Text,
    TouchableOpacity,
    View,
} from 'react-native';
import CarDetailsCard from './CarDetailsCard';
import SelectPriceCard from '../SelectPriceCard';
import Header from '../Header';
import {connect} from 'react-redux';
import HolidayDetailLoader from '../HolidayDetailLoader';
import FullPageError from '../ReviewRating/components/FullPageError';
import {changeTransferListingNew} from '../../Actions/HolidayDetailActions';
import {
    getCarTransfersListFromMap,
    getDayDateMapForTransfers,
    getTransferData,
    getTransferObject,
    getTransfersListFromMap,
} from '../../Utils/TransferUtils';
import {
    componentImageTypes,
    packageActionComponent,
    packageActions,
} from '../../DetailConstants';
import {isAndroidClient, isMobileClient, isNotNullAndEmptyCollection} from '../../../utils/HolidayUtils';
import {has, isEmpty} from 'lodash';
import ConfirmationPopup from '../ConfirmationPopup/ConfirmationPopup';
import {getPaxCount} from '../../Utils/HolidayDetailUtils';
import {getDateForPriceHeader} from '../../Utils/PhoenixDetailUtils';
import {
    getPageName,
    getSelectedItemNameForBlackStrip,
    getUpdatedBlackStripObject,
} from './TransfersListingUtils';
import { HOTEL_DETAIL_FULL_PAGE_TRANSITION_SOURCE } from '../../Utils/PheonixDetailPageConstants';
import { trackPhoenixDetailLocalClickEvent} from '../../Utils/PhoenixDetailTracking';
import { Overlay } from '../DetailOverlays/OverlayConstants';
import { hideOverlays, showOverlay } from '../DetailOverlays/Redux/DetailOverlaysActions';
import { HOLIDAY_ROUTE_KEYS, HolidayNavigation } from '../../../Navigation';
import SimilarPackagePopup from '../Common/SimilarPackagePopup';
import cab from '@mmt/legacy-assets/src/holidays/cab.webp';
import { holidayColors } from '../../../Styles/holidayColors';
import { holidayNavigationPop, holidayNavigationPush } from '../../Utils/DetailPageNavigationUtils';
import { logPhoenixDetailPDTEvents } from 'apps/holidays/src/utils/PhoenixDetailPDTTrackingUtils';
import { PDT_EVENT_TYPES } from '../../../utils/HolidayPDTConstants';
import useBackHandler from '../../../hooks/useBackHandler';

const VIEW_STATE_NO_NETWORK = 'no_network';
const VIEW_STATE_SUCCESS = 'success';
const VIEW_STATE_ERROR = 'error';

const TransferListingPage = (props) => {
    const {
        item: selectedItem,
        roomDetails,
        packageDetailDTO,
        onComponentChange,
        accessRestriction,
        lastPageName,
        fromPage,
        detailData,
        hideOverlays,
        showOverlay,
      } = props || {};
      const [selectedCabIndex, setSelectedCabIndex] = useState(0);
    const [viewState, setViewState] = useState(VIEW_STATE_SUCCESS);
    const [loading, setLoading] =  useState(true);
    const [transfersList, setTransfersList] = useState([]);
    const [showUpdateButtonOnHeader, setShowUpdateButtonOnHeader] =  useState(false);
    const [confirmDialogVisibility, setConfirmDialogVisibility] = useState(false);
    const [blackStripObject, setBlackStripObject] = useState(null);
    let flatListRef = useRef(null);

    const {day} =  selectedItem || {};
    const {packageDetail} = detailData || {};

    const transferDataMap = getTransferData(packageDetail);
    const dayDateMap = getDayDateMapForTransfers(packageDetail);
    const transferObj = getTransferObject(transferDataMap, dayDateMap)[day];

    const removeTransferRestricted = accessRestriction ? accessRestriction.removeTransferRestricted : false;
    const changeTransferRestricted = accessRestriction ? accessRestriction.changeTransferRestricted : false;

    useEffect(() => {
        loadData(packageDetailDTO, onTransferChange, transferObj, onApiError);
    },[]);


    const backHandlerCallback = React.useCallback(() => {
        onBackPressed();
        return true;
      }, [showUpdateButtonOnHeader]);

    useBackHandler(backHandlerCallback);
    const adultCount = getPaxCount(roomDetails, 'noOfAdults');
    const childCount = getPaxCount(roomDetails, 'noOfChildrenWB') + getPaxCount(roomDetails, 'noOfChildrenWOB');
    const infantCount = getPaxCount(roomDetails, 'noOfInfants');
    const kidsCount = childCount + infantCount;


    const onCloseScreen = ({ navigationFunctionProps = {}, navigationFunction = null } = {}) => {
        holidayNavigationPop({
          overlayKeys: [props.overlayKey],
          hideOverlays,
          navigationFunction,
          navigationFunctionProps,
        });
    };

    const loadData = async (packageDetailDTO, onTransferChange, transferObj, onApiError) => {
        setLoading(true);
        await changeTransferListingNew(packageDetailDTO, onTransferChange, transferObj, onApiError);
        setLoading(false);
    };

    const onApiError = (msg, alert, reload) => {
        if (msg) {
            if (alert) {
                setViewState(VIEW_STATE_ERROR);
            } else {
                setViewState(VIEW_STATE_ERROR);
            }
        }
        if (reload) {
            setViewState(VIEW_STATE_ERROR);
        }
    };

    const getSelectedItem = transfersList => {
        // Handle Add case.
        if (transfersList && selectedCabIndex >= 0 && transfersList[selectedCabIndex]) {
            return transfersList[selectedCabIndex];
            // Handle remove case.
        } else {
           return transfersList[0];
        }
    };

    const getSelectedItemName = transfersList => {
        let item = getSelectedItem(transfersList);
        if (item && has(item, 'commute.vehicleInfo.model')) {
            return item.commute.vehicleInfo.model;
        } else if (item && has(item, 'transferObj.vehicleInfo.model')) {
            return item.transferObj.vehicleInfo.model;
        } else {
            return '';
        }
    };

    const handleSelectCab = (selectedIndex) => {
        setSelectedCabIndex(selectedIndex);
        setShowUpdateButtonOnHeader(getUpdateButtonOnHeaderStatus(selectedIndex));

        // Update black strip data.
        if (transfersList && selectedIndex >= 0 && transfersList[selectedIndex]) {
            updateBlackStripObject(transfersList[selectedIndex], selectedIndex);
            captureClickEvents('select_' + transfersList[selectedIndex].type);
            // Handle remove case.
        } else {
            updateBlackStripObject(transfersList[0], selectedIndex);
            captureClickEvents('select_' + transfersList[0].type);
        }
    };


    // Default selected cab is the the cab in zeroth index.
    const handleRemoveCab = (index) => {
        let item = transfersList[0];
        // When a cab is removed which is not default selected index > 0
        if (index > 0) {
            // Make the the first item as default selected.
            setSelectedCabIndex(0);
            setShowUpdateButtonOnHeader(getUpdateButtonOnHeaderStatus(0));
            // Update BlackStrip object
            updateBlackStripObject(item, index);

        // when some one tries to remove a default selected cab.
        } else {
            setSelectedCabIndex(-1);
            setShowUpdateButtonOnHeader(getUpdateButtonOnHeaderStatus(-1));
            // Update BlackStrip object
            updateBlackStripObject(item, -1);
        }
        captureClickEvents('remove_transfer');
    };

    const updateBlackStripObject = (selectedItem, index) => {
        // Update BlackStrip object
        setBlackStripObject(getUpdatedBlackStripObject(
          day,
          getDateForPriceHeader(selectedItem),
          packageDetailDTO.discountedPrice,
          getUpdateButtonOnHeaderStatus(index),
          getSelectedItemNameForBlackStrip(selectedItem),
          getPriceDiffForHeader(transfersList,selectedItem, index ),
          selectedItem
        ));
    };

    const updateBlackStripObjectFromDetailPage = (blackStripObject, index) => {
        // Update BlackStrip object
        const {showUpdateButtonOnHeader} =  blackStripObject || {};
        setShowUpdateButtonOnHeader(showUpdateButtonOnHeader);
        setSelectedCabIndex(index);
        setBlackStripObject(blackStripObject);
    };

    const getUpdateButtonOnHeaderStatus = index => {
        if (index < 0) {
            return true;
        }

        if (transfersList[index].commute) {
            return selectedItem.data.sellableId !== transfersList[index].commute.sellableId;
        } else if (transfersList[index].transferObj) {
            return selectedItem.data.sellableId !== transfersList[index].transferObj.sellableId;
        } else {
            return false;
        }
    };

    const onUpdatePackageClicked = () => {
        let sellbleId = '';
        // If selectedIndex is negative, handle remove item, default item should be considered for removal.
        // item on zeroth index is the default item.
        const selectedItem = selectedCabIndex >= 0 ? getSelectedItem(transfersList) : transfersList[0];

        if (selectedItem) {
            if (has(selectedItem, 'commute.sellableId')) {
                sellbleId = selectedItem.commute.sellableId;
            }
            if (has(selectedItem, 'transferObj.sellableId')){
                sellbleId = selectedItem.transferObj.sellableId;

            }
        }

        // Break if sellableId is empty
        if (isEmpty(sellbleId)){
            console.log('sellableId is empty');
            return;
        }

        const actionData = {};

        if (selectedItem.commute) {
            actionData.sellableId = selectedItem.transferObj.carItinerary.sellableId;
            actionData.startDay = selectedItem.transferObj.startDay;
            actionData.resultSellableId = selectedItem.commute.sellableId;
            actionData.packageComponent = packageActionComponent.CAR;
            actionData.action = selectedCabIndex < 0 ? packageActions.TOGGLE : packageActions.CHANGE;
        } else {
            actionData.transferSelectionType = selectedCabIndex < 0 ? 'NONE' : selectedItem.type;
            actionData.transferSequence = selectedItem.transferSequence;
            actionData.packageComponent = packageActionComponent.TRANSFER;
            actionData.action = packageActions.CHANGE;
        }

       onComponentChange(actionData, componentImageTypes.TRANSFERS);
        const eventText = confirmDialogVisibility ? 'confirmation_transfer_update_package_' : 'update_transfer_';
        captureClickEvents(eventText + blackStripObject.selectedItem.type + '_' + blackStripObject.priceDiff);
        // If the client is a mobile device
        if (
            fromPage !== HOTEL_DETAIL_FULL_PAGE_TRANSITION_SOURCE.OVERLAY_PAGE &&
            isMobileClient()
          ) {
            onCloseScreen({
              navigationFunctionProps: lastPageName,
              navigationFunction: HolidayNavigation.navigate,
            });
          } else {
            onCloseScreen();
          }
    };

    const onBackPressed = () => {
        if (showUpdateButtonOnHeader) {
            setConfirmDialogVisibility(true);
        } else {
            onCloseScreen();
            captureClickEvents('back');
        }
    };

    const onNotNowClicked = () => {
        onCloseScreen();
    };

    const onShowMeClicked = () => {
        // Hide Dialogue
        setConfirmDialogVisibility(false);
        scrollToIndex(selectedCabIndex);
    };

     const getDiscountedPrice = (actualPrice, discountedFactor) => {
        if (discountedFactor) {
            return -Math.ceil(actualPrice * discountedFactor);
        } else {
            return -actualPrice;
        }
    };

    const captureClickEvents = (eventName = '') => {
        logPhoenixDetailPDTEvents({
            actionType: PDT_EVENT_TYPES.buttonClicked,
            value: eventName.replace(/_/g, '|'),
            subPageName: getPageName(transfersList),
        })
        trackPhoenixDetailLocalClickEvent({eventName, prop1: getPageName(transfersList)});
    };

    const getPriceDiffForHeader = (transfersList, selectedItem, selectedCabIndex) => {
        if (selectedCabIndex < 0) {
            // Show remove price.
            if (selectedItem.commute) {
               // Handle removal case for car itinerary
              const {sellableId} = selectedItem.commute || {};
              const {removedPriceMap, discountedFactor} = selectedItem || {};
                if (removedPriceMap && removedPriceMap[sellableId]) {
                    const actualPrice = packageDetailDTO.price - removedPriceMap[sellableId];
                    return getDiscountedPrice(actualPrice, discountedFactor);
                }
            } else {
                // Handle removal case for transfer.
                const {priceMap, discountedFactor, type} = selectedItem || {};
                const {NONE, PRIVATE, SHARED} = priceMap || {};
                if (type === 'SHARED') {
                    const actualPrice = SHARED - NONE;
                    return getDiscountedPrice(actualPrice, discountedFactor);
                } else {
                    const actualPrice = PRIVATE - NONE;
                    return getDiscountedPrice(actualPrice, discountedFactor);
                }
            }
        } else {
            // Show add price
            if (selectedItem){
                return selectedItem.price;
            }
            return  0;
        }
    };

    const scrollToIndex = (index) => {
        if (index >= 0) {
            flatListRef.scrollToIndex({animated: true, index});
        }
    };

    const renderHeader = () => (
        <Header
            adultCount={adultCount}
            kidsCount={kidsCount}
            cityName={'Add Transfer'}
            onBackPress={() => onBackPressed()}
        />
    );

    const renderNoNetwork = () => (
        <FullPageError
            title="Uh oh!"
            subTitle="No Internet Connection"
            suggestion="Try with active internet connection"
            onRefreshPressed={() => loadData(packageDetailDTO, onTransferChange, transferObj, onApiError)}
            renderStickyHeader={renderHeader}
            onBackPressed={this.onBackPressed}
        />
    );

    const renderError = () => (
        <FullPageError
            title="Uh oh!"
            subTitle="Something went wrong"
            suggestion="Try again"
            onRefreshPressed={onCloseScreen}
            renderStickyHeader={renderHeader}
            onBackPressed={onBackPressed}
        />
    );

    const renderProgressView = () => (
        <View style={styles.loader}>
            <HolidayDetailLoader
                openingSavedPackage
                showDateText={false}
                changeAction
                loadingText="Loading Transfer packages..."
            />
            </View>
    );

    const openTransferDetailPage = (item, index, day, lastPageName) => {
        const transferDetailProps = {
            item,
            index,
            packageDetailDTO,
            roomDetails,
            onComponentChange,
            accessRestriction,
            day,
            lastPageName,
            blackStripObject,
            updateBlackStripObjectFromDetailPage,
            selectedCabIndex,
            fromPage: 'TRANSFERS_LISTING',
        };

        holidayNavigationPush({
            pageKey: HOLIDAY_ROUTE_KEYS.TRANSFER_DETAIL,
            overlayKey: Overlay.TRANSFER_DETAIL_PAGE,
            props: transferDetailProps,
            hideOverlays,
            showOverlay,
        });
    };

    const onTransferChange = (transfersResponse, transferObj) => {
        const transferDataForSeq = {};
        transferDataForSeq.transferObj = transferObj;
        transferDataForSeq.packagePrice = packageDetailDTO.price;

        const {carItinerary} = transferObj || {};

        if (carItinerary) {
            transferDataForSeq.transferData = transfersResponse.carListingData;
            transferDataForSeq.discountedFactor = transfersResponse.carListingData.discountedFactor;
        } else {
            const transferDataList =
              transfersResponse.transfersListingData.airportTransferChangeList
                .filter(changeItem => changeItem.transferSequence === transferObj.transferSequence);
            if (isNotNullAndEmptyCollection(transferDataList)) {
                transferDataForSeq.transferData = transferDataList[0];
            }
            transferDataForSeq.discountedFactor = transfersResponse.transfersListingData.discountedFactor;
        }

        if (transferDataForSeq) {
            let transfersList = [];
            if (carItinerary) {
                transfersList = getCarTransfersListFromMap(transferDataForSeq);
            } else {
                transfersList = getTransfersListFromMap(transferDataForSeq);
            }

            setTransfersList(transfersList);

            const selectedItem = selectedCabIndex >= 0 ? getSelectedItem(transfersList) : transfersList[0];
            setBlackStripObject(getUpdatedBlackStripObject(
              day,
              getDateForPriceHeader(getSelectedItem(transfersList)),
              packageDetailDTO.discountedPrice,
              showUpdateButtonOnHeader,
              getSelectedItemName(transfersList),
              getPriceDiffForHeader(transfersList, selectedItem, selectedCabIndex),
              selectedItem
              )
            );
        }
        //this.trackLocalClickEvent(PDTConstants.CHANGE_TRANSFER, '');
    };

    const renderListItem = (item, index,length) => {
        return <TouchableOpacity onPress={() => openTransferDetailPage(item, index, selectedItem.day, lastPageName)}>
        <CarDetailsCard
            item={item}
            index={index}
            handleSelectCab={handleSelectCab}
            handleRemoveCab={handleRemoveCab}
            selectCab={selectedCabIndex}
            selectedItem={selectedItem}
            removeTransferRestricted={removeTransferRestricted}
        />
       {length === 1 && index === selectedCabIndex && <SimilarPackagePopup name={'Transfers'} icon={cab}/>}
        </TouchableOpacity>;
    };

    const renderListSeparator = () => (
        <View style={styles.separator}/>
    );

    const renderContent = () => {
        const {durationText,dateText, perPersonPrice, name, priceDiff } = blackStripObject || {};
        const filterTransfersList = transfersList?.filter((el)=>!isEmpty(el?.transferObj));
        return (
          <View style={styles.pageWrap}>
              <ConfirmationPopup
                confirmDialogVisibility={confirmDialogVisibility}
                onUpdatePackageClickFromPopup={onUpdatePackageClicked}
                onNotNowClicked={onNotNowClicked}
                onShowMeClicked={onShowMeClicked}/>

            {renderHeader()}

            <SelectPriceCard
                durationText={durationText}
                dateText={dateText}
                perPersonPrice={perPersonPrice}
                showUpdateButtonOnHeader={showUpdateButtonOnHeader}
                name={name}
                onUpdateClickedFromHeader={onUpdatePackageClicked}
                priceDiff={priceDiff}
            />

            <Text
                style={{
                    margin: 16,
                    fontFamily: 'Lato-Bold',
                    color: '#000000',
                    fontSize: 14,
                }}>Changes would be reflected to whole itinerary</Text>
            <FlatList
                style={styles.list}
                data={filterTransfersList}
                ref={(ref) => { flatListRef = ref; }}
                keyExtractor={(item, index) => index.toString()}
                ItemSeparatorComponent={renderListSeparator}
                renderItem={({item, index}) => renderListItem(item, index,filterTransfersList.length)}
              />
              <Text styles={{marginTop:10}}/>
          </View>
        );
    };

    return (
        <View style={styles.container}>
            {loading && renderProgressView()}
            {viewState === VIEW_STATE_NO_NETWORK && renderNoNetwork()}
            {viewState === VIEW_STATE_ERROR && renderError()}
            {viewState === VIEW_STATE_SUCCESS && renderContent()}
        </View>
    );
};


const styles = StyleSheet.create({
    pageWrap: {
        flex: 1,
        backgroundColor: holidayColors.white,
    },
    innerDtls: {
        paddingVertical: 25,
        paddingHorizontal: 16,
    },
    tabsWrapper: {
        paddingHorizontal: 10,
        paddingVertical: 2,
        flexDirection: 'row',
    },
    container: {
        flex: 1,
        backgroundColor: holidayColors.white,
    },
    list: {
        paddingLeft: 16,
        paddingRight: 16,
    },
    separator: {
        width: '100%',
        height: 16,
    },
    loader:{
        height:'100%',
        width:'100%'
    }
});


const mapDispatchToProps = dispatch => ({
    showOverlay: (key, data) => dispatch(showOverlay(key, data)),
    hideOverlays: (keys) => dispatch(hideOverlays(keys)),
});

export default connect(null, mapDispatchToProps)(TransferListingPage);
