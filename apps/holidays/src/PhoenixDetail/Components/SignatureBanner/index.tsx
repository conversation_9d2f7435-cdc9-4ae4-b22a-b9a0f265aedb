import React from 'react';
import {Image, StyleSheet, Text, TouchableOpacity, View} from 'react-native';
import LinearGradient from 'react-native-linear-gradient';
import {GenreDetail} from '../../Types/PackageDetailApiTypes';
import {convertUrlToHttps} from '../../../utils/HolidayNetworkUtils';

interface SignatureBannerProps {
  showStory: boolean;
  genreDetail: GenreDetail;
  toggleStoryPage: () => void,
}

const SignatureBanner = ({showStory, genreDetail, toggleStoryPage}: SignatureBannerProps) => {
  const {images, genreType = ''} = genreDetail?.[0] ?? {};
  const genreImageURL = images?.ANDROID;
  const isMMTSignature = genreType === 'MMT Signature';

  const labelImage = genreImageURL
      ? {uri: convertUrlToHttps(genreImageURL)}
      : require('../images/signature-label.png');

  const renderLabel = isMMTSignature && (
      <Image style={styles.label} source={labelImage} />
  );

  const renderStoryButton = showStory && (
      <TouchableOpacity style={styles.btn} onPress={toggleStoryPage}>
        <Text style={styles.text}>VIEW STORY</Text>
        <Image style={styles.image} source={require('../images/forward.png')} />
      </TouchableOpacity>
  );

  if (isMMTSignature || showStory) {
    return (
        <LinearGradient
            start={{x: 0.0, y: 0.0}}
            end={{x: 1.0, y: 0.0}}
            colors={['#a30768', '#ee4b60']}
            style={styles.container}
        >
          {renderLabel}
          {renderStoryButton}
        </LinearGradient>
    );
  } else {
    return [];
  }
};

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 3,
  },
  label: {
    width: 95,
    height: 20,
  },
  text: {
    color: '#fff',
    marginRight: 10,
    fontSize: 12,
    fontWeight: 'bold',
  },
  image: {
    width: 18,
    height: 14,
  },
  btn: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 8,
  },
});

export default SignatureBanner;
