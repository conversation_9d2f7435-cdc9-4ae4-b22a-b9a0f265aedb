import React from 'react';
import { FlatList, StyleSheet, Text, BackHandler, View, ActivityIndicator, StatusBar, Platform } from 'react-native';
import LinearGradient from 'react-native-linear-gradient';
import { has } from 'lodash';
import FullPageError from './components/FullPageError';
import HolidayDetailLoader from '../HolidayDetailLoader';
import AtomicCss from '@mmt/legacy-commons/Styles/AtomicCss';
import { HARDWARE_BACK_PRESS } from '../../../SearchWidget/SearchWidgetConstants';
import ReviewCard from './components/ReviewCard';
import { fetchHotelReviews } from '../../../utils/HolidayNetworkUtils';
import { isNetworkAvailable } from '@mmt/legacy-commons/Common/utils/AppUtils';
import { HolidayNavigation } from '../../../Navigation';
import PageHeader from 'mobile-holidays-react-native/src/Common/Components/PageHeader';
import { paddingStyles } from 'mobile-holidays-react-native/src/Styles/Spacing';
import { fontStyles } from 'mobile-holidays-react-native/src/Styles/holidayFonts';
import { holidayColors } from 'mobile-holidays-react-native/src/Styles/holidayColors';
import { marginStyles } from '../../../Styles/Spacing';
import {isAndroidClient, isMobileClient} from "../../../utils/HolidayUtils";
import Spinner from '@Frontend_Ui_Lib_App/Spinner';
import withBackHandler from '../../../hooks/withBackHandler';
const VIEW_STATE_LOADING = 'loading';
const VIEW_STATE_NO_NETWORK = 'no_network';
const VIEW_STATE_SUCCESS = 'success';
const VIEW_STATE_ERROR = 'error';
 class HotelReviewsContainer extends React.Component {

  static navigationOptions = {
    header: null,
  }

  constructor(props) {
    super(props);
    this.state = {
      listData: [],
      viewState: VIEW_STATE_LOADING,
      hasMore: false,
      ratingBarItems: this.createRatingBarItems(),
    };
  }
  componentDidMount() {
    this.fetchReviews();
  }
  onBackClick = ()=> {
    return this.onBackPressed();
}

   getBackgroundColorArray = rating => {
    if (rating < 3) {
      return  ['#ef456c', '#ef0e42'];
    } else if (rating >= 3 && rating <= 3.7) {
      return ['#fac056', '#FFA500'];
    } else {
      return ['#49a099', '#23A095'];
    }
  };

  createRatingBarItems = () => {
    const { hotelDetailsData } = this.props;
    const ratingBarItems = [];
    if (!has(hotelDetailsData, 'hotel.mmtRatingInfo')) {
      return ratingBarItems;
    }
    const { locationRating, foodRating, hospitalityRating, valueRating, facilitiesRating, roomsRating, cleanlinessRating } = hotelDetailsData.hotel.mmtRatingInfo;
    ratingBarItems.push({
      key: 'Location',
      value: locationRating || 0,
    });
    ratingBarItems.push({
      key: 'Food',
      value: foodRating || 0,
    });
    ratingBarItems.push({
      key: 'Staff',
      value: hospitalityRating || 0,
    });
    ratingBarItems.push({
      key: 'Value',
      value: valueRating || 0,
    });
    ratingBarItems.push({
      key: 'Facilities',
      value: facilitiesRating || 0,
    });
    ratingBarItems.push({
      key: 'Room',
      value: roomsRating || 0,
    });
    ratingBarItems.push({
      key: 'Cleaning',
      value: cleanlinessRating || 0,
    });
    ratingBarItems.sort((a, b) => b.value - a.value);
    return ratingBarItems.slice(0, 3);
  }
  fetchReviews = async () => {
    const hasNetwork = await isNetworkAvailable();
    if (!hasNetwork) {
      this.setState({
        viewState: VIEW_STATE_NO_NETWORK,
      });
      return;
    }
    const { hotelDetailsData } = this.props;
    const { hotelCode = '', ratingType = '' } = hotelDetailsData.hotel;
    const response = await fetchHotelReviews({
      hotelCode,
      offset: 0,
      ratingType,
    });
    if (response && response.success) {
      let { listData, hasMore } = this.state;
      if (response.userReviewDetails && response.userReviewDetails.length) {
        listData = response.userReviewDetails;
        hasMore = true;
      } else {
        listData = [];
        hasMore = false;
      }
      this.setState({
        viewState: VIEW_STATE_SUCCESS,
        listData,
        hasMore,
      });
    } else {
      this.setState({
        viewState: VIEW_STATE_ERROR,
      });
    }
  };
  handleLoadMore = async () => {
    if (!this.state.hasMore) {
      return;
    }
    const { hotelDetailsData } = this.props;
    let { listData, hasMore } = this.state;
    const response = await fetchHotelReviews({
      hotelCode: hotelDetailsData.hotel.hotelCode,
      offset: listData.length,
      ratingType: hotelDetailsData?.hotel?.ratingType,
    });
    if (response && response.success) {
      if (response.userReviewDetails && response.userReviewDetails.length) {
        listData = listData.concat(response.userReviewDetails);
        hasMore = true;
      } else {
        hasMore = false;
      }
      this.setState({
        listData,
        hasMore,
      });
    }
  }
  onBackPressed = () => {
    isMobileClient()
        ? HolidayNavigation.pop()
        : this.props.back();
    return true;
  };
  onRefreshPressed = () => {
    this.setState({
      viewState: VIEW_STATE_LOADING,
    }, () => {
      this.fetchReviews();
    });
  };
  getSubTitle = () => {
    const { hotelDetailsData } = this.props;
    if (has(hotelDetailsData, 'hotel.mmtRatingInfo.reviewCount')) {
      return `Showing  ${hotelDetailsData.hotel.mmtRatingInfo.reviewCount} Results`;
    }
    return hotelDetailsData.hotel.name;
  };
  render() {
    return (
      <View style={styles.container}>
        {this.state.viewState === VIEW_STATE_LOADING && this.renderProgressView()}
        {this.state.viewState === VIEW_STATE_NO_NETWORK && this.renderNoNetwork()}
        {this.state.viewState === VIEW_STATE_ERROR && this.renderError()}
        {this.state.viewState === VIEW_STATE_SUCCESS && this.renderContent()}
      </View>
    );
  }
  renderNoNetwork = () => (
    <FullPageError
      title="Uh oh!"
      subTitle="No Internet Connection"
      suggestion="Try with active internet connection"
      onRefreshPressed={this.onRefreshPressed}
      renderStickyHeader={this.renderStickyHeader}
      onBackPressed={this.onBackPressed}
    />
  )
  renderError = () => (
    <FullPageError
      title="Uh oh!"
      subTitle="Something went wrong"
      suggestion="Try again"
      onRefreshPressed={this.onRefreshPressed}
      renderStickyHeader={this.renderStickyHeader}
      onBackPressed={this.onBackPressed}
    />
  )
  renderProgressView = () => (
    <HolidayDetailLoader
      openingSavedPackage
      showDateText={false}
      changeAction
      loadingText="Loading Reviews..."
    />
  )
  renderContent = () => {
    return (
      <>
        {this.renderStickyHeader()}
        <View style={styles.topSection}>
          {this.renderRatingsBar()}
        </View>
        <FlatList
          style={styles.list}
          data={this.state.listData}
          extraData={this.state}
          keyExtractor={(item, index) => index.toString()}
          renderItem={({ item, index }) => this.renderListItem(item, index)}
          ItemSeparatorComponent={this.renderListSeparator}
          ListFooterComponent={this.renderListFooter}
          onEndReachedThreshold={0.2}
          onEndReached={this.handleLoadMore}
        />
      </>
    );
  }
  renderListItem = (item, index) => (
    <ReviewCard key={index} item={item} index={index} />
  )
  renderListSeparator = () => (
    <View style={styles.separator} />
  )
  renderListFooter = () => {
    return (this.state.hasMore ?
      <View style={[styles.footer]}>
      <Spinner
          size={36}
          strokeWidth={4}
          progressPercent={85}
          speed={1.5}
          color={holidayColors.primaryBlue}
          />
      </View> : <View style={styles.Nofooter}/>
    
    );
  }
  renderStickyHeader = () => (
    <PageHeader
      showBackBtn
      showShadow
      title={'Review & Rating'}
      subTitle={this.getSubTitle()}
      onBackPressed={this.onBackPressed}
  />
  )
  renderRatingsBar = () => {
    const { ratingBarItems } = this.state;
    if (!ratingBarItems || ratingBarItems.length === 0) {
      return null;
    }
    const {userRating, ratingCount, reviewCount} = this.props.hotelDetailsData.hotel.mmtRatingInfo;
    return (
      <View style={styles.ratingSection}>
        <LinearGradient
          start={{ x: 0.0, y: 0.0 }}
          end={{ x: 0.0, y: 1.0 }}
          colors={this.getBackgroundColorArray(userRating)}
          style={styles.overallRating}
        >
          <Text style={styles.overallRatingText}>
            {userRating}
            <Text style={styles.totalRating}>/5</Text>
          </Text>
        </LinearGradient>
        <View style={styles.totalReviews}>
          <View style={AtomicCss.flexRow}>
            <Text style={styles.value}>{ratingCount}</Text>
            <Text style={styles.heading}> RATINGS</Text>
          </View>
          <View style={AtomicCss.flexRow}>
            <Text style={styles.value}>{reviewCount}</Text>
            <Text style={styles.heading}> REVIEWS</Text>
          </View>
        </View>
        <View style={[AtomicCss.flexRow, AtomicCss.flex1]}>
          {ratingBarItems.map((item) => this.renderRatingBarViews(item.key, item.value))}
        </View>
      </View>
    );
  }
  renderRatingBarViews = (key, value) => (
    <View style={[AtomicCss.flex1, AtomicCss.alignCenter]}>
      <Text style={styles.itemValue}>{value}</Text>
      <Text style={styles.itemHeading} numberOfLines={1}>{key?.toUpperCase()}</Text>
    </View>
  )
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: 'white',
  },
  footer: {
    paddingTop: 24,
    paddingBottom: 48,
    alignItems:"center"
  },
  Nofooter :{
    marginBottom: 24,
  },
  list: {
    padding: 16,
  },
  separator: {
    width: '100%',
    height: 0.5,
    backgroundColor: '#f2f2f2',
  },
  topSection: {
    padding: 15,
    paddingBottom: 0,
  },
  ratingSection: {
    backgroundColor: '#f2f2f2',
    borderRadius: 4,
    overflow: 'hidden',
    flexDirection: 'row',
    alignItems: 'center',
  },
  overallRating: {
    width: 54,
    height: 46,
    alignItems: 'center',
    justifyContent: 'center',
    borderRadius: 4,
  },
  overallRatingText: {
    ...fontStyles.headingBase,
    color: holidayColors.white,
  },
  totalRating: {
    ...fontStyles.labelBaseBold,
  },
  totalReviews: {
    paddingHorizontal: 10,
    borderRightColor: holidayColors.grayBorder,
    borderRightWidth: 1,
  },
  heading: {
    ...fontStyles.labelSmallRegular,
    color: holidayColors.gray,
  },
  value: {
    ...fontStyles.labelSmallBlack,
    color: holidayColors.gray,

  },
  itemHeading: {
    ...fontStyles.labelSmallRegular,
  },

  itemValue: {
    ...fontStyles.labelBaseBold,
    color: holidayColors.gray,
    ...marginStyles.mb2,
  },
});
export default withBackHandler(HotelReviewsContainer);
