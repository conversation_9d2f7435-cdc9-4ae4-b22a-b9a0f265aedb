import React, { useState } from 'react';
import { StyleSheet, Text, Touchable, TouchableOpacity, View } from 'react-native';
import AtomicCss from '@mmt/legacy-commons/Styles/AtomicCss';
import { fontStyles } from '../../../../Styles/holidayFonts';
import { holidayColors } from '../../../../Styles/holidayColors';
import { marginStyles, paddingStyles } from '../../../../Styles/Spacing';
import { smallHeightSeperator } from '../../../../Styles/holidaySpacing';
import { holidayBorderRadius } from '../../../../Styles/holidayBorderRadius';
import {
  IMAGE_ICON_KEYS,
  getOpitimsedImageUrl,
} from '../../../../Common/Components/HolidayImageUrls';

/* Components */
import HolidayImageHolder from '../../../../Common/Components/HolidayImageHolder';
import BottomSheetOverlay from '../../../../Common/Components/BottomSheetOverlay';
import { RESIZE_MODE_IMAGE } from '../../../../HolidayConstants';

const getBackgroundColor = (rating) => {
  if (rating < 3) {
    return '#ff0844';
  } else if (rating >= 3 && rating <= 3.7) {
    return '#f5a623';
  } else {
    return '#249995';
  }
};

const ReviewCard = (props) => {
  const {
    reviewerName,
    publishDate,
    reviewText,
    rating,
    title,
    popupInfoText = '',
    logo = '',
    travellerType = '',
  } = props.item;

  const [openPopup, setOpenPopup] = useState(false);

  const handlePopupToggle = () => {
    setOpenPopup(!openPopup);
  };

  return (
    <>
      <View style={styles.card}>
        <View style={[styles.rating, { backgroundColor: getBackgroundColor(rating) }]}>
          <Text style={styles.ratingText}>{rating}</Text>
        </View>
        <View style={AtomicCss.flex1}>
          {!!title && <Text style={styles.title}>{title}</Text>}
          <View style={styles.subTitleContainer}>
            <Text style={styles.subTitle}>{`By ${reviewerName}, ${
              travellerType ? `${travellerType}, ` : ''
            }${publishDate}`}</Text>
            {!!popupInfoText && !!logo && (
              <TouchableOpacity activeOpacity={1} onPress={handlePopupToggle}>
                <HolidayImageHolder
                  imageUrl={getOpitimsedImageUrl(IMAGE_ICON_KEYS.INFO)}
                  style={styles.infoIcon}
                />
              </TouchableOpacity>
            )}
          </View>
          <Text style={styles.desc}>{reviewText}</Text>
        </View>
      </View>
      {openPopup && (
        <BottomSheetOverlay containerStyles={styles.containerStyles} showCross title="Verified Reviews" visible={openPopup} toggleModal={handlePopupToggle}>
          <View style={styles.infoTextContainer}>
            <Text style={styles.infoText}>{popupInfoText}</Text>
            <HolidayImageHolder
              imageUrl={logo}
              style={styles.infoTextIcon}
              resizeMode={RESIZE_MODE_IMAGE.CONTAIN}
            />
          </View>
        </BottomSheetOverlay>
      )}
    </>
  );
};

const styles = StyleSheet.create({
  card: {
    flexDirection: 'row',
    marginHorizontal: -15,
    paddingHorizontal: 15,
    ...smallHeightSeperator,
    ...paddingStyles.pt12,
  },
  rating: {
    backgroundColor: '#249995',
    ...holidayBorderRadius.borderRadius4,
    width: 30,
    height: 20,
    alignItems: 'center',
    justifyContent: 'center',
    ...marginStyles.mr12,
  },
  ratingText: {
    ...fontStyles.labelBaseBold,
    color: holidayColors.white,
  },
  title: {
    ...fontStyles.labelBaseBlack,
    color: holidayColors.gray,
    ...marginStyles.mb2,
  },
  subTitleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    ...marginStyles.mb10,
  },
  subTitle: {
    ...fontStyles.labelSmallRegular,
    color: holidayColors.lightGray,
  },
  roomName: {
    ...fontStyles.labelSmallBold,
    color: holidayColors.lightGray,
    ...marginStyles.mb2,
  },
  desc: {
    ...fontStyles.labelSmallRegular,
    color: holidayColors.gray,
    ...marginStyles.mb10,
  },
  infoIcon: {
    width: 15,
    height: 15,
    tintColor: holidayColors.gray,
    ...marginStyles.mh4,
  },
  infoTextContainer:{
    flexDirection: 'row',
    alignItems: 'center',
    ...paddingStyles.pv16
  },
  infoText: {
    ...fontStyles.labelBaseRegular,
  },
  infoTextIcon: {
    width: 100,
    height: 20,
    ...marginStyles.ml10,
  },
  containerStyles: {
    ...paddingStyles.pa16,
    ...paddingStyles.pb30,
  }
});

export default ReviewCard;
