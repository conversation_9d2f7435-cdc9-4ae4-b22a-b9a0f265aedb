import React from 'react';
import {Image, StyleSheet, Text, TouchableOpacity, View} from 'react-native';
import LinearGradient from 'react-native-linear-gradient';
import {colors, fonts} from '@mmt/legacy-commons/Styles/globalStyles';
import cStyles from '@mmt/legacy-commons/Styles/AtomicCss';
import iconQuotes from '../images/ic_quotes.png';
import iconArrowRight from '../images/ic_rightArrow.png';
import { getImageUrl, IMAGE_ICON_KEYS } from '../../../Common/Components/HolidayImageUrls';

const picSiddhi = getImageUrl(IMAGE_ICON_KEYS.SIDDHI);
const HolidayExpertBanner = () => {
  return (
    <View style={styles.holidayExpertBannerWrap}>
      <LinearGradient
        start={{x: 1.0, y: 1.0}}
        end={{x: 0.0, y: 1.0}}
        colors={['#e6faf7', '#e9f1d1']}
        style={styles.holidayExpertBanner}>
        <View>
          <View style={styles.bannerHdrWrap}><Text style={styles.bannerHdr}>Siddhi Suggests</Text></View>
          <View style={[styles.contentRow, styles.bannerContent]}>
            <View><Image source={iconQuotes} style={styles.iconQuotes}/></View>
            <View style={styles.contentWrap}><Text style={styles.cardDesc}>A tea tasting & plantation walk
              in Munnar is a must-do, why not add this activity to your day ? </Text></View>
          </View>
          <View style={[styles.contentRow, cStyles.alignCenter]}>
            <View><Image source={picSiddhi} style={styles.imageHolidayExpert}/></View>
            <View>
              <Text style={[cStyles.font12, cStyles.black, cStyles.blackFont]}>Siddhi Barigai</Text>
              <View style={[cStyles.paddingTop3]}><Text
                style={[cStyles.font11, cStyles.black, cStyles.regularFont]}>MMT Holidays Expert</Text></View>
              <View style={[cStyles.paddingTop3]}><Text
                style={[cStyles.font11, cStyles.black, cStyles.regularFont]}>7+ Years</Text></View>
            </View>
          </View>
        </View>
        <TouchableOpacity style={styles.activityBtnWrap}>
          <View style={styles.viewActivityBtn}>
            <View style={styles.activityBtnImageWrap}>
              <View style={styles.activityBtnImage}><Image source={picSiddhi}
                                                           style={styles.imagesPics}/></View>
              <View style={[styles.activityBtnImage, {marginLeft: -15}]}><Image source={picSiddhi}
                                                                                style={styles.imagesPics}/></View>
              <View style={[styles.activityBtnImage, {marginLeft: -15}]}><Image source={picSiddhi}
                                                                                style={styles.imagesPics}/></View>
            </View>
            <View><Text style={styles.linkText}>VIEW ACTIVITY </Text></View>
            <View><Image source={iconArrowRight} style={styles.iconRightArrow}/></View>
          </View>
        </TouchableOpacity>
      </LinearGradient>
    </View>
  );
};

const styles = StyleSheet.create({
  holidayExpertBannerWrap: {
    padding: 15,
    paddingBottom: 35,
    backgroundColor: 'white',
    position: 'relative',
    marginBottom: 7,
  },
  holidayExpertBanner: {
    borderRadius: 4,
  },
  cardDesc: {
    fontFamily: fonts.italic,
    color: colors.defaultTextColor,
    fontSize: 12,
    lineHeight: 18,
  },
  contentRow: {
    flexDirection: 'row',
    flexWrap: 'wrap',

  },
  imageHolidayExpert: {
    width: 81,
    height: 102,
    resizeMode: 'cover',
    marginRight: 20,
  },
  bannerHdr: {
    fontSize: 14,
    color: colors.defaultTextColor,
    fontFamily: fonts.black,
  },
  bannerHdrWrap: {
    paddingHorizontal: 20,
    paddingTop: 15,
  },
  bannerContent: {
    paddingHorizontal: 20,
    paddingTop: 15,
  },
  iconQuotes: {
    width: 20,
    height: 20,
    resizeMode: 'contain',
    marginRight: 20,
  },
  contentWrap: {
    width: '80%',
  },
  activityBtnWrap: {
    position: 'absolute',
    bottom: -20,
    alignItems: 'center',
    width: '100%',
  },
  viewActivityBtn: {
    padding: 2,
    borderRadius: 20,
    backgroundColor: 'white',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.22,
    shadowRadius: 2.22,

    elevation: 3,
    width: 250,
  },
  activityBtnImageWrap: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  activityBtnImage: {
    width: 32,
    height: 32,
    borderRadius: 32,
    borderWidth: 2,
    borderColor: 'white',
    backgroundColor: '#e5e5e5',
    alignItems: 'center',
    justifyContent: 'center',
    overflow: 'hidden',
  },
  imagesPics: {
    width: 32,
    height: 32,
    resizeMode: 'cover',
  },
  linkText: {
    color: '#008cff',
    fontFamily: fonts.black,
    fontSize: 12,
  },
  iconRightArrow: {
    width: 24,
    height: 24,
    resizeMode: 'cover',
    marginRight: 10,
  },
});

export default HolidayExpertBanner;
