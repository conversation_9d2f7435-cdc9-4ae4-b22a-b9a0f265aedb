import React from 'react';
import PropTypes from 'prop-types';
import {Image, Text, View} from 'react-native';
import isEmpty from 'lodash/isEmpty';
import styles from '../../../../Grouping/Components/HolidayGroupingCss';
import {
  getPackageImageForSimilarPackage,
  getDestinationsLabelForSimilarPackage,
  isRawClient,
  rupeeFormatterUtils,
} from '../../../../utils/HolidayUtils';
import genericCardDefaultImage from '@mmt/legacy-assets/src/no_dest_default.webp';
import {getHashTags} from '../../../../Grouping/Utils/HolidayGroupingUtils';
import TouchableRipple from '@mmt/legacy-commons/Common/Components/TouchableRipple';
import {MAX_HASHTAGS_TO_SHOW} from '../../../../HolidayConstants';

class SimilarPackage extends React.PureComponent {
  constructor(props) {
    super(props);
    this.state = {
      groupImageLoadFailed: false,
    };
  }

  _onGroupImageLoadError = () => {
    this.setState({groupImageLoadFailed: true});
  };

  render() {
    const packageDetail = this.props.item;
    const {storyImageSize} = this.props;
    const inclusionLabel = getInclusionLabel(packageDetail);
    const packageCategoryPrice =
      getPackageCategoryPrice(this.props.item.defaultCategory, packageDetail);
    let packagePrice = packageCategoryPrice.price;
    let packageDiscountedPrice = packageCategoryPrice.discountedPrice;
    if (!packageDiscountedPrice || packageDiscountedPrice === packagePrice) {
      packageDiscountedPrice = packagePrice;
      packagePrice = false;
    }
    const destinationLabel = getDestinationsLabelForSimilarPackage(packageDetail.destinationDetails);
    let noOfLines = 2;
    if (isRawClient()) {
      noOfLines = 1;
    }
    const hashTags = getHashTags(packageDetail, MAX_HASHTAGS_TO_SHOW);
    return (
      <TouchableRipple
        feedbackColor={'transparent'}
        onPress={() => this.props.onPackageClicked(packageDetail, storyImageSize, this.props.index)}
      >
        <View>
          <View
            style={[styles.carousalCard,
              this.props.index === 0 ? styles.marginLeft15 : styles.marginLeft1]}
          >
            <Image
              style={styles.experienceimg}
              source={!this.state.groupImageLoadFailed ?
                {uri: getPackageImageForSimilarPackage(packageDetail)}
                : genericCardDefaultImage}
              onError={this._onGroupImageLoadError}
            />
            <View style={styles.stayLength}>
              <Text style={styles.stayLengthText}>{packageDetail.nights} N
                . {packageDetail.nights + 1} D
              </Text>
            </View>
            <View style={styles.experienceDetails}>
              <Text
                numberOfLines={1}
                ellipsizeMode="tail"
                style={[styles.heading]}
              >
                {packageDetail.packageName}
              </Text>
              <View style={styles.flexRow}>
                <Text
                  numberOfLines={noOfLines}
                  ellipsizeMode="tail"
                  style={styles.stayDays}
                >{destinationLabel}
                </Text>
              </View>
              <View style={styles.flexRow}>
                <View style={[styles.pushLeft, styles.width47]}>
                  <Text style={styles.inclusions}>{inclusionLabel}</Text>
                </View>
                <View style={[styles.pushRight, styles.width52]}>
                  {!!packagePrice &&
                  <View>
                    <Text
                      style={styles.grossPrice}
                    >
                      {rupeeFormatterUtils(packagePrice)}
                    </Text>
                  </View>
                  }
                  <Text style={styles.netPrice}>
                      {rupeeFormatterUtils(packageDiscountedPrice)}
                  </Text>
                  <Text style={styles.perPersonTxt}> per person</Text>
                </View>
              </View>

            </View>
          </View>
          <Text
            numberOfLines={2}
            ellipsizeMode="tail"
            style={
              [styles.hashtags, this.props.index === 0 ? styles.marginLeft15 : styles.marginLeft1]
            }
          >{hashTags}
          </Text>
        </View>
      </TouchableRipple>
    );
  }
}

export const getPackageDetail = (packageId, packageDetails) =>
  packageDetails.filter(packageDetail => packageDetail.id === packageId)[0];

  export const getInclusionLabel = (packageDetail) => {
    let inclusionArray = [];

    if (packageDetail.inclusionDetails && packageDetail.inclusionDetails.flights) {
      inclusionArray.push('Flights');
    }

    if (packageDetail.inclusionDetails && packageDetail.inclusionDetails.activities) {
      inclusionArray.push('Activities');
    }

    if (packageDetail.inclusionDetails && packageDetail.inclusionDetails.transfers) {
      inclusionArray.push('Transfers');
    }

    let inclusionString = '';
    inclusionArray.forEach((inclusionText, i) => {
    if (i < 2) {
      inclusionString += inclusionText;
      if (inclusionArray.length > 1) {
        if (inclusionArray.length === 2 && i === 0) {
          inclusionString += ' & ';
        } else if (i === 0) {
          inclusionString += ', ';
        }
      }
    }
  });

  if (inclusionArray.length > 2) {
    inclusionString += ' & More';
  }

  return inclusionString;

};

export const addInclusionDelimiter = label => (
  isEmpty(label) ? '' : label.includes(',') ? ' & More' : ', '
);

export const getPackageCategoryPrice = (defaultCategoryId, packageDetail) => (
  packageDetail.categoryDetails.filter(row =>
    row.categoryId === defaultCategoryId)[0]
);

SimilarPackage.propTypes = {
  item: PropTypes.object.isRequired,
  index: PropTypes.number.isRequired,
  storyImageSize: PropTypes.string,
  onPackageClicked: PropTypes.func.isRequired,
};
export default SimilarPackage;
