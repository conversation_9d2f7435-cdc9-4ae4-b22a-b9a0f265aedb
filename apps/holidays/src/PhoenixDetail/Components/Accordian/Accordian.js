import React, {useState} from 'react';
import {Image, LayoutAnimation, Platform, StyleSheet, Text, TouchableOpacity, UIManager, View} from 'react-native';
import iconUpArrow from '../images/ic_upArrow.png';
import iconDownArrow from '../images/ic_downArrow.png';
import SimilarPackagesForNewDetailPage from './SimilarPackages/SimilarPackagesForNewDetailPage';
import { trackPhoenixDetailLocalClickEvent } from '../../Utils/PhoenixDetailTracking';
import DynamicCoachMark from '@mmt/legacy-commons/Common/Components/CoachMarks/DynamicCoachMark';
import SummaryView from '../SummaryView';
import { fontStyles } from '../../../Styles/holidayFonts';
import { holidayColors } from '../../../Styles/holidayColors';
import { logPhoenixDetailPDTEvents } from 'mobile-holidays-react-native/src/utils/PhoenixDetailPDTTrackingUtils';
import { PDT_EVENT_TYPES } from 'mobile-holidays-react-native/src/utils/HolidayPDTConstants';
import { isSummaryTabDefaultOpen } from 'mobile-holidays-react-native/src/utils/HolidayUtils';
import {isAndroidClient} from "../../../utils/HolidayUtils";

const AccordianData = ['Summary', 'Terms and Conditions', 'Policies', 'Similar Packages'];

if (isAndroidClient() && UIManager.setLayoutAnimationEnabledExperimental) {
  UIManager.setLayoutAnimationEnabledExperimental(true);
}

const Accordian = (props) => {
  const {
    onSimilarPackageClicked,
    onTermsAndConditionClicked,
    onPolicyClicked,
    openCustomizationPopup,
    showSimilarPackage,
    showTermsAndCondition,
    showPolicy,
    scrollToBottom,
    summaryData,
    fromPreSales = false,
  } = props;
  const [currentIndex, setCurrentIndex] = useState(false);
  const captureClickEvents = ({eventName = '', suffix = ''}) => {
    logPhoenixDetailPDTEvents({
      actionType : PDT_EVENT_TYPES.buttonClicked,
      value : eventName + suffix,
    })
    trackPhoenixDetailLocalClickEvent({ eventName, suffix });
  }

  const isSummaryOnDayPlan = isSummaryTabDefaultOpen({ fromPreSales });
  const toggleListItem = (index) => {
    // Handle click on policy tab.
    // No animation to be shown.
    if (index === 1) {
      captureClickEvents({ eventName: 'navigation_', suffix: 'termsAndCondition' });
      onTermsAndConditionClicked();
    } else if (index === 2) {
      captureClickEvents({ eventName: 'navigation_', suffix: 'policy' });
      onPolicyClicked();
    } else if (index === 4) {
      openCustomizationPopup();
    } else {
      //Handle animation when item is clicked.
      // set CurrentIndex. This will rotate icon.
      const newIndex = index === currentIndex ? null : index;
      LayoutAnimation.configureNext(
        LayoutAnimation.create(
          500,
          LayoutAnimation.Types.easeInEaseOut,
          LayoutAnimation.Properties.opacity
        )
      );
      scrollToBottom();

      setCurrentIndex(newIndex);
      if (newIndex === 0) {
        captureClickEvents({ eventName: 'navigation_', suffix: 'summary' });
      } else if (newIndex === 3) {
        captureClickEvents({ eventName: 'navigation_', suffix: 'similar packages' });
      }
    }
  };

  const getView = (list, index) => {
    return <View style={styles.accordianRow} key={index}>
      <TouchableOpacity onPress={() => {
        toggleListItem(index);
      }}>
        {(index === currentIndex && (
          <View style={[styles.accordHdr, styles.accordHdrActive]}>
            <Text style={styles.accordHdrTxtActive}>{list}</Text>
            <View>
              <Image source={iconUpArrow} style={styles.toggleArrows}/>
            </View>
          </View>
        ) || (
          <View style={styles.accordHdr}>
            <Text style={styles.accordHdrTxt}>{list}</Text>
            <View>
              <Image source={iconDownArrow} style={styles.toggleArrows}/>
            </View>
          </View>
        ))}
      </TouchableOpacity>
      {(index === currentIndex && (
        <View style={[styles.accordContainer]}>
          {(list.indexOf('Summary') > -1) && !isSummaryOnDayPlan && <SummaryView summaryData={summaryData}/>}
          {(list.indexOf('Similar Packages') > -1) && <SimilarPackagesForNewDetailPage onSimilarPackageClicked={onSimilarPackageClicked}/>}
        </View>
      ) || null)}
    </View>;
  };
  return (
    <View style={styles.accordian}>
      {!isSummaryOnDayPlan && <DynamicCoachMark cueStepKey="summaryTab" offsetHeight = {70} offsetWidth = {70}>
        {getView('Summary', 0)}
      </DynamicCoachMark>}
      {showTermsAndCondition && getView(AccordianData[1], 1)}
      {showPolicy && getView('Policies', 2)}
    </View>
  );


};

const styles = StyleSheet.create({
  accordian: {
    backgroundColor: 'white',
    paddingHorizontal: 15,
    marginBottom: 45,
  },
  accordianRow: {
    // borderBottomWidth: 1,
    // borderColor: '#e7e7e7'
  },
  accordHdr: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 15,
    borderBottomWidth: 1,
    borderColor: '#e7e7e7',
  },
  accordHdrActive: {
    borderBottomWidth: 0,
  },
  accordHdrTxt: {
    ...fontStyles.labelMediumBold,
    color: holidayColors.primaryBlue,
  },
  accordHdrTxtActive: {
    ...fontStyles.headingBase,
    color: holidayColors.black,
  },
  accordContainer: {
    flexGrow: 1,
  },
  toggleArrows: {
    width: 24,
    height: 24,
    resizeMode: 'cover',

  },
  iconArrowDown: {
    transform: [{rotate: '180deg'}],
  },
});

export default Accordian;
