import React from 'react';
import { Text, View, TouchableOpacity, StyleSheet } from 'react-native';
import HolidayImageHolder from '../../../../Common/Components/HolidayImageHolder';
import { RESIZE_MODE_IMAGE } from '../../../../HolidayConstants';
import { holidayBorderRadius } from '../../../../Styles/holidayBorderRadius';
import { dayPlanRowImage } from '../../DayPlan/dayPlanStyles';

const ItineraryUnitImageWrapper = ({
  imageUrl,
  children,
  containerStyle = {},
  imageStyle = {},
  resizeMode = null,
}) => {
  return (
    <View style={[imageStyles.thumbWrapper, containerStyle]}>
      <HolidayImageHolder
        imageUrl={imageUrl}
        style={[imageStyles.image, imageStyle]}
        resizeMode={resizeMode}
      />
      {children}
    </View>
  );
};

const imageStyles = StyleSheet.create({
  image: {
    width: '100%',
    height: '100%',
    ...dayPlanRowImage,
  },
  thumbWrapper: {
    width: 94,
    height: 94,
    ...holidayBorderRadius.borderRadius16,
    overflow: 'hidden',
    position: 'relative',
  },
});

export default ItineraryUnitImageWrapper;
