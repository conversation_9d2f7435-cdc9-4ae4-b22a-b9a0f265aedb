import React from 'react';
import { View, StyleSheet } from 'react-native';
import HolidayImageHolder from '../../../../Common/Components/HolidayImageHolder';
import CardSeperator from '@mmt/legacy-assets/src/holidays/hol-card-seperator.png';
import { RESIZE_MODE_IMAGE } from '../../../../HolidayConstants';
import { marginStyles } from '../../../../Styles/Spacing';
import { holidayColors } from '../../../../Styles/holidayColors';

const DashedSeperator = ({ marginSpacing }) => {
  return (
    <HolidayImageHolder
      defaultImage={CardSeperator}
      style={[styles.dashedLine, marginSpacing]}
      resizeMode={RESIZE_MODE_IMAGE.COVER}
    />
  );
};

const DotSeperator = ({ style }) => {
  return <View style={[styles.dot, style]} />;
};

const styles = StyleSheet.create({
  dashedLine: {
    height: 1,
  },
  dot: {
    height: 4,
    width: 4,
    backgroundColor: holidayColors.gray,
    borderRadius: 50,
  },
});
export { DashedSeperator, DotSeperator };
