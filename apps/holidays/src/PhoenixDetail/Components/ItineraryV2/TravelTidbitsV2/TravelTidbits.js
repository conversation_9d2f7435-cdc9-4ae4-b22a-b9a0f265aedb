import React, { useState } from 'react';
import { useFocusEffect } from '@mmt/navigation';
import { BackHandler, Platform, StatusBar, StyleSheet, Text, useWindowDimensions, View } from 'react-native';
import { TabView } from 'react-native-tab-view';
import TidbitsTabBar from './TidbitsTabBar';
import BlackUpdateFooter from '../../BlackUpdateFooter';
import Divider from '../../../../Common/Components/Divider';
import {
  createStaySeqData,
  createTabRoutes,
  getActivityDataByType,
  getTidbitListingPageHeader,
  hasNewActivitiesSelected,
} from './TidBitsUtils';
import ActivityListingPage from '../../ActivityOverlay/ActivityListing/ActivityListingPageNewV2';
import ConfirmationPopup from '../../ConfirmationPopup/ConfirmationPopup';
import ActivityPageHeader from '../../../../Common/Components/ActivityPageHeader';
import { holidayColors } from '../../../../Styles/holidayColors';
import CitySelectionPopup from './CitySelectionPopup';
import SubtitleComponent from './HeaderSubtitle';
import ActivityInfoMessage from './ActivityInfoMessage';
import { isEmpty, noop } from 'lodash';
import { HolidayNavigation } from '../../../../Navigation';
import { trackPhoenixDetailLocalClickEvent } from '../../../Utils/PhoenixDetailTracking';
import { HOLIDAYS_ACTIVITY_OVERLAY_LISTING } from '../../../Utils/PheonixDetailPageConstants';
import { connect } from 'react-redux';
import { fontStyles } from '../../../../Styles/holidayFonts';
import { logPhoenixDetailPDTEvents } from 'mobile-holidays-react-native/src/utils/PhoenixDetailPDTTrackingUtils';
import { PDT_EVENT_TYPES } from 'mobile-holidays-react-native/src/utils/HolidayPDTConstants';
import ActivitySearchShimmer from './ActivitySearchShimmer';
import { capitalizeText } from '../../../../../src/utils/textTransformUtil';

const TravelTidbits = (props) => {
  const {
    response,
    roomDetails,
    departureDetail,
    itineraryUnitDate = '',
    onBackPressed,
    selectedActivities,
    onUpdatePress,
    setActiveDaySequence,
    activeDaySequence,
    preSelectedActivities,
    tabIndex,
    setTabIndex,
    selectedActivitiesStripData,
    filterListData,
    appliedFilters,
    applyFilter,
    onEndReached = noop,
    updateActiveTab,
    searchedText,
    updateSearchText,
    isLoadMore,
    isSearchLoading,
    //childLoader,
    productType,
  } = props || {};
  if (isEmpty(response)) {
    return [];
  }

  const layout = useWindowDimensions();
  const [index, setIndex] = useState(tabIndex);
  const [confirmDialogVisibility, setConfirmDialogVisibility] = useState(false);
  const [selectCityPopupVisibility, setSelectCityPopupVisibility] = useState(false);
  const staySequences = createStaySeqData(props.activityReqParams);
  const activeDayResponse = response[activeDaySequence];

  const getFilterDataByTab = (type)=>{
   return  filterListData[type.toLowerCase()];
  };
  const getAppliedFilter = (type)=>{
    if (type === 'ACTIVITY'){
      return appliedFilters?.activity;
    }
    else if (type === 'MEAL'){
      return appliedFilters?.meal;
    }
    else if (type === 'TRANSFER'){
      return appliedFilters?.transfer;
    }
  };

  const TAB_ROUTES = createTabRoutes(activeDayResponse);
  const SHOW_TABS = TAB_ROUTES.length > 1;

  // Function to check if the current active tab has empty data (error state)
  const isCurrentTabEmpty = () => {
    if (!TAB_ROUTES || TAB_ROUTES.length === 0) {
      return true;
    }
    const currentTabRoute = TAB_ROUTES[index];
    if (!currentTabRoute) {
      return true;
    }
    const pageData = getActivityDataByType(currentTabRoute.key, activeDayResponse);
    return isEmpty(pageData?.listingActivities);
  };

  const renderScene = ({ route }) => {
    const { key ,type } = route || '';
    const pageData = getActivityDataByType(key, activeDayResponse);

    // Show shimmer when search is loading (either searching or clearing search)
    if (isSearchLoading) {
      return <ActivitySearchShimmer itemCount={6} />;
    }

    if (pageData) {
      return (
        <ActivityListingPage
          pageData={pageData}
          {...props}
          activityType={type}
          searchedText={searchedText}
          filterListData={filterListData}
          appliedFilters={getAppliedFilter(type)}
          applyFilter={applyFilter}
          onEndReached={onEndReached}
          isLoadMore={isLoadMore}
          isSearchLoading={isSearchLoading}
         // childLoader={childLoader}
          productType={productType}
        />
      );
    }
    return [];
  };

  const handleBackPressed = () => {
    if (hasNewActivitiesSelected(preSelectedActivities, selectedActivities)) {
      setConfirmDialogVisibility(true);
    } else {
      HolidayNavigation.pop();
    }
  };

  useFocusEffect(
    React.useCallback(() => {
      const hardwareBackPressed = () => {
        handleBackPressed();
        return true;
      };
      BackHandler.addEventListener('hardwareBackPress', hardwareBackPressed);

      return () =>
        BackHandler.removeEventListener('hardwareBackPress', hardwareBackPressed);
    }, []),
  );
  const onNotNowClicked = () => HolidayNavigation.pop(); //onBackPressed();

  const onChangeCity = (staySequence) => {
    setActiveDaySequence(staySequence);
    toggleSelectCityPopupVisibility();
  };

  const toggleSelectCityPopupVisibility = () =>
    setSelectCityPopupVisibility(!selectCityPopupVisibility);

  const captureClickEvents = ({ eventName = ''}) => {
    logPhoenixDetailPDTEvents({
      actionType: PDT_EVENT_TYPES.buttonClicked,
      value: eventName.replace(/_/g, '|'),
      subPageName : HOLIDAYS_ACTIVITY_OVERLAY_LISTING,

    });
    trackPhoenixDetailLocalClickEvent({
      eventName,
      prop1: HOLIDAYS_ACTIVITY_OVERLAY_LISTING,
    });

  };
  const onIndexChange = (index) => {
    const activityType = TAB_ROUTES?.[index].key;
    const activityTypeV2 = TAB_ROUTES?.[index].type;
    captureClickEvents({
      eventName: `${'TAB'}_${activityType}_${index}`,
    });
    setTabIndex(index);
    setIndex(index);
    updateActiveTab(activityTypeV2);
  };
  const getSubtitleViews = () => {
    return (
      <SubtitleComponent
        departureDetail={departureDetail}
        roomDetails={roomDetails}
        staySequences={staySequences}
        activeDaySequence={activeDaySequence}
        toggleSelectCityPopupVisibility={toggleSelectCityPopupVisibility}
      />
    );
  };
  const TitleAndSubTitleView = () => {
    return (
      <View style={styles.titleStyles}>
        <Text style={styles.title}>
          {capitalizeText(getTidbitListingPageHeader(TAB_ROUTES))}
        </Text>
        {getSubtitleViews()}
      </View>
    );
  };
  const onTextChange = (text) => {
    let filteredData = getAppliedFilter(TAB_ROUTES?.[index].type);
    updateSearchText(filteredData,TAB_ROUTES?.[index].type,text);
  };


  return (
    <View style={[styles.container, { backgroundColor: holidayColors.lightGray2 }]}>
      <ActivityPageHeader
        showShadow
        showBackBtn
        onBackPressed={handleBackPressed}
        containerStyles={styles.headerContainer}
        onTextChange={onTextChange}
        searchTextPlaceholder={'Search by Activity Name'}
        searchedText={searchedText}
        activeTabIndex={index}
        isSearchLoading={isSearchLoading}
        leftComponent={<TitleAndSubTitleView />}
      />
      <Divider height={2} />
      {SHOW_TABS ? (
        <TabView
          navigationState={{ index, routes: TAB_ROUTES }}
          renderScene={renderScene}
          onIndexChange={onIndexChange}
          initialLayout={{ width: layout.width }}
          renderTabBar={TidbitsTabBar}
        />
      ) : (
        // Show shimmer when search is loading (single tab scenario)
        isSearchLoading ? (
          <ActivitySearchShimmer itemCount={6} />
        ) : (
          <ActivityListingPage
            pageData={getActivityDataByType(TAB_ROUTES?.[0]?.key, activeDayResponse)}
            {...props}
            activityType={TAB_ROUTES?.[0]?.type}
            searchedText={searchedText}
            filterListData={filterListData}
            appliedFilters={getAppliedFilter(TAB_ROUTES?.[0]?.type)}
            applyFilter={applyFilter}
            onEndReached={onEndReached}
            isLoadMore={isLoadMore}
            // childLoader={childLoader}
            productType={productType}
          />
        ))}

      <>
      <ActivityInfoMessage selectedActivitiesStripData={selectedActivitiesStripData} />
      <BlackUpdateFooter onUpdatePress={onUpdatePress} />
      </>

      <ConfirmationPopup
        confirmDialogVisibility={confirmDialogVisibility}
        onUpdatePackageClickFromPopup={onUpdatePress}
        onNotNowClicked={onNotNowClicked}
        onShowMeClicked={() => setConfirmDialogVisibility(false)}
      />
      <CitySelectionPopup
        staySequences={staySequences}
        activeDaySequence={activeDaySequence}
        onChangeCity={onChangeCity}
        selectCityPopupVisibility={selectCityPopupVisibility}
        hideSelectCityPopupVisibility={toggleSelectCityPopupVisibility}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  headerContainer: {
    backgroundColor: holidayColors.white,
  },
  title: {
    ...fontStyles.labelMediumBold,
    color: holidayColors.black,
  },
  blackFooterContainer:{
    position:'absolute',
    bottom:0,
    width:'100%',
  },
});

const mapStateToProps = state => {
  const { selectedActivitiesStripData } = state.travelTidbitsReducer;
  return { selectedActivitiesStripData };
};

export default connect(mapStateToProps, null)(TravelTidbits);
