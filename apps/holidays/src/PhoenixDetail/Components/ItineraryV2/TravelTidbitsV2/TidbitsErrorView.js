import React, { useEffect } from 'react';
import { BackHandler, Platform, Pressable, StatusBar, StyleSheet, Text, View, Image } from 'react-native';
import errorImageIcon from '@mmt/legacy-assets/src/visa_error_image.webp';
import resetIcon from '@mmt/legacy-assets/src/ic_clear_black.webp';
import { fontStyles } from '../../../../Styles/holidayFonts';
import { holidayColors } from '../../../../Styles/holidayColors';
import { marginStyles, paddingStyles } from '../../../../Styles/Spacing';
import {
  getErrorButtonText,
  getProductTypeErrorIcon,
  getProductTypeText,
  getTidbitErrorPageHeader,
} from './TidBitsUtils';
import { HolidayNavigation } from '../../../../Navigation';
import { SUB_HEADER_DATE_FORMAT } from '../../Header';
import { getNewDate } from '@mmt/legacy-commons/Common/utils/DateUtils';
import fecha from 'fecha';
import { addDays } from '@mmt/legacy-commons/Helpers/dateTimehelpers';

/* Components */
import PageHeader from '../../../../Common/Components/PageHeader';
import HolidayImageHolder from 'mobile-holidays-react-native/src/Common/Components/HolidayImageHolder';
import { noop } from 'lodash';

const TidbitsErrorView = (props) => {
  const { productType, departureDetail, day ,isHeaderShown = true, resetFilters = noop, appliedFilters, type } = props || {};
  const date = fecha.format(
    addDays(getNewDate(departureDetail?.departureDate), day - 1 < 0 ? 0 : day - 1),
    SUB_HEADER_DATE_FORMAT,
  );

  const resetAppliedFilters = () =>  resetFilters();

  const showResetFilterButton = () => {
    if (!appliedFilters) return false;

    // Check if any category has filters or sorters
    return Object.values(appliedFilters).some(category =>
      category?.length > 0 || category?.length > 0
    );
  };

  const onBackPressed = () => {
    HolidayNavigation.pop();
    return true;
  };

  // Effect hook for handling the hardware back button press.
  useEffect(() => {
    BackHandler.addEventListener('hardwareBackPress', onBackPressed);
    return () => BackHandler.removeEventListener('hardwareBackPress', onBackPressed);
  }, []);

  const productTypeText = getProductTypeText(type, false);
  const productTypeIcon = getProductTypeErrorIcon(type);

  return (
    <View style={styles.container}>
      {isHeaderShown && <PageHeader
        showBackBtn
        showShadow
        title={getTidbitErrorPageHeader(productType)}
        onBackPressed={onBackPressed}
        containerStyles={styles.header}
      />}
      <View style={styles.errorDetails}>
        <View style={styles.errorDetailsText}>
          <HolidayImageHolder
            style={styles.errorImage1}
            defaultImage={errorImageIcon}
            imageUrl={productTypeIcon}
          />
          <Text style={styles.title}>{productTypeText} not available</Text>
          <Text style={styles.subTitle}>
            No {productTypeText} available to be added for this package on {date}
          </Text>

          {showResetFilterButton() && <Pressable
            style={({ pressed }) => [
              styles.resetButton,
              pressed && {
                backgroundColor: holidayColors.primaryBlue,
                transform: [{ scale: 0.98 }],
                shadowOpacity: 0.2,
              },
            ]}
            onPress={resetAppliedFilters}
          >
            {({ pressed }) => (
              <>
                <Image
                  source={resetIcon}
                  style={[
                    styles.resetIcon,
                    pressed && { tintColor: holidayColors.white },
                  ]}
                />
                <Text
                  style={[
                    styles.resetButtonText,
                    pressed && { color: holidayColors.white },
                  ]}
                >
                  {getErrorButtonText(appliedFilters)}
                </Text>
              </>
            )}
          </Pressable>}
        </View>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'space-between',
    backgroundColor: holidayColors.white,
    width: '100%',
  },
  header: {
    marginTop: Platform.OS === 'android' ? StatusBar.currentHeight : 0,
  },
  errorDetails: {
    alignItems: 'center',
    paddingVertical: 15,
    flex: 1,
    width: '100%',
  },
  errorDetailsText: {
    alignItems: 'center',
    justifyContent: 'center',
    flex: 1,
    ...paddingStyles.pa16,
  },
  errorImage1: {
    width: 72,
    height: 72,
    ...marginStyles.mb30,
  },
  title: {
    ...fontStyles.headingMedium,
    color: holidayColors.black,
    ...marginStyles.mb10,
    textAlign: 'center',
  },
  subTitle: {
    ...fontStyles.labelLargeRegular,
    color: holidayColors.gray,
    ...marginStyles.mb70,
    lineHeight: 20,
    textAlign: 'center',
  },
  button: {
    paddingHorizontal: 80,
  },
  resetButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: holidayColors.white,
    borderWidth: 1.5,
    borderColor: holidayColors.primaryBlue,
    borderRadius: 12,
    paddingVertical: 12,
    paddingHorizontal: 24,
    marginTop: 20,
    alignSelf: 'center',
    minWidth: 160,
    shadowColor: holidayColors.primaryBlue,
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 3,
  },
  resetIcon: {
    width: 18,
    height: 18,
    marginRight: 8,
    tintColor: holidayColors.primaryBlue,
  },
  resetButtonText: {
    ...fontStyles.labelLargeRegular,
    color: holidayColors.primaryBlue,
    fontWeight: '700',
    fontSize: 15,
    letterSpacing: 0.5,
  },
});

export default TidbitsErrorView;
