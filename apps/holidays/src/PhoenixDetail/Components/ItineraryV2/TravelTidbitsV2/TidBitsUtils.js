import { itineraryUnitSubTypes, itineraryUnitTypes } from '../../../DetailConstants';
import {
  ACTIVITY_RESPONSE_SECTIONS,
  ACTIVITY_TYPE,
  TABS_MAPPING,
} from '../../../Utils/PheonixDetailPageConstants';
import { TABS } from '../../DayPlan/Sorter';
import { isEmpty } from 'lodash';
import { logCloseEvent, logUpdateEvent } from '../../ActivityOverlay/ActivityUtils';
import { getPackagePrice } from '../../../Utils/ActivityOverlayUtils';
import { IMAGE_ICON_KEYS, getOpitimsedImageUrl } from '../../../../Common/Components/HolidayImageUrls';
import { SELECTED_ACTIVITIES_INFO_STRIP_STATE } from '../../../Reducers/HolidayTidbitsReducer';

export const createTabRoutes = (data) => {
  if (!data) return [];
  return Object.entries(data)
    .filter(([key, value]) => TABS_MAPPING[key])
    .map(([key]) => TABS_MAPPING[key]);
};

export const createStaySeqData = (activityReqParams) => {
  const {citySeq = []} = activityReqParams || {};
  const newCitySeq = [];
  if (citySeq) {
    citySeq.forEach((cs) => {
      newCitySeq.push({
        name: cs.name,
        staySequence: cs.staySequence,
        data: cs,
      });
    });
  }
  return newCitySeq;
}

const getActivitiesByType = (jsonData, staySequence, type) => {
  return jsonData[staySequence]?.[`${type}ListingData`]?.listingActivities || [];
};

export const getCombinedMap = (response, staySequences, getPriceMapByType) => {
  if (!response || isEmpty(staySequences)) return new Map();

  let result = new Map();
  staySequences.forEach(item => {
    const activityPriceMap = getPriceMapByType(response, item?.staySequence, 'activity');
    const transferPriceMap = getPriceMapByType(response, item?.staySequence, 'transfer');
    const mealPriceMap = getPriceMapByType(response, item?.staySequence, 'meal');
    result =  { ...result, ...activityPriceMap, ...transferPriceMap, ...mealPriceMap };
  });
  return result;
};

export const getCombinedPackagePriceMap = (response, staySequences) => {
  return getCombinedMap(response, staySequences, getPackagePriceMapByType);
};

export const getCombinedRemovedPriceMap = (response, staySequences) => {
  return getCombinedMap(response, staySequences, getRemovedPriceMapByType);
};

const getPackagePriceMapByType = (jsonData, staySequence, type) => {
  return jsonData[staySequence]?.[`${type}ListingData`]?.packagePriceMap || new Map();
};

const getRemovedPriceMapByType = (jsonData, staySequence, type) => {
  return jsonData[staySequence]?.[`${type}ListingData`]?.removedPriceMap || new Map();
};
export const getListingActivities = (jsonData, staySequence) => {
  return [
    ...(jsonData[staySequence]?.activityListingData?.listingActivities || []),
    ...(jsonData[staySequence]?.transferListingData?.listingActivities || []),
    ...(jsonData[staySequence]?.mealListingData?.listingActivities || [])
  ];
};

const mapAndFilterData = (data, selectedActivities, type) => {
  return data
    .filter(activity => selectedActivities.some(selectedActivity => selectedActivity.code === activity.metaData.code))
    .map(activity => ({
      name: activity.metaData.name,
      type,
    }));
};

export const getSelectedActivitiesData = (jsonData, staySequence, selectedActivities = [], preSelectedActivities = []) => {
  // HLD-17906 Filter activities that are preselected and also present in selectedActivities.
  const filteredActivities = selectedActivities.filter(activity =>
    !preSelectedActivities.some(preSelectedActivity => preSelectedActivity.code === activity.code)
  );

  if (!jsonData || !staySequence ) return [];

  const activityData = getActivitiesByType(jsonData, staySequence, 'activity');
  const transferData = getActivitiesByType(jsonData, staySequence, 'transfer');
  const mealData = getActivitiesByType(jsonData, staySequence, 'meal');

  const selectedActivitiesData =  [
    ...mapAndFilterData(activityData, filteredActivities, ACTIVITY_TYPE.ACTIVITY),
    ...mapAndFilterData(transferData, filteredActivities, ACTIVITY_TYPE.TRANSFERS),
    ...mapAndFilterData(mealData, filteredActivities , ACTIVITY_TYPE.MEALS),
  ];

  const result = {};

  selectedActivitiesData.forEach(item => {
    const type = item.type.toLowerCase();

    if (result[type]) {
      result[type].count += 1;
      result[type].names.push(item.name);
    } else {
      result[type] = {
        count: 1,
        names: [item.name]
      };
    }
  });

  return result;
};

const extractListingActivities = (jsonData, staySequences = []) => {
  return staySequences.flatMap(ss => {
    const { staySequence } = ss || {};
    return getListingActivities(jsonData, staySequence);
  });
};

const findPrice = (jsonData, activityCode, staySequences = []) => {
  let mergedPackagePriceMap = {};
  staySequences.forEach(ss => {
    const { staySequence } = ss || {};
    mergedPackagePriceMap = {
      ...mergedPackagePriceMap,
      ...jsonData[staySequence]?.activityListingData?.packagePriceMap,
      ...jsonData[staySequence]?.transferListingData?.packagePriceMap,
      ...jsonData[staySequence]?.mealListingData?.packagePriceMap,
    };
  });
  return mergedPackagePriceMap[activityCode];
};


const findActivityDiscountFactor = (jsonData, activityCode, staySequences = []) => {
  if (!jsonData || !activityCode ) return null;

  for (const ss of staySequences) {
    if (typeof ss !== 'object' || !ss.staySequence) continue;
    const { staySequence } = ss;

    for (const section of ACTIVITY_RESPONSE_SECTIONS) {
      // Check if the activity exists in the current section and staySequence
      const activityExists = jsonData[staySequence]?.[section]?.listingActivities?.some(activity => activity.metaData.code === activityCode);

      // If the activity exists, return its discountedFactor or null if not defined
      if (activityExists) {
        return jsonData[staySequence][section].discountedFactor ?? null;
      }
    }
  }
  // Return null if no matching activity code is found or if jsonData does not have the expected structure
  return null;
};


export const findActivityPricingDetails = (jsonData, staySequences, activityCodes, ) => {
  if (!jsonData || !activityCodes) return {};
  const codes = Array.isArray(activityCodes) ? activityCodes : [activityCodes];
  const listingActivities = extractListingActivities(jsonData, staySequences);

  return codes.map(code => {
    const activityFound = listingActivities.some(activity => activity?.metaData?.code === code);
    const price = findPrice(jsonData, code, staySequences);
    const discountFactor = findActivityDiscountFactor(jsonData, code, staySequences);
    return { code, discountFactor, price };
  });
};


export const getSelectedActivityCodes = (jsonData, staySequences) => {
  if (!jsonData || !staySequences) return [];
  // Combine all listing activities from different sections into one array
  const combinedActivities = extractListingActivities(jsonData, staySequences);

  // Filter activities where 'selected' is true and map to their 'code'
  return combinedActivities
    .filter(activity => activity?.metaData?.selected)
    .map(activity => activity?.metaData.code);
};

export const getActivityDataByType = (activityType, data) => {
  const { activityListingData = {}, mealListingData = {}, transferListingData = {} } = data || {};

  const activityTypeToData = {
    [ACTIVITY_TYPE.ACTIVITY]: activityListingData,
    [ACTIVITY_TYPE.MEALS]: mealListingData,
    [ACTIVITY_TYPE.TRANSFERS]: transferListingData,
  };

  return activityTypeToData[activityType];
}

const filterAndMapActivities = (activities, activityCodesWithRecheckKey, staySequence) => {
  return activities
    .filter(activity => activityCodesWithRecheckKey.some(selectedActivity => selectedActivity.code === activity.metaData.code))
    .map(activity => ({
      "activityCode": activity?.metaData?.code,
      "optionRecheckKey": activity?.recheckKey,
      "staySequence": staySequence,
      "name": activity?.metaData?.name,
      "acmeType": activity?.metaData?.acmeType,
      "acmeSubType": activity?.metaData?.acmeSubType,
    }));
};

const extractActivities = (jsonData, activityCodesWithRecheckKey, staySequences) => {
  return staySequences.flatMap(ss => {
    if (typeof ss !== 'object' || !ss.staySequence) return [];
    const { staySequence } = ss;

    const retValue =  ACTIVITY_RESPONSE_SECTIONS.flatMap(section => {
      const activities = jsonData[staySequence]?.[section]?.listingActivities || [];
      return filterAndMapActivities(activities, activityCodesWithRecheckKey, staySequence);
    });

    activityCodesWithRecheckKey.forEach( selectedActivity => {
      if(selectedActivity.recheckKey && !retValue.find(activity => activity.activityCode === selectedActivity.code) && selectedActivity.staySequence === staySequence)
        retValue.push({
          'activityCode': selectedActivity.code,
          'optionRecheckKey': selectedActivity.recheckKey,
          'staySequence': staySequence,
          'name': selectedActivity.name,
          'acmeType': selectedActivity.acmeType,
          'acmeSubType': selectedActivity.acmeSubType,
        });
    }
  );
  return retValue;
  });
};

export const getSelectedActivitiesPayload = (jsonData, activityCodesWithRecheckKey, staySequences = []) => {
  if (!jsonData || !activityCodesWithRecheckKey) return [];
  return extractActivities(jsonData, activityCodesWithRecheckKey, staySequences);
};

export const getCityForSelectedStaySequence = (staySequences, selectedStaySequence) => {
  if (!staySequences || !selectedStaySequence) return '';
  const selectedCity = staySequences?.find(ss => ss.staySequence === selectedStaySequence);
  return selectedCity?.name || '';
};


const DEFAULT_PRODUCTS = [itineraryUnitTypes.ACTIVITY, itineraryUnitSubTypes.ACT_TRANSFER, itineraryUnitTypes.MEAL];

const productTypeMap = {
  [TABS.DAY]: DEFAULT_PRODUCTS,
  [TABS.MEALS]: [itineraryUnitTypes.MEAL],
  [TABS.COMMUTE]: [itineraryUnitSubTypes.ACT_TRANSFER],
  [TABS.ACTIVITIES]: [itineraryUnitTypes.ACTIVITY],
};

export const getProductType = (tab) => {
  return productTypeMap[tab] || DEFAULT_PRODUCTS;
};

export const getPreSelectedActivityFromDetailResponse = activityReqParams => {
const { activityList } = activityReqParams || {};
  return activityList.map(activity => {
   if (activity?.metaData?.code) {
     return activity?.metaData?.code;
   }
 });
};

export const getPreSelectedActivityWithRecheckKeyFromDetailResponse = activityReqParams => {
  if (!activityReqParams) {
    return [];
  }

  return activityReqParams.activityList
    .filter(activity => activity?.metaData?.code)
    .map(({ metaData: { code, name, acmeType, acmeSubType }, recheckKey, staySequence }) => ({ name, code, recheckKey, acmeType, acmeSubType, staySequence }));
};

/**
 * This function validates an array of responses.
 * Each response is an array where the second element is either null or an object.
 * If the second element is null or has an 'error' property, the function returns false.
 * If all responses pass the validation, the function returns true.
 *
 * @param {Array} responses - An array of responses. Each response is an array where the second element is either null or an object.
 * @returns {boolean} - Returns true if all responses pass the validation, otherwise returns false.
 */
export const validateResponses = (responses) => {
  return responses.every(([_, response]) => response !== null && !response.hasOwnProperty('error'));
}

export function updateActivityWithNewRecheckKey(activitiesPayload, activityCode, optionRecheckKey, name = '', acmeType, acmeSubType) {
  const activity = activitiesPayload.find(activity => activity.activityCode === activityCode);

  if (activity) {
    activity.optionRecheckKey = optionRecheckKey;
  } else {
    activitiesPayload.push({
      activityCode: activityCode,
      optionRecheckKey: optionRecheckKey,
      name,
      acmeType,
      acmeSubType,
    });
  }

  return activitiesPayload;
}

export const formatTimeSlot = (startTime, endTime, slotDurationHour) => {
  let formattedTimeSlot = '';
  if (startTime && endTime) {
    formattedTimeSlot += slotDurationHour ? `${slotDurationHour} ${slotDurationHour > 1 ? 'Hours' : 'Hour'} • ` : '';
    formattedTimeSlot += `${startTime} - ${endTime}`;
  }
  return formattedTimeSlot;
};

/**
 * Generates a header text for a tidbit listing page, indicating the items to add to the day.
 *
 * @param {Array} TAB_ROUTES - An array containing tab objects. Each object should have a `key` property.
 * @param {boolean} useKey - A boolean indicating whether to use the `key` property of the tab objects. If true, the `key` property is used. If false or not provided, the tab object itself is used.
 * @returns {string} A string representing the header text indicating the items to add to the day.
 *
 * @example
 * const TAB_ROUTES = [{ key: 'Activity' }, { key: 'Meals' }, { key: 'Transfer' }];
 * const headerText = getHeader(TAB_ROUTES, true);
 * console.log(headerText); // Output: "Add Activity, Meal or Transfer to day."
 */
const getHeader = (TAB_ROUTES = [], useKey = false) => {
  const tabs = TAB_ROUTES.map(tab => useKey ? tab?.key : tab);
  if (tabs.length > 1) {
    const lastTab = tabs.pop(); // Remove the last tab temporarily
    return 'Add ' + tabs.join(', ') + ' or ' + lastTab;
  } else {
    return 'Add ' + tabs.join(', '); // If only one tab, no need for 'or'
  }
};

export const getTidbitListingPageHeader = (TAB_ROUTES) => getHeader(TAB_ROUTES, true);
export const getTidbitErrorPageHeader = getHeader;

export const getProductTypeText = type => {
    switch (type) {
      case itineraryUnitTypes.ACTIVITY:
        return 'Activity';
      case itineraryUnitTypes.MEAL:
        return 'Meal';
      case itineraryUnitSubTypes.ACT_TRANSFER:
        return 'Transfers';
      default:
        return 'Activity';
  }
};

export const getProductTypeErrorIcon = (type) => {
  switch (type) {
    case itineraryUnitTypes.ACTIVITY:
      return getOpitimsedImageUrl(IMAGE_ICON_KEYS.NO_ACTIVITY);
    case itineraryUnitTypes.MEAL:
      return getOpitimsedImageUrl(IMAGE_ICON_KEYS.NO_MEAL);
    case itineraryUnitSubTypes.ACT_TRANSFER:
      return getOpitimsedImageUrl(IMAGE_ICON_KEYS.NO_TRANSFER);
    default:
      return getOpitimsedImageUrl(IMAGE_ICON_KEYS.NO_ACTIVITY);
  }
}
/**
 * This function checks if there are new activities selected by comparing two arrays of activities.
 * Each activity is an object with properties `name`, `code`, and `recheckKey`.
 * The function converts each activity object to a string using `JSON.stringify` before adding it to a Set.
 * Then, it compares these Sets to determine if there are new activities selected.
 *
 * @param {Array} preSelectedActivities - An array of previously selected activities.
 * @param {Array} selectedActivities - An array of currently selected activities.
 * @returns {boolean} - Returns true if there are new activities selected, otherwise returns false.
 */
export const hasNewActivitiesSelected = (preSelectedActivities, selectedActivities) => {
  const set1 = new Set(preSelectedActivities.map(activity => JSON.stringify(activity)));
  const set2 = new Set(selectedActivities.map(activity => JSON.stringify(activity)));

  if (set1.size !== set2.size) {
    return true;
  }

  for (const item of set1) {
    if (!set2.has(item)) {
      return true;
    }
  }
  return false;
};

export const getNewSelectedActivities = (selectedActivities, preSelectedActivities) => {
  return selectedActivities.filter(selectedActivity =>
    !preSelectedActivities.some(preSelectedActivity => preSelectedActivity.code === selectedActivity.code)
  );
}
export const getDiscountedFactor = (response, selectedStaySequence) => {
  if (!response || !selectedStaySequence) {
    return null;
  }

  const data = response[selectedStaySequence];
  if (!data) {
    return null;
  }

  if (data.activityListingData && data.activityListingData.discountedFactor) {
    return data.activityListingData.discountedFactor;
  } else if (data.transferListingData && data.transferListingData.discountedFactor) {
    return data.transferListingData.discountedFactor;
  } else if (data.mealListingData && data.mealListingData.discountedFactor) {
    return data.mealListingData.discountedFactor;
  } else {
    return 1;
  }
}

export const getPackageTotalPriceFromBlackStripData = blackStripData => {
  const {activityPrice, packagePrice, discountedFactor} = blackStripData;
  return (activityPrice + packagePrice ) * discountedFactor;
}

export const logAndTrackUpdatePress = (blackStripData, staySequences) => {
  const staySequenceNames = staySequences.map(item => item.name);
  const selectedActivityTotalPrice = getPackageTotalPriceFromBlackStripData(blackStripData);
  logUpdateEvent(staySequenceNames, selectedActivityTotalPrice);
  logCloseEvent();
}

export const updateSelectedActivitiesInState = (name, activityCode, recheckKey, acmeType, acmeSubType, setSelectedActivities) => {
  setSelectedActivities(currentActivities => {
    const newActivity = {
      name,
      code: activityCode,
      recheckKey,
      acmeType,
      acmeSubType,
    };

    const existingActivityIndex = currentActivities.findIndex(
      (codeWithRecheckKey) => codeWithRecheckKey.code === activityCode,
    );

    if (existingActivityIndex === -1) {
      // If the activity code is not already in the array, add it.
      return [...currentActivities, newActivity];
    } else {
      // If the activity code is already in the array, update its recheckKey.
      const updatedCodesWithRecheckKey = [...currentActivities];
      updatedCodesWithRecheckKey[existingActivityIndex] = newActivity;
      return updatedCodesWithRecheckKey;
    }
  });
}

export const createBlackStripData = (response, staySequences, activeDaySequence , pricingDetail, selectedActivities , preSelectedActivities, day) => {
  const packagePriceMap =  getCombinedPackagePriceMap(response, staySequences);
  const removedPriceMap = getCombinedRemovedPriceMap(response, staySequences);
  const discountedFactor = getDiscountedFactor(response, activeDaySequence);

  let finalPackagePrice = getPackagePrice(pricingDetail);
  const newSelectedActivities = getNewSelectedActivities(selectedActivities, preSelectedActivities);

  const newSelectedActivitiesPrices = newSelectedActivities.map((activity) => {
    const selectedPackagePrice = packagePriceMap[activity.recheckKey] || packagePriceMap[activity.code] || 0;
    return selectedPackagePrice - finalPackagePrice;
  });

  const newRemovedActivitiesPrices = Object.values(removedPriceMap).map((price) => price - finalPackagePrice);

  let selectedActivityTotalPrice = [...newSelectedActivitiesPrices, ...newRemovedActivitiesPrices].reduce((a, b) => a + b, 0);
  const showUpdate = hasNewActivitiesSelected(selectedActivities, preSelectedActivities);

  return {
    day,
    numberOfActivities: selectedActivities.size,
    activityPrice: Math.round(selectedActivityTotalPrice * discountedFactor),
    packagePrice: Math.round(finalPackagePrice * discountedFactor),
    showUpdate,
    discountedFactor,
  };
};

export const getFilterTagBottomMargin = (selectedActivitiesInfoStripState, selectedActivitiesStripData) => {
  // Constants for state checks and calculations
  const INITIAL_BOTTOM_MARGIN = 40;
  const NO_MARGIN = 0;
  const MULTIPLIER_PER_COUNT = 23;

  // Calculate the initial bottom margin based on whether the data is empty
  let filterTagBottomMargin = isEmpty(selectedActivitiesStripData) ? NO_MARGIN : INITIAL_BOTTOM_MARGIN;

  // Add additional margin if the strip is in the expanded state
  if (selectedActivitiesInfoStripState === SELECTED_ACTIVITIES_INFO_STRIP_STATE.EXPANDED) {
    const totalAdditionalMargin = Object.values(selectedActivitiesStripData)
      .reduce((acc, item) => item.count ? acc + item.count * MULTIPLIER_PER_COUNT : acc, 0);

    filterTagBottomMargin += totalAdditionalMargin;
  }
  return filterTagBottomMargin;
}

/**
 * Cleans productMetadata by removing empty searchText fields
 * @param {Object} metadata - The productMetadata object containing filter data for each tab
 * @returns {Object} - Cleaned metadata with empty searchText fields removed
 */
export const cleanProductMetadata = (metadata) => {
  // Handle null/undefined metadata
  if (!metadata || typeof metadata !== 'object') {
    return {};
  }

  const cleaned = {};
  Object.entries(metadata).forEach(([key, value]) => {
    // Handle null/undefined values
    if (!value || typeof value !== 'object') {
      cleaned[key] = value;
      return;
    }

    const cleanedValue = { ...value };
    // Remove searchText if it exists and is empty
    if (cleanedValue.searchText && isEmpty(cleanedValue.searchText)) {
      delete cleanedValue.searchText;
    }
    cleaned[key] = cleanedValue;
  });
  return cleaned;
};

export const  getErrorButtonText = data => {
  try {
    // Validate input
    if (!data || typeof data !== 'object') {
      console.warn('Invalid data provided to getButtonText:', data);
      return 'Reset Filters';
    }

    // Check if filter exists and has values
    const hasFilter = data.filter &&
      Array.isArray(data.filter) &&
      data.filter.length > 0 &&
      data.filter.some(f => f && f.values && Array.isArray(f.values) && f.values.length > 0);

    // Check if sorter exists and has entries
    const hasSorter = data.sorter &&
      Array.isArray(data.sorter) &&
      data.sorter.length > 0;

    // Check if searchText exists and is not empty
    const hasSearchText = data.searchText &&
      typeof data.searchText === 'string' &&
      data.searchText.trim().length > 0;

    // Apply logic based on combinations
    if (hasFilter && hasSorter && hasSearchText) {
      return 'Reset All';
    } else if ((hasFilter || hasSorter) && !hasSearchText) {
      return 'Reset Filters';
    } else if (hasSearchText && !hasFilter && !hasSorter) {
      return 'Clear Search';
    } else if (hasSearchText && (hasFilter || hasSorter)) {
      return 'Reset All';
    } else {
      return 'Reset Filters'; // Default case
    }
  } catch (error) {
    console.error('Error in getButtonText function:', error);
    // Return default safe value in case of any unexpected error
    return 'Reset Filters';
  }
};
