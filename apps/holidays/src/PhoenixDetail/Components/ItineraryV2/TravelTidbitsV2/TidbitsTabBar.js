import React from 'react';
import { TabBar } from 'react-native-tab-view';
import { StyleSheet, Text } from 'react-native';
import { fontStyles } from '../../../../Styles/holidayFonts';
import { marginStyles } from '../../../../Styles/Spacing';
import { holidayColors } from '../../../../Styles/holidayColors';

const TidbitsTabBar = (props) => (
  <TabBar
    {...props}
    indicatorStyle={styles.indicator}
    style={styles.tabBar}
    renderLabel={({ route, focused }) => (
      <Text style={[styles.tab, focused ? styles.tabSelected : {}]}>
        {' '+route.title +' '}
      </Text>
    )}
  />
);

const styles = StyleSheet.create({
  indicator: {
    backgroundColor: holidayColors.primaryBlue,
    height: 4,
  },
  tabBar: {
    backgroundColor: holidayColors.white,
  },
  tab: {
    ...marginStyles.ma8,
    ...fontStyles.labelBaseRegular,
    color: holidayColors.gray,
  },
  tabSelected: {
    ...fontStyles.labelBaseBlack,
    fontFamily: 'Lato-Regular',
    color: holidayColors.black,
  },
});

export default TidbitsTabBar;
