import React from 'react';
import { View, StyleSheet } from 'react-native';
import ShimmerCommon from '@Frontend_Ui_Lib_App/Shimmer';
import { holidayColors } from '../../../../Styles/holidayColors';
import { marginStyles, paddingStyles } from '../../../../Styles/Spacing';
import { holidayBorderRadius } from '../../../../Styles/holidayBorderRadius';

const ActivityCardShimmer = () => {
  return (
    <View style={styles.cardContainer}>
      {/* Activity Image */}
      <View style={[styles.animatedOuter, styles.imageContainer]}>
        <ShimmerCommon
          animatedInnerColor={holidayColors.lightGray4}
          animationRange={holidayColors.lightGray5}
        />
      </View>

      <View style={styles.contentContainer}>
        {/* Activity Title */}
        <View style={[styles.animatedOuter, styles.titleShimmer]}>
          <ShimmerCommon
            animatedInnerColor={holidayColors.lightGray4}
            animationRange={holidayColors.lightGray5}
          />
        </View>

        {/* Activity Description - 2 lines */}
        <View style={[styles.animatedOuter, styles.descriptionShimmer]}>
          <ShimmerCommon
            animatedInnerColor={holidayColors.lightGray4}
            animationRange={holidayColors.lightGray5}
          />
        </View>
        <View style={[styles.animatedOuter, styles.descriptionShimmerSecond]}>
          <ShimmerCommon
            animatedInnerColor={holidayColors.lightGray4}
            animationRange={holidayColors.lightGray5}
          />
        </View>

        {/* Bottom Row with Duration/Time and Price */}
        <View style={styles.bottomRow}>
          <View style={styles.leftSection}>
            {/* Duration */}
            <View style={[styles.animatedOuter, styles.durationShimmer]}>
              <ShimmerCommon
                animatedInnerColor={holidayColors.lightGray4}
                animationRange={holidayColors.lightGray5}
              />
            </View>
            {/* Time */}
            <View style={[styles.animatedOuter, styles.timeShimmer]}>
              <ShimmerCommon
                animatedInnerColor={holidayColors.lightGray4}
                animationRange={holidayColors.lightGray5}
              />
            </View>
          </View>

          <View style={styles.rightSection}>
            {/* Price */}
            <View style={[styles.animatedOuter, styles.priceShimmer]}>
              <ShimmerCommon
                animatedInnerColor={holidayColors.lightGray4}
                animationRange={holidayColors.lightGray5}
              />
            </View>
            {/* Price per person text */}
            <View style={[styles.animatedOuter, styles.priceTextShimmer]}>
              <ShimmerCommon
                animatedInnerColor={holidayColors.lightGray4}
                animationRange={holidayColors.lightGray5}
              />
            </View>
          </View>
        </View>

        {/* Action Row with Know More and Select Button */}
        <View style={styles.actionRow}>
          <View style={[styles.animatedOuter, styles.knowMoreShimmer]}>
            <ShimmerCommon
              animatedInnerColor={holidayColors.lightGray4}
              animationRange={holidayColors.lightGray5}
            />
          </View>
          <View style={[styles.animatedOuter, styles.selectButtonShimmer]}>
            <ShimmerCommon
              animatedInnerColor={holidayColors.lightGray4}
              animationRange={holidayColors.lightGray5}
            />
          </View>
        </View>
      </View>
    </View>
  );
};

const ActivitySearchShimmer = ({ itemCount = 5 }) => {
  return (
    <View style={styles.container}>
      {Array.from({ length: itemCount }, (_, index) => (
        <ActivityCardShimmer key={`shimmer-${index}`} />
      ))}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: holidayColors.lightGray2,
    ...paddingStyles.ph16,
    ...paddingStyles.pv8,
  },
  cardContainer: {
    backgroundColor: holidayColors.white,
    ...marginStyles.mb12,
    ...paddingStyles.pa16,
    ...holidayBorderRadius.borderRadius8,
    shadowColor: holidayColors.black,
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
    height: 246, // Fixed height to prevent overflow
  },
  contentContainer: {
    flex: 1,
  },
  imageContainer: {
    height: 120,
    width: '100%',
    ...marginStyles.mb12,
    ...holidayBorderRadius.borderRadius8,
  },
  titleShimmer: {
    height: 16,
    width: '85%',
    ...marginStyles.mb8,
  },
  descriptionShimmer: {
    height: 12,
    width: '90%',
    ...marginStyles.mb4,
  },
  descriptionShimmerSecond: {
    height: 12,
    width: '70%',
    ...marginStyles.mb12,
  },
  bottomRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    ...marginStyles.mb12,
  },
  leftSection: {
    flex: 1,
  },
  rightSection: {
    alignItems: 'flex-end',
  },
  durationShimmer: {
    height: 12,
    width: 80,
    ...marginStyles.mb2,
  },
  timeShimmer: {
    height: 10,
    width: 50,
  },
  priceShimmer: {
    height: 16,
    width: 60,
    ...marginStyles.mb2,
  },
  priceTextShimmer: {
    height: 10,
    width: 60,
  },
  actionRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  knowMoreShimmer: {
    height: 14,
    width: 70,
  },
  selectButtonShimmer: {
    height: 32,
    width: 80,
    ...holidayBorderRadius.borderRadius6,
  },
  animatedOuter: {
    overflow: 'hidden',
    ...holidayBorderRadius.borderRadius4,
  },
});

export default ActivitySearchShimmer;
