import React, { useState } from 'react';
import { StyleSheet, Text, TouchableOpacity, View, Image } from 'react-native';
import CrossIcon from '@mmt/legacy-assets/src/ic_cross__gray.webp';
import { getPackageDestinations } from 'mobile-holidays-react-native/src/PhoenixDetail/Utils/PhoenixDetailUtils';
import {
  componentImageTypes,
  itineraryUnitTypes,
  packageActions,
} from 'mobile-holidays-react-native/src/PhoenixDetail/DetailConstants';
import {
  getPackagePrice,
  onRemoveActivityPress,
} from 'mobile-holidays-react-native/src/PhoenixDetail/Utils/ActivityOverlayUtils';
import { trackPhoenixDetailLocalClickEvent } from 'mobile-holidays-react-native/src/PhoenixDetail/Utils/PhoenixDetailTracking';
import { HOLIDAY_ROUTE_KEYS, HolidayNavigation } from 'mobile-holidays-react-native/src/Navigation';
import {
  actionSeperator,
  actionStyle,
} from 'mobile-holidays-react-native/src/PhoenixDetail/Components/DayPlan/dayPlanStyles';
import { fontStyles } from 'mobile-holidays-react-native/src/Styles/holidayFonts';
import { holidayColors } from 'mobile-holidays-react-native/src/Styles/holidayColors';
import { holidayBorderRadius } from 'mobile-holidays-react-native/src/Styles/holidayBorderRadius';
import { getIconForItineraryUnitType } from '../../../../Utils/PhoenixDetailUtils';
import { RESIZE_MODE_IMAGE } from '../../../../../HolidayConstants';
import { marginStyles } from '../../../../../Styles/Spacing';

/* Components */
import DayPlanRowHOC from '../../Common/DayPlanRowHOC';
import ItineraryUnitImageWrapper from '../../Common/ItineraryUnitImage';
import { ActivityBasicDetail, ActivityRatePlanInfo } from './Components';
import { DashedSeperator } from '../../Common/Seperators';
import { updateOnActivityDetailPage } from '../../ItineraryV2Utils';
import { isEmpty } from 'lodash';
import { logPhoenixDetailPDTEvents } from 'mobile-holidays-react-native/src/utils/PhoenixDetailPDTTrackingUtils';
import { PDT_EVENT_TYPES } from 'mobile-holidays-react-native/src/utils/HolidayPDTConstants';
import { HOLIDAYS_ACTIVITY_OVERLAY_DETAIL } from 'mobile-holidays-react-native/src/PhoenixDetail/Utils/PheonixDetailPageConstants';
import { holidayNavigationPush } from 'apps/holidays/src/PhoenixDetail/Utils/DetailPageNavigationUtils';
import { Overlay } from '../../../DetailOverlays/OverlayConstants';
import { isMobileClient } from '../../../../../utils/HolidayUtils';

const ActivityRowV2 = ({
  day,
  city,
  // packageDetail,
  itineraryUnit,
  destinationName,
  activityDetail,
  accessRestriction,
  onViewDetailPress,
  removeActivity,
  changeActivity,
  activityReqParams,
  packageDetailDTO,
  onComponentChange,
  lastPageName,
  pricingDetail,
  subtitleData,
  branch,
  roomDetails,
  showOverlay,
  hideOverlays,
  fromPresales = false,
  defaultCollapsedState = '',
}) => {
  const { itineraryUnitType, itineraryUnitSubType, activity, text, cityId } = itineraryUnit || {};
  const addActivityRestricted = accessRestriction ? accessRestriction.addActivityRestricted : false;
  const ratePlanRestricted = accessRestriction ? accessRestriction.ratePlanRestricted : true;
  const { sellableId } = activity || {};
  const activityData = activityDetail.hasOwnProperty(sellableId)
    ? activityDetail[sellableId]
    : undefined;

  const { metaData, imageDetail, additionalData } = activityData || {};
  const {
    duration,
    durationMins,
    name,
    freebie,
    cityCode,
    locked,
    code,
    cardHeaderTexts = [],
    additionalInfo = [],
    ratePlanInfo = {},
  } = metaData || {};
  const [showNotification, setShowNotification] = useState(true);
  const unavailable = activityData?.unavailable;
  const bannerNotification = activityData?.bannerNotification;
  const activityCode = metaData?.code;
  const { images } = imageDetail || {};
  const { pickUpPoint, dropOffPoint, safe, inclusions, shortDescription } = additionalData || {};
  const dynamicPackageId = packageDetailDTO?.dynamicPackageId;

  const onUpdatePress = ({ recheckKey = '' } = {}) => {
    updateOnActivityDetailPage({
      day,
      lastPageName,
      newRecheckKey: recheckKey,
      dynamicPackageId,
      activityReqParams,
      onComponentChange,
      activityMetaData: metaData,
      hideOverlays,
    });
  };

  const onRemovePress = () => {
    logPhoenixDetailPDTEvents({
      actionType: PDT_EVENT_TYPES.buttonClicked,
      value: `remove|${activityCode}`,
      subPageName: HOLIDAYS_ACTIVITY_OVERLAY_DETAIL,
    })
    onRemoveActivityPress(activityReqParams, activityCode, dynamicPackageId, day, onComponentChange);
  };

  const onChangePress = () => {
    HolidayNavigation.push(HOLIDAY_ROUTE_KEYS.ACTIVITY_LISTING, {
      activityReqParams: activityReqParams,
      day,
      dynamicId: dynamicPackageId,
      pricingDetail,
      onComponentChange,
      subtitleData,
      lastPage,
      branch,
      packageDetailDTO,
      roomDetails,
    });
  };

  const captureClickEvents = ({ eventName = '', suffix = '' }) => {
    const value = eventName + suffix;
    logPhoenixDetailPDTEvents({
      actionType: PDT_EVENT_TYPES.buttonClicked,
      value: value.replace(/_/g, '|'),
    });
    trackPhoenixDetailLocalClickEvent({
      eventName,
      suffix,
      prop1: `base:${itineraryUnitTypes.ACTIVITY}`,
    });
  };

  const openActivityDetailPage = (scrollToRatePlan = false, openSlotOverlay = false) => {
    captureClickEvents({
      eventName: scrollToRatePlan ? 'MoreOptions_': openSlotOverlay ? 'Changetimeslot_': `view_`,
      suffix: `${itineraryUnitSubType}_${destinationName}_${day}`,
    });


    const packagePrice = getPackagePrice(pricingDetail);
    const { activityList = [] } = activityReqParams || {};
    const activityObj = activityList.find((x) => {
      const { metaData } = x;
      const { code } = metaData;
      return code === activityCode;
    });
    const { recheckKey, staySequence = 1 } = activityObj || {};

    const blackStripData = {
      day: day,
      numberOfActivities: activityList.length,
      activityPrice: 0,
      packagePrice: pricingDetail?.categoryPrices?.[0]?.discountedPrice || packagePrice,
      showUpdate: false,
      addonPrice:pricingDetail?.categoryPrices?.[0]?.addonsPrice
    };

    const navigationParams = {
      blackStripData: blackStripData,
      packagePrice: packagePrice,
      dynamicPackageId,
      staySequence: staySequence,
      day,
      activityCode: activityCode,
      selectedRecheckKey: recheckKey,
      modifyActivityDetail: () => {},
      onUpdatePress: onUpdatePress,
      selected: true,
      onRemovePress: onRemovePress,
      // onChangePress: changeActivity,
      isActivityDetailFirstPage: true,
      scrollToRatePlan,
      openSlotOverlay,
      showOverlay,
      hideOverlays,
      subtitleData: subtitleData,
      branch: branch,
      packageDetailDTO,
      roomDetails,
      addActivityRestricted,
      ratePlanRestricted,
    }
    if (isMobileClient()) {
      HolidayNavigation.push(HOLIDAY_ROUTE_KEYS.ACTIVITY_DETAIL_V2, navigationParams);
    } else {
      holidayNavigationPush({
        props: navigationParams,
        pageKey: HOLIDAY_ROUTE_KEYS.ACTIVITY_DETAIL_V2,
        overlayKey: Overlay.ACTIVITY_DETAIL_V2,
        showOverlay: showOverlay,
        hideOverlays: hideOverlays,
      })
    }
  };

  const onViewDetail = (value = false) => {
    openActivityDetailPage(false);
  };

  const onMoreOptionsClick = () => {
    openActivityDetailPage(true);
  };

  const onSlotChangeClick = () => {
    openActivityDetailPage(false, true);
  };

  const handleChangeClick = () => {
    changeActivity(itineraryUnit);
  };
  return (
    <DayPlanRowHOC
      day={day}
      city={city}
      unit={itineraryUnit}
      cardHeaderTexts={cardHeaderTexts}
      defaultCollapsedState={defaultCollapsedState}
    >
      <View style={styles.activityRow}>
        <View style={[styles.activityDetails, styles.opacityContainer, unavailable && styles.opacityReduced]}>
          <ActivityBasicDetail
            name={text}
            shortDescription={shortDescription}
            onViewDetail={onViewDetail}
            info={additionalInfo}
          />
          <ItineraryUnitImageWrapper
            imageUrl={images?.[0]?.path || ''}
            resizeMode={RESIZE_MODE_IMAGE.COVER}   // @todo to be discussed
          />
        </View>
        <View style={styles.footer}>
          <View style={styles.footerContainer}>
            {!locked && !freebie && (
              <>
                {!unavailable && (
                  <>
                    <TouchableOpacity onPress={() => removeActivity(code)}>
                      <Text style={actionStyle}>Remove</Text>
                    </TouchableOpacity>
                    <Text style={actionSeperator}>|</Text>
                  </>
                )}
                <TouchableOpacity onPress={handleChangeClick}>
                  <Text style={actionStyle}>Change</Text>
                </TouchableOpacity>
              </>
            )}
          </View>
          {unavailable ? (
            <Text style={styles.unavailableText}>Unavailable</Text>
          ) : (
            <TouchableOpacity onPress={onViewDetail}>
              <Text style={actionStyle}>View Details</Text>
            </TouchableOpacity>
          )}
        </View>
        {!isEmpty(ratePlanInfo) && <DashedSeperator marginSpacing={marginStyles.mv8} />}
          <ActivityRatePlanInfo
            day={day}
            city={city}
            component={itineraryUnitSubType}    
            ratePlanInfo={ratePlanInfo}
            onViewDetail={onMoreOptionsClick}
            onSlotChangeClick={onSlotChangeClick}
            packageDetailDTO={packageDetailDTO}
            unavailable={unavailable}
          />
        {unavailable && bannerNotification && showNotification && (
          <View style={[styles.bannerContainer, { backgroundColor: bannerNotification?.bannerConfig?.backgroundColor }]}>
            <View style={styles.bannerContent}>
              <View style={styles.bannerHeader}>
                <View style={{flex: 1, flexDirection: 'row', alignItems: 'center'}}>
                  <View style={[styles.iconContainer]}>
                    <Image
                      source={{ uri: bannerNotification?.content?.icon?.iconUrl }}
                      style={[styles.iconText]}
                    />
                  </View>
                  <Text style={[styles.notificationType, { color: bannerNotification?.content?.title?.color }]}>
                    {bannerNotification?.content?.title?.text}
                  </Text>
                </View>
                <TouchableOpacity style={styles.crossIconContainer} onPress={() => setShowNotification(false)}>
                  <Image source={CrossIcon} style={[styles.crossIcon, { tintColor: bannerNotification?.bannerConfig?.backgroundColor }]} />
                </TouchableOpacity>
              </View>
              <Text style={[styles.bannerDescription, { color: bannerNotification?.content?.description?.color, marginHorizontal: 32 }]}>
                {bannerNotification?.content?.description?.text}
              </Text>
            </View>
          </View>
        )}
      </View>
    </DayPlanRowHOC>
  );
};

const styles = StyleSheet.create({
  activityRow: {
    // ...dayPlanRowContainerStyle,
  },

  activityDetails: {
    flexDirection: 'row',
  },

  footer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginTop: 12,
  },

  footerContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  unavailableText: {
    ...fontStyles.labelSmallBold,
    color: holidayColors.red,
    marginTop: 'auto',
  },
  bannerContainer: {
    marginTop: 12,
    borderRadius: 8,
    padding: 12,
  },
  bannerContent: {
    flexDirection: 'column',
  },
  bannerHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  iconContainer: {
    width: 24,
    height: 24,
    borderRadius: 12,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 8,
  },
  iconText: {
    width: 24,
    height: 24,
  },
  notificationType: {
    ...fontStyles.labelBaseBold,
  },
  bannerDescription: {
    ...fontStyles.bodySmall,
    lineHeight: 18,
  },
  crossIcon: {
    height: 12,
    width: 12,
    marginVertical: 4,
  },
  crossIconContainer: {
    backgroundColor: holidayColors.lightGray,
    width: 20,
    height: 20,
    borderRadius: 20,
    alignItems: 'center',
    justifyContent: 'center',
  },
  opacityContainer: {
    opacity: 1,
  },
  opacityReduced: {
    opacity: 0.5,
  },
});

export default ActivityRowV2;
