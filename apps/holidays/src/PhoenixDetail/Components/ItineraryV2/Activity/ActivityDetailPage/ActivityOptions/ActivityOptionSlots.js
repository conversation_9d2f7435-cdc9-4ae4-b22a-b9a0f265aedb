import React, { useEffect, useState } from 'react';
import { View, Text, FlatList, StyleSheet, Dimensions } from 'react-native';
import { isEmpty } from 'lodash';
import { connect } from 'react-redux';
import { holidayBorderRadius } from 'mobile-holidays-react-native/src/Styles/holidayBorderRadius';
import { holidayColors } from 'mobile-holidays-react-native/src/Styles/holidayColors';
import { marginStyles, paddingStyles } from 'mobile-holidays-react-native/src/Styles/Spacing';
import { fontStyles } from 'mobile-holidays-react-native/src/Styles/holidayFonts';
import { getPriceText } from '../../../../FlightDetailPage/FlightListing/FlightsUtils';
import { changeSlotTimingOverlayToDefault } from '../../../../../Actions/HolidayDetailActions';
import { isEmptyString } from 'mobile-holidays-react-native/src/utils/HolidayUtils';

/* Components */
import BottomSheetOverlay from '../../../../../../Common/Components/BottomSheetOverlay';
import RadioGroupButton from '../../../../../../Common/Components/RadioButtonGroup';
import PrimaryButton from 'mobile-holidays-react-native/src/Common/Components/Buttons/PrimaryButton';
import { trackPhoenixDetailLocalClickEvent } from '../../../../../Utils/PhoenixDetailTracking';
import { HOLIDAYS_ACTIVITY_OVERLAY_DETAIL } from '../../../../../Utils/PheonixDetailPageConstants';
import { logPhoenixDetailPDTEvents } from 'mobile-holidays-react-native/src/utils/PhoenixDetailPDTTrackingUtils';
import { PDT_EVENT_TYPES } from 'mobile-holidays-react-native/src/utils/HolidayPDTConstants';

const ActivityOptionSlotSOverlay = ({
  day = '',
  city = '',
  slotDetails = {},
  onSlotClick = () => {},
  handleClose,
  getPrice,
  selectedSlotKey = '',
  changeSlotTimingOverlayToDefault,
  openSlotOverlay
}) => {
  const { slots = [] } = slotDetails || {};
  const [defaultSelectedSlot, setDefaultSelectedSlot] = useState(
    slots?.find((slot) => slot.slotRecheckKey === selectedSlotKey) || {},
  );

  if (slots?.length === 0) {
    return null;
  }
  useEffect(() => {
    changeSlotTimingOverlayToDefault();
  }, []);

  const captureClickEvents = ({ eventName = ''}) => {
    logPhoenixDetailPDTEvents({
      actionType: PDT_EVENT_TYPES.buttonClicked,
      value: eventName,
      subPageName: HOLIDAYS_ACTIVITY_OVERLAY_DETAIL,
    })
    trackPhoenixDetailLocalClickEvent({
      eventName: eventName,
      prop1:  HOLIDAYS_ACTIVITY_OVERLAY_DETAIL
    });
  }

  const renderSlotItem = ({ item, index }) => {
    const { costFare, startTime, endTime, slotRecheckKey } = item || {};
    const isSelectedSlot = defaultSelectedSlot?.slotRecheckKey === slotRecheckKey;
    const priceText = getPriceText(getPrice(slotRecheckKey, 'slot'));
    const handleSlotClick = () => {
      captureClickEvents({
        eventName: `select_timeSlot_${day}_${city}`,
      });
      setDefaultSelectedSlot(item);
    };

    return (
      <RadioGroupButton
        selected={isSelectedSlot}
        optionText={`${startTime}-${endTime}`}
        viewContainerStyle={styles.viewContainerStyle}
        handleClick={handleSlotClick}
        textLineCount={1}
        optionTextStyle={{
          ...fontStyles.labelBaseBlack,
          color: holidayColors.black,
          flex: 1,
        }}
        btnContainerStyles={[
          styles.btnContainerStyle,
          { borderColor: isSelectedSlot ? holidayColors.primaryBlue : holidayColors.grayBorder },
        ]}
        optionContainerStyle={styles.optionContainerStyle}
        children={!isEmptyString(priceText?.trim()) && (
          <View style={styles.priceContainer}>
            <Text style={styles.priceText} numberOfLines={1}>
              {priceText}
            </Text>
            <Text style={styles.pricePerson}>per person</Text>
          </View>
        )}
      >
      </RadioGroupButton>
    );
  };

  const handleConfirm = () => {
    captureClickEvents({
      eventName: `confirm_timeSlot_${day}_${city}`,
    })
    onSlotClick(defaultSelectedSlot?.slotRecheckKey);
    handleClose();
  };

  const toggleModal = () => {
    handleClose();
  };

  return (
    <BottomSheetOverlay
      title={'Confirm Time Slot'}
      toggleModal={toggleModal}
      containerStyles={styles.childStyle}
      headingContainerStyles={styles.headingContainerStyles}
      visible={openSlotOverlay}
    >
      <View style={styles.listWrapper}>
      <FlatList
        data={slots}
        renderItem={renderSlotItem}
        numColumns={2}
        showsVerticalScrollIndicator={false}
        contentContainerStyle={{
          ...marginStyles.mt16,
          ...paddingStyles.pb20,
        }}
      />
      </View>
      <PrimaryButton
        buttonText={'CONFIRM'}
        handleClick={handleConfirm}
        btnContainerStyles={{
          ...paddingStyles.pv16,
          ...marginStyles.mt16,
        }}
      />
    </BottomSheetOverlay>
  );
};

const styles = StyleSheet.create({
  viewContainerStyle: {
    flex: 0.5,
  },
  childStyle: {
    ...paddingStyles.pa16
  },
  headingContainerStyles: {
    paddingBottom: 12
  },
  listWrapper: {
    maxHeight: Dimensions.get('window').height - 400,
  },
  btnContainerStyle: {
    borderWidth: 1,
    flex: 1,
    flexDirection: 'column',
    ...paddingStyles.pv8,
    ...paddingStyles.ph10,
    ...marginStyles.ma6,
    ...holidayBorderRadius.borderRadius16,
  },
  priceContainer: {
    ...paddingStyles.pt4,
    ...marginStyles.ml30,
    flex: 1,
  },
  priceText: {
    ...fontStyles.labelBaseBlack,
    color: holidayColors.gray,
    flex: 1,
  },
  pricePerson: {
    ...fontStyles.labelSmallRegular,
    color: holidayColors.lightGray,
  },
  optionContainerStyle: {
    flexDirection: 'column'
  }
});

const mapDispatchToProps = {
  changeSlotTimingOverlayToDefault,
};
export default connect(null, mapDispatchToProps)(ActivityOptionSlotSOverlay);
