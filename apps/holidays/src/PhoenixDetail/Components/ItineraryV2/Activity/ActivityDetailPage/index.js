import React, { useEffect, useState } from 'react';
import { View, BackHandler, StatusBar, Platform, StyleSheet } from 'react-native';
import { HARDWARE_BACK_PRESS } from 'mobile-holidays-react-native/src/SearchWidget/SearchWidgetConstants';
import { paddingStyles, marginStyles } from '../../../../../Styles/Spacing';
import { trackPhoenixDetailLocalClickEvent } from '../../../../Utils/PhoenixDetailTracking';
import { fetchActivityDetailsResponse } from '../../../../../utils/HolidayNetworkUtils';
import { HOLIDAYS_ACTIVITY_OVERLAY_DETAIL } from '../../../../Utils/PheonixDetailPageConstants';
import { HolidayNavigation } from '../../../../../Navigation';
import { createTravellerObjForLoader } from '../../../../Utils/HolidayDetailUtils';

/* Actions */
import { openSlotTimingOverlay } from '../../../../Actions/HolidayDetailActions';

/* Components */
import HolidayDataHolder from '../../../../../utils/HolidayDataHolder';
import { SUB_PAGE_NAMES } from '../../../../../HolidayConstants';
import ActivityDetailPageComponent from './DetailPageComponent';
import GenericErrorPage from '../../../../../Common/Components/ErrorPage/GenericErrorPage';
import PrimaryButton from '../../../../../Common/Components/Buttons/PrimaryButton';
import HolidayDetailLoader from '../../../HolidayDetailLoader';
import { connect } from 'react-redux';
import { holidayColors } from '../../../../../Styles/holidayColors';
import { logPhoenixDetailPDTEvents } from 'mobile-holidays-react-native/src/utils/PhoenixDetailPDTTrackingUtils';
import { PDT_EVENT_TYPES } from 'mobile-holidays-react-native/src/utils/HolidayPDTConstants';
import { holidayNavigationPop } from '../../../../Utils/DetailPageNavigationUtils';
import { Overlay } from '../../../DetailOverlays/OverlayConstants';
import useBackHandler from '../../../../../hooks/useBackHandler';
import { getSubPageName } from '../../../ActivityOverlay/ActivityUtils';
import { isMobileClient } from '../../../../../utils/HolidayUtils';

const PAGE_STATES = {
  LOADING: 'LOADING',
  SUCCESS: 'SUCCESS',
  ERROR: 'ERROR',
};
const ActivityDetailPage = (props) => {
  const {
    selectedRecheckKey,
    packagePrice = '',
    openSlotOverlay: openSlotOverlayProp,
    openSlotTimingOverlay,
    hideOverlays,
  } = props;

  const [viewState, setViewState] = useState(PAGE_STATES.LOADING);
  const [activity, setActivity] = useState(null);
  const [selectedRecheckKeyState, setSelectedReCheckKeyState] = useState(selectedRecheckKey);
  const [openSlotOverlay, setOpenSlotOverlay] = useState(false);
  const [horizontalLoader, setHorizontalLoader] = useState(false);
  const [backClickCount, setBackClickCount] = useState(0);

  useEffect(() => {
    hitDetailPageApi();
    HolidayDataHolder.getInstance().setCurrentPage('phoenixActivityDetail');
    return () => {
      HolidayDataHolder.getInstance().clearSubPageName()
    };
  }, []);
  useBackHandler(onBackButtonPress);

  useEffect(() => {
    if (openSlotOverlayProp) {
      async function sendActionToOpenSlotOverlay() {
        await openSlotTimingOverlay();
      }
      setTimeout(() => {
        sendActionToOpenSlotOverlay();
      }, 500);
    }
  }, [openSlotOverlayProp]);


  const captureClickEvents = ({ eventName = '' }) => {
    const {metaData} =activity || {}
    const { acmeType} =metaData || {}
    logPhoenixDetailPDTEvents({
      actionType: PDT_EVENT_TYPES.buttonClicked,
      value: eventName,
      subPageName: getSubPageName(acmeType),
    });
    trackPhoenixDetailLocalClickEvent({
      eventName,
      prop1: HOLIDAYS_ACTIVITY_OVERLAY_DETAIL,
    });
  };
  const onBackButtonPress = () => {
    captureClickEvents({
      eventName: 'back',
    });
    if (isMobileClient()) {
      HolidayNavigation.pop();
    } else {  
      holidayNavigationPop({
        overlayKeys: [Overlay.ACTIVITY_DETAIL_V2],
        hideOverlays,
      });
    }
    return true;
  };

  const hitDetailPageApi = async () => {
    const { dynamicPackageId, activityCode, day, staySequence } = props;

    const responseBody = await fetchActivityDetailsResponse({
      dynamicPackageId: dynamicPackageId,
      staySequence: staySequence,
      day: day,
      activityCode: activityCode, // 'ACM5533', // activityCode
      selectedRecheckKey: selectedRecheckKey,
    });

    if (responseBody && responseBody.success && responseBody.activity) {
      const { activity } = responseBody;
      setActivity(activity);
      setViewState(PAGE_STATES.SUCCESS);
      const { recheckKey, metaData } = activity;
      const { availableOptionsCount, hasSlot } = metaData || {};
      if (selectedRecheckKey && props.selected && availableOptionsCount === 1 && !hasSlot) {
        setSelectedReCheckKeyState(recheckKey);
      }
    } else {
      setViewState(PAGE_STATES.ERROR);
    }
  };

  const renderLoader = () => {
    const { packageDetailDTO, roomDetails } = props;
    return (
      <HolidayDetailLoader
        departureCity={packageDetailDTO.depCityName}
        departureDate={packageDetailDTO.departureDate}
        duration={packageDetailDTO.duration}
        travellerObj={createTravellerObjForLoader(roomDetails)}
        changeAction={false}
      />
    );
  };

  const renderError = () => {
    return (
      <View style={styles.genericErrorPageStyle}>
      <GenericErrorPage
        title="Oops! Page not found"
        description="We can’t seem to find the page you’re looking for"
        pageHeader={'Add Activity'}
        onBackPress={onBackButtonPress}
      >
        <PrimaryButton
          buttonText={'Back To Previous Page'}
          btnContainerStyles={paddingStyles.ph10}
          handleClick={onBackButtonPress}
        />
      </GenericErrorPage>
      </View>
    );
  };

  const renderContent = () => {
    return (
      <ActivityDetailPageComponent
        activity={activity}
        onBackPress={onBackButtonPress}
        packagePrice={packagePrice}
        selectedRecheckKeyState={selectedRecheckKeyState}
        setSelectedReCheckKeyState={setSelectedReCheckKeyState}
        setHorizontalLoader={setHorizontalLoader}
        hideOverlays={hideOverlays}
        {...props}
      />
    );
  };

  return (
    <View style={{ flex: 1 }}>
      {viewState === PAGE_STATES.LOADING && renderLoader()}
      {viewState === PAGE_STATES.SUCCESS && renderContent()}
      {viewState === PAGE_STATES.ERROR && renderError()}
    </View>
  );
};


const mapDispatchToProps = {
  openSlotTimingOverlay,
};

const styles = StyleSheet.create({
  genericErrorPageStyle:{
   flex:1,
  },
});

export default connect(null, mapDispatchToProps)(ActivityDetailPage);
