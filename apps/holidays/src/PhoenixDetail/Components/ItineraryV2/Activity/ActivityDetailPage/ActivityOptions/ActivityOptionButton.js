import React from 'react';
import { TouchableOpacity, Text, Image, StyleSheet, View } from 'react-native';
import HolidayImageHolder from '../../../../../../Common/Components/HolidayImageHolder';
import { holidayBorderRadius } from '../../../../../../Styles/holidayBorderRadius';
import { holidayColors } from '../../../../../../Styles/holidayColors';
import { fontStyles } from '../../../../../../Styles/holidayFonts';
import { marginStyles, paddingStyles } from '../../../../../../Styles/Spacing';
import { IMAGE_ICON_KEYS, getOpitimsedImageUrl } from '../../../../../../Common/Components/HolidayImageUrls';

const ActivityOptionButton = ({
  buttonText,
  handleClick,
  btnContainerStyles,
  btnTextStyles,
  isActive,
  index,
}) => {

  return (
    <TouchableOpacity onPress={handleClick} activeOpacity={1}>
      <View style={[styles.btnContainer, btnContainerStyles, isActive && styles.activeButton]}>
        {isActive ? (
          <HolidayImageHolder
            imageUrl={getOpitimsedImageUrl(IMAGE_ICON_KEYS.TICK)}
            style={styles.iconStyle}
          />
        ) : null}
        <Text style={[styles.btnText, btnTextStyles]}>{isActive ? 'SELECTED' : buttonText?.toUpperCase()}</Text>
      </View>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  btnContainer: {
    borderWidth: 1,
    ...paddingStyles.ph14,
    ...paddingStyles.pv4,
    backgroundColor: holidayColors.white,
    ...holidayBorderRadius.borderRadius8,
    borderColor: holidayColors.primaryBlue,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  btnText: {
    textAlign: 'center',
    color: holidayColors.primaryBlue,
    ...fontStyles.labelSmallBlack,
  },
  iconStyle: {
    width: 12,
    height: 12,
    ...marginStyles.mr4,
    tintColor: holidayColors.primaryBlue,
  },
  activeButton: {
    backgroundColor: '#CCE8FF',
  },
});
export default ActivityOptionButton;
