import React, { useMemo } from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { isEmpty } from 'lodash';
import { fontStyles } from '../../../../../Styles/holidayFonts';
import { paddingStyles } from '../../../../../Styles/Spacing';
import { holidayColors } from 'mobile-holidays-react-native/src/Styles/holidayColors';
import { rupeeFormatterUtils } from '../../../../../utils/HolidayUtils';
import { getPriceText } from '../../../FlightDetailPage/FlightListing/FlightsUtils';

/* Components */
import PrimaryButton from '../../../../../Common/Components/Buttons/PrimaryButton';
import SelectedTagBlackFooter from '../../../../../Common/Components/Tags/SelectedTagBlackFooter';

const PriceFooter = ({
  numberOfActivities,
  activityPrice,
  packagePrice,
  showUpdate,
  onUpdatePress,
  isActivityOptionSelected = false,
  addonPrice = 0
}) => {
  const finalPackagePrice = activityPrice + packagePrice + addonPrice

  if (!isActivityOptionSelected) {
    return null;
  }
  // if (numberOfActivities === 0 && !showUpdate) {
  //   return [];
  // }

  const handleClick = () => {
    if (showUpdate) {
      onUpdatePress();
    }
  };

  return (
    <View style={styles.footerContainer}>
      <View style={styles.flexOne}>
        <View style={styles.makeRow}>
          <Text style={styles.priceText}>{getPriceText(activityPrice)}</Text>
          <Text style={styles.descriptionText}> /person</Text>
        </View>
        <Text style={styles.descriptionText}>
          Total Price {rupeeFormatterUtils(finalPackagePrice)}{' '}
        </Text>
      </View>
      {showUpdate ? (
        <PrimaryButton
          buttonText={'UPDATE'}
          btnContainerStyles={styles.buttonPadding}
          handleClick={handleClick}
          activeOpacity={showUpdate ? 0.2 : 1}
        />
      ) : (
        <SelectedTagBlackFooter />
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  footerContainer: {
    backgroundColor: '#282828',
    ...paddingStyles.ph12,
    ...paddingStyles.pv10,
    bottom: 0,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  priceText: {
    ...fontStyles.labelLargeBlack,
    color: holidayColors.white,
  },
  descriptionText: {
    ...fontStyles.labelBaseRegular,
    color: holidayColors.white,
  },
  buttonPadding: { paddingHorizontal: 24 },
  flexOne: { flex: 1 },
  makeRow: { flexDirection: 'row' },
});

export default PriceFooter;
