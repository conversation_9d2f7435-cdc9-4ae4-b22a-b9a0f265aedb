import React, { useEffect, useMemo, useState } from 'react';
import { connect } from 'react-redux';
import { isEmpty } from 'lodash';
import { StyleSheet, Text, TouchableOpacity, View } from 'react-native';
import { holidayColors } from 'mobile-holidays-react-native/src/Styles/holidayColors';
import { fontStyles } from 'mobile-holidays-react-native/src/Styles/holidayFonts';
import { marginStyles, paddingStyles } from 'mobile-holidays-react-native/src/Styles/Spacing';
import { HOLIDAY_ROUTE_KEYS, HolidayNavigation } from 'mobile-holidays-react-native/src/Navigation';
import { checkAndChangePriceToZero } from '../../../../../Utils/PhoenixDetailUtils';
import { getPriceDiff } from '../../../../../../utils/HolidayUtils';
import { getPriceText } from '../../../../FlightDetailPage/FlightListing/FlightsUtils';
import { trackPhoenixDetailLocalClickEvent } from '../../../../../Utils/PhoenixDetailTracking';
import { HOLIDAYS_ACTIVITY_OVERLAY_DETAIL } from '../../../../../Utils/PheonixDetailPageConstants';

/* Components */
import ItineraryUnitInformation, { INFO_SIZE } from '../../../Common/ItineraryUnitInformation';
import ActivityOptionButton from './ActivityOptionButton';
import { ActivityRatePlanSlotInfo } from '../../ActivityRow/Components';
import { DashedSeperator } from '../../../Common/Seperators';
import ActivityOptionSlotsOverlay from './ActivityOptionSlots';
import Tag from '../../ActivityListingPage/Tag';
import { logPhoenixDetailPDTEvents } from 'mobile-holidays-react-native/src/utils/PhoenixDetailPDTTrackingUtils';
import { PDT_EVENT_TYPES } from 'mobile-holidays-react-native/src/utils/HolidayPDTConstants';
import { holidayNavigationPop, holidayNavigationPush } from 'apps/holidays/src/PhoenixDetail/Utils/DetailPageNavigationUtils';
import { Overlay } from '../../../../DetailOverlays/OverlayConstants';
import { getSubPageName } from '../../../../ActivityOverlay/ActivityUtils';
import { isMobileClient } from '../../../../../../utils/HolidayUtils';

const ActivityOptionItem = ({
  day,
  city = '',
  activity,
  optionItem,
  index,
  selectedRecheckKeyState,
  ratePlanResctricted,
  updateRecheckKey,
  packagePrice,
  subtitleData = [],
  openSlotOverlayProp = false,
  packageDetailDTO = {},
  onRemoveCtaPress = null,
  showOverlay,
  hideOverlays,
  acmeType
}) => {
  const {
    name,
    description,
    otherInfos = [],
    activityTourGrades = [],
    activityTourGrade = [],
    recheckKey = '',
    slotDetails = {},
    label = {},
    unavailable = false,
  } = optionItem || {};
  const {texts = [''], textColorCode = holidayColors.black, borderColorCode = holidayColors.black} = label || {};
  const tagText = texts[0];
  const { slots = [] } = slotDetails || {};
  const { metaData = {} } = activity || {}
  const defaultSelectedSlot = useMemo(() => {
    const foundSlot =
      slots && slots.find((slot) => slot.slotRecheckKey === selectedRecheckKeyState);
    return foundSlot ? selectedRecheckKeyState : recheckKey;
  }, [selectedRecheckKeyState, recheckKey, slots]);

  const [openSlotOverlay, setOpenSlotOverlay] = useState(false);

  const showSelected = useMemo(() => {
    if (selectedRecheckKeyState === recheckKey) {
      return true;
    }
    if (slots?.some((slot) => slot?.slotRecheckKey === selectedRecheckKeyState)) {
      return true;
    }
    return false;
  }, [selectedRecheckKeyState, recheckKey, slots]);

  useEffect(() => {
    if (openSlotOverlayProp && (showSelected || optionItem?.selected)) {
      setOpenSlotOverlay(true);
    }
  }, [openSlotOverlayProp]);

  const [selectedSlotKey, setSlotKey] = useState(defaultSelectedSlot);
  const showDefault = (showSelected && !ratePlanResctricted) || !!ratePlanResctricted;
  const showPrice = showSelected && !ratePlanResctricted;

  const captureClickEvents = ({ eventName = '', suffix = ''}) => {
    const value = eventName + suffix;
    logPhoenixDetailPDTEvents({
      actionType: PDT_EVENT_TYPES.buttonClicked,
      value: value.replace(/_/g, '|'),
      subPageName: getSubPageName(acmeType),
    })
    trackPhoenixDetailLocalClickEvent({
      eventName,
      prop1: HOLIDAYS_ACTIVITY_OVERLAY_DETAIL,
    })
  }
  const openRatePlanPage = () => {
    const component = activity?.metaData?.acmeType || 'ACTIVITY';
    captureClickEvents({
      eventName: `view_moreDetails_${component}_rateplan`,
    });
    if (isMobileClient()) {
      HolidayNavigation.push(HOLIDAY_ROUTE_KEYS.ACTIVITY_DETAIL_RATE_PLAN_V2, {
        ratePlanInfo: optionItem,
        subtitleData,
        activityMetaData: activity?.metaData || {},
      });
    } else {
      holidayNavigationPush({
        pageKey: HOLIDAY_ROUTE_KEYS.ACTIVITY_DETAIL_RATE_PLAN_V2,
        overlayKey: Overlay.ACTIVITY_DETAIL_RATE_PLAN_V2,
        props: {
          ratePlanInfo: optionItem,
          subtitleData,
          activityMetaData: activity?.metaData || {},
          showOverlay,
          hideOverlays
        },
        showOverlay,
        hideOverlays,    
      })
    }
  };

  const updateSlot = (key) => {
    setSlotKey(key);
    // if (showSelected) {
      updateRecheckKey(key);
    // }
  };

  const getPrice = (key) => {
    const { packagePriceMap = {}, discountedFactor } = activity || {};
    return checkAndChangePriceToZero(
      getPriceDiff(packagePrice, packagePriceMap[key], discountedFactor),
    );
  };

  const toggleSlotOverlay = () => {
    setOpenSlotOverlay(!openSlotOverlay);
  };
  const handleSlotChange = () => {
    captureClickEvents({
      eventName: 'Changetimeslot_',
      suffix: `${metaData?.acmeType}_${city}_${day}`,
    });
    toggleSlotOverlay();
  };

  const handleSelectButton = () => {
    if(showSelected) {
      onRemoveCtaPress();
    }
    else {
      updateRecheckKey(selectedSlotKey)
    }
  }
  return (
    <React.Fragment key={index}>
      <View style={[isEmpty(slotDetails) ? marginStyles.mb12 : {},unavailable && styles.opacity]}>
        <View style={styles.tagContainer}>
          <Tag textColor={borderColorCode} borderColor={textColorCode} labelText={tagText} />
        </View>
        <Text style={styles.activityTitle}>{name}</Text>
        <ActivityRatePlanSlotInfo
          component={metaData?.acmeType}
          day={day}
          city={city}
          slotDetails={slotDetails}
          onSlotChangeClick={handleSlotChange}
          selectedSlotKey={selectedSlotKey}
          packageDetailDTO={packageDetailDTO}
        />
      </View>
      {!isEmpty(slotDetails) && <DashedSeperator marginSpacing={marginStyles.mv8} />}
      <View style={[styles.listingContainer]}>
        <View style={[styles.flexOne,unavailable && styles.opacity]}>
          <ItineraryUnitInformation info={otherInfos} size={INFO_SIZE.SMALL} />
          <TouchableOpacity onPress={openRatePlanPage} style={{ marginTop: 'auto'}}>
            <Text style={[styles.moreDetailsText, marginStyles.mt4]}>More Details</Text>
          </TouchableOpacity>
        </View>
        {!unavailable ? (
          <View style={styles.priceContainer}>
            {!showSelected && (
              <>
                <Text style={styles.priceText}>{getPriceText(getPrice(selectedSlotKey))}</Text>
                <Text style={[styles.priceSubText, marginStyles.mb8]}>price/person</Text>
              </>
            )}
            <ActivityOptionButton
              buttonText="Select"
              handleClick={handleSelectButton}
              isActive={showSelected}
              index={index}
            />
          </View>
        ) : (
          <Text style={styles.unavailableText}>Unavailable</Text>
        )}
      </View>
      {openSlotOverlay && (
        <ActivityOptionSlotsOverlay
          city={city}
          day={day}
          slotDetails={slotDetails}
          onSlotClick={updateSlot}
          handleClose={toggleSlotOverlay}
          getPrice={getPrice}
          selectedSlotKey={selectedSlotKey}
          openSlotOverlay={openSlotOverlay}
        />
      )}
    </React.Fragment>
  );
};

const styles = StyleSheet.create({
  activityTitle: {
    ...fontStyles.labelBaseBlack,
    color: holidayColors.gray,
  },
  activityDescription: {
    ...fontStyles.labelSmallRegular,
    color: holidayColors.gray,
    lineHeight: 16,
  },
  listingContainer: {
    flexDirection: 'row',
    alignItems: 'flex-end',
    justifyContent: 'space-between',
  },
  noBorderBottom: {
    borderBottomWidth: 0,
    ...paddingStyles.pb0,
    ...marginStyles.mb0,
  },
  contentWrapper: {
    flexDirection: 'row',
    alignItems: 'flex-start',
  },
  priceContainer: {
    alignItems: 'flex-end',
  },
  priceText: {
    ...fontStyles.labelMediumBlack,
    color: holidayColors.black,
  },
  priceSubText: {
    ...fontStyles.labelSmallRegular,
    color: holidayColors.gray,
  },
  moreDetailsText: {
    ...fontStyles.labelSmallBold,
    color: holidayColors.primaryBlue,
  },
  flexOne: { flex: 1 },
  tagContainer: {
    ...marginStyles.mb6,
  },
  unavailableText: {
    ...fontStyles.labelSmallBold,
    color: holidayColors.red,
    marginTop: 'auto',
  },
  opacity: {
    opacity: 0.5,
  },
});

const mapStateToProps = (state) => {
  return {
    openSlotOverlayProp: state.holidaysDetail.openSlotTimingOverlay,
  };
};
export default connect(mapStateToProps, null)(ActivityOptionItem);
