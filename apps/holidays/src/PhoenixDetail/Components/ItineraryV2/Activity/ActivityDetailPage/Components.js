import React from 'react';
import { View, Text, Image, StyleSheet } from 'react-native';
import HolidayImageHolder from '../../../../../Common/Components/HolidayImageHolder';
import { holidayBorderRadius } from '../../../../../Styles/holidayBorderRadius';
import { holidayColors } from '../../../../../Styles/holidayColors';
import { fontStyles } from '../../../../../Styles/holidayFonts';
import { marginStyles, paddingStyles } from '../../../../../Styles/Spacing';

/* Components */
import ItineraryUnitInformation, {
  INFO_SEPERATOR,
  INFO_SIZE,
} from '../../Common/ItineraryUnitInformation';
import { actionStyle } from '../../../DayPlan/dayPlanStyles';
import { RESIZE_MODE_IMAGE } from 'mobile-holidays-react-native/src/HolidayConstants';
import { capitalizeText } from '../../../../../../src/utils/textTransformUtil';

export const BaseCard = ({
  headerText,
  subHeaderText,
  children,
  bgcolor = null,
  textColor = null,
}) => {
  return (
    <View
      style={[baseCardStyles.cardContainer, { backgroundColor: bgcolor || holidayColors.white }]}
    >
      {headerText && (
        <View style={baseCardStyles.headlineWrapper}>
          <Text style={[baseCardStyles.headlineText, { color: textColor || holidayColors.black }]}>
            {capitalizeText(headerText)}
          </Text>
          {subHeaderText && <Text style={baseCardStyles.subHeaderText}>{subHeaderText}</Text>}
        </View>
      )}
      <View style={baseCardStyles.contentWrapper}>{children}</View>
    </View>
  );
};

export const ActivityPhotos = ({ data }) => {
  return (
    <View style={activityPhotoStyles.imageContainer}>
      {data.map((photo, index) => {
        return (
          <Image
            key={index}
            source={{ uri: photo.path }}
            style={[
              activityPhotoStyles.imageStyle,
              index === 0 || index === data.length - 1 ? { width: '100%' } : { width: '48%' },
              index % 3 === 1 && index !== data.length - 1 ? { marginRight: '4%' } : null,
            ]}
            resizeMode={RESIZE_MODE_IMAGE.COVER}
          />
        );
      })}
    </View>
  );
};

const baseCardStyles = StyleSheet.create({
  cardContainer: {
    ...holidayBorderRadius.borderRadius16,
    backgroundColor: holidayColors.white,
    zIndex: -1,
    ...marginStyles.mb16,
  },
  whiteBg: {
    backgroundColor: holidayColors.white,
  },
  fadedYellow: {
    backgroundColor: holidayColors.fadedYellow,
  },
  headlineWrapper: {
    borderBottomWidth: 1,
    borderBottomColor: holidayColors.grayBorder,
    ...paddingStyles.ph16,
    ...paddingStyles.pt16,
    ...paddingStyles.pb12,
  },
  headlineText: {
    ...fontStyles.labelMediumBlack,
  },
  colorGray: {
    color: holidayColors.gray,
  },
  colorYellow: {
    color: holidayColors.yellow,
  },
  subHeaderText: {
    ...fontStyles.labelSmallRegular,
    color: holidayColors.gray,
    ...marginStyles.mt2,
  },
  contentWrapper: {
    ...paddingStyles.ph12,
    ...paddingStyles.pt12,
    ...paddingStyles.pb16,
  },
});


const activityPhotoStyles = StyleSheet.create({
  imageContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
    flex: 1,
  },
  imageStyle: {
    height: 128,
    // width: 100,
    borderRadius: 16,
    marginBottom: 10,
  },
});
