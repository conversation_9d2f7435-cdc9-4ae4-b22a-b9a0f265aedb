import React, { useState } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, FlatList } from 'react-native';
import { holidayColors } from 'mobile-holidays-react-native/src/Styles/holidayColors';
import { fontStyles } from 'mobile-holidays-react-native/src/Styles/holidayFonts';
import { marginStyles, paddingStyles } from 'mobile-holidays-react-native/src/Styles/Spacing';
import { HolidayNavigation, HOLIDAY_ROUTE_KEYS } from 'mobile-holidays-react-native/src/Navigation';
import { checkAndChangePriceToZero } from '../../../../../Utils/PhoenixDetailUtils';
import { getPriceDiff } from '../../../../../../utils/HolidayUtils';
import { trackPhoenixDetailLocalClickEvent } from '../../../../../Utils/PhoenixDetailTracking';
import { HOLIDAYS_ACTIVITY_OVERLAY_DETAIL } from '../../../../../Utils/PheonixDetailPageConstants';
import { PRESALES_MIMA_DETAIL_PAGE } from 'mobile-holidays-react-native/src/MimaPreSales/utils/PreSalesMimaConstants';
import { getPreselectedActivity } from '../../../ItineraryV2Utils';

/* Components */
import ActivityOptionItem from './ActivityOptionItem';
import { logPhoenixDetailPDTEvents } from 'mobile-holidays-react-native/src/utils/PhoenixDetailPDTTrackingUtils';
import { PDT_EVENT_TYPES } from 'mobile-holidays-react-native/src/utils/HolidayPDTConstants';
import { getSubPageName } from '../../../../ActivityOverlay/ActivityUtils';

const ActivityOptions = ({ data, activity, packagePrice, ...rest }) => {
  const {
    setSelectedReCheckKeyState = () => {},
    selectedRecheckKeyState,
    updateBlackStripData,
    setHorizontalLoader,
    packageDetailDTO,
    selectedRecheckKey = null,
    acmeType
  } = rest || {};

  const activityOptionsData =
    packageDetailDTO?.pageName === PRESALES_MIMA_DETAIL_PAGE
      ? activity?.activityOptions?.filter((option) => option?.selected)
      : activity?.activityOptions || {};

  const handleModifyActivity = (add, activityCode, recheckKey, name, price, updateBlackStrip) => {
    const { modifyActivityDetail, modifyActivityTrue = false } = rest;
    setHorizontalLoader(true);
    if (modifyActivityTrue) {
      modifyActivityDetail(add, activityCode, recheckKey, name, price)
        .then(() => {
          updateBlackStrip();
          setHorizontalLoader(false);
          setSelectedReCheckKeyState(recheckKey);
        })
        .catch(() => {
          showShortToast('Something went wrong');
          setHorizontalLoader(false);
        });
    } else {
      modifyActivityDetail(add, activityCode, recheckKey, name, price);
      updateBlackStrip();
      setHorizontalLoader(false);
      setSelectedReCheckKeyState(recheckKey);
    }
  };

  const captureClickEvents = ({ eventName = '' }) => {
    logPhoenixDetailPDTEvents({
      actionType: PDT_EVENT_TYPES.buttonClicked,
      value: eventName.replace(/_/g, '|'),
      subPageName:getSubPageName(acmeType),
    });
    trackPhoenixDetailLocalClickEvent({
      eventName,
      prop1: HOLIDAYS_ACTIVITY_OVERLAY_DETAIL,
    });
  };
  const updateRecheckKey = (newReCheckKey) => {
    const { metaData, packagePriceMap, discountedFactor, recheckKey } = activity || {};
    const { code, name } = metaData || {};
    captureClickEvents({
      eventName: 'change_tourgrade',
    });
    
    // get preselected activity - by checking selected key in data and compare current selected price with it
    const preSelectedActivity = getPreselectedActivity({ selectedRecheckKey, activityOptions: activity?.activityOptions })?.recheckKey;
    const preSelectedActivityUnavailable = getPreselectedActivity({ selectedRecheckKey, activityOptions: activity?.activityOptions })?.unavailable;

    if (preSelectedActivity) {
      let price1 = checkAndChangePriceToZero(
        getPriceDiff(packagePrice, packagePriceMap[preSelectedActivity], discountedFactor),
      );
      let price2 = null;
      if (newReCheckKey) {
        price2 = checkAndChangePriceToZero(
          getPriceDiff(packagePrice, packagePriceMap[newReCheckKey], discountedFactor),
        );
        // update blackStrip data
        if(preSelectedActivityUnavailable){
          price1 = 0;
        }
        let updateBlackStrip = () => updateBlackStripData(0, price2 - price1, newReCheckKey);
        handleModifyActivity(true, code, newReCheckKey, name, price2 - price1, updateBlackStrip);
      } else {
        price2 = 0;
        // update blackStrip data
        let updateBlackStrip = () => updateBlackStripData(-1, price2 - price1, newReCheckKey);
        handleModifyActivity(false, code, newReCheckKey, name, price1, updateBlackStrip);
      }
    } else {
      if (newReCheckKey) {
        const price = checkAndChangePriceToZero(
          getPriceDiff(packagePrice, packagePriceMap[newReCheckKey], discountedFactor),
        );
        // update blackStrip data
        let updateBlackStrip = () => updateBlackStripData(1, price, newReCheckKey);
        handleModifyActivity(true, code, newReCheckKey, name, price, updateBlackStrip);
      } else {
        const price = checkAndChangePriceToZero(
          getPriceDiff(packagePrice, packagePriceMap[reCheckKey], discountedFactor),
        );
        // update blackStrip data
        let updateBlackStrip = () => updateBlackStripData(0, 0, newReCheckKey);
        handleModifyActivity(false, code, newReCheckKey, name, price, updateBlackStrip);
      }
    }
  };

  const renderActivityOptions = ({ item, index }) => {
    return (
      <ActivityOptionItem
        activity={activity}
        optionItem={item}
        index={index}
        updateRecheckKey={updateRecheckKey}
        packagePrice={packagePrice}
        actionType={acmeType}
        {...rest}
      />
    );
  };
  return (
    <View style={styles.activityContainer}>
      <FlatList
        data={activityOptionsData}
        renderItem={renderActivityOptions}
        keyExtractor={(item, index) => `activity-option-${index}`}
        ItemSeparatorComponent={() => <View style={styles.optionSeperator} />}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  activityContainer: {
    flex: 1,
    ...paddingStyles.ph4,
  },
  optionSeperator: {
    borderBottomWidth: 1,
    borderBottomColor: holidayColors.grayBorder,
    ...paddingStyles.pb16,
    ...marginStyles.mb16,
  },
});

export default ActivityOptions;
