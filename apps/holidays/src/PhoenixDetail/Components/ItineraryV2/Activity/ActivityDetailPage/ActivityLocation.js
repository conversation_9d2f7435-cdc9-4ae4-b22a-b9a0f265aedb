import React, { useEffect, useState } from 'react';
import { View, Text, StyleSheet, Image } from 'react-native';
import AtomicCss from '@mmt/legacy-commons/Styles/AtomicCss';
import {
  getGoogleAPIKeyForAllPlarforms,
  getStaticMapUriForCoordinatesListV2,
} from '../../../../../utils/HolidayUtils';
import { isNumber } from 'lodash';
import BasePage from '@mmt/legacy-commons/Common/navigation/BasePage';
import { holidayColors } from 'mobile-holidays-react-native/src/Styles/holidayColors';
import { fontStyles } from 'mobile-holidays-react-native/src/Styles/holidayFonts';
import { marginStyles } from 'mobile-holidays-react-native/src/Styles/Spacing';
import {
  borderRadiusValues,
  holidayBorderRadius,
} from 'mobile-holidays-react-native/src/Styles/holidayBorderRadius';

export const ACTIVITY_MAP_PARAMS = {
  BASE_URL: 'https://maps.googleapis.com/maps/api/staticmap',
  SIZE: '291x120',
  ZOOM: 7,
  SCALE: 2,
  MAP_TYPE: 'roadmap',
  MARKER_SIZE: 'mid',
  MARKER_COLOR: 'blue',
};

const ActivityLocation = (props) => {
  const [googleAPIKey, setGoogleAPIKey] = useState('');
  useEffect(() => {
    const getApiKey = async () => {
      const key = await getGoogleAPIKeyForAllPlarforms();
      setGoogleAPIKey(key);
    };

    getApiKey();
  }, []);

  const {
    showMap,
    venueAddress,
    latitude = '',
    longitude = '',
  } = props.venueDetails || {};
  if (venueAddress && showMap) {
  const map = isNumber(latitude) && isNumber(longitude);
  const coordinatesList = [
    {
      lat: latitude,
      lon: longitude,
    },
  ];

  return (
    <View style={styles.locContent}>
      <View style={styles.locInnerWrap}>
        {!!venueAddress && (
          <Text style={styles.addressBold}>
            Address:
            <Text style={styles.address}>{venueAddress}</Text>
          </Text>
        )}
      </View>
      {!map && (
        <Image
          source={{
            uri: getStaticMapUriForCoordinatesListV2(
              coordinatesList,
              ACTIVITY_MAP_PARAMS,
              googleAPIKey,
            ),
          }}
          style={styles.mapImage}
        />
      )}
    </View>
  );
  }
  return [];
  // }
};

const styles = StyleSheet.create({
  address: {
    ...fontStyles.labelBaseRegular,
    color: holidayColors.gray,
  },
  addressBold: {
    ...fontStyles.labelBaseBold,
    color: holidayColors.gray,
  },
  locInnerWrap: {
    flexWrap: 'wrap',
    flexDirection: 'row',
    ...marginStyles.mb16,
  },
  mapImage: {
    width: '100%',
    height: 120,
    borderTopLeftRadius: borderRadiusValues.br8,
    borderTopRightRadius: borderRadiusValues.br8,
    borderBottomLeftRadius: borderRadiusValues.br8,
    borderBottomRightRadius: borderRadiusValues.br8,
  },
  locContent: {
    marginBottom: 16,
    width: '100%',
  },
});

export default ActivityLocation;
