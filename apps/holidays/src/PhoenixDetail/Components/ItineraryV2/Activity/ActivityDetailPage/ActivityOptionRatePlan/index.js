import React, { useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  FlatList,
  BackHandler,
  Platform,
  StatusBar,
} from 'react-native';
import { HARDWARE_BACK_PRESS } from 'mobile-holidays-react-native/src/SearchWidget/SearchWidgetConstants';
import { paddingStyles, marginStyles } from 'mobile-holidays-react-native/src/Styles/Spacing';
import { fontStyles } from 'mobile-holidays-react-native/src/Styles/holidayFonts';
import { holidayColors } from 'mobile-holidays-react-native/src/Styles/holidayColors';
import { HolidayNavigation } from '../../../../../../Navigation';
import { createActivtyRatePlanOptionData } from '../../../ItineraryV2Utils';

/* Components */
import PageHeader from '../../../../../../Common/Components/PageHeader';
import ActivitySectionComponent, { ACTIVITY_SECTION_TYPES } from '../ActivitySectionComponent';
import RatePlanPoints from './RatePlanPoints';
import { BaseCard } from '../Components';
import PhoenixHeader from '../../../../PhoenixHeader';
import { HOLIDAYS_ACTIVITY_OVERLAY_DETAIL_RATE_PLAN } from '../../../../../Utils/PheonixDetailPageConstants';
import { holidayNavigationPop } from 'apps/holidays/src/PhoenixDetail/Utils/DetailPageNavigationUtils';
import { Overlay } from '../../../../DetailOverlays/OverlayConstants';
import useBackHandler from '../../../../../../hooks/useBackHandler';
import { isMobileClient } from '../../../../../../utils/HolidayUtils';

const RatePlanPage = ({
  ratePlanInfo,
  subtitleData = [],
  pageHeader = '',
  subHeader = '',
  activityMetaData = {},
  showOverlays,
  hideOverlays,
}) => {
  const { name } = ratePlanInfo || {};
  const ratePlanDetails = createActivtyRatePlanOptionData({ activityOption: ratePlanInfo });

  useBackHandler(onBackPress);

  const onBackPress = () => {
    if (isMobileClient()) {
      HolidayNavigation.pop();
    } else {
    holidayNavigationPop({
      overlayKeys:[Overlay.ACTIVITY_DETAIL_RATE_PLAN_V2],
      hideOverlays,
    })
    }
    return true;
  };

  const renderItemHeader = () => {
    return (
      <View>
        <Text style={styles.headlineText}>{name}</Text>
      </View>
    );
  };

  const renderRatePlanData = ({ item, index }) => {
    const { sectionType, title = '', info = [] } = item || {};
    const { acmeType = 'ACTIVITY' } = activityMetaData;
    const clickEventParams = {
      component: acmeType,
      sectionName: `Rateplan_${title}`,
      prop1: HOLIDAYS_ACTIVITY_OVERLAY_DETAIL_RATE_PLAN,
    };
    return (
      <ActivitySectionComponent
        data={item}
        activity={{
          metaData: activityMetaData,
        }}
        clickEventParams={clickEventParams}
        showOverlays
        hideOverlays
      />
    );
  };

  return (
    <View style={styles.ratePlanContainer}>
      <PhoenixHeader
        subtitleData={subtitleData}
        title="Rate Plan Details"
        handleClose={onBackPress}
      />
      <FlatList
        data={ratePlanDetails}
        ListHeaderComponent={renderItemHeader}
        renderItem={renderRatePlanData}
        keyExtractor={(item, index) => `${item?.sectionType || ''}-${index}`}
        contentContainerStyle={paddingStyles.pa16}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  ratePlanContainer: {
    backgroundColor: holidayColors.lightGray2,
    height: '100%',
    width: '100%',
  },
  headlineText: {
    ...fontStyles.labelMediumBlack,
    color: holidayColors.black,
    ...paddingStyles.pb16,
  },
});

export default RatePlanPage;
