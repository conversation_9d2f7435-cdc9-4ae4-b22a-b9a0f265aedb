import HolidayImageHolder from 'mobile-holidays-react-native/src/Common/Components/HolidayImageHolder';
import ReadMoreReadLessHTMLText from 'mobile-holidays-react-native/src/Common/Components/ReadMoreLessHTMLText';
import React, { useState } from 'react';
import { View, Text, Image, StyleSheet, TouchableOpacity, FlatList } from 'react-native';
import { holidayColors } from '../../../../../../Styles/holidayColors';
import { fontStyles } from '../../../../../../Styles/holidayFonts';
import { marginStyles, paddingStyles } from '../../../../../../Styles/Spacing';
import { htmlTextSmallStyles } from '../../../dayPlanV2Styles';
import { logPhoenixDetailPDTEvents } from 'mobile-holidays-react-native/src/utils/PhoenixDetailPDTTrackingUtils';
import { PDT_EVENT_TYPES } from '../../../../../../utils/HolidayPDTConstants';
import { trackPhoenixDetailLocalClickEvent } from '../../../../../Utils/PhoenixDetailTracking';

const RatePlanPoints = ({ data, clickEventParams }) => {
  const [showAll, setShowAll] = useState(false);

  const toggleShowAll = () => {
    setShowAll(!showAll);
  };

  const captureClickEvents = ({eventName = '', prop1 = ''}) => {
    logPhoenixDetailPDTEvents({
      actionType: PDT_EVENT_TYPES.buttonClicked,
      value: eventName.replace(/_/g, '|'),
      subPageName: prop1,
    })
    trackPhoenixDetailLocalClickEvent({
      eventName, 
      prop1,
    })
  }

  const renderRatePlanPoint = ({ item, index }) => {
    const { name, imageDetail = {}, description } = item || {};
    const { images = [] } = imageDetail || {};
    const ratePlanPointImage = images?.[0]?.path || '';
    return (
      <View
        style={[styles.ratePlanPointsCard, index === data.length - 1 ? styles.lastCard : null]}
        key={index}
      >
        <View>
          <HolidayImageHolder
            imageUrl={ratePlanPointImage}
            style={[styles.imageStyle, marginStyles.mb12]}
          />
          <Text style={[styles.titleText, marginStyles.mb4]}>{name}</Text>
        </View>
        <View style={styles.descriptionWrapper}>
          <ReadMoreReadLessHTMLText
            textValue={description}
            limit={2}
            textStyle={htmlTextSmallStyles}
            clickEventParams={clickEventParams}
            captureClickEvents={captureClickEvents}
          />
        </View>
      </View>
    );
  };
  return (
    <View style={styles.ratePlanPointsContainer}>
      <FlatList data={data} renderItem={renderRatePlanPoint} />
    </View>
  );
};

const styles = StyleSheet.create({
  ratePlanPointsContainer: {
    ...paddingStyles.ph4,
  },
  ratePlanPointsCard: {
    borderBottomWidth: 1,
    borderBottomColor: holidayColors.grayBorder,
    ...paddingStyles.pb12,
    ...marginStyles.mb16,
  },
  lastCard: {
    borderBottomWidth: 0,
    ...marginStyles.pb0,
    ...marginStyles.mb0,
  },
  iconStyle: {
    width: 24,
    height: 24,
  },
  imageStyle: {
    width: '100%',
    height: 187,
    borderRadius: 16,
  },
  ticketIconStyle: {
    width: 16,
    height: 16,
    marginRight: 6,
  },
  titleText: {
    ...fontStyles.labelMediumBold,
    color: holidayColors.black,
    paddingHorizontal: 4,
  },
  ticketWrapper: {
    flexDirection: 'row',
    alignItems: 'center',
    ...paddingStyles.ph4,
    ...marginStyles.mb4,
  },
  greenText: {
    ...fontStyles.labelBaseBold,
    color: holidayColors.green,
  },
  blackText: {
    ...fontStyles.labelBaseBold,
    color: holidayColors.black,
  },
  ratePlanPointsText: {
    ...fontStyles.labelBaseRegular,
    color: holidayColors.gray,
    flex: 1,
  },
  descriptionWrapper: {
    paddingHorizontal: 4,
  },
  readMoreText: {
    ...fontStyles.labelBaseBold,
    color: holidayColors.primaryBlue,
  },
});

export default RatePlanPoints;
