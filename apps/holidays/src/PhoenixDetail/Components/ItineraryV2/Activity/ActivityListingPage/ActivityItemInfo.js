import React from 'react';
import { StyleSheet, Text, TouchableOpacity, View } from 'react-native';
import { marginStyles, paddingStyles } from '../../../../../Styles/Spacing';
import { fontStyles } from '../../../../../Styles/holidayFonts';
import { holidayColors } from '../../../../../Styles/holidayColors';
import { htmlTextSmallStyles } from '../../../ItineraryV2/dayPlanV2Styles';
import { isEmpty } from 'lodash';

/* Components */
import DottedLine from '../../../../../Common/Components/DottedLine';
import ItineraryUnitInformation from '../../../ItineraryV2/Common/ItineraryUnitInformation';
import ReadMoreReadLessHTMLText from '../../../../../Common/Components/ReadMoreLessHTMLText';
import { PriceSection } from './ActivityItemFooter';
import { logPhoenixDetailPDTEvents } from 'mobile-holidays-react-native/src/utils/PhoenixDetailPDTTrackingUtils';
import { PDT_EVENT_TYPES } from 'mobile-holidays-react-native/src/utils/HolidayPDTConstants';
import { trackPhoenixDetailLocalClickEvent } from 'mobile-holidays-react-native/src/PhoenixDetail/Utils/PhoenixDetailTracking';


const ActivityItemInfo = (props) => {
  const { item, handleOnPress } = props || {};
  const { metaData, additionalData, recheckKey } = item || {};
  const { shortDescription } = additionalData || {};
  const { name = '', additionalInfo = [], ratePlanInfo = {} } = metaData || {};
  const { slotDetails = {}, otherInfos = [] } = ratePlanInfo || {};


  const captureClickEvents = ({eventName = '', prop1 = ''}) => {
    logPhoenixDetailPDTEvents({
      actionType: PDT_EVENT_TYPES.buttonClicked,
      value: eventName.replace(/_/g, '|'),
    })
    trackPhoenixDetailLocalClickEvent({
      eventName, 
      prop1,
    })
  }
  return (
    <View>
      <Text style={styles.packageHeading} numberOfLines={2}>
        {name}
      </Text>
      <ReadMoreReadLessHTMLText
        textValue={shortDescription}
        limit={2}
        anchorStyle={htmlTextSmallStyles.actionStyle}
        textStyle={htmlTextSmallStyles}
        captureClickEvents={captureClickEvents}
        // handleOnClick={handleOnPress}
        hideReadMoreText={true}
      />
      <View style={styles.activityWrapper}>
        <View style={[styles.activityListingContainer]}>
          <View style={{ flex: 1 }}>
            <ItineraryUnitInformation info={additionalInfo} />
            <TouchableOpacity onPress={handleOnPress} style={{ marginTop: 'auto' }}>
              <Text style={[styles.moreDetailsText, styles.appendTop4]}>Know More</Text>
            </TouchableOpacity>
          </View>
          {(isEmpty(ratePlanInfo) || (!isEmpty(slotDetails) && isEmpty(otherInfos))) && (
            item?.unavailable ? (
              <Text style={styles.unavailableText}>Unavailable</Text>
            ) : (
              <PriceSection metaData={metaData} recheckKey={recheckKey} {...props} />
            )
          )}
        </View>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  packageHeading: {
    ...fontStyles.labelLargeBlack,
    color: holidayColors.black,
    lineHeight: 22,
  },
  activityWrapper: {
    ...paddingStyles.pt8,
    // ...paddingStyles.pb16,
  },
  // headingWrapper: {
  //   ...paddingStyles.ph16,
  //   ...paddingStyles.pt16,
  // },
  appendBottom8: {
    ...marginStyles.mb8,
  },
  activityListingContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    ...paddingStyles.pb12,
  },

  contentWrapper: {
    flexDirection: 'row',
    alignItems: 'flex-start',
  },
  iconStyle: {
    width: 14,
    height: 14,
    ...marginStyles.mt4,
    ...marginStyles.mr6,
    justifyContent: 'center',
    alignItems: 'center',
  },
  dot: {
    width: 4,
    height: 4,
    backgroundColor: holidayColors.gray,
    borderRadius: 4,
  },
  listingText: {
    ...fontStyles.labelSmallRegular,
    ...marginStyles.mr10,
    flex: 1,
  },
  shortDescription: {
    ...fontStyles.labelSmallRegular,
    ...marginStyles.mr10,
    ...marginStyles.mt8,
    color: holidayColors.gray,
    flex: 1,
  },
  appendTop4: {
    ...marginStyles.mt4,
  },
  moreDetailsText: {
    ...fontStyles.labelSmallBold,
    color: holidayColors.primaryBlue,
  },
  greenText: {
    color: holidayColors.green,
  },
  grayText: {
    color: holidayColors.gray,
  },
  unavailableText: {
    ...fontStyles.labelSmallBold,
    color: holidayColors.red,
    marginTop: 'auto',
  },
});
export default ActivityItemInfo;
