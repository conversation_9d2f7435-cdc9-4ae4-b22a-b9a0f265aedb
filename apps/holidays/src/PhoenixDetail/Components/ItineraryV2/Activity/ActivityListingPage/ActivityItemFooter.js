import React from 'react';
import { StyleSheet, Text, TouchableOpacity, View } from 'react-native';
import { showShortToast } from '@mmt/core/helpers/toast';
import { ACTIVITY_NOT_AVAILABLE } from '../../../../Utils/PheonixDetailPageConstants';
import { isEmpty } from 'lodash';
import { holidayColors } from '../../../../../Styles/holidayColors';
import { fontStyles } from '../../../../../Styles/holidayFonts';
import { marginStyles, paddingStyles } from '../../../../../Styles/Spacing';
import { rupeeFormatterUtils } from '../../../../../utils/HolidayUtils';

/* Components */
import TimeSlot from './TimeSlot';
import Button from './Button';
import ItineraryUnitInformation from '../../../ItineraryV2/Common/ItineraryUnitInformation';

const ActivityItemFooter = (props) => {
  const { item, index, openActivityDetailPage, updateActivities, price } = props || {};
  const { metaData = {}, additionalData = {}, recheckKey } = item;
  const { code, acmeType, cityName, ratePlanInfo = {} } = metaData || {};
  const { safe } = additionalData;

  // If no otherInfos are present then priceSection is moved to the top
  if(isEmpty(ratePlanInfo?.otherInfos || [])) {
    return null;
  }
  const onSelectPress = () => {
    // The third parameter 'false' indicates that the openSlotOverlay should not be triggered
    // The fourth parameter 'true' indicates that the scrollToRatePlan should be triggered
    openActivityDetailPage({
      code,
      safe,
      openSlotOverlay: false,
      scrollToRatePlan: true,
      activityType: acmeType,
      city: cityName,
    });
  };
  return (
    <View style={styles.activityListingContainer}>
      <ActivityHighlights onSelectPress={onSelectPress} item={item} />
      {item?.unavailable ? (
        <Text style={styles.unavailableText}>Unavailable</Text>
      ) : (
      <PriceSection
        index={index}
        metaData={metaData}
        recheckKey={recheckKey}
        updateActivities={updateActivities}
        price={price}
      />
      )}
    </View>
  );
};

const ActivityHighlights = (props) => {
  const { onSelectPress, item } = props || {};
  const { metaData, additionalData } = item || {};
  const { additionalInfo, locked, ratePlanInfo } = metaData || {};
  const { inclusionList = [], exclusionList = [] } = additionalData || {};
  const { moreOptionsCtaText, otherInfos = [], name = '' } = ratePlanInfo || {};

  if (isEmpty(ratePlanInfo)) {
    return [];
  }
  return (
    <View style={styles.flexContainer}>

        <ItineraryUnitInformation info={otherInfos} />
        {!locked && (
          <TouchableOpacity onPress={onSelectPress} style={{ marginTop: 'auto' }}>
            <Text style={[styles.moreDetailsText, styles.appendTop4]}>{moreOptionsCtaText}</Text>
          </TouchableOpacity>
        )}

    </View>
  );
};

export const PriceSection = ({ metaData, updateActivities, price, index, recheckKey }) => {
  const { name = '', code, selected, locked, acmeType, acmeSubType } = metaData || {};

  const onClick = () => {
    if (locked) {
      showShortToast(ACTIVITY_NOT_AVAILABLE);
      return;
    }
    updateActivities(!selected, code, name, recheckKey, acmeType, acmeSubType);
  };

  return (
    <View style={{marginLeft: 'auto', marginTop: 'auto'}}>
      {!selected && (
        <View style={styles.price}>
          <Text style={styles.priceText}>{rupeeFormatterUtils(price)}</Text>
          <Text style={[styles.priceSubText, styles.appendBottom8]}>Price/Person</Text>
        </View>
      )}
      <Button
        buttonText={selected || locked ? 'Selected' : "Select"}
        handleClick={onClick}
        activeImg={selected || locked}
        index={index}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  activityWrapper: {
    ...paddingStyles.ph16,
    ...paddingStyles.pb16,
  },
  activityListingContainer: {
    flexDirection: 'row',
    // alignItems: 'flex-end',
    justifyContent: 'space-between',
    ...marginStyles.mr8,
  },
  flexContainer: {
    flex: 1,
  },
  activityTitle: {
    ...fontStyles.labelSmallBold,
    color: holidayColors.gray,
  },
  activityDescription: {
    ...fontStyles.labelSmallRegular,
    color: holidayColors.gray,
    lineHeight: 16,
  },
  priceText: {
    ...fontStyles.labelMediumBlack,
    color: holidayColors.black,
  },
  priceSubText: {
    ...fontStyles.labelSmallRegular,
    color: holidayColors.gray,
  },
  moreDetailsText: {
    ...fontStyles.labelSmallBold,
    color: holidayColors.primaryBlue,
  },
  appendBottom8: {
    ...marginStyles.mb8,
  },
  appendTop4: {
    ...marginStyles.mt4,
  },
  price: {
    flex: 1,
    justifyContent: 'flex-end',
    alignItems: 'flex-end',
  },
  slotName: {
    ...fontStyles.labelSmallBold,
    ...marginStyles.mr10,
    ...marginStyles.mb10,
    color: holidayColors.gray,
    flex: 1,
  },
  unavailableText: {
    ...fontStyles.labelSmallBold,
    color: holidayColors.red,
    marginTop: 'auto',
  },
});

export default ActivityItemFooter;
