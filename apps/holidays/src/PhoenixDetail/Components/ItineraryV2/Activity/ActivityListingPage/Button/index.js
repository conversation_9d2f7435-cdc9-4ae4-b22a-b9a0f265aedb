import React from 'react';
import { TouchableOpacity, Text, Image, StyleSheet, View } from 'react-native';
import blueTickIcon from '../../../../images/blueTickIcon.png';
import HolidayImageHolder from '../../../../../../Common/Components/HolidayImageHolder';
import { holidayColors } from '../../../../../../Styles/holidayColors';
import { fontStyles } from '../../../../../../Styles/holidayFonts';
import { marginStyles, paddingStyles } from '../../../../../../Styles/Spacing';
import { holidayBorderRadius } from '../../../../../../Styles/holidayBorderRadius';

const Button = ({ buttonText, handleClick, btnContainerStyles, btnTextStyles, activeImg }) => {
  return (
    <TouchableOpacity onPress={handleClick}>
      <View style={[styles.btnContainer, btnContainerStyles, activeImg && styles.activeButton]}>
        {activeImg ? (
          <HolidayImageHolder defaultImage={blueTickIcon} style={styles.iconStyle} />
        ) : null}
        <Text style={[styles.btnText, btnTextStyles]}>{buttonText?.toUpperCase()}</Text>
      </View>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  btnContainer: {
    borderWidth: 1,
    ...paddingStyles.ph14,
    ...paddingStyles.pv4,
    backgroundColor: holidayColors.white,
    ...holidayBorderRadius.borderRadius8,
    borderColor: holidayColors.primaryBlue,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  btnText: {
    textAlign: 'center',
    color: holidayColors.primaryBlue,
    ...fontStyles.labelSmallBlack,
  },
  iconStyle: {
    width: 12,
    height: 12,
    ...marginStyles.mr4,
    tintColor: holidayColors.primaryBlue,
  },
  activeButton: {
    backgroundColor: '#CCE8FF',
  },
});
export default Button;
