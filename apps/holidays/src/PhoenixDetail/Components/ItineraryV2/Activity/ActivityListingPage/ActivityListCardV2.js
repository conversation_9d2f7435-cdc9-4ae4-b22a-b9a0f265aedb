import React, { useMemo } from 'react';
import { StyleSheet, TouchableOpacity, View, Text } from 'react-native';
import { marginStyles, paddingStyles } from '../../../../../Styles/Spacing';
import { fontStyles } from '../../../../../Styles/holidayFonts';
import { holidayColors } from '../../../../../Styles/holidayColors';
import { isEmpty } from 'lodash';

/* Components */
import ActivityItemImageHeader from './ActivityItemImageHeader';
import ActivityItemInfo from './ActivityItemInfo';
import ActivityItemFooter, { PriceSection } from './ActivityItemFooter';
import TimeSlot from './TimeSlot';
import DottedLine from '../../../../../Common/Components/DottedLine';
import Tag from './Tag';


const ActivityListCardV2 = (props) => {
  const { index, isActive } = props || {};
  const { item, openActivityDetailPage } = props || {};
  const { metaData } = item || {};
  const { code, ratePlanInfo = {} } = metaData || {};
  const { name, label } = ratePlanInfo || {};
  const handleOnPress = () => {
    openActivityDetailPage({ code, activityType: metaData?.acmeType, city: metaData?.cityName });
  };

  const renderTags = useMemo(() => {
    const { black } = holidayColors || {};
    const { texts = [''], textColorCode = black, borderColorCode = black } = label || {};
    const tagText = Array.isArray(texts) && texts.length > 0 ? texts[0] : '';

    return (
      <Tag
        textColor={borderColorCode}
        borderColor={textColorCode}
        labelText={tagText}
        containerStyle={styles.tagContainer}
      />
    );
  }, [label]);

  return (
    <View key={index} style={styles.activityContainer}>
      <TouchableOpacity disabled={item?.unavailable} onPress={handleOnPress}>
        <ActivityItemImageHeader {...props} />
        <View style={[styles.headingWrapper]}>
          <ActivityItemInfo isActive={isActive} handleOnPress={handleOnPress} {...props} />
          {!isEmpty(ratePlanInfo) && (
            <View style={paddingStyles.pb16}>
              <DottedLine />
            </View>
          )}
        </View>
        {renderTags}
        {!isEmpty(ratePlanInfo) && (
          <View style={[paddingStyles.ph16, paddingStyles.pb16]}>
            {!!name && (
              <Text style={styles.slotName} numberOfLines={2}>
                {name}
              </Text>
            )}
            <TimeSlot {...props} />
            <ActivityItemFooter index={index} {...props} />
          </View>
        )}
      </TouchableOpacity>
    </View>
  );
};

const styles = StyleSheet.create({
  activityContainer: {
    flex: 1,
    borderRadius: 8,
    backgroundColor: holidayColors.white,
    borderWidth: 1,
    borderColor: holidayColors.grayBorder,
  },
  headingWrapper: {
    ...paddingStyles.ph16,
    ...paddingStyles.pt16,
  },
  slotName: {
    ...fontStyles.labelSmallBold,
    ...marginStyles.mr10,
    color: holidayColors.gray,
    flex: 1,
  },
  tagContainer: {
    ...marginStyles.ml16,
    ...marginStyles.mb6,
  },
});

export default ActivityListCardV2;
