import React from 'react';
import { FlatList, StyleSheet, View } from 'react-native';
import _ from 'lodash';
import Tag from './Tag';
import { holidayColors } from '../../../../../Styles/holidayColors';
import HolidayImageHolder from '../../../../../Common/Components/HolidayImageHolder';
import { BASE_IMAGE_PATH } from '../../../../../HolidayConstants';

const ActivityItemImageHeader = (props) => {
const { item } = props || {};
  const { metaData } = item || {};
  const { labels = [] } = metaData || {};

  // Safely access the image path and use BASE_IMAGE_PATH as a fallback
  const imageUrl = _.get(item, 'imageDetail.images[0].path', BASE_IMAGE_PATH);
  const renderSeparator = () => <View style={styles.separator} />;
  const renderTags = ({ item }) => {
    const { texts = [''], textColorCode = holidayColors.black, borderColorCode = holidayColors.black } = item || {};
    return (
      <Tag
        textColor={borderColorCode}
        borderColor={textColorCode}
        labelText={texts[0]}
      />
    );
  }
  return (
    <View style={styles.imageContainer}>
      <HolidayImageHolder imageUrl={imageUrl} style={styles.packageImageStyle} />
      <View style={styles.tagContainer}>
      <FlatList
        data={labels}
        renderItem={renderTags}
        keyExtractor={(item, index) => index.toString()}
        horizontal={true}
        ItemSeparatorComponent={renderSeparator}
      />
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  imageContainer: {
    position: 'relative',
  },
  tagContainer: {
    position: 'absolute',
    top: 12,
    left: 12,
  },
  packageImageStyle: {
    width: '100%',
    height: 140,
    borderTopLeftRadius: 8,
    borderTopRightRadius: 8,
  },
  separator: {
    width: 6,
  },
});

export default ActivityItemImageHeader;
