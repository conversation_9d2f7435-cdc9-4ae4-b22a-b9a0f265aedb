import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { borderRadiusValues } from '../../../../../../Styles/holidayBorderRadius';
import { paddingStyles } from '../../../../../../Styles/Spacing';
import { holidayColors } from '../../../../../../Styles/holidayColors';
import { fontStyles } from '../../../../../../Styles/holidayFonts';
import { isEmpty } from 'lodash';

const Tag = (props) => {
    const { labelText, borderColor, textColor, containerStyle = {} } = props || {};
    if (isEmpty(labelText)) {
        return [];
    }
    return (
        <View style={[styles.tagContainer, { borderColor: borderColor }, containerStyle]}>
            <Text style={[styles.label, { color: textColor }]}>{labelText?.toUpperCase()}</Text>
        </View>
    );
  };

const styles = StyleSheet.create({
    tagContainer: {
        borderRadius:borderRadiusValues.br16,
        borderWidth: 1,
        ...paddingStyles.ph8,
        alignSelf:'flex-start',
        backgroundColor: holidayColors.white,
    },
    label: {
        ...fontStyles.labelSmallBlack,
    },
});

export default Tag;
