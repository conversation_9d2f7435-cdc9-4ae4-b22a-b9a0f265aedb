import React from 'react';
import { StyleSheet, Text, TouchableOpacity, View } from 'react-native';
import { marginStyles, paddingStyles } from '../../../../../Styles/Spacing';
import { fontStyles } from '../../../../../Styles/holidayFonts';
import { holidayColors } from '../../../../../Styles/holidayColors';
import DottedLine from '../../../../../Common/Components/DottedLine';
import {
  getOpitimsedImageUrl,
  IMAGE_ICON_KEYS,
} from '../../../../../Common/Components/HolidayImageUrls';
import HolidayImageHolder from '../../../../../Common/Components/HolidayImageHolder';
import { formatTimeSlot } from '../../TravelTidbits/TidBitsUtils';
import { isEmpty } from 'lodash';

const TimeSlot = (props) => {
  const { item: { metaData = {}, additionalData = {} } = {}, openActivityDetailPage } = props || {};
  const { safe } = additionalData || {};
  const { code, ratePlanInfo = {} } = metaData || {};
  const { slotDetails = {}, name, otherInfos = [] } = ratePlanInfo || {};
  const { timeslot = {}, changeCtaText } = slotDetails || {};
  const { startTime, endTime, slotDurationHour = '' } = timeslot || {};

  if (!(slotDetails && timeslot && startTime && endTime)) {
    return [];
  }

  const onSelectPress = () => {
    // The third parameter 'true' indicates that the openSlotOverlay should be triggered
    // The fourth parameter 'false' indicates that the scrollToRatePlan should not be triggered
    openActivityDetailPage({
      code,
      safe,
      openSlotOverlay: true,
      scrollToRatePlan: true,
      activityType: metaData?.acmeType,
      city: metaData?.cityName,
    });
  };

  return (
    <View style={styles.Wrapper}>
      <View style={styles.row}>
        <HolidayImageHolder
          imageUrl={getOpitimsedImageUrl(IMAGE_ICON_KEYS.CLOCK)}
          style={styles.icon}
        />
        <Text style={styles.selectedText}>Selected Time Slot</Text>
        <TouchableOpacity onPress={onSelectPress} style={styles.autoMarginLeft}>
          <Text style={styles.changeTextCta}>{changeCtaText}</Text>
        </TouchableOpacity>
      </View>
      <View style={styles.slotTextContainer}>
        {!!startTime && !!endTime && (
          <Text style={styles.slotTimeText} numberOfLines={1}>
            {formatTimeSlot(startTime, endTime, slotDurationHour)}
          </Text>
        )}
      </View>
      {!isEmpty(otherInfos) && (
        <View style={styles.dottedLine}>
          <DottedLine />
        </View>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  Wrapper: {
    ...paddingStyles.pb16,
  },
  slotName: {
    ...fontStyles.labelSmallBold,
    ...marginStyles.mr10,
    ...marginStyles.mb10,
    color: holidayColors.gray,
    flex: 1,
  },
  dottedLine: {
    ...marginStyles.ml16,
    ...marginStyles.mt10,
  },
  icon: {
    width: 14,
    height: 14,
    ...marginStyles.mr4,
    ...marginStyles.mt4,
    tintColor: holidayColors.gray,
  },
  selectedText: {
    ...fontStyles.labelSmallRegular,
    color: holidayColors.gray,
  },
  slotTextContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    ...marginStyles.ml16,
  },
  slotTimeText: {
    ...fontStyles.labelSmallRegular,
    color: holidayColors.gray,
    alignItems: 'center',
  },
  slotTimeTextTime: {
    ...fontStyles.labelSmallRegular,
    color: holidayColors.gray,
    alignItems: 'center',
  },
  changeTextCta: {
    ...fontStyles.labelSmallBold,
    color: holidayColors.primaryBlue,
    marginLeft: 'auto',
  },
  row: {
    flexDirection: 'row',
  },
  autoMarginLeft: {
    marginLeft: 'auto',
  },
});
export default TimeSlot;
