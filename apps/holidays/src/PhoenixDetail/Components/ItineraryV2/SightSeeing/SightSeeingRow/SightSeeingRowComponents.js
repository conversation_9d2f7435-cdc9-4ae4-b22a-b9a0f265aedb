import React from 'react';
import { isEmpty } from 'lodash';
import { Image, View, TouchableOpacity, Text, StyleSheet, FlatList } from 'react-native';
import { RESIZE_MODE_IMAGE } from '../../../../../HolidayConstants';
import { holidayColors } from '../../../../../Styles/holidayColors';
import { fontStyles } from '../../../../../Styles/holidayFonts';
import { marginStyles } from '../../../../../Styles/Spacing';
import { getStaticMapUriForCoordinatesList } from '../../../../../utils/HolidayUtils';
import { dayPlanRowImage } from '../../../DayPlan/dayPlanStyles';
import {
  IMAGE_ICON_KEYS,
  getOpitimsedImageUrl,
} from '../../../../../Common/Components/HolidayImageUrls';

/* Components */
import HolidayImageHolder from '../../../../../Common/Components/HolidayImageHolder';
import ItineraryUnitInformation, { INFO_SIZE } from '../../Common/ItineraryUnitInformation';
import ItineraryUnitImageWrapper from '../../Common/ItineraryUnitImage';
export const SightSeeingRowImageWrapper = ({
  imageLocationAvailable,
  locationImageUrl,
  coordinateList,
  googleAPIKey,
  handleImageClick,
  imageSight,
}) => {
  const imageUrl = imageLocationAvailable
    ? locationImageUrl
    : getStaticMapUriForCoordinatesList(coordinateList, false, googleAPIKey);
  return (
    <View style={sightSeeingDetailStyles.sightseeingImage}>
      {coordinateList && coordinateList.length > 0 && (
        <TouchableOpacity onPress={handleImageClick}>
          <ItineraryUnitImageWrapper imageUrl={imageUrl} resizeMode={RESIZE_MODE_IMAGE.COVER} />
        </TouchableOpacity>
      )}
      {/* If no co-ordinates available set image on our own */}
      {!imageLocationAvailable &&
        !(coordinateList && coordinateList.length > 0) &&
        !isEmpty(imageSight) && (
          <View style={imageStyles.thumbWrapper}>
            <Image source={{ uri: imageSight }} style={imageStyles.activityImg} />
          </View>
        )}
    </View>
  );
};

export const SightSeeingRowDetails = ({
  locationName = [],
  placesCovered = '',
  duration = '' /*onViewDetail = () => {}*/,
}) => {
  const infoList = [
    {
      iconUrl: getOpitimsedImageUrl(IMAGE_ICON_KEYS.LOCATION),
      text: `${locationName?.slice(0, 5).join(', ')}${
        locationName?.length > 5 ? `, + ${locationName?.length - 5} More` : ''
      }`,
    },
    {
      iconUrl: getOpitimsedImageUrl(IMAGE_ICON_KEYS.CLOCK),
      text: `Duration: ${duration}${duration > 1 ? 'hrs' : 'hr'}, Places Covered: ${placesCovered}`,
    },
  ];

  return (
    <View style={sightSeeingDetailStyles.columnView}>
      <ItineraryUnitInformation info={infoList} size={INFO_SIZE.SMALL} />
      {/* {locationName.length > 5 && (
        <TouchableOpacity onPress={onViewDetail}>
          <Text style={sightSeeingDetailStyles.anchorTextSm}>+ {locationName.length - 5} More</Text>
        </TouchableOpacity>
      )} */}
    </View>
  );
};

const imageStyles = StyleSheet.create({
  thumbWrapper: {
    width: 100,
    height: 68,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 10,
    borderRadius: 4,
    overflow: 'hidden',
  },
  activityImg: {
    width: 100,
    height: 68,
    ...dayPlanRowImage,
  },
});

const sightSeeingDetailStyles = StyleSheet.create({
  columnView: {
    flex: 1,
    flexDirection: 'column',
    ...marginStyles.mr2,
  },
  sightseeingImage: {
    marginLeft: 'auto',
    ...marginStyles.mt4,
  },
  locationStyle: {
    ...fontStyles.labelBaseRegular,
    color: holidayColors.black,
    marginTop: 5,
  },
  anchorTextSm: {
    marginTop: 10,
    ...fontStyles.labelSmallBold,
    color: '#008cff',
  },
});
