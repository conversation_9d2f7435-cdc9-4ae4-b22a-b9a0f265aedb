import React, { useState, useEffect } from 'react';
import { Image, StyleSheet, Text, TouchableOpacity, View } from 'react-native';
import { fonts } from '@mmt/legacy-commons/Styles/globalStyles';
import {
  getIconForItineraryUnitType,
  sightSeeingCitiesV2,
} from '../../../../Utils/PhoenixDetailUtils';
import { OVERLAY_CAROUSAL_POSITION } from '../../../../Utils/PheonixDetailPageConstants';
import {
  getGoogleAPIKeyForAllPlarforms,
  getStaticMapUriForCoordinatesList,
  isRawClient,
} from '../../../../../utils/HolidayUtils';
import { isNumber, isEmpty } from 'lodash';

import BasePage from '@mmt/legacy-commons/Common/navigation/BasePage';
import { HOLIDAY_ROUTE_KEYS, HolidayNavigation } from '../../../../../Navigation';
import { trackPhoenixDetailLocalClickEvent } from '../../../../Utils/PhoenixDetailTracking';
import { getEnableCarouselViewDetail } from '../../../../../utils/HolidaysPokusUtils';
import {
  actionStyle,
  dayPlanRowContainerStyle,
  dayPlanRowHeadingStyles,
  dayPlanRowImage,
} from '../../../DayPlan/dayPlanStyles';
import { sectionHeaderSpacing } from 'mobile-holidays-react-native/src/Styles/holidaySpacing';
import { itineraryUnitTypes } from '../../../../DetailConstants';
import { fontStyles } from '../../../../../Styles/holidayFonts';
import { holidayColors } from '../../../../../Styles/holidayColors';
import DayPlanRowHOC from '../../Common/DayPlanRowHOC';
import { SightSeeingRowDetails, SightSeeingRowImageWrapper } from './SightSeeingRowComponents';
import { marginStyles } from '../../../../../Styles/Spacing';
import ItineraryUnitExtraInfoMessages from '../../../ItineraryUnitExtraInfoMessages';
import { logPhoenixDetailPDTEvents } from 'mobile-holidays-react-native/src/utils/PhoenixDetailPDTTrackingUtils';
import { PDT_EVENT_TYPES } from 'mobile-holidays-react-native/src/utils/HolidayPDTConstants';
import { holidayNavigationPush } from 'apps/holidays/src/PhoenixDetail/Utils/DetailPageNavigationUtils';
import { Overlay } from '../../../DetailOverlays/OverlayConstants';

const SightSeeingRowV2 = ({
  itineraryUnit,
  packageDetailDTO,
  subtitleData,
  day,
  city = '',
  destinationName,
  defaultCollapsedState,
  showOverlay,
  hideOverlays,
}) => {
  const [googleAPIKey, setGoogleAPIKey] = useState('');
  const { locationImageUrl, carExtraInfo = [] } = itineraryUnit || {};
  const { sellableId, } = itineraryUnit || {};

  useEffect(() => {
    const getAPIKey = async () => {
      let googleAPIKey = await getGoogleAPIKeyForAllPlarforms();
      setGoogleAPIKey(googleAPIKey);
    };
    getAPIKey();
  }, []);

  const captureClickEvents = ({ eventName = '' , suffix = ''}) => {
    const value = eventName + suffix;
    logPhoenixDetailPDTEvents({
      actionType: PDT_EVENT_TYPES.buttonClicked,
      value : value.replace(/_/g , '|'),
    })
    trackPhoenixDetailLocalClickEvent({
      eventName,
      suffix,
      prop1: `base:${itineraryUnitTypes.ACTIVITY}`,
    })
  }

  const onViewDetail = () => {
    captureClickEvents({
      eventName: 'view_',
      suffix: `${itineraryUnitTypes.ACTIVITY}_${destinationName}_${day}`,
    });
    const navigationParams = {
      item: { data: itineraryUnit },
      subtitleData,
      packageDetailDTO,
    }
    if (isMobileClient()) {
      HolidayNavigation.push(HOLIDAY_ROUTE_KEYS.SIGHTSEEING_DETAIL, navigationParams);
    } else {
      holidayNavigationPush({
        props: navigationParams,
        pageKey: HOLIDAY_ROUTE_KEYS.SIGHTSEEING_DETAIL,
        overlayKey: Overlay.SIGHTSEEING_DETAIL_PAGE,
        showOverlay,
        hideOverlays,
      });
    }
  };

  const getSightseeingData = () => {
    let duration = 0;
    let coordinateList = [];
    let locationName = [];
    const citySet = new Set();
    let markers = [];
    let imageSight = '';
    let imageLocationAvailable = !isEmpty(locationImageUrl);
    let placesCovered = 0;
    itineraryUnit.locations.forEach((item, index) => {
      duration += item.durationInHours;
      placesCovered = placesCovered + 1;
      if (isNumber(item.latitude) && isNumber(item.longitude)) {
        coordinateList.push({ lat: item.latitude, lon: item.longitude });
        markers.push({
          title: item.name,
          coordinates: {
            latitude: parseFloat(item.latitude),
            longitude: parseFloat(item.longitude),
          },
        });
      } else if (isEmpty(imageSight) && item?.images?.length > 0) {
        imageSight = item?.images[0].path;
      }

      locationName.push(item.name);
      citySet.add(item.cityName);
    });
    return {
      markers,
      imageSight,
      imageLocationAvailable,
      duration,
      coordinateList,
      locationName,
      citySet,
      placesCovered,
    };
  };
  const {
    markers,
    imageSight,
    imageLocationAvailable,
    duration,
    coordinateList,
    locationName,
    citySet,
    placesCovered,
  } = getSightseeingData();

  const handlePress = () => {
    if(isRawClient()) return;
    HolidayNavigation.push(HOLIDAY_ROUTE_KEYS.PHOENIX_DETAIL_MAP_PAGE, {
      markers,
      name: 'Sightseeing in ' + sightSeeingCitiesV2(citySet),
    });
  };
  return (
    <DayPlanRowHOC
      day={day}
      city={city}
      unit={itineraryUnit}
      cardHeaderTexts={[
        { text: 'SIGHTSEEING', isEmphasized: true },
        ...(duration ? [{ text: `${duration}${duration > 1 ? 'hrs' : 'hr'}` }] : []),
        ...(citySet ? [{ text: `In ${sightSeeingCitiesV2(citySet)}` }] : []),
      ]}
      defaultCollapsedState={defaultCollapsedState}
    >
      <View style={styles.activityRow}>
        <View style={{flexDirection:'row'}}>
        <View style = {styles.activityRowDetails}>
          <Text style={styles.activityHeading}>Sightseeing in {sightSeeingCitiesV2(citySet)}</Text>
          <SightSeeingRowDetails
            locationName={locationName}
            placesCovered={placesCovered}
            duration={duration}
          />
          </View>
          <SightSeeingRowImageWrapper
            coordinateList={coordinateList}
            imageLocationAvailable={imageLocationAvailable}
            googleAPIKey={googleAPIKey}
            locationImageUrl={locationImageUrl}
            handleImageClick={handlePress}
            imageSight={imageSight}
          />
          </View>
        <ItineraryUnitExtraInfoMessages extraInfo={carExtraInfo}/>
        <View style={styles.footer}>
          <TouchableOpacity onPress={onViewDetail}>
            <Text style={actionStyle}>View Details</Text>
          </TouchableOpacity>
        </View>
      </View>
    </DayPlanRowHOC>
  );
};

const styles = StyleSheet.create({
  activityRow: {
  },
  activityRowDetails: {
    flex: 1,
  },
  activityHeading: {
    ...fontStyles.labelLargeBlack,
    ...marginStyles.mb4,
    color:holidayColors.black,
  },
  activityDetails: {
    flexDirection: 'row',
  },
  footer: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
    alignItems: 'center',
    marginTop: 12,
  },
});

export default SightSeeingRowV2;
