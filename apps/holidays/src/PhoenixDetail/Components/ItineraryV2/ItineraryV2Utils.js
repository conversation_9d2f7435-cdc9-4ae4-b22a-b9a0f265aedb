import { getNewDate } from '@mmt/legacy-commons/Common/utils/DateUtils';
import { isEmpty } from 'lodash';
import { MONTH_ARR_CAMEL } from '../../../HolidayConstants';
import { holidayColors } from '../../../Styles/holidayColors';
import { TABS } from '../DayPlan/Sorter';
import { ACTIVITY_SECTION_TYPES } from './Activity/ActivityDetailPage/ActivitySectionComponent';
import { ITINERARY_COLLAPSED_STATE } from './constants';
import { IMAGE_ICON_KEYS, getOpitimsedImageUrl } from '../../../Common/Components/HolidayImageUrls';
import { INFO_SEPERATOR } from './Common/ItineraryUnitInformation';
import { HolidayNavigation } from '../../../Navigation';
import { componentImageTypes, itineraryUnitSubTypes, itineraryUnitTypes, packageActionComponent, packageActions } from '../../DetailConstants';
import { holidayNavigationPop } from '../../Utils/DetailPageNavigationUtils';
import { Overlay } from '../DetailOverlays/OverlayConstants';

export const getMealDetailObject = ({ mealDetail, sellableId }) => {
  if (!sellableId) {
    return {};
  }
  const { meals = [] } = mealDetail || {};
  return meals.filter((meal) => meal.sellableId === sellableId)?.[0];
};

export const restructureInclusionData = ({ data = [] }) => {
  const newData = [];
  data.map((item, index) => {
    let newItem = {
      iconUrl: getOpitimsedImageUrl(IMAGE_ICON_KEYS.TICK),
      text: item?.description || '',
      textColorCode: item?.color || holidayColors.green,
    };
    newData.push(newItem);
  });

  return newData;
};

export const getCollapsedState = ({ unit, currentActivePlanOnItinerary }) => {
  const { cardCollapsibleState = '' } = unit;

  // IF - cardCollapsibleState does not exist
  if (isEmpty(cardCollapsibleState)) {
    return ITINERARY_COLLAPSED_STATE.NON_COLLAPSIBLE;
  }

  // ELSE - cardCollapsibleStates exist then check value
  if (cardCollapsibleState === ITINERARY_COLLAPSED_STATE.NON_COLLAPSIBLE) {
    // If the card is non-collapsible, return its state
    return cardCollapsibleState;
  }

  // If the card is collapsible, check the active plan on itinerary
  if (currentActivePlanOnItinerary === TABS.DAY) {
    // If the active plan is on the "DAY" tab, return the card's state
    return cardCollapsibleState;
  }

  // If none of the above conditions are met, default to COLLAPSABLE_EXPANDED
  return ITINERARY_COLLAPSED_STATE.COLLAPSIBLE_EXPANDED;
};

export const getActivityType = ({ acmeType }) => { 
  switch(acmeType) {
    case itineraryUnitTypes.ACTIVITY:
      return itineraryUnitTypes.ACTIVITY;
    case itineraryUnitTypes.MEALS:
      return itineraryUnitTypes.MEAL;
    case itineraryUnitSubTypes.ACT_TRANSFER:
      return itineraryUnitSubTypes.ACT_TRANSFER
    default:
      return itineraryUnitTypes.ACTIVITY
  }
 }

export const createActivityData = ({ activity, addActivityRestricted, ratePlanRestricted }) => {
  let day = null;
  let date = null;
  if (activity.day && activity.date) {
    day = activity.day;
    date = activity.date;
  } else {
    day = activity.activityAvailabilityInfoList?.[0].day;
    date = activity.activityAvailabilityInfoList?.[0].date;
  }
  if (date) {
    const dd = getNewDate(date);
    date = dd.getDate() + ' ' + MONTH_ARR_CAMEL[dd.getMonth()];
  }
  const { metaData = {}, imageDetail, additionalData = {}, detailExtraInfo = {} } = activity;
  const {
    locked = false,
    freebie,
    name = '',
    userRating = '',
    cityName = '',
    duration = '',
    suitableForPaxType = [],
    additionalInfo: additionaActivitylInfo = [],
    acmeType = itineraryUnitTypes.ACTIVITY,
  } = metaData || {};
  const { venueDetails = {} } = detailExtraInfo || {};
  const {
    description = '',
    importantInfo = [],
    highlightsList = [],
    additionalInfo = [],
  } = additionalData || {};
  const { images = [] } = imageDetail || {};

  const activityType = getActivityType({ acmeType })?.toLowerCase();
  const sectionData = [
    {
      sectionType: ACTIVITY_SECTION_TYPES.GENERIC,
      title: `About ${activityType}`,
      backgroundColor: holidayColors.white,
      titleTextColor: holidayColors.black,
      subTitle: '',
      images: [],
      info: additionaActivitylInfo,
      description,
      infoSeperator: INFO_SEPERATOR.DOT,
    },
    {
      sectionType: ACTIVITY_SECTION_TYPES.GENERIC,
      title: 'Highlights',
      backgroundColor: holidayColors.white,
      titleTextColor: holidayColors.black,
      subTitle: '',
      images: [],
      shouldShow: highlightsList?.length > 0,
      info: highlightsList,
      infoLimit: 3,
    },
    {
      sectionType: ACTIVITY_SECTION_TYPES.ACTIVITY_OPTIONS,
      title: `${activityType} Options`,
      backgroundColor: holidayColors.white,
      titleTextColor: holidayColors.black,
      shouldShow:
        (!addActivityRestricted || !ratePlanRestricted) && activity?.activityOptions.length > 0,
    },
    {
      sectionType: ACTIVITY_SECTION_TYPES.GENERIC,
      title: 'Important Info',
      backgroundColor: holidayColors.fadedYellow, //#FFEDD1,
      titleTextColor: holidayColors.yellow, //#CF8100,
      shouldShow: importantInfo?.length > 0,
      info: importantInfo.map((info) => {
        return {
          ...info,
          iconUrl: '',
        };
      }),
      infoLimit:3,
    },
    {
      sectionType: ACTIVITY_SECTION_TYPES.GENERIC,
      title: `${activityType} Photo and Videos`,
      images: images.slice(4).slice(-4) || [],
      shouldShow: images.slice(4).slice(-4)?.length > 0,
    },
    {
      sectionType: ACTIVITY_SECTION_TYPES.GENERIC,
      title: 'Addtional Info',
      shouldShow: additionalInfo?.length > 0,
      info: additionalInfo.map((info) => {
        return {
          ...info,
          iconUrl: '',
        };
      }),
      infoLimit:3,
    },
    {
      sectionType: ACTIVITY_SECTION_TYPES.LOCATION,
      title: 'Location',
      shouldShow: !isEmpty(venueDetails) && venueDetails?.showMap,
    },
  ];

  return sectionData;
};

export const createActivtyRatePlanOptionData = ({ activityOption }) => {
  const { description, sightSeeingDetail = {} } = activityOption || {};
  const {
    inclusionList = [],
    exclusionList = [],
    sightSeeingPoints = [],
  } = sightSeeingDetail || {};
  const sectionData = [
    {
      sectionType: ACTIVITY_SECTION_TYPES.GENERIC,
      title: 'About Activity',
      description,
    },
    {
      sectionType: ACTIVITY_SECTION_TYPES.GENERIC,
      title: 'Inclusion & Exclusion',
      shouldShow: [...inclusionList, ...exclusionList]?.length > 0,
      info: [...inclusionList, ...exclusionList],
      infoLimit: 10,
    },
    {
      sectionType: ACTIVITY_SECTION_TYPES.RATE_PLAN_POINTS,
      title: 'SightSeeing Point',
      info: sightSeeingPoints,
      shouldShow: sightSeeingPoints?.length > 0,
    },
  ];

  return sectionData;
};

export const updateOnActivityDetailPage = ({
  day,
  lastPageName,
  newRecheckKey,
  dynamicPackageId,
  activityReqParams,
  onComponentChange,
  activityMetaData = {},
  hideOverlays
}) => {
  const { activityList = [] } = activityReqParams || {};
  const { code: activityCode } = activityMetaData || {};
  const activitiesRequest = activityList.map(({ metaData, recheckKey, staySequence = 1 }) => {
    const { code, name, acmeSubType, acmeType } = metaData;

    const baseActivityRequest = {
      activityCode: code,
      staySequence,
      name,
      acmeType,
      acmeSubType,
    };

    if (code === activityCode && newRecheckKey) {
      return {
        ...baseActivityRequest,
        optionRecheckKey: newRecheckKey,
      };
    } else if (code !== activityCode) {
      return {
        ...baseActivityRequest,
        optionRecheckKey: recheckKey,
      };
    }
  });

  const actionData = {
    action: packageActions.MODIFY,
    dynamicPackageId,
    selectedActivities: activitiesRequest,
    day,
  };
  onComponentChange(actionData, componentImageTypes.ACTIVITY);
  if (isRawClient()) {
    holidayNavigationPop({
      overlayKeys: [Overlay.ACTIVITY_DETAIL_V2],
      hideOverlays,
    });
  } else {
    HolidayNavigation.navigate(lastPageName);

  }
};

export const getPreselectedActivity = ({ selectedRecheckKey, activityOptions }) => {
  return activityOptions.find((option) => {
    if (!isEmpty(option.slots)) {
      return options?.slots.find((slot) => {
        slot.slotRecheckKey === selectedRecheckKey;
      });
    }
    return option.recheckKey === selectedRecheckKey;
  });
};

// Array comparison utility - ignores order for primitive values
export const arraysEqualIgnoreOrder = (arr1, arr2) => {
  if (arr1.length !== arr2.length) return false;
  const set1 = new Set(arr1);
  const set2 = new Set(arr2);
  if (set1.size !== set2.size) return false;
  for (let item of set1) {
    if (!set2.has(item)) return false;
  }
  return true;
};

// Filter comparison utility for activity filters
export const filtersEqual = (filters1, filters2) => {
  if (!filters1 && !filters2) return true;
  if (!filters1 || !filters2) return false;
  if (filters1.length !== filters2.length) return false;

  // Sort filters by id for consistent comparison
  const sortedFilters1 = [...filters1].sort((a, b) => a.id - b.id);
  const sortedFilters2 = [...filters2].sort((a, b) => a.id - b.id);

  return sortedFilters1.every((filter1, index) => {
    const filter2 = sortedFilters2[index];
    return filter1.id === filter2.id && 
           arraysEqualIgnoreOrder(filter1.values, filter2.values);
  });
};

// Sorter comparison utility - optimized for single-selection sorters
export const sortersEqual = (sorters1, sorters2) => {
  if (!sorters1 && !sorters2) return true;
  if (!sorters1 || !sorters2) return false;
  if (sorters1.length !== sorters2.length) return false;
  
  // For single-selection sorters: either both empty or both have same id
  if (sorters1.length === 0 && sorters2.length === 0) return true;
  if (sorters1.length === 1 && sorters2.length === 1) {
    return sorters1[0].id === sorters2[0].id;
  }
  
  return false;
};