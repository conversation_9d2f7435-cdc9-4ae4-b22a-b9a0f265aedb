import { StyleSheet, Text, View } from 'react-native';
import React from 'react';
import { getIconForItineraryUnitType } from '../../../Utils/PhoenixDetailUtils';
import { PLEASE_NOTE_TEXT } from '../../combo/ComboConstats';
import { holidayColors } from 'mobile-holidays-react-native/src/Styles/holidayColors';
import { fontStyles } from 'mobile-holidays-react-native/src/Styles/holidayFonts';
import { marginStyles, paddingStyles } from 'mobile-holidays-react-native/src/Styles/Spacing';
import DayPlanRowHOC from '../Common/DayPlanRowHOC';

const NoCommute = (props) => {
  const {
    showBottomDivider = false,
    subText,
    removedText,
    type = '',
    defaultCollapsedState,
    itineraryUnit = {},
  } = props || {};
  const backgroundColor = showBottomDivider ? holidayColors.grayBorder : holidayColors.white;
  return (
    <DayPlanRowHOC
      unit={itineraryUnit}
      cardHeaderTexts={[{ text: type, isEmphasized: true }]}
      isFromCommute={true}
      defaultCollapsedState={defaultCollapsedState}
    >
      <View style={styles.container}>
        <View style={{ ...marginStyles.mr16 }}>
          {/* <Text style={styles.type}>{type}</Text> */}
          <Text style={styles.arriveText}>{subText}</Text>
          <Text style={styles.longMessage}>
            {PLEASE_NOTE_TEXT}
            <Text style={styles.longMessageSubText}>{removedText}</Text>
          </Text>
          {<View style={[styles.divider, { backgroundColor }]} />}
        </View>
      </View>
    </DayPlanRowHOC>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: holidayColors.white,
    // ...marginStyles.ml16,
    ...marginStyles.mr8,
  },
  arriveText: {
    color: holidayColors.black,
    ...fontStyles.labelBaseRegular,
  },
  type: {
    ...fontStyles.labelLargeBlack,
    color: holidayColors.black,
    ...marginStyles.mb8,
  },
  divider: {
    height: 1,
    ...marginStyles.mt16,
  },
  longMessage: {
    color: holidayColors.red,
    ...fontStyles.labelMediumBold,
    ...marginStyles.mt10,
  },
  longMessageSubText: {
    color: holidayColors.red,
    ...fontStyles.labelBaseRegular,
  },
  viewOptions: {
    marginLeft: 42,
    ...marginStyles.mr16,
    ...marginStyles.mb16,
  },
  iconStyle: {
    width: 20,
    height: 20,
    resizeMode: 'cover',
  },
});
export default NoCommute;
