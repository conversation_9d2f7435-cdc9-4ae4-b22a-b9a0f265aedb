import React, { useState } from 'react';
import { FlatList, Image, StyleSheet, Text, TouchableOpacity, View } from 'react-native';
import { connect } from 'react-redux';
import { actionStyle } from '../../../DayPlan/dayPlanStyles';
import { marginStyles, paddingStyles } from 'mobile-holidays-react-native/src/Styles/Spacing';
import { getIconForItineraryUnitType } from 'mobile-holidays-react-native/src/PhoenixDetail/Utils/PhoenixDetailUtils';
import { getMealDetailObject } from '../../ItineraryV2Utils';
import { fontStyles } from 'mobile-holidays-react-native/src/Styles/holidayFonts';
import { holidayColors } from 'mobile-holidays-react-native/src/Styles/holidayColors';

/* Components */
import HolidayImageHolder from 'mobile-holidays-react-native/src/Common/Components/HolidayImageHolder';
import DayPlanRowHOC from '../../Common/DayPlanRowHOC';
import MealsOverlay, { TYPE_OF_MEAL } from '../MealsOverlay';
import ItineraryUnitInformation from '../../Common/ItineraryUnitInformation';
import { trackPhoenixDetailLocalClickEvent } from '../../../../Utils/PhoenixDetailTracking';
import { logPhoenixDetailPDTEvents } from 'mobile-holidays-react-native/src/utils/PhoenixDetailPDTTrackingUtils';
import { PDT_EVENT_TYPES } from 'mobile-holidays-react-native/src/utils/HolidayPDTConstants';
import { capitalizeText } from 'mobile-holidays-react-native/src/utils/textTransformUtil';

const MealRowV2 = ({ itineraryUnit, packageDetail, updateMeal, defaultCollapsedState = '', day = '', city: dayCity = '' }) => {
  const [openMealOverlay, setOpenMealOverlay] = useState(false);
  const {
    text,
    parentComponentMeta,
    mealType = '',
    address = '',
    city = '',
    additionalInfos = [],
    ctaText = '',
  } = itineraryUnit || {};
  const { dynamicId, mealDetail = {} } = packageDetail || {};

  const renderInfoItem = ({ item, index }) => {
    const { iconUrl, textColorCode = holidayColors.gray, text, subType = '' } = item || {};
    const infoIconStyles = {
      ...styles.infoItemIcon,
      tintColor: textColorCode,
    };
    return (
      <View style={styles.infoItemContainer}>
        <HolidayImageHolder imageUrl={iconUrl} style={infoIconStyles} />
        <Text style={[styles.infoItemText, { color: textColorCode }]}>{text}</Text>
      </View>
    );
  };

  const captureClickEvents = ({ eventName = '' }) => {
    logPhoenixDetailPDTEvents({
      actionType: PDT_EVENT_TYPES.buttonClicked,
      value: eventName.replace(/_/g, '|'),
    });
    trackPhoenixDetailLocalClickEvent({
      eventName,
    });
  };

  const handleCtaClick = () => {
    const typeOfMeal =
      itineraryUnit?.itineraryUnitSubTypeMeta === TYPE_OF_MEAL.DAY_WISE_MEAL
        ? 'view_allpackage'
        : 'edit_package';
    captureClickEvents({ eventName: `${typeOfMeal}Meal_${day}_${dayCity}` });
    setOpenMealOverlay(true);
  };

  const handleCloseModal = () => {
    setOpenMealOverlay(false);
  };
  return (
    <>
      <DayPlanRowHOC
        day={day}
        city={dayCity}
        unit={itineraryUnit}
        defaultCollapsedState={defaultCollapsedState}
        cardHeaderTexts={[
          { text: 'MEAL', isEmphasized: true },
          ...(mealType ? [{ text: mealType, isEmphasized: false }] : []),
          ...(city ? [{ text: city, isEmphasized: false }] : []),
        ]}
        // cardSubHeader={city}
      >
        <View style={styles.mealRow}>
          {!!address && <Text style={styles.address}>{address}</Text>}
          <ItineraryUnitInformation info={additionalInfos} />
          {!!ctaText && (
            <TouchableOpacity onPress={handleCtaClick}>
              <Text style={styles.actionStyle}>{capitalizeText(ctaText)}</Text>
            </TouchableOpacity>
          )}
        </View>
      </DayPlanRowHOC>
      {openMealOverlay ? (
        <MealsOverlay
          day={day}
          city={dayCity}
          dynamicPackageId={dynamicId}
          closeModal={handleCloseModal}
          itineraryUnit={itineraryUnit}
          updateMeal={updateMeal}
          selectedMealCode={mealDetail?.meal?.mealCode}
          openMealOverlay={openMealOverlay}
        />
      ) : null}
    </>
  );
};

const styles = StyleSheet.create({
  mealRow: {
    flex:1,
  },
  address: {
    ...fontStyles.labelSmallRegular,
    color: holidayColors.gray,
  },
  infoItemContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    ...marginStyles.mv2,
  },
  infoItemText: {
    ...fontStyles.labelSmallRegula,
    color: holidayColors.gray,
  },
  infoItemIcon: {
    width: 14,
    height: 14,
    ...marginStyles.mr4,
  },
  actionStyle: {
    ...actionStyle,
    ...fontStyles.labelSmallBold,
  },
});

const mapStateToProps = (state) => {
  return {
    packageDetail: state.holidaysDetail?.detailData?.packageDetail || {},
  };
};

// export default MealRowV2;
export default connect(mapStateToProps, null)(MealRowV2);
