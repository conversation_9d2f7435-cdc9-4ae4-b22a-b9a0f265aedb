import React, { useEffect, useState } from 'react';
import { Text, View, Dimensions, StyleSheet, ActivityIndicator } from 'react-native';
import { packageMealsPromise } from '../../../../../utils/HolidayNetworkUtils';
import { holidayColors } from 'mobile-holidays-react-native/src/Styles/holidayColors';
import { logPhoenixDetailPDTEvents } from 'mobile-holidays-react-native/src/utils/PhoenixDetailPDTTrackingUtils';
import { PDT_EVENT_TYPES } from 'mobile-holidays-react-native/src/utils/HolidayPDTConstants';

/* Components */
import MealPlan from 'mobile-holidays-react-native/src/Common/Components/MealPlan/MealPlan';
import BottomSheetOverlay from '../../../../../Common/Components/BottomSheetOverlay';
import SelectableMealOption from '../../../../../Common/Components/MealPlan/SelectableMealPlan';
import { trackPhoenixDetailLocalClickEvent } from 'mobile-holidays-react-native/src/PhoenixDetail/Utils/PhoenixDetailTracking';
import { fontStyles } from 'mobile-holidays-react-native/src/Styles/holidayFonts';
import { marginStyles, paddingStyles } from 'mobile-holidays-react-native/src/Styles/Spacing';

export const TYPE_OF_MEAL = {
  PACKAGE_WISE_MEAL: 'PACKAGE_WISE_MEAL',
  DAY_WISE_MEAL: 'DAY_WISE_MEAL',
};
const MealOverlay = ({
  day = '',
  city = '',
  dynamicPackageId = '',
  closeModal,
  itineraryUnit = {},
  selectedMealCode,
  updateMeal,
  openMealOverlay
}) => {
  const [mealDetail, setMealDetail] = useState(null);
  const [loading, setLoading] = useState(true);
  const { itineraryUnitSubTypeMeta = 'DAY_WISE_MEAL' } = itineraryUnit || {};
  const isDayMeal = itineraryUnitSubTypeMeta === TYPE_OF_MEAL.DAY_WISE_MEAL;

  const fetchMealDetail = async () => {
    try {
      const packageMealResponse = await packageMealsPromise(dynamicPackageId);
      const response = await packageMealResponse.json();
      setMealDetail(response);
      setLoading(false);
    } catch (e) {
      setMealDetail([]);
      setLoading(false);
    }
  };
  useEffect(() => {
    fetchMealDetail();
    setLoading(true);
  }, []);

  const captureClickEvents = (eventName = '', suffix = '') => {
    logPhoenixDetailPDTEvents({
      actionType: PDT_EVENT_TYPES.buttonClicked,
      value: eventName + suffix,
    });
    trackPhoenixDetailLocalClickEvent({
      eventName,
      suffix,
    });
  };

  const handleUpdateMeal = (mealCode) => {
    captureClickEvents({
      eventName: 'feature_select_MEAL_',
      suffix: `${selectedMealCode}_${mealCode}`,
    });
    updateMeal(mealCode);
  };

  const renderMealOption = () => {
    return (
      <MealPlan
        packageMeals={mealDetail}
        containerStyle={{ flex: 0, ...paddingStyles.pl0 }}
        mealItemContainerStyles={{
          ...paddingStyles.pl16,
        }}
      />
    );
  };

  const renderSelectableMealOption = () => {
    const { mealListingData = {} } = mealDetail || {};
    const { meals = [], discountedFactor, packagePriceMap = [] } = mealListingData || {};
    if (mealListingData?.meals?.length > 0) {
      return (
        <SelectableMealOption
          day={day}
          city={city}
          options={meals}
          discountedFactor={discountedFactor}
          packagePriceMap={packagePriceMap}
          selectedMealCode={selectedMealCode}
          updateMeal={handleUpdateMeal}
        />
      );
    } else {
      return [];
    }
  };
  const renderContent = (mealType) => {
    switch (mealType) {
      case 'MEALS':
        return renderMealOption();
      case 'MEAL':
        return renderSelectableMealOption();
      default:
        return () => {};
    }
  };
  const renderProgressView = () => (
    <View style={styles.progressView}>
      <ActivityIndicator styleAttr="Inverse" size="large" color="#008cff" />
    </View>
  );

  return (
    <BottomSheetOverlay
      toggleModal={closeModal}
      showCross
      title={isDayMeal ? 'Day Wise Meal Plan' : 'Select Meal Option'}
      containerStyles={styles.bottomsheetContainer}
      visible={openMealOverlay}
    >
      {loading && renderProgressView()}
      <Text style={styles.description}>{mealDetail?.description}</Text>
      {!loading && mealDetail && renderContent(isDayMeal ? 'MEALS' : 'MEAL')}      
    </BottomSheetOverlay>
  );
};

const styles = StyleSheet.create({
  bottomsheetContainer: {
    maxHeight: Dimensions.get('window').height - 100,
    ...paddingStyles.pa16
  },
  progressView: {
    width: '100%',
    height: 200,
    backgroundColor: holidayColors.white,
    alignItems: 'center',
    justifyContent: 'center',
  },
  description: {
    ...fontStyles.labelBaseRegular,
    color: holidayColors.gray,
    ...paddingStyles.pl16,
    ...marginStyles.mt12,
  },
});

export default MealOverlay;
