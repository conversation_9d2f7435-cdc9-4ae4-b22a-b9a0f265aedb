import FlightIcon from '@mmt/legacy-assets/src/holidays/flight.webp';

export const flightRemovalPrompt = {
  title: 'Removing this flight?',
  content: 'Removing this flight will remove all flights in this package',
  icon: FlightIcon,
  onContinue: 'YES, REMOVE',
  cancel: `DON'T REMOVE`,
};

// Enum for component states
export const ITINERARY_COLLAPSED_STATE = {
  NON_COLLAPSIBLE: 'NONCOLLAPSIBLE',
  COLLAPSIBLE_EXPANDED: 'COLLAPSIBLE_EXPANDED',
  COLLAPISBLE_COLLAPSED: 'COLLAPSIBLE_COLLAPSED',
};

