import React, { useCallback, useEffect, useState } from 'react';
import { BackHandler, View } from 'react-native';
import { HOLIDAY_ROUTE_KEYS, HolidayNavigation } from '../../../../Navigation';
import { HOLIDAYS_ACTIVITY_OVERLAY_DETAIL, HOLIDAYS_ACTIVITY_OVERLAY_LISTING, VIEW_STATE } from '../../../Utils/PheonixDetailPageConstants';
import TravelTidbits from './TravelTidbits';
import TidbitsErrorView from './TidbitsErrorView';
import TidbitsProgressView from './TidbitsProgressView';
import { attachVersionInRequest, fetchActivityListingResponseForTidbits } from '../../../../utils/HolidayNetworkUtils';
import {
  createBlackStripData,
  createStaySeqData,
  getPreSelectedActivityWithRecheckKeyFromDetailResponse,
  getProductType,
  getSelectedActivitiesData,
  getSelectedActivitiesPayload,
  logAndTrackUpdatePress,
  updateActivityWithNewRecheckKey,
  updateSelectedActivitiesInState,
  validateResponses,
} from './TidBitsUtils';
import { DEFAULT_SELECTED_STAY_SEQUENCE } from '../../ActivityOverlay/ActivityListing/ActivityListingPageNew';
import { componentImageTypes, packageActions } from '../../../DetailConstants';
import { trackPhoenixDetailLocalClickEvent } from '../../../Utils/PhoenixDetailTracking';
import { PAGE_TYPES_FOR_API } from '../../../../HolidayConstants';
import { getPackagePrice } from '../../../Utils/ActivityOverlayUtils';
import { connect } from 'react-redux';
import { resetPageState, setBlackStripData, setHolidayActivitiesListingState, setSelectedActivitiesStripData } from '../../../Actions/HolidayTidbitsAction';
import { logPhoenixDetailPDTEvents } from 'mobile-holidays-react-native/src/utils/PhoenixDetailPDTTrackingUtils';
import { PDT_EVENT_TYPES } from 'mobile-holidays-react-native/src/utils/HolidayPDTConstants';
import { holidayNavigationPop, holidayNavigationPush } from '../../../Utils/DetailPageNavigationUtils';
import { Overlay } from '../../DetailOverlays/OverlayConstants';
import { isMobileClient } from '../../../../utils/HolidayUtils';
import HolidayDataHolder from '../../../../utils/HolidayDataHolder';
import { SUB_PAGE_NAMES } from '../../../../HolidayConstants';
import { getSubPageName, getSubPageNameListing } from '../../ActivityOverlay/ActivityUtils';
import { isEmpty } from 'lodash';

/**
 * TravelTidbitsContainer component.
 * This component is responsible for managing the state and behavior of the TravelTidbits feature.
 * It fetches data, handles user interactions, and renders the appropriate subcomponents based on the current state.
 *
 * @param {Object} props - The properties passed to the component.
 */
const TravelTidbitsContainer = (props) => {
  const {
    dynamicId,
    day,
    activityReqParams,
    pricingDetail,
    packageDetailDTO,
    roomDetails,
    onComponentChange,
    lastPage,
    currentActivePlanOnItinerary,
    activityProductType = '',
    viewState,
    setViewState,
    resetPageState,
    setBlackStripData,
    setSelectedActivitiesStripData,
    blackStripData,
    showOverlay,
    hideOverlays,
  } = props;

  const [response, setResponse] = useState(null);
  const [tabIndex, setTabIndex] = useState(0);
  const preSelectedActivities = getPreSelectedActivityWithRecheckKeyFromDetailResponse(activityReqParams);
  const [selectedActivities, setSelectedActivities] = useState(preSelectedActivities);

  const staySequences = createStaySeqData(activityReqParams);
  const [activeDaySequence, setActiveDaySequence] = useState( staySequences ? staySequences[0].staySequence : DEFAULT_SELECTED_STAY_SEQUENCE);
  const productType = activityProductType ? [activityProductType] : getProductType(currentActivePlanOnItinerary);

  useEffect(() => () => resetPageState(), [resetPageState]);

  const selectActivity = (activityCode, reCheckKey, name, acmeType, acmeSubType  ) => {
    updateSelectedActivitiesInState(name, activityCode, reCheckKey, acmeType, acmeSubType, setSelectedActivities);
  };

  const removeActivity = activityCode => {
    setSelectedActivities((currentCodesWithRecheckKey) =>
      currentCodesWithRecheckKey.filter((codeWithRecheckKey) => codeWithRecheckKey.code !== activityCode),
    );
  };


  // Effect hook for updating the pricing data when the selected activities change.
  useEffect(() => {
    const blackStripData = createBlackStripData(response, staySequences, activeDaySequence , pricingDetail, selectedActivities , preSelectedActivities, day);
    const selectedActivitiesData = getSelectedActivitiesData(response, activeDaySequence, selectedActivities, preSelectedActivities);
    setBlackStripData(blackStripData);
    setSelectedActivitiesStripData(selectedActivitiesData)
  }, [response, selectedActivities, activeDaySequence]);

  const createActionData = (selectedActivities) => {
    return {
      action: packageActions.MODIFY,
      dynamicPackageId: dynamicId,
      selectedActivities,
      day,
    };
  };

  const handleComponentChange = (actionData) =>
    onComponentChange(actionData, componentImageTypes.ACTIVITY);

  const navigateToLastPage = () => {
    if (isMobileClient()) {
      HolidayNavigation.replace(lastPage);
    } else {
      hideOverlays([Overlay.ACTIVITY_DETAIL_V2, Overlay.TRAVEL_TIBITS]);
    }
  };

  /* parametes are only needed when function called activity detail page */
  const onUpdatePress = ({ recheckKey = '', code = '', name = '', acmeType = '', acmeSubType = '' } = {}) => {
    const updatedSelectedActivities = [...selectedActivities, { code, recheckKey }].filter(Boolean);
    const selectedActivitiesPayload = getSelectedActivitiesPayload( response, updatedSelectedActivities, staySequences );

    if (code && recheckKey) {
      updateActivityWithNewRecheckKey(selectedActivitiesPayload, code, recheckKey, name, acmeType, acmeSubType);
    }

    const actionData = createActionData(selectedActivitiesPayload);
    handleComponentChange(actionData);
    navigateToLastPage();
    logAndTrackUpdatePress(blackStripData, staySequences);
  };

  const captureClickEvents = ({ eventName = '', suffix = '', prop1 = '' ,activityType=""}) => {
    const value = eventName + suffix ;
    logPhoenixDetailPDTEvents({
      actionType: PDT_EVENT_TYPES.buttonClicked,
      value : value.replace(/_/g , '|'),
      subPageName: isEmpty(prop1) ? getSubPageNameListing(activityType) : getSubPageName(activityType), 
    })
    trackPhoenixDetailLocalClickEvent({
      eventName, 
      suffix, 
      prop1: prop1 || HOLIDAYS_ACTIVITY_OVERLAY_LISTING,
    })

  }

 const modifyActivityDetail = async (add, activityCode, recheckKey, name, activityType) => {
  if (add) {
    selectActivity(activityCode, recheckKey, name);
  } else {
    removeActivity(activityCode);
  }

  captureClickEvents({
    eventName: add ? 'add_' : 'remove_',
    suffix: activityCode,
    prop1: HOLIDAYS_ACTIVITY_OVERLAY_DETAIL,
    activityType
  });
};

  const openActivityDetailPage = ({ code, safe, openSlotOverlay = false, scrollToRatePlan = false, activityType = '', city = '' } = {}) => {
    const activity = selectedActivities.find((activity) => activity.code === code);
    let selected = false
    if(activity) {
      selected = !activity;
    }

    captureClickEvents({
      eventName: openSlotOverlay ? 'ChangetimeSlot_' : scrollToRatePlan ?  'MoreOptions_' : 'view_',
      suffix: `${activityType || 'ACTIVITY'}_${city}_${day}`,
      activityType
    });
    const navigationParams = {
      blackStripData: {
        ...blackStripData,
        addonPrice: pricingDetail?.categoryPrices?.[0]?.addonsPrice ?? 0,
      },
      packagePrice: getPackagePrice(pricingDetail),
      dynamicPackageId: dynamicId,
      staySequence: activeDaySequence,
      day,
      activityCode: code,
      selectedRecheckKey: activity?.recheckKey,
      modifyActivityDetail: (add, activityCode, recheckKey, name)=>modifyActivityDetail(add, activityCode, recheckKey, name, activityType),
      modifyActivityTrue: true,
      onUpdatePress: onUpdatePress,
      selected,
      subtitleData: props.subtitleData,
      branch: props.branch,
      packageDetailDTO: packageDetailDTO,
      roomDetails: roomDetails,
      setActivityList: ()=> {},
      openSlotOverlay,
      showOverlay,
      hideOverlays,
      scrollToRatePlan,
    };
    if(isMobileClient()) {
      HolidayNavigation.push(HOLIDAY_ROUTE_KEYS.ACTIVITY_DETAIL_V2, navigationParams);
    } else {
      holidayNavigationPush({
        props: navigationParams,
        pageKey: HOLIDAY_ROUTE_KEYS.ACTIVITY_DETAIL_V2,
        overlayKey: Overlay.ACTIVITY_DETAIL_V2,
        showOverlay: showOverlay,
        hideOverlays: hideOverlays,
      })
    }
    // HolidayNavigation.push(HOLIDAY_ROUTE_KEYS.ACTIVITY_DETAIL_V2, navigationParams);
  };

  const fetchData = async () => {
    setViewState(VIEW_STATE.LOADING);
    try {
      const responses = await fetchResponses();
      if (!validateResponses(responses)) {
        setViewState(VIEW_STATE.ERROR);
        return;
      }
      updateResponses(responses);
      setViewState(VIEW_STATE.SUCCESS);
    } catch (error) {
      setViewState(VIEW_STATE.ERROR);
    }
  };

  useEffect(() => {
    fetchData();
  }, [day, selectedActivities]);

  // Function for fetching the responses for all stay sequences.
  const fetchResponses = async () => {
    return Promise.all(staySequences.map(fetchSequenceResponse));
  };

  const createFetchPayload = (sequence) => {
    const { staySequence } = sequence || {};
    const payload = {
      dynamicPackageId: dynamicId,
      staySequence,
      day: day,
      //@todo ASHISH remove this once monika change's is live
      selectedActivities: selectedActivities.map((activity) => {
        return {
          code: activity.code,
          recheckKey: activity.recheckKey,
        };
      }),
      activityCodes: selectedActivities.map((activity) => {
        return activity.code;
      }),
      v2: true,
      productType,
    };
    return attachVersionInRequest(payload, PAGE_TYPES_FOR_API.DETAIL);
  };

  const fetchSequenceResponse = async ( sequence ) => {
    const payload = createFetchPayload(sequence);
    const responseBody = await fetchActivityListingResponseForTidbits(payload);
    return [sequence.staySequence, responseBody];
  };

  const updateResponses = (responses) => {
    const responseMap = Object.fromEntries(responses);
    setResponse(responseMap);
  };
  useEffect(() => {
    HolidayDataHolder.getInstance().setSubPageName(SUB_PAGE_NAMES.ACTIVITY_LISTING)
    return () => {
      HolidayDataHolder.getInstance().clearSubPageName()
    }
  }, [])

  // Rendering the appropriate subcomponent based on the view state.
  return (
    <View style={{ flex: 1 }}>
      {viewState === VIEW_STATE.SUCCESS && (
        <TravelTidbits
          {...props}
          activeDaySequence={activeDaySequence}
          response={response}
          setActiveDaySequence={setActiveDaySequence}
          selectActivity={selectActivity}
          removeActivity={removeActivity}
          onUpdatePress={onUpdatePress}
          selectedActivities={selectedActivities}
          preSelectedActivities={preSelectedActivities}
          blackStripData={blackStripData}
          openActivityDetailPage={openActivityDetailPage}
          tabIndex={tabIndex}
          setTabIndex={setTabIndex}
        />
      )}
      {viewState === VIEW_STATE.ERROR && (
        <TidbitsErrorView
          productType={productType}
          departureDetail={props?.departureDetail || {}}
          day={day}
        />
      )}
      {viewState === VIEW_STATE.LOADING && <TidbitsProgressView />}
    </View>
  );
};

const mapDispatchToProps = (dispatch) => {
  return {
    setViewState: state => dispatch(setHolidayActivitiesListingState(state)),
    setBlackStripData: data => dispatch(setBlackStripData(data)),
    resetPageState: () => dispatch(resetPageState()),
    setSelectedActivitiesStripData : data => dispatch(setSelectedActivitiesStripData(data)),
  }
}
const  mapStateToProps = state => {
  const { viewState, blackStripData } = state.travelTidbitsReducer
  return { viewState, blackStripData };
}

export default connect(mapStateToProps, mapDispatchToProps)(TravelTidbitsContainer);
