import React, { useCallback, useEffect, useMemo, useState } from 'react';
import { useFocusEffect } from '@mmt/navigation';
import {
  BackHandler,
  StatusBar,
  StyleSheet,
  useWindowDimensions,
  View,
  Platform,
  Text,
} from 'react-native';
import { TabView } from 'react-native-tab-view';
import TidbitsTabBar from './TidbitsTabBar';
import BlackUpdateFooter from '../../BlackUpdateFooter';
import Divider from '../../../../Common/Components/Divider';
import {
  createStaySeqData,
  createTabRoutes,
  getActivityDataByType,
  getSelectedActivitiesData,
  getTidbitListingPageHeader,
  hasNewActivitiesSelected,
} from './TidBitsUtils';
import ActivityListingPage from '../../ActivityOverlay/ActivityListing/ActivityListingPageNew';
import ConfirmationPopup from '../../ConfirmationPopup/ConfirmationPopup';
import PageHeader from '../../../../Common/Components/PageHeader';
import { paddingStyles } from '../../../../Styles/Spacing';
import { holidayColors } from '../../../../Styles/holidayColors';
import CitySelectionPopup from './CitySelectionPopup';
import SubtitleComponent from './HeaderSubtitle';
import ActivityInfoMessage from './ActivityInfoMessage';
import { isEmpty } from 'lodash';
import { HARDWARE_BACK_PRESS } from '../../../../SearchWidget/SearchWidgetConstants';
import { HolidayNavigation } from '../../../../Navigation';
import { trackPhoenixDetailLocalClickEvent } from '../../../Utils/PhoenixDetailTracking';
import { HOLIDAYS_ACTIVITY_OVERLAY_LISTING } from '../../../Utils/PheonixDetailPageConstants';
import { connect } from 'react-redux';
import { fontStyles } from '../../../../Styles/holidayFonts';
import { logPhoenixDetailPDTEvents } from 'mobile-holidays-react-native/src/utils/PhoenixDetailPDTTrackingUtils';
import { PDT_EVENT_TYPES } from 'mobile-holidays-react-native/src/utils/HolidayPDTConstants';
import { holidayNavigationPop } from '../../../Utils/DetailPageNavigationUtils';
import { Overlay } from '../../DetailOverlays/OverlayConstants';
import { getSubPageNameListing } from '../../ActivityOverlay/ActivityUtils';
import useBackHandler from '../../../../hooks/useBackHandler';
import { capitalizeText } from '../../../../../src/utils/textTransformUtil';

const TravelTidbits = (props) => {
  const {
    response,
    roomDetails,
    departureDetail,
    itineraryUnitDate = '',
    onBackPressed,
    selectedActivities,
    onUpdatePress,
    setActiveDaySequence,
    activeDaySequence,
    preSelectedActivities,
    tabIndex,
    setTabIndex,
    selectedActivitiesStripData,
    hideOverlays,
  } = props || {};
  if (isEmpty(response)) {
    return [];
  }

  const layout = useWindowDimensions();
  const [index, setIndex] = useState(tabIndex);
  const [searchedText, setSearchedText] = useState('');
  const [confirmDialogVisibility, setConfirmDialogVisibility] = useState(false);
  const [selectCityPopupVisibility, setSelectCityPopupVisibility] = useState(false);
  const staySequences = createStaySeqData(props.activityReqParams);
  const activeDayResponse = response[activeDaySequence];

  const TAB_ROUTES = createTabRoutes(activeDayResponse);
  const SHOW_TABS = TAB_ROUTES.length > 1;
  const renderScene = ({ route }) => {
    const { key } = route || '';
    const pageData = getActivityDataByType(key, activeDayResponse);
    if (pageData) {
      return (
        <ActivityListingPage
          pageData={pageData}
          {...props}
          activityType={key}
          searchedText={searchedText}
        />
      );
    }
    return [];
  };

  const handleBackPressed = () => {
    if (hasNewActivitiesSelected(preSelectedActivities, selectedActivities)) {
      setConfirmDialogVisibility(true);
    } else {
      if (isMobileClient()) {
        HolidayNavigation.pop();
      } else {
        onBack();
      }
    }
  };
  const onBackPress = () => {
    handleBackPressed();
    return true;
  };

  const onNotNowClicked = () => {
    onBack();
  }; //onBackPressed();

  const onBack = () => {
    if (isMobileClient()) {
      HolidayNavigation.pop();
    } else {
      holidayNavigationPop({
        overlayKeys: [Overlay.TRAVEL_TIBITS],
        hideOverlays,
      });
    }
  };

  useBackHandler(onBackPress)

  const onChangeCity = (staySequence) => {
    setActiveDaySequence(staySequence);
    toggleSelectCityPopupVisibility();
  };

  const toggleSelectCityPopupVisibility = () =>
    setSelectCityPopupVisibility(!selectCityPopupVisibility);

  const captureClickEvents = ({ eventName = ''}) => {
    const acmeType=eventName.split('_')[1]
    logPhoenixDetailPDTEvents({
      actionType: PDT_EVENT_TYPES.buttonClicked,
      value: eventName.replace(/_/g, '|'),
      subPageName : getSubPageNameListing(acmeType),

    })
    trackPhoenixDetailLocalClickEvent({
      eventName,
      prop1: HOLIDAYS_ACTIVITY_OVERLAY_LISTING,
    });
  };
  const onIndexChange = (index) => {
    const activityType = TAB_ROUTES?.[index].key;
    captureClickEvents({
      eventName: `${'TAB'}_${activityType}_${index}`,
    });
    setTabIndex(index);
    setIndex(index);
  };
  const getSubtitleViews = () => {
    return (
      <SubtitleComponent
        departureDetail={departureDetail}
        roomDetails={roomDetails}
        staySequences={staySequences}
        activeDaySequence={activeDaySequence}
        toggleSelectCityPopupVisibility={toggleSelectCityPopupVisibility}
      />
    );
  };
  const TitleAndSubTitleView = () => {
    return (
      <View style={styles.titleStyles}>
        <Text style={styles.title}>{capitalizeText(getTidbitListingPageHeader(TAB_ROUTES))}</Text>
        {getSubtitleViews()}
      </View>
    );
  };
  return (
    <View style={[styles.container, { backgroundColor: holidayColors.lightGray2 }]}>
      <PageHeader
        showShadow
        showBackBtn
        onBackPressed={handleBackPressed}
        containerStyles={styles.headerContainer}
        showSearch={true}
        onTextChange={setSearchedText}
        searchTextPlaceholder={'Search by Activity Name'}
        activeTabIndex={index}
        leftComponent={<TitleAndSubTitleView />}
      />
      <Divider height={2} />
      {SHOW_TABS ? (
        <TabView
          navigationState={{ index, routes: TAB_ROUTES }}
          renderScene={renderScene}
          onIndexChange={onIndexChange}
          initialLayout={{ width: layout.width }}
          renderTabBar={TidbitsTabBar}
        />
      ) : (
        <ActivityListingPage
          pageData={getActivityDataByType(TAB_ROUTES?.[0]?.key, activeDayResponse)}
          {...props}
          activityType={TAB_ROUTES?.[0]?.key}
          searchedText={searchedText}
        />
      )}
      <ActivityInfoMessage selectedActivitiesStripData={selectedActivitiesStripData} />
      <BlackUpdateFooter onUpdatePress={onUpdatePress} />
      <ConfirmationPopup
        confirmDialogVisibility={confirmDialogVisibility}
        onUpdatePackageClickFromPopup={onUpdatePress}
        onNotNowClicked={onNotNowClicked}
        onShowMeClicked={() => setConfirmDialogVisibility(false)}
      />
      <CitySelectionPopup
        staySequences={staySequences}
        activeDaySequence={activeDaySequence}
        onChangeCity={onChangeCity}
        selectCityPopupVisibility={selectCityPopupVisibility}
        hideSelectCityPopupVisibility={toggleSelectCityPopupVisibility}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  headerContainer: {
    backgroundColor: holidayColors.white,
  },
  title: {
    ...fontStyles.labelMediumBold,
    color: holidayColors.black,
  },
});

const mapStateToProps = state => {
  const { selectedActivitiesStripData } = state.travelTidbitsReducer;
  return { selectedActivitiesStripData };
};

export default connect(mapStateToProps, null)(TravelTidbits);
