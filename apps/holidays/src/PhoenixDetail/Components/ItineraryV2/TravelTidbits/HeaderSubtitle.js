import React from 'react';
import { View, Text, TouchableOpacity, StyleSheet } from 'react-native';
import { SUB_HEADER_DATE_FORMAT } from '../../Header';
import { getNewDate } from '@mmt/legacy-commons/Common/utils/DateUtils';
import fecha from 'fecha';
import HolidayImageHolder from '../../../../Common/Components/HolidayImageHolder';
import iconArrowDown from '../../images/ic_downArrow.png';
import { getCityForSelectedStaySequence } from './TidBitsUtils';
import { marginStyles, paddingStyles } from '../../../../Styles/Spacing';
import { holidayColors } from '../../../../Styles/holidayColors';
import { fontStyles } from '../../../../Styles/holidayFonts';
import { getPaxDetails } from '../../../../utils/HolidayUtils';

const SubtitleComponent = ({
  departureDetail,
  staySequences,
  activeDaySequence,
  toggleSelectCityPopupVisibility,
  roomDetails,
  itineraryUnitDate = null,
}) => {
  if (!departureDetail?.departureDate && !itineraryUnitDate) return null;
  const { kidsCount, adult } = getPaxDetails({ roomDetails });

  const date = fecha.format(getNewDate(itineraryUnitDate || departureDetail.departureDate), SUB_HEADER_DATE_FORMAT);
  const adultText = adult > 0 ? `${adult} ${adult === 1 ? 'Adult' : 'Adults'}` : null;
  const kidsText = kidsCount > 0 ? `${kidsCount} ${kidsCount === 1 ? 'Child' : 'Children'}` : null;
  const cityName = getCityForSelectedStaySequence(staySequences, activeDaySequence);
  const showSelectCityText = staySequences.length > 1;

  return (
    <View style={styles.subTitleContainer}>
      {date && <Text style={styles.subTitle}>{date}</Text>}
      {adultText && (
          <Text style={styles.subTitle}>, {adultText}</Text>
      )}
      {kidsText && (
          <Text style={styles.subTitle}>,{kidsText}</Text>
      )}
      {showSelectCityText && (
        <TouchableOpacity
          onPress={toggleSelectCityPopupVisibility}
          style={{ flexDirection: 'row' }}
        >
          <Text style={styles.city}>{cityName}</Text>
          <HolidayImageHolder style={styles.iconArrowLeft} defaultImage={iconArrowDown} />
        </TouchableOpacity>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  iconArrowLeft: {
    width: 24,
    height: 24,
    marginTop: -3,
  },
  subTitleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    ...paddingStyles.pt2,
  },
  subTitle: {
    ...fontStyles.labelSmallRegular,
    color: holidayColors.gray,
  },
  city: {
    ...fontStyles.labelSmallBold,
    color: holidayColors.primaryBlue,
    ...marginStyles.ml8,
  },
  dot: {
    height: 4,
    width: 4,
    backgroundColor: holidayColors.green,
    borderRadius: 50,
    ...marginStyles.ml6,
    ...marginStyles.mr6,
  },
  iconArrowDown: {
    width: 16,
    height: 16,
    ...marginStyles.mh8,
  },
});
export default SubtitleComponent;
