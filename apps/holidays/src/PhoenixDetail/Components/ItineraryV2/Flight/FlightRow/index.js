import React from 'react';
import { FlatList, StyleSheet, View } from 'react-native';
import {
  getFlightDuration,
  getFlightObject,
  getIconForItineraryUnitType,
} from '../../../../Utils/PhoenixDetailUtils';
import {
  trackLocalClickEvent,
  trackPhoenixDetailLocalClickEvent,
} from '../../../../Utils/PhoenixDetailTracking';
import { itineraryUnitTypes } from '../../../../DetailConstants';
import { createBaggageDetailRequestBody } from '../../../FlightDetailPage/FlightListing/FlightsUtils';
import { ischeckinBaggageInfoAvailable } from '../../../../../utils/HolidayUtils';
import { HOLIDAY_ROUTE_KEYS, HolidayNavigation } from '../../../../../Navigation';
import { holidayColors } from '../../../../../Styles/holidayColors';
import { paddingStyles } from 'mobile-holidays-react-native/src/Styles/Spacing';
import { smallHeightSeperator } from 'mobile-holidays-react-native/src/Styles/holidaySpacing';

/* Components */
import DayPlanRowHOC from '../../Common/DayPlanRowHOC';
import FlightCard from './FlightCardComponents/FlightCard';
import BaggageIconDetail from '../../../../../Common/Components/BaggageIconDetail';
import DummyFlightRow from '../DummyFlightRowV2';
import FlightMessages from '../../../FlightMessages';
import ItineraryUnitExtraInfoMessages from '../../../ItineraryUnitExtraInfoMessages';
import { logPhoenixDetailPDTEvents } from 'mobile-holidays-react-native/src/utils/PhoenixDetailPDTTrackingUtils';
import { PDT_EVENT_TYPES } from 'mobile-holidays-react-native/src/utils/HolidayPDTConstants';

const FlightRow = ({
  ifFlightGroupFailed,
  day,
  city,
  from = '',
  flightDetail,
  itineraryUnit,
  accessRestriction,
  viewDetailAccessRestriction,
  onViewDetailPress,
  packageContent,
  removeFlights,
  flightReqParams,
  packageDetailDTO,
  trackLocalPageLoadEvent,
  lastPageName,
  onPackageComponentToggle,
  destinationName,
  onComponentChange,
  pricingDetail,
  subtitleData,
  roomDetails,
  fromPresales = false,
  showBottomDivider = true,
  defaultCollapsedState,
  isFlightRowFromCommute,
  showCommuteBorder = false,
}) => {
  const { text = '', flight = {} } = itineraryUnit || {};
  const sellableId = flight?.sellableId || '';
  // NULL CHECK Condition
  if(!sellableId) {
    return null;
  }

  const flightObject = getFlightObject(flightDetail, sellableId);
  const {
    flightMetadataDetail = {},
    fromAirport,
    toAirport,
    flightLegs,
    duration: dur,
    stops,
  } = flightObject || {};
  const {
    checkInBaggage = true,
    isDummy = false,
    flightExtraInfo = [],
  } = flightMetadataDetail || {};
  const borderColor = showBottomDivider ? holidayColors.grayBorder : holidayColors.white; //TODO check if this is needed

  if (!flightObject) {
    return null;
  }
  const duration = getFlightDuration(dur);
  const showBaggageInfo = ischeckinBaggageInfoAvailable(flightMetadataDetail);
  const cardHeaderTexts = isDummy
    ? [{ text: 'TENTATIVE FLIGHT', isEmphasized: true }]
    : [
        { text: 'FLIGHT', isEmphasized: true },
        { text: duration, isEmphasized: false },
      ];

  const captureClickEvents = ({eventName = '', suffix = '', prop1 = ''}) => {
    const value = eventName + suffix
    logPhoenixDetailPDTEvents({
      actionType : PDT_EVENT_TYPES.buttonClicked,
      value : value.replace(/_/g, '|'),
    })
    trackPhoenixDetailLocalClickEvent({
      eventName,
      suffix,
      prop1: `base:${itineraryUnitTypes.FLIGHT}`, 
    })
  }

  const openFlightPage = ({ replace = false, showUpgradeableFlights = false } = {}) => {
    const { flightSelections, overnightDelays } = flightReqParams;
    const requestParams = {};
    if (flightObject.flightSequence) {
      requestParams.listingFlightSequence = flightObject.flightSequence;
    }
    if (flightSelections && flightSelections.length > 0) {
      requestParams.flightSelections = flightSelections;
    }
    if (overnightDelays) {
      requestParams.overnightDelays = overnightDelays;
    }
    captureClickEvents({
      eventName: showUpgradeableFlights ? 'upgrade_flights_' : 'change_',
      suffix: showUpgradeableFlights
        ? `${day}`
        : `${itineraryUnitTypes.FLIGHT}_${destinationName}_${day}`,
    });
    const flightListingProps = {
      flightRequestObject: requestParams,
      dynamicId: packageDetailDTO.dynamicPackageId,
      pricingDetail: pricingDetail,
      onComponentChange: onComponentChange,
      onPackageComponentToggle: onPackageComponentToggle,
      subtitleData: subtitleData,
      accessRestriction: accessRestriction,
      lastPage: lastPageName,
      packageDetailDTO,
      roomDetails,
      trackLocalClickEvent,
      trackLocalPageLoadEvent,
      isFlightFailed: ifFlightGroupFailed,
      showUpgradeableFlights,
      flightUpgradeDetails: flightDetail?.flightUpgradeDetail || {},
    };
    if (replace) {
      HolidayNavigation.replace(HOLIDAY_ROUTE_KEYS.FLIGHT_LISTING, flightListingProps);
    } else {
      HolidayNavigation.push(HOLIDAY_ROUTE_KEYS.FLIGHT_LISTING, flightListingProps);
    }
  };

  const openFlightDetailPage = () => {
    captureClickEvents({
      eventName: 'view_',
      suffix: `${itineraryUnitTypes.FLIGHT}_${destinationName}_${day}`,
    });
    const { flightSelections, overnightDelays } = flightReqParams;
    const data = { initialScrollIndex: 0, flights: [], roundTrip: false };
    const flightName = ['Departure Flights', 'Return Flights'];
    let heading = '';
    const len = flightSelections ? flightSelections.length : 2;
    if (flightObject.flightSequence < len) {
      heading = flightName[0];
    } else {
      heading = flightName[1];
    }
    data.flights.push({
      heading: heading,
      flight: flightObject,
    });
    const requestObject = createBaggageDetailRequestBody(
      flightSelections,
      packageDetailDTO.dynamicPackageId,
      overnightDelays,
    );
    let baggageInfo = null;
    if (
      packageContent &&
      packageContent.flightContent &&
      packageContent.flightContent.baggageInfoMap
    ) {
      baggageInfo = packageContent.flightContent.baggageInfoMap;
    }

    HolidayNavigation.push(HOLIDAY_ROUTE_KEYS.FLIGHT_DETAIL, {
      data: data,
      onUpdatePress: () => {},
      shortListCardData: null,
      onRemovePress: () => remove(),
      requestObject: requestObject,
      subtitleData: subtitleData,
      accessRestriction: accessRestriction,
      showOptions: true,
      baggageInfo: baggageInfo,
      trackLocalClickEvent: trackLocalClickEvent,
      onBackPress: () => openFlightPage({ replace: true }),
    });
  };

  const onViewDetail = (pos, hotelSellableId, day, lastPageName, lob) => {
    openFlightDetailPage();
  };

  const remove = () => {
    HolidayNavigation.pop();
    captureClickEvents({
      eventName: 'remove',
      suffix: `${day}_flights_confirm`,
    });

    removeFlights();
  };

  const FlightCards = (flightCardProps) => {
    const { flightLegs } = flightCardProps || {};
    const renderFlightCard = ({ item, index }) => (
      <FlightCard
        key={index}
        index={index}
        flightData={item}
        flightCardProps={flightCardProps}
        viewDetailAccessRestriction={viewDetailAccessRestriction}
      />
    );

    const keyExtractor = (item, index) => index.toString();

    return (
      <FlatList
        data={flightLegs}
        renderItem={renderFlightCard}
        keyExtractor={keyExtractor}
        ListEmptyComponent={null}
      />
    );
  };

  const renderBaggageInfo = () => {
    return (
      <View style={styles.baggageIconContainer}>
        {showBaggageInfo ? <BaggageIconDetail checkInBaggage={checkInBaggage} /> : null}
      </View>
    );
  };

  const renderContent = () => {
    const containerStyle = isFlightRowFromCommute ? smallHeightSeperator : '';
    if (isDummy) {
      return (
        <DummyFlightRow
          flightObject={flightObject}
          itineraryUnit={itineraryUnit}
          removeFlights={removeFlights}
          day={day}
        />
      );
    }
    
    return (
      <View style={[styles.flightRow, containerStyle, { borderColor }]}>
        <FlightCards
          ifFlightGroupFailed={ifFlightGroupFailed}
          day={day}
          flightLegs={flightLegs}
          stops={stops}
          accessRestriction={accessRestriction}
          onViewDetailPress={onViewDetail}
          sellableId={sellableId}
          openFlightListingPage={openFlightPage}
          removeFlights={removeFlights}
        />
        {/* Add All Flight Related Messages in the below Component */}
        <ItineraryUnitExtraInfoMessages extraInfo={flightExtraInfo} containerStyles={{ ...paddingStyles.pb6}}/>
        {/* Upgrade Flight Messages in the below Component */}
        <FlightMessages
          flightDetail={flightDetail}
          flightObject={flightObject}
          fromPresales={fromPresales}
          openFlightPage={openFlightPage}
        />
      </View>
    );
  };

  return (
    <DayPlanRowHOC
      day={day}
      city={city}
      unit={itineraryUnit}
      cardHeaderTexts={cardHeaderTexts}
      isFromCommute={isFlightRowFromCommute}
      defaultCollapsedState={defaultCollapsedState}
      renderHeaderDetails={isDummy ? () => {} : renderBaggageInfo}
      cardSubHeader={`${fromAirport?.airportCity} to ${toAirport?.airportCity}`}
      showCommuteBorder={showCommuteBorder}
    >
      {renderContent()}
    </DayPlanRowHOC>
  );
};

const styles = StyleSheet.create({
  flightRow: {
    flex: 1,
  },
  baggageIconContainer: {
    marginLeft: 'auto',
  },
});

export default FlightRow;
