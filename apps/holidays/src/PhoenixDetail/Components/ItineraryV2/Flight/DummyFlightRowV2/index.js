import React from 'react';
import { StyleSheet, View } from 'react-native';
import PropTypes from 'prop-types';
import AlertIcon from '@mmt/legacy-assets/src/alert_cream.webp';
import DummyFlightCards from './dummyFlightCardV2';
import { isEmpty } from 'lodash';
import { holidayColors } from '../../../../../Styles/holidayColors';
import { fontStyles } from '../../../../../Styles/holidayFonts';
import { paddingStyles } from 'mobile-holidays-react-native/src/Styles/Spacing';
import ItineraryUnitExtraInfoMessages from '../../../ItineraryUnitExtraInfoMessages';

const DummyFlightRow = ({
  day = 0,
  flightObject,
  itineraryUnit,
  removeFlights = null,
  isReviewPage = false,
}) => {
  const { flightMetadataDetail = {}, flightLegs = [], stops } = flightObject || {};
  const { dummyFlightMessage = '', flightExtraInfo = [] } = flightMetadataDetail || {};

  return (
    <DummyFlightCards
      flightLegs={flightLegs}
      stops={stops}
      day={day}
      removeFlights={removeFlights}
      isReviewPage={isReviewPage}
      flightExtraInfo={flightExtraInfo}
    />
  );
};

DummyFlightRow.propTypes = {
  day: PropTypes.number,
  flightObject: PropTypes.object.isRequired,
  itineraryUnit: PropTypes.object.isRequired,
  removeFlights: PropTypes.func,
  isReviewPage: PropTypes.bool,
};

export default DummyFlightRow;
