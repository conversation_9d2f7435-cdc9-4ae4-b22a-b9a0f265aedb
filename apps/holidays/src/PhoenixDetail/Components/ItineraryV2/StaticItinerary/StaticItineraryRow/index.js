import React, { useState } from 'react';
import { View, Text, FlatList, TouchableOpacity, StyleSheet } from 'react-native';
import LinearGradient from 'react-native-linear-gradient';
import {
  staticDataFDInterface,
  StaticItinerary,
  Image,
  Destination,
} from '../../../../Types/PackageDetailApiTypes';
import { actionStyle, dayPlanRowContainerStyle } from '../../../DayPlan/dayPlanStyles';
import { fontStyles } from '../../../../../Styles/holidayFonts';
import { holidayColors } from '../../../../../Styles/holidayColors';
import { holidayBorderRadius } from '../../../../../Styles/holidayBorderRadius';
import { marginStyles, paddingStyles } from 'mobile-holidays-react-native/src/Styles/Spacing';
import { RESIZE_MODE_IMAGE } from 'mobile-holidays-react-native/src/HolidayConstants';

/* Components */
import ItineraryUnitImageWrapper from '../../Common/ItineraryUnitImage';
import ReadMoreReadLessHTMLText from '../../../../../Common/Components/ReadMoreLessHTMLText';
import DayPlanRowHOC from '../../Common/DayPlanRowHOC';
import HolidayImageHolder from '../../../../../Common/Components/HolidayImageHolder';
import { htmlTextSmallStyles } from '../../dayPlanV2Styles';
import { logPhoenixDetailPDTEvents } from 'mobile-holidays-react-native/src/utils/PhoenixDetailPDTTrackingUtils';
import { PDT_EVENT_TYPES } from '../../../../../utils/HolidayPDTConstants';
import { trackPhoenixDetailLocalClickEvent } from '../../../../Utils/PhoenixDetailTracking';

const StaticItineraryComponentV2 = ({
  staticData,
  isOverlay,
  destinationMap,
  defaultCollapsedState,
  city = '',
}) => {
  const { data = {}, images, day } = staticData || {};
  const { description } = data || {};
  const destination =
    destinationMap && destinationMap.has(day) ? destinationMap.get(day) : undefined;
  const destinationName = destination ? destination.name : null;
  const clickEventParams = {component: 'dayOverview', sectionName: `${day}_${city}`};

  const captureClickEvents = ({eventName = '', prop1 = ''}) => {
    logPhoenixDetailPDTEvents({
      actionType: PDT_EVENT_TYPES.buttonClicked,
      value: eventName.replace(/_/g, '|'),
      subPageName: prop1,
    })
    trackPhoenixDetailLocalClickEvent({
      eventName, 
      prop1,
    })
  }

  const renderImages = ({ item, index, isSingleImage = false }) => {
    const { fullPath = '' } = item || {};

    const iconStyles = isSingleImage
      ? {
          image: styles.singleImage,
          imageContainer: styles.singleImageContainer,
        }
      : {
          image: styles.image,
          imageContainer: styles.imageContainer,
        };
    return (
      <ItineraryUnitImageWrapper
        imageUrl={fullPath}
        containerStyle={iconStyles.imageContainer}
        imageStyle={iconStyles.imageContainer}
        esizeMode={RESIZE_MODE_IMAGE.COVER}
      />
    );
  };

  return (
    <DayPlanRowHOC
      day={day}
      city={city}
      cardHeaderTexts={[
        {
          text: 'DAY OVERVIEW',
          isEmphasized: true,
        },
      ]}
      defaultCollapsedState={defaultCollapsedState}
    >
      <View style={styles.descriptionContainer}>
        <ReadMoreReadLessHTMLText
          containerStyle={marginStyles.mr6}
          textValue={description}
          textStyle={htmlTextSmallStyles}
          limit={2}
          anchorStyle={htmlTextSmallStyles.actionStyle}
          clickEventParams={clickEventParams}
          captureClickEvents={captureClickEvents}
        />
        {images?.length === 1 && renderImages({ item: images?.[0], index: 0, isSingleImage: true })}
      </View>
      {images?.length > 1 && (
        <FlatList
          horizontal
          data={images}
          renderItem={renderImages}
          showsHorizontalScrollIndicator={false}
        />
      )}
    </DayPlanRowHOC>
  );
};

const styles = StyleSheet.create({
  descriptionContainer: {
    marginBottom: 10,
    flex: 1,
    flexDirection: 'row',
  },
  imagesArrayContainer: {
    justifyContent: 'flex-start',
    flexDirection: 'row',
    ...marginStyles.mt10,
  },
  singleImageContainer: {
    height: 94,
    width: 94,
    borderRadius: 16,
    marginLeft: 'auto',
  },
  singleImage: {
    height: 94,
    width: 94,
    borderRadius: 16,
    overflow: 'hidden',
  },
  imageContainer: {
    height: 94,
    width: 120,
    borderRadius: 16,
    ...marginStyles.mr16,
  },
  image: {
    height: 94,
    width: 120,
    borderRadius: 16,
    overflow: 'hidden',
    position: 'relative',
  },
  imageTextContainer: {
    position: 'absolute',
    bottom: 0,
    height: 22,
    width: '100%',
    justifyContent: 'center',
    alignItems: 'center',
  },
  imageText: {
    ...fontStyles.labelSmallBold,
    color: holidayColors.white,
    fontSize: 10,
    textAlign: 'center',
    ...marginStyles.mt4,
  },
});

export default StaticItineraryComponentV2;
