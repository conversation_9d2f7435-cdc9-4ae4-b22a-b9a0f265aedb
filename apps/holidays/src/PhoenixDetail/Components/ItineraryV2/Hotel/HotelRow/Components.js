import PlaceholderImageView from '@mmt/legacy-commons/Common/Components/PlaceholderImageView';
import React from 'react';
import { Text, View, StyleSheet, TouchableOpacity } from 'react-native';
import { FUNNEL_PAGE_NAMES, RESIZE_MODE_IMAGE } from '../../../../../HolidayConstants';
import { holidayColors } from '../../../../../Styles/holidayColors';
import { fontStyles } from '../../../../../Styles/holidayFonts';
import { marginStyles, paddingStyles } from '../../../../../Styles/Spacing';
import { createHotelDayImgList } from '../../../../Utils/HotelUtils';
import { isEmpty } from 'lodash';
import { actionStyle, dayPlanRowImage } from '../../../DayPlan/dayPlanStyles';
import { holidayBorderRadius } from '../../../../../Styles/holidayBorderRadius';
import { MAX_STAR_COUNT } from '../../../../Utils/PheonixDetailPageConstants';
import { getAllInclusions } from '../../../../Utils/PhoenixDetailUtils';
import { restructureInclusionData } from '../../ItineraryV2Utils';
import { capitalizeText } from '../../../../../../src/utils/textTransformUtil';
/* Icons */
import blackStar from '@mmt/legacy-assets/src/star.webp';
import taLogo from '@mmt/legacy-assets/src/tripAdvisorLogo.webp';
import greyStar from '@mmt/legacy-assets/src/greyStar.webp';

/* Components */
import UserRatingCommon from '../../../../../Common/Components/UserRatingCommon/ratingCommonV2';
import HolidayImageHolder from '../../../../../Common/Components/HolidayImageHolder';
import ItineraryUnitInformation from '../../Common/ItineraryUnitInformation';
import { DashedSeperator } from '../../Common/Seperators';
import { htmlTextSmallStyles } from '../../dayPlanV2Styles';
import PremiumTagWithToolTip from '../../../PhoenixHotelsListing/HotelListing/MMTPremium/PremiumTagWithToolTip';

const MAX_INCLUSION_TO_SHOW = 2;

export const HotelImageComponent = ({ hotelObject }) => {
  const { locationInfo, taInfo, mmtRatingInfo, ratingType, roomTypes } = hotelObject;
  const images = createHotelDayImgList(hotelObject, roomTypes?.[0]) || [];
  let userRatinginfo = mmtRatingInfo || {};
  const { userRating = 5 } = userRatinginfo;
  let isMMTRating = true;

  if (!!ratingType && ratingType === 'TA' && !isEmpty(taInfo)) {
    userRatinginfo = taInfo;
    isMMTRating = false;
  }

  const MAX_USER_RATING = 5;

  return (
    <View style={hotelImageStyles.thumbWrapper}>
      <PlaceholderImageView url={images?.[0]?.fullPath} style={hotelImageStyles.hotelImg} />
      <View style={hotelImageStyles.ratingContainerStyle}>
        {!!userRating && userRating > 0 ? (
          isMMTRating ? (
            <UserRatingCommon
              ratingValue={userRating}
              containerStyle={hotelImageStyles.ratingContainer}
            />
          ) : (
            <View style={hotelImageStyles.taContainer}>
              <HolidayImageHolder
                defaultImage={taLogo}
                style={hotelImageStyles.taLogo}
                resizeMode={RESIZE_MODE_IMAGE.CONTAIN}
              />
              <Text style={[hotelImageStyles.hotelTagText]}>
                {userRating}/{' '}
                <Text style={hotelImageStyles.maxHotelTagRating}>{MAX_USER_RATING}</Text>
              </Text>
            </View>
          )
        ) : null}
      </View>
    </View>
  );
};

export const HotelBasicDetail = ({ hotelObject }) => {
  const {
    similarHotels,
    bundled,
    name,
    starRating,
    roomTypes,
    locationInfo,
    taInfo,
    mmtRatingInfo,
    ratingType,
    additionalInfos = [],
    premiumHotelInfo
  } = hotelObject;
  // const { roomInformation, ratePlan } = roomTypes?.[0] || {};
  const { areaName, pointOfInterest = '' } = locationInfo || {};
  const getOrSimilarTextView = () => {
    return <Text style={hotelBasicDetailStyles.OrSimilar}>{' (or Similar)'}</Text>;
  };

  const getRatingView = () => {
    const starImages = [];
    const MAX_INCLUSION_TO_SHOW = 2;
    for (let i = 0; i < MAX_STAR_COUNT; i++) {
      starImages.push(i < starRating ? blackStar : greyStar);
    }

    return (
      starRating > 0 && (
        <View style={hotelBasicDetailStyles.ratingStarWrapper}>
          {starImages.map((item, index) => (
            <HolidayImageHolder
              key={index}
              style={hotelBasicDetailStyles.starRating}
              defaultImage={item}
            />
          ))}
        </View>
      )
    );
  };
  return (
    <View style={[hotelBasicDetailStyles.hotelNameContainer]}>
      {/*<View style={hotelBasicDetailStyles.toolTipContainer}>
      <PremiumTagWithToolTip premiumHotelInfo={premiumHotelInfo}/>
      </View>*/}
      <Text numberOfLines={2} style={hotelBasicDetailStyles.hotelName}>
        {name} {((similarHotels && similarHotels.length > 0) || bundled) && getOrSimilarTextView()}
      </Text>
      {getRatingView()}
      <Text style={hotelBasicDetailStyles.hotelAddress}>
        {areaName}
        {pointOfInterest && <Text style={hotelBasicDetailStyles.poi}>,{' '}{pointOfInterest}</Text>}
      </Text>
      <ItineraryUnitInformation info={additionalInfos} />
    </View>
  );
};

export const HotelRatePlan = ({
  fromPage = '',
  openDetailPage,
  hotelObject,
  isHotelUnavailable,
  changeHotelRestricted,
  onViewAllInclusionsClicked,
}) => {
  const { bundled, roomTypes, roomType, ratePlanInfo = {} } = hotelObject;
  const { roomInformation, } = roomTypes?.[0] || roomType || {};
  const { inclusions = [], inclusionsHighlighted = [] } = roomInformation || {};
  const inclusionList = getAllInclusions(inclusionsHighlighted, inclusions);
  const {
    name = '',
    otherInfos = [],
    viewAllInclusionsCtaText = 'View All Inclusions',
    moreRoomOptionsCtaText = 'More Options',
  } = ratePlanInfo || {};
  const showViewAllInclusionsCta = inclusionList?.length > 0 &&
  inclusionList.length > MAX_INCLUSION_TO_SHOW && !!viewAllInclusionsCtaText

  const renderMoreOptionsCta = ({ containerStyle } = {}) => {
    return (
      !isHotelUnavailable &&
      !bundled &&
      !changeHotelRestricted && (
        <TouchableOpacity onPress={openDetailPage} style={containerStyle}>
          <Text style={htmlTextSmallStyles.actionStyle}>{moreRoomOptionsCtaText}</Text>
        </TouchableOpacity>
      )
    );
  };
  return (
    !bundled && (
      <View>
        <DashedSeperator marginSpacing={marginStyles.mv8} />
        <View style={{ flexDirection: 'row', flex: 1 }}>
          {!!name && (
            <Text numberOfLines={2} style={hotelRatePlanStyles.infoTitle}>
              {name}
            </Text>
          )}
          {showViewAllInclusionsCta &&
            renderMoreOptionsCta({ containerStyle: { marginLeft: 'auto' } })}
        </View>
        <View>
          <ItineraryUnitInformation info={otherInfos} />
          <ItineraryUnitInformation
            info={restructureInclusionData({ data: inclusionList.slice(0, MAX_INCLUSION_TO_SHOW) })}
          />
        </View>

        {showViewAllInclusionsCta && fromPage !== FUNNEL_PAGE_NAMES.REVIEW ? (
          <View style={hotelRatePlanStyles.footer}>
            <TouchableOpacity onPress={onViewAllInclusionsClicked}>
              <Text style={actionStyle}>{capitalizeText(viewAllInclusionsCtaText)}</Text>
            </TouchableOpacity>
          </View>
        ) : (
          <View style={hotelRatePlanStyles.footer}>
            {renderMoreOptionsCta()}
          </View>
        )}
        {/* Show more options below if no inclusions are present */}
      </View>
    )
  );
};
const additionalInfoStyles = StyleSheet.create({
  container: {
    flexDirection: 'column',
  },
  infoContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  imageIcon: {
    width: 15,
    height: 15,
    ...marginStyles.mr6,
  },
  text: {
    ...fontStyles.labelSmallRegular,
    color: holidayColors.gray,
  },
});

const hotelImageStyles = StyleSheet.create({
  hotelImg: {
    width: '100%',
    height: '100%',
    ...dayPlanRowImage,
  },
  thumbWrapper: {
    width: 94,
    height: 94,
    ...holidayBorderRadius.borderRadius16,
    overflow: 'hidden',
    position: 'relative',
  },
  ratingContainerStyle: {
    position: 'relative',
  },
  ratingContainer: {
    ...paddingStyles.ph8,
    bottom: 0,
    right: 0,
    position: 'absolute',
    zIndex: 1,
    borderTopLeftRadius: 20,
  },
  taContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    backgroundColor: '#34E0A1',
    height: 24,
    alignItems: 'center',
    bottom: 0,
    position: 'absolute',
    zIndex: 1,
    borderTopLeftRadius: 0,
    borderBottomRightRadius: 0,
    borderRadius: 4,
    ...paddingStyles.ph6,
  },
  taLogo: {
    width: 14,
    height: 9,
  },
  hotelTagText: {
    ...fontStyles.labelSmallBold,
    color: holidayColors.black,
    marginLeft: 3,
  },
  maxHotelTagRating: {
    ...fontStyles.labelSmallRegular,
    color: holidayColors.black,
  },
});

const hotelBasicDetailStyles = StyleSheet.create({
  hotelNameContainer: {
    flex: 1,
    justifyContent: 'center',
    ...marginStyles.mr10,
  },
  hotelName: {
    ...fontStyles.labelLargeBlack,
    color: holidayColors.black,
    textAlignVertical: 'center',
  },
  hotelAddress: {
    ...fontStyles.labelSmallRegular,
    color: holidayColors.gray,
    ...marginStyles.mt6,
  },
  poi: {
    ...fontStyles.labelSmallRegular,
    color: holidayColors.gray,
  },
  ratingStarWrapper: {
    flexDirection: 'row',
    ...marginStyles.mt6,
  },
  starRating: {
    width: 10,
    height: 10,
  },
  OrSimilar: {
    ...fontStyles.labelBaseBold,
    color: holidayColors.lightGray,
  },
  toolTipContainer: {
    ...marginStyles.mb10,
    ...marginStyles.mt4,
    zIndex: 1,
  },
});

const hotelRatePlanStyles = StyleSheet.create({
  container: {},
  infoTitle: {
    ...fontStyles.labelSmallBold,
    lineHeight: 19,
    color: holidayColors.gray,
    ...marginStyles.mb4,
    flex: 1,
  },
  inclusionIcon: {
    width: 14,
    height: 14,
    ...marginStyles.mr10,
    tintColor: holidayColors.gray,
  },
  roomRates: {
    ...fontStyles.labelBaseBold,
    color: holidayColors.gray,
  },
  inclusions: {
    ...fontStyles.labelSmallRegular,
    color: holidayColors.gray,
    ...marginStyles.mr14,
  },
  inclusionsContainer: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
  },
  footer: {
    ...marginStyles.mt10,
    flexDirection: 'row',
    flex: 1
  },
});
