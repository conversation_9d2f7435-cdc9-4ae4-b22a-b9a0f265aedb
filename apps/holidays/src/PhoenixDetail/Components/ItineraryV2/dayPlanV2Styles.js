import { fontStyles } from 'mobile-holidays-react-native/src/Styles/holidayFonts';
import { holidayColors } from '../../../Styles/holidayColors';
import { smallHeightSeperator } from '../../../Styles/holidaySpacing';
import { marginStyles, paddingStyles } from '../../../Styles/Spacing';
import { StyleSheet, Platform } from 'react-native';

export const dayPlanV2RowHeadingStyles = {
  heading: {
    ...fontStyles.labelMediumBlack,
    color: holidayColors.black,
  },
  bold: {
    ...fontStyles.labelMediumBlack,
    color: holidayColors.black,
  },
};
export const dayPlanV2RowContainerStyle = {
  ...paddingStyles.pv10,
  ...smallHeightSeperator,
  backgroundColor: holidayColors.white,
};

export const dotSeperator = {
  width: 5,
  height: 5,
  borderRadius: 10,
  backgroundColor: holidayColors.gray,
  ...marginStyles.mh4,
  ...marginStyles.mt2,
};

export const dayPlanHeaderBorder = {
  height: 24,
  borderWidth: 1.5,
  borderColor: holidayColors.gray,
  backgroundColor: holidayColors.gray,
  ...marginStyles.mr6,
};

export const headerSeperator = {
  borderWidth: 0.5,
  width: '26%',
  height: 1,
  borderColor: holidayColors.grayBorder,
  ...marginStyles.mv6,
};

export const htmlTextBaseStyles = StyleSheet.create({
  heading: {
    ...fontStyles.labelBaseRegular,
    color: holidayColors.gray,
  },
  title: {
    ...fontStyles.labelBaseBold,
    color: holidayColors.gray,
  },
  bold: {
    ...fontStyles.labelBaseBold,
    color: holidayColors.gray,
  },
  paragraph: {
    ...fontStyles.labelBaseRegular,
    lineHeight: 19,
    color: holidayColors.gray,
  },
  actionStyle: {
    ...fontStyles.labelBaseBold,
    color: holidayColors.primaryBlue,
  },
  ...Platform.select({
    ios: {
      ...fontStyles.labelBaseRegular,
      color: holidayColors.gray,
    },
    android: {
      ...fontStyles.labelBaseRegular,
      color: holidayColors.gray,
    },
    web: {
      default: {
        ...fontStyles.labelBaseRegular,
        color: holidayColors.gray,
        lineHeight: 1.6, //since this takes em in web
      }
    },
  }),
});

export const htmlTextSmallStyles = StyleSheet.create({
  heading: {
    ...fontStyles.labelSmallRegular,
    color: holidayColors.gray,
  },
  title: {
    ...fontStyles.labelBaseBold,
    color: holidayColors.gray,
  },
  bold: {
    ...fontStyles.labelSmallBold,
    color: holidayColors.gray,
  },
  paragraph: {
    ...fontStyles.labelSmallRegular,
    lineHeight: 19,
    color: holidayColors.gray,
  },
  actionStyle: {
    ...fontStyles.labelSmallBold,
    color: holidayColors.primaryBlue,
  },
  ...Platform.select({
    ios: {
      ...fontStyles.labelSmallRegular,
      color: holidayColors.gray,
    },
    android: {
      ...fontStyles.labelSmallRegular,
      color: holidayColors.gray,
    },
    web: {
      default: {
        ...fontStyles.labelSmallRegular,
        color: holidayColors.gray,
        lineHeight: 1.6, //since this takes em in web
      }
    },
  }),
});
