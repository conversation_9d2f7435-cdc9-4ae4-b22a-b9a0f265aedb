import React from 'react';
import { StyleSheet, Text, TouchableOpacity, View } from 'react-native';
import HolidayImageHolder from '../../../../Common/Components/HolidayImageHolder';
import { trackPhoenixDetailLocalClickEvent } from '../../../Utils/PhoenixDetailTracking';
import { fontStyles } from '../../../../Styles/holidayFonts';
import { holidayColors } from '../../../../Styles/holidayColors';
import iconArrowDown from '../../images/ic_downArrow.png';
import { marginStyles, paddingStyles } from 'mobile-holidays-react-native/src/Styles/Spacing';
import { logPhoenixDetailPDTEvents } from 'mobile-holidays-react-native/src/utils/PhoenixDetailPDTTrackingUtils';
import { PDT_EVENT_TYPES } from 'mobile-holidays-react-native/src/utils/HolidayPDTConstants';
const MIN_CARD_COUNT = 3;

const FeatureCard = (props) => {
  const {
    item,
    index,
    disableOnPress,
    packageFeaturesLength,
    editable = true,
    fromPresales = false,
  } = props;
  const { isEditable, imageUrl = '', title = '' } = item;
  const fullWidthMode = packageFeaturesLength < MIN_CARD_COUNT;
  const captureClickEvents = ({ eventName = '', suffix = ''}) => {
    logPhoenixDetailPDTEvents({
      actionType : PDT_EVENT_TYPES.buttonClicked,
      value: eventName + suffix
    })
    trackPhoenixDetailLocalClickEvent({
      eventName,
      suffix
    })
  
  }
  const handlePress = () => {
    if (!fromPresales) {
      props.handlePress(item, index);
    }
  };
  return (
    <TouchableOpacity
      style={[styles.featureContainer]}
      activeOpacity={fromPresales ? 1 : 0.8}
      onPress={handlePress}
    >
      <View style={styles.iconContainer}>
        <HolidayImageHolder style={styles.icon} imageUrl={imageUrl} />
      </View>
      {!!title && (
        <View style={[styles.titleContainer, styles.featureTextContainer]}>
          <Text style={styles.subHeading} numberOfLines={2}>
            {title}
          </Text>
          {!fromPresales && (
            <HolidayImageHolder style={styles.iconArrowDown} defaultImage={iconArrowDown} />
          )}
        </View>
      )}
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  featureContainer: {
    justifyContent: 'flex-start',
    alignItems: 'center',
    flexDirection: 'row',
    borderColor: holidayColors.disableGrayBg,
    borderWidth: 1,
    borderRadius: 8,
    minHeight: 36,
    // width: '100%',
    ...paddingStyles.ph6,
    ...marginStyles.mr10,
  },
  icon: {
    width: 20,
    height: 20,
    borderRadius: 100,
    overflow: 'visible',
    // ...marginStyles.ml10,
  },
  iconContainer: {
    // padding: 4,
    ...marginStyles.mr8,
    borderRadius: 100,
  },
  wrapper: {
    // marginRight: 10,
    ...paddingStyles.ph8,
    alignContent: 'center',
  },
  titleContainer: {
    minHeight: 25,
    flexDirection: 'row',
    flexWrap: 'wrap',
  },
  subHeading: {
    ...fontStyles.labelSmallRegular,
    color: holidayColors.gray,
    textAlign: 'center',
    paddingVertical: 2,
  },
  featureTextContainer: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  iconArrowDown: {
    width: 16,
    height: 16,
    ...marginStyles.ml8,
  },
});

export default FeatureCard;
