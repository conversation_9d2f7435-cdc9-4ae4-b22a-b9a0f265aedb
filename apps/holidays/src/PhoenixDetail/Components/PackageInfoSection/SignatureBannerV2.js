import React from 'react';
import { Image, StyleSheet, Text, TouchableOpacity, View } from 'react-native';
import LinearGradient from 'react-native-linear-gradient';
import { GenreDetail } from '../../Types/PackageDetailApiTypes';
import { convertUrlToHttps } from '../../../utils/HolidayNetworkUtils.js';
import { colors, fonts } from '@mmt/legacy-commons/Styles/globalStyles';

const getGenreImageUrl = ({ genreDetail }) => {
  if (genreDetail?.[0]?.images) {
    return genreDetail[0].images.ANDROID;
  }
  return '';
};
const SignatureBanner = ({ showStory, storyData, genreDetail, toggleStoryPage }) => {
  let genreImageURL = getGenreImageUrl({ genreDetail });
  let genreType = genreDetail?.[0]?.genreType || '';
  if ((genreType && genreType === 'MMT Signature') || showStory) {
    const heading = storyData.packageSections?.[0]?.heading || '';
    return (
      <View style={styles.container}>
        {genreType && genreType === 'MMT Signature' ? (
          <LinearGradient
            start={{ x: 0.0, y: 0.0 }}
            end={{ x: 1.0, y: 0.0 }}
            colors={['#a30768', '#ee4b60']}
            style={styles.banner}
          >
            <Image
              style={styles.label}
              source={
                genreImageURL
                  ? { uri: convertUrlToHttps(genreImageURL) }
                  : require('../images/signature-label.png')
              }
            />
          </LinearGradient>
        ) : (
          <View />
        )}
        <Text style={styles.heading} numberOfLines={1} ellipsizeMode={'tail'}>
          {heading}
        </Text>
        {showStory && (
          <TouchableOpacity style={styles.btn} onPress={() => toggleStoryPage()}>
            <Text style={styles.text}>View Story</Text>
          </TouchableOpacity>
        )}
      </View>
    );
  } else {
    return [];
  }
};

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 8,
    marginTop: 4,
    backgroundColor: colors.lightPink2,
    borderColor: colors.pink4,
    borderWidth: 1,
  },
  label: {
    width: 85,
    height: 17,
  },
  banner: {
    borderRadius: 15,
    paddingHorizontal: 20,
    paddingVertical: 3,
  },
  heading: {
    fontSize: 11,
    color: colors.textGrey,
    flexWrap: 'wrap',
    width: '40%',
    paddingLeft: 8,
    fontFamily: fonts.regular,
  },
  text: {
    color: colors.azure,
    marginRight: 10,
    fontSize: 12,
    fontWeight: '900',
    fontFamily: fonts.regular,
  },
  btn: {
    marginLeft: 'auto',
    paddingRight: 10,
  },
});

export default SignatureBanner;
