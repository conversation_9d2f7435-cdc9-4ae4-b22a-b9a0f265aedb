import {
  getPokusForGalleryV2,
  getPokusForNewDetailContent,
} from 'apps/holidays/src/utils/HolidaysPokusUtils';
import { isRawClient } from 'apps/holidays/src/utils/HolidayUtils';
import { HolidayNavigation, HOLIDAY_ROUTE_KEYS } from '../../../Navigation';
import { subTypes } from '../Gallery/GalleryUtils';
import { Overlay } from '../DetailOverlays/OverlayConstants';
export const categories = {
  AROUND_THE_DESTINATION: 'around_the_destination',
  ACTIVITY: 'activities_and_sightseeing',
  PROPERTY_PHOTOS: 'property_photos',
  PACKAGE_HIGHLIGHTS: 'package_highlights',
};
export const tag = 'id';

export const getGalleryEntryPointData = (data) => {
  let mediaArray = [];
  let savedMedia = []; /*for no property case, saving extra media to show */
  data?.map((section) => {
    const { [tag]: category, galleryMedia: media = {}, title = '' } = section || {};
    switch (category) {
      case categories?.AROUND_THE_DESTINATION:
        /*first 2 media to be taken from "Around The destination"  1 image 1 video*/
        const aroundTheDestMedia = media;
        const getVideoIndex = aroundTheDestMedia?.findIndex((el) => {
          return el?.mediaType === subTypes.VIDEO;
        });

        savedMedia = aroundTheDestMedia?.filter((el, index) => {
          if (el?.index !== getVideoIndex) {
            return { ...el, title };
          }
        });

        mediaArray?.push({
          ...savedMedia?.[0],
          category: categories?.AROUND_THE_DESTINATION,
          title,
        }); /* pushed image  */

        /* if video is present video will be pushed otherwise image will be pushed */
        const dataToBePushedMedia =
          getVideoIndex > -1 ? aroundTheDestMedia?.[getVideoIndex] : savedMedia?.[1];

        /*  remove used data from saved array */
        savedMedia.splice(
          0,
          getVideoIndex > -1 ? 1 : 2,
        ); /* if video is present we need to remove just one image,else 2 */
        mediaArray?.push({
          ...dataToBePushedMedia,
          category: categories?.AROUND_THE_DESTINATION,
          title,
        }); /* pushed image  */
        break;
      case categories?.ACTIVITY:
        if (media?.length > 0) {
          mediaArray?.push({
            ...media?.[0],
            category: categories?.ACTIVITY,
            title,
          }); /* if activity is present ,1 activity media will be displayed */
        }
        break;
      case categories?.PROPERTY_PHOTOS:
        if (media?.length > 0) {
          mediaArray?.push({
            ...media?.[0],
            category: categories?.PROPERTY_PHOTOS,
            title,
          }); /* 1 property photo ,media always will be displayed */
        }
    }
  });
  if (mediaArray?.length < 3) {
    mediaArray?.push({
      ...savedMedia?.[1],
      category: categories?.PROPERTY_PHOTOS,
      title: savedMedia?.[1]?.title,
    }); /* exception -handling -if property media is not there ,pushing around the dest media  */
  }
  return mediaArray;
};

export const openGallery = ({ title, imageDetail, showOverlay, fromPresales }) => {
  const index = getActiveTabIndex({ title, imageDetail });
  const isGallery = imageDetail?.gallery?.length > 0;
  const isOpenGalleryV2 =
    (getPokusForGalleryV2() || getPokusForNewDetailContent(fromPresales)) &&
    imageDetail?.gallery?.length > 0;
  if (isRawClient()) {
    showOverlay(isOpenGalleryV2 ? Overlay.GRID_GALLERY_V2 : Overlay.GRID_GALLERY, {
      activeTabIndex: index,
    });
  } else {
    HolidayNavigation.push(
      isGallery
        ? HOLIDAY_ROUTE_KEYS.DETAIL_GRID_GALLERY_V2
        : HOLIDAY_ROUTE_KEYS.DETAIL_GRID_GALLERY,
      { activeTabIndex: index },
    );
  }
};

const getActiveTabIndex = ({ title, imageDetail }) => {
  const { gallery = [] } = imageDetail || {};
  if (gallery) {
    for (let index = 0; index < gallery?.length; index++) {
      if (gallery?.[index]?.[tag] === title) {
        return index;
      }
    }
  }
};
