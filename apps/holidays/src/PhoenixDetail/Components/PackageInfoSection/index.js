import React from 'react';
import FeatureList from '../FDFeatureEdit/FeatureList';
import { StyleSheet, View } from 'react-native';
import { FeedbackCard } from '../ItenaryCard';
import HolidayExpertSection from './HolidayExpertSection';
import SignatureBannerV2 from './SignatureBannerV2';
import {
  getPokusForNewDetailContent,
  getPokusForPackageHighlights,
  showVPPAndInsuranceOnDetails,
} from '../../../utils/HolidaysPokusUtils';
import PackageHighlightStrip from './PackageHighlightStrip';
import FeaturesListV2 from './FDFeatureListV2';
import { categories as packageCategories, openGallery } from './PackageInfoUtils';
import { trackPhoenixDetailLocalClickEvent } from '../../Utils/PhoenixDetailTracking';
import { isEmpty } from 'lodash';
import ActivityInclusionSection from '../../../DetailMimaComponents/ActivityInclusionsSection';
import { paddingStyles } from 'mobile-holidays-react-native/src/Styles/Spacing';
import { marginStyles } from '../../../Styles/Spacing';
import { holidayColors } from '../../../Styles/holidayColors';

const PackageInfoSection = ({
  newPackageDetail,
  toggleReadMore,
  showStory,
  toggleStoryPage,
  toggleFDFeatureBottomSheet,
  fromPresales,
  updateVisa,
  ...rest
}) => {
  const { storySuccess, storyData, detailData, editable,showOverlay,hideOverlays } = rest;
  const { packageDetail = {} } = detailData || {};
  const { genreDetail = {}, imageDetail = {}, additionalDetail = {} } = packageDetail || {};
  const { gallerySections = [] } = imageDetail || {};
  const { inclusions = [] } = additionalDetail || {};
  const packageHighlights = gallerySections.filter(
    (section) => section.id === packageCategories.PACKAGE_HIGHLIGHTS,
  )?.[0];
  const packageHighlightsData = packageHighlights?.highlights || [];

  // Filter package features based on showVPPAndInsuranceOnDetails
  const getFilteredPackageFeatures = (features) => {
    if (!features) return features;
    
    // If showVPPAndInsuranceOnDetails returns false, filter out ADDON type features
    if (fromPresales) {
      return features
    }
    if (!showVPPAndInsuranceOnDetails()) {
      return features.filter(feature => feature.type !== 'ADDON');
    }
    
    return features;
  };

  const filteredPackageFeatures = getFilteredPackageFeatures(packageDetail.packageFeatures);
  const handlePackageHighlightClick = () => {
    openGallery({ title: packageHighlights?.id,showOverlay,hideOverlays, imageDetail });
    trackPhoenixDetailLocalClickEvent({ eventName: 'view_highlights' });
  };

  const pokusValue = getPokusForNewDetailContent(fromPresales);
  const showPackageHighlights = getPokusForPackageHighlights();
  const showActivityInclusionSection = !isEmpty(inclusions || []);
  if (pokusValue) {
    return (
      <View>
        {filteredPackageFeatures ? (
          <FeaturesListV2
            packageDetail={{ ...packageDetail, packageFeatures: filteredPackageFeatures }}
            isOverlay={false}
            editable={editable}
            toggleFDFeatureBottomSheet={(show, index) => toggleFDFeatureBottomSheet(show, index)}
            containerStyles={styles.featureListContainer}
            updateVisa={updateVisa}
            fromPresales={fromPresales}
            showOverlay={showOverlay}
            hideOverlays={hideOverlays}
          />
        ) : null}
        {showActivityInclusionSection && (
          <ActivityInclusionSection
            inclusionDetails={inclusions}
            containerStyles={styles.paddingHorizontal15}
          />
        )}
        <SignatureBannerV2
          showStory={showStory && storySuccess}
          genreDetail={genreDetail}
          storyData={storyData}
          toggleStoryPage={toggleStoryPage}
        />
      </View>
    );
  }
  return (
    <View>
      {fromPresales ? null : (
        <FeedbackCard packageDetail={newPackageDetail} toggleReadMore={toggleReadMore} /> // HE section not to be shown on PSM
      )}
      {filteredPackageFeatures ? (
        <FeatureList
          packageFeatures={filteredPackageFeatures}
          isOverlay={false}
          editable={editable}
          toggleFDFeatureBottomSheet={(show, index) => toggleFDFeatureBottomSheet(show, index)}
          showOverlay={showOverlay}
          hideOverlays={hideOverlays}
        />
      ) : null}
    </View>
  );
};

const styles = StyleSheet.create({
  paddingHorizontal15: {
    ...paddingStyles.ph14,
    ...marginStyles.mt0,
  },
});
export default PackageInfoSection;
