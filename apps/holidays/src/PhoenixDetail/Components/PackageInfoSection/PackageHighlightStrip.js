import React, { useEffect } from 'react';
import { FlatList, StyleSheet, Text, TouchableOpacity, View } from 'react-native';
import LinearGradient from 'react-native-linear-gradient';
import { colors } from '@mmt/legacy-commons/Styles/globalStyles';
import { holidayColors } from '../../../Styles/holidayColors';
import { fontStyles } from '../../../Styles/holidayFonts';
import { marginStyles, paddingStyles } from '../../../Styles/Spacing';
import { holidayBorderRadius } from '../../../Styles/holidayBorderRadius';
import TextButton from 'mobile-holidays-react-native/src/Common/Components/Buttons/TextButton';

const PackageHighlightStrip = ({ openGallery, packageHighlightsData ,trackClick }) => {
  if (packageHighlightsData.length === 0) {
    return null;
  }
  useEffect(()=>{
    trackClick({ eventName: 'packageHighlights_shown' });
  },[]);

  const renderPackageItem = ({ item, index }) => {
    const { title } = item || '';
    if (!title) {
      return [];
    }
    return (
      <TouchableOpacity onPress={openGallery} activeOpacity={1}>
        <View style={styles.highlight}>
          <Text style={styles.hightlightText} numberOfLines={1} ellipsizeMode={'tail'}>
            {title}
          </Text>
        </View>
      </TouchableOpacity>
    );
  };

  const renderPackageStripHeader = () => (
    <TouchableOpacity onPress={openGallery} activeOpacity={1}>
      <LinearGradient
        start={{ x: 0.0, y: 0.0 }}
        end={{ x: 1.0, y: 0.0 }}
        colors={[holidayColors.orangeGradient, holidayColors.orangeGradientDark]}
        style={styles.banner}
      >
        <Text style={styles.label}>Package Highlights</Text>
      </LinearGradient>
    </TouchableOpacity>
  );

  const renderPackageStripFooter = () => (
    <TextButton
        buttonText="View All"
        handleClick={openGallery}
        btnTextStyle={styles.moreHighlights}
    />
  );

  return (
    <View style={styles.container} activeOpacity={1}>
      <View style={styles.highlightsContainer}>
        <FlatList
          horizontal
          scrollEnabled={true}
          data={packageHighlightsData}
          showsHorizontalScrollIndicator={false}
          renderItem={renderPackageItem}
          contentContainerStyle={styles.containerContent}
          ItemSeparatorComponent={() => <View style={styles.seperator} />}
          ListHeaderComponent={renderPackageStripHeader}
          ListFooterComponent={renderPackageStripFooter}
        />
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    alignItems: 'center',
    ...paddingStyles.ph16,
    paddingVertical: 2,
    ...marginStyles.mt6,
    backgroundColor: holidayColors.fadedYellow,
    width: '100%',
  },
  containerContent: {
    alignItems: 'center',
  },
  label: {
    ...fontStyles.labelSmallRegular,
    color: holidayColors.white,
  },
  banner: {
    borderRadius: 15,
    ...paddingStyles.ph14,
    paddingVertical: 5,
    ...marginStyles.mr10,
  },
  highlightsContainer: {
    flexDirection: 'row',
    marginTop: 2,
    flex: 1,
  },
  highlight: {
    alignItems: 'center',
    flexDirection: 'row',
    marginLeft: 2,
    paddingVertical: 10,
  },
  hightlightText: {
    ...fontStyles.labelSmallRegular,
    color: holidayColors.gray,
    lineHeight: 19,
  },
  moreHighlights: {
    ...fontStyles.labelSmallBold,
    color: holidayColors.primaryBlue,
    marginLeft: 10,
  },
  seperator: {
    width: 4,
    height: 4,
    ...holidayBorderRadius.borderRadius8,
    backgroundColor: holidayColors.gray,
    ...marginStyles.mh6,
    ...marginStyles.mt18,
  },
});

export default PackageHighlightStrip;
