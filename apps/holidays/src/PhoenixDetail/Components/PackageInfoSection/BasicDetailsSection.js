import React from 'react';
import { View, Text, StyleSheet, Image } from 'react-native';
import LinearGradient from 'react-native-linear-gradient';
import { colors } from '@mmt/legacy-commons/Styles/globalStyles';
import DestinationDuration from './DestinationDuration';
import mySafeImage from '../images/my-safety.png';
import Highlights from './Highlights';
import PackageVariantsSection from './PackageVariantsSections';
import GalleryEntryPoint from './GalleryEntryPoint';
import { openGallery } from './PackageInfoUtils';
import { trackPhoenixDetailLocalClickEvent } from '../../Utils/PhoenixDetailTracking';
import { isEmpty } from 'lodash';
import { fontStyles } from 'mobile-holidays-react-native/src/Styles/holidayFonts';
import { holidayColors } from 'mobile-holidays-react-native/src/Styles/holidayColors';
import { paddingStyles } from '../../../Styles/Spacing';
import { marginStyles } from '../../../Styles/Spacing';
import { gradientColors } from '../../../Styles/holidayColors';
import { logPhoenixDetailPDTEvents } from 'mobile-holidays-react-native/src/utils/PhoenixDetailPDTTrackingUtils';
import { PDT_EVENT_TYPES } from 'mobile-holidays-react-native/src/utils/HolidayPDTConstants';
import PremiumCard from './MmtPremium/PremiumCard';

// TO-DO @vatsala will remove dummy data once json is updated
const BasicDetailsSection = ({
  detailData,
  dynamicPackageId = '',
  updatePackage,
  fromPresales = false,
  showOverlay,
}) => {
  const { packageDetail } = detailData || {};
  const { metadataDetail, imageDetail, variantDetails = [], videoDetail } = packageDetail || {};
  const { packageVideo = {} } = videoDetail || {};

  const { safe, bundled, premiumInfo } = metadataDetail || {};
  let isSafe = safe ? true : false;

  const captureClickEvents = ({ eventName, eventSuffix }) => {
    trackPhoenixDetailLocalClickEvent({
      eventName: eventName + '_' + eventSuffix,
    });
    logPhoenixDetailPDTEvents({
      actionType: PDT_EVENT_TYPES.contentClicked,
      value: eventName + '|' + eventSuffix,
    });
  };
  const openGalleryLocal = (item, index) => {
    const title = item?.category || '';
    openGallery({ title, imageDetail, fromPresales, showOverlay });
    const eventName = 'gallery_open';
    const eventSuffix = `${item?.mediaType}_${item?.title || ''}_${index}`;
    captureClickEvents({ eventName, eventSuffix });
  };
  return (
    <LinearGradient
      colors={[gradientColors.lightBlueOpacity55, gradientColors.whiteOpacity76]}
      useAngle={true}
      angle={270}
      style={styles.container}
    >
      <View>
        {!isEmpty(imageDetail?.gallerySections) && (
          <GalleryEntryPoint
            imageDetail={imageDetail}
            openGallery={openGalleryLocal}
            fromPresales={fromPresales}
            packageVideo={packageVideo}
            showOverlay={showOverlay}
          />
        )}
        <PremiumCard premiumInfo={premiumInfo} containerStyle={{ ...marginStyles.mb6 }} />

        <View style={styles.leftRightSection}>
          <Highlights detailData={detailData} />
          {isSafe && <Image style={styles.mySafeImage} source={mySafeImage} />}
        </View>
        <Text style={styles.pkgName}>{packageDetail.name}</Text>
        {bundled && (
          <PackageVariantsSection
            packageVariants={variantDetails}
            dynamicPackageId={dynamicPackageId}
            updatePackage={updatePackage}
          />
        )}
        <DestinationDuration detailData={detailData} />
      </View>
    </LinearGradient>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    ...paddingStyles.pa16,
    ...paddingStyles.pb6,
    ...paddingStyles.pt2,
  },
  pkgName: {
    ...marginStyles.mt6,
    ...fontStyles.labelLargeBlack,
    color: holidayColors.black,
  },
  mySafeImage: {
    width: 60,
    height: 16,
  },
  leftRightSection: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    ...marginStyles.mt10,
  },
  highlightContainer: {
    flexDirection: 'row',
  },
});
export default BasicDetailsSection;
