import React from 'react';
import { View, Image, StyleSheet } from 'react-native';
import { paddingStyles } from '../../../../Styles/Spacing';
import { holidayColors } from '../../../../Styles/holidayColors';
import PremiumDescriptionContainer from './PremiumDescriptionContainer';
import LinearGradient from 'react-native-linear-gradient';

const PremiumCard = (props) => {
  const { premiumInfo, containerStyle = {} } = props || {};
  const {
    imageUrl = '',
    items = [],
    borderGradient1 = holidayColors.gold,
    borderGradient2 = holidayColors.premium,
  } = premiumInfo || {};

  const borderColorGradient = [borderGradient1, borderGradient2];

  if (!premiumInfo || !imageUrl) {
    return null;
  }

  return (
    <View style={containerStyle}>
      <Image source={{ uri: imageUrl }} style={styles.image} />
      <LinearGradient colors={borderColorGradient} style={styles.gradientBorder}>
        <View style={styles.cardContainer}>
          <PremiumDescriptionContainer items={items} dotColor={holidayColors.premium} />
        </View>
      </LinearGradient>
    </View>
  );
};

const styles = StyleSheet.create({
  cardContainer: {
    backgroundColor: holidayColors.white,
    flexDirection: 'row',
    alignItems: 'center',
    shadowColor: holidayColors.black,
    borderRadius: 8,
    shadowOpacity: 0.1,
    shadowRadius: 5,
    shadowOffset: { width: 0, height: 2 },
    ...paddingStyles.pt10,
    ...paddingStyles.pl10,
    ...paddingStyles.pb10,
    flex: 1,
    elevation: 5,
  },
  image: {
    width: '36%',
    height: 18,
    resizeMode: 'contain',
    bottom: -10,
    zIndex: 1,
  },
  gradientBorder: {
    borderRadius: 8,
    padding: 1,
  },
});

export default PremiumCard;
