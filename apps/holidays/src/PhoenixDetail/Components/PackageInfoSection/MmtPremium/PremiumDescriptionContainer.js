import React from 'react';
import { View, StyleSheet } from 'react-native';
import PremiumItem from './PremiumItems';
import { marginStyles } from '../../../../Styles/Spacing';

const PremiumDescriptionContainer = ({ items, dotColor }) => (
  <View style={styles.descriptionContainer}>
    {items.map((item, index) => (
      <PremiumItem key={index} item={item} index={index} dotColor={dotColor} itemsLength={items.length} />
    ))}
  </View>
);

const styles = StyleSheet.create({
  descriptionContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    flexWrap: 'wrap',
    ...marginStyles.mt4,
  },
});

export default PremiumDescriptionContainer;
