import React, { Fragment, useMemo } from 'react';
import { Dimensions, StyleSheet, View, TouchableOpacity,ScrollView, Platform, Text } from 'react-native';
import { categories, getGalleryEntryPointData } from './PackageInfoUtils';
import AtomicCss from '@mmt/legacy-commons/Styles/AtomicCss';
import { getOptimisedUrl, subTypes } from '../Gallery/GalleryUtils';
import { isEmpty } from 'lodash';
import { fontStyles } from '../../../Styles/holidayFonts';
import { holidayColors } from '../../../Styles/holidayColors';
import {isRawClient} from "../../../utils/HolidayUtils";
import {getDeviceDimension} from "react-native-ad-wrapper/src/utils/helper";
import { marginStyles, paddingStyles } from '../../../Styles/Spacing';
import LinearGradient from 'react-native-linear-gradient';
import ArrowIcon from '@mmt/legacy-assets/src/blue_right_arrow.webp';
import { openGallery as openGalleryPage } from './PackageInfoUtils';
/* Components */
import PlaceholderImageWithText from '../Common/PlaceholderImageWithText';
import PhoenixVideoSection from '../HolidaysVideoPlayer/PhoenixVideoSection';
import HolidayImageHolder from '../../../Common/Components/HolidayImageHolder';
import { trackPhoenixDetailLocalClickEvent } from '../../Utils/PhoenixDetailTracking';
import { RESIZE_MODE_IMAGE } from 'apps/holidays/src/HolidayConstants';
import { holidayBorderRadius } from 'apps/holidays/src/Styles/holidayBorderRadius';
import { borderRadiusValues } from '../../../Styles/holidayBorderRadius';

// Constants
const rightMedia = 0;
const leftTopMedia = 1;
const leftBottomLeftMedia = 2;
const leftBottomRightMedia = 3;
const containerHeight = 227;
const screenWidth = getDeviceDimension().fullWidth / 2;

const GalleryEntryPoint = React.memo((props) => {
  const { imageDetail, openGallery, fromPresales = false, packageVideo, showOverlay } = props || {};
  const { gallerySections, images, gallery = {} } = imageDetail || {};
  const packageHighlightGalleryCount = gallery?.find(
    (galleryItem) => galleryItem?.id === categories.PACKAGE_HIGHLIGHTS,
  )?.count || 0;
  
  if (isEmpty(gallerySections)) {
    return <HolidayImageHolder style={styles.wrapperDimensions} />;
  }
  const data = useMemo(() => getGalleryEntryPointData(gallerySections), [gallerySections]);

  const getImage = (data, imageStyle, index) => {
    const { mediaUrl = '', subtitle = '', title = '', category = '' } = data || {};
    const isPackageHiglight = category === categories.PACKAGE_HIGHLIGHTS;
    const showText = category !== categories?.AROUND_THE_DESTINATION;
    const openGalleryLocal = () => {
      openGallery(data, index);
    };
    return (
      !!mediaUrl && <View style={{ flex: 1 }}>
        <TouchableOpacity
          style={imageStyle}
          onPress={() => openGallery({ ...data, ...(isPackageHiglight && { mediaType: 'highlights' }) }, index)}
        >
          <PlaceholderImageWithText
              url={getOptimisedUrl({ url: mediaUrl, height: containerHeight, width: screenWidth })}
              style={styles.imageWrapper}
              wrapperStyle={styles.wrapperStyle}
              description={showText ? (isPackageHiglight ? subtitle : title) : ''}
              imageStyle={styles.placeholderImage}
              fontStyle={{
                ...styles.fontOverImage,
                ...(isPackageHiglight ? {} : { textAlign: 'center' }),
              }}
            >
              {isPackageHiglight && (
                <LinearGradient
                  start={{ x: 0.0, y: 0.0 }}
                  end={{ x: 1.0, y: 0.0 }}
                  colors={[holidayColors.orangeGradient, holidayColors.orangeGradientDark]}
                  style={styles.banner}
                >
                  <Text style={styles.label}>Package Highlights</Text>
                </LinearGradient>
              )}
            </PlaceholderImageWithText>
        </TouchableOpacity>
      </View>
    );
  };

  const getVideo = ({ data, index }) => {
    const openGalleryLocal = () => {
      openGallery(data, index);
    };
    let mediaurl = data?.mediaUrl;
    if(isRawClient() && !isEmpty(packageVideo?.mpdUrl)) {
      mediaurl = packageVideo?.mpdUrl;
    }
    return (
      <Fragment>
        <View style={[styles.imageWrapper]}>
          <PhoenixVideoSection
            videoUrl={mediaurl}
            disableSeekbar
            disableTimer
            videoPaused={false}
            disableVolume
            disablePlayPause={true}
            containerStyle={styles.videoContainer}
            mediaStyle={styles.videoMediaStyle}
            repeat={true}
            width={screenWidth - 10}
            height={100}
          />
        </View>
        <View onPress={openGalleryLocal} style={styles.videoWrapper}>
          <TouchableOpacity onPress={openGalleryLocal} style={{width :'100%', height: '100%'}}>
            <View style={styles.videoContent} />
          </TouchableOpacity>
        </View>
      </Fragment>
    );
  };

  const highlightsData =
  gallerySections.find((section) => section?.id === categories.PACKAGE_HIGHLIGHTS) || {};
  const { highlights } = highlightsData || {};
  const SHOW_HIGHLIGHTS = highlights?.length > 0;

  const renderViewAll = () => {
    const handleViewAllClick = () => {
      openGalleryPage({ title: highlightsData?.id, imageDetail, showOverlay });
      trackPhoenixDetailLocalClickEvent({ eventName: 'view_highlights' });
    }
    return (
      <LinearGradient
        colors={[holidayColors.white, holidayColors.skyBlue]}
        angle={75}
        useAngle
        locations={[0.4, 1]}
        style={styles.viewAllContainer}
      >
      <TouchableOpacity style={styles.viewAllContainer} onPress={handleViewAllClick}>
        <Text style={styles.viewAllText}>View All Highlights</Text>
        <HolidayImageHolder defaultImage={ArrowIcon} style={styles.viewAllIcon} resizeMode={RESIZE_MODE_IMAGE.CONTAIN} />
      </TouchableOpacity>
      </LinearGradient>

    );
  };

  const renderHighlights = (highlights) => {
    return highlights.map((item) => {
      item.category = categories.PACKAGE_HIGHLIGHTS;
      return (
        <View style={styles.highlightWrapper}>
          {getImage(item, styles.rightImage, leftBottomRightMedia)}
        </View>
      );
    });
  };

  const renderImageGallery = () => {
    return ( <View style={{ flexDirection: 'row', alignItems: 'flex-end' }}>
    <View
      style={[
        styles.wrapperDimensions,
        styles.wrapper,
        fromPresales ? {} : styles.marginTop50,
        SHOW_HIGHLIGHTS ? { width: 230 } : { flex: 1 },
      ]}
    >
      <View style={styles.leftWrapper}>
        <View style={styles.leftTopImage}>
          {data?.[1]?.mediaType === subTypes.VIDEO
            ? getVideo({ data: data?.[leftTopMedia], index: leftTopMedia })
            : getImage(data?.[leftTopMedia], styles.rightTopImage, [], leftTopMedia)}
        </View>
        <View style={styles.leftBottomWrapper}>
          {getImage(
            data?.[leftBottomLeftMedia],
            styles.leftBottomLeftImage,
            leftBottomLeftMedia,
          )}
          {getImage(
            data?.[leftBottomRightMedia],
            styles.leftBottomRightImage,
            leftBottomRightMedia,
          )}
        </View>
      </View>
    </View>
    {SHOW_HIGHLIGHTS ? (
      <View style={{ flexDirection: 'row',...marginStyles.mr10 }}>
        {renderHighlights(highlights)}
        {packageHighlightGalleryCount > 2 && renderViewAll()}
      </View>
    ) : (
      <View style={[styles.highlightWrapper, { flex: 1,}]}>
        {getImage(data?.[rightMedia], styles.rightImage, rightMedia)}
      </View>
    )}
  </View>)
  }
  return (
    <View style={{ flex: 1, marginRight: -16 }}>
      {SHOW_HIGHLIGHTS ? (
        <ScrollView
          horizontal
          showsHorizontalScrollIndicator={false}
        >
          {renderImageGallery()}
        </ScrollView>
      ) : (
        renderImageGallery()
      )}
    </View>
  );
});

const styles = StyleSheet.create({
  fontOverImage: {
    color: holidayColors.white,
    ...fontStyles.labelSmallBold,
    display: 'flex',
    alignItems: 'center',
    position: 'absolute',
    zIndex: 10,
    width: '100%',
    paddingBottom: 10,
    paddingHorizontal: 2,
    ...marginStyles.ml2,

    left: 0,
  },
  videoContainer: {
    height: '100%',
    borderRadius: 6,
  },
  videoMediaStyle: {
    height: '100%',
    borderRadius: 6,
    overflow: 'hidden',
  },
  wrapperDimensions: {
    height: containerHeight,
    width: 200, //230,
    marginRight: 10,
  },
  wrapper: {
    marginBottom: 10,
    display: 'flex',
    flexDirection: 'row',
  },
  imageWrapper: { height: '100%', width: '100%', },
  placeholderImage: { borderRadius: 6, resizeMode: 'cover', justifyContent: 'flex-end' },
  videoOverlay: {
    position: 'absolute',
    height: '100%',
    width: '100%',
    zIndex: 10000000,
    elevation: 10,
  },
  videoWrapper: { height: '100%', width: '100%', position: 'absolute', zIndex: 10000000 },
  videoContent: { height: '100%', width: '100%' },
  wrapperStyle: {
    width: '100%',
    ...Platform.select({
      web: {
        flex: 1,
      },
    }),
  },
  rightImage: {
    overflow: 'hidden',
    borderRadius: 6,
    ...Platform.select({
      web: {
        flex: 1,
      },
    }),
  },
  leftWrapper: { flex: 1, borderRadius: 6, flexDirection: 'column' },
  leftBottomWrapper: { flex: 1.2, marginTop: 10, flexDirection: 'row' },
  leftTopImage: {
    flex: 1,
    borderRadius: 6,
    overflow: 'hidden',
  },
  leftBottomLeftImage: {
    borderRadius: 6, 
    ...Platform.select({
      web: {
        flex: 1,
      },
    }), 
  },
  rightTopImage: {
    flex: 1,
    borderRadius: 5,
    overflow: 'hidden',
  },
  leftBottomRightImage: {
    marginLeft: 10,
    borderRadius: 6,
    overflow: 'hidden',
    ...Platform.select({
      web: {
        flex: 1,
      },
    }),
  },
  marginTop50: {
    marginTop: 50,
  },
  highlightWrapper: {
    width: 195,
    marginBottom: 10,
    height: containerHeight,
    marginRight: 10,
  },
  label: {
    ...fontStyles.labelSmallBold,
    color: holidayColors.white,
  },
  banner: {
    borderRadius: 15,
    ...paddingStyles.ph6,
    ...paddingStyles.pv2,
    alignSelf: 'flex-start',
    bottom: 30,
    left: 10,
  },
  viewAllContainer: {
    width: 195,
    height: containerHeight,
    alignItems: 'center',
    flexDirection:'row',
    justifyContent: 'center',
    ...holidayBorderRadius.borderRadius6,
  },
  viewAllText: {
    color: holidayColors.primaryBlue,
    ...fontStyles.labelSmallBold,
  },
viewAllIcon: {
    width: 20,
    height: 20,
    ...marginStyles.ml4,
  },
});
export default GalleryEntryPoint;
