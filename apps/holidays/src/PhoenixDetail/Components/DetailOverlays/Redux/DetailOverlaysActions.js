import { overlayActionTypes } from '../OverlayConstants';


export const showOverlay = (key, data = {}) => dispatch => {
  dispatch({
    type: overlayActionTypes.SHOW_OVERLAYS,
    key: key,
    data: data,
  });
};

export const hideOverlays = (keys = []) => dispatch => {
  dispatch({
    type: overlayActionTypes.HIDE_OVERLAYS,
    overlays: keys,
  });
};
export const clearOverlays = () => dispatch => {
  dispatch({
    type: overlayActionTypes.CLEAR_OVERLAYS,
  });
};
