import React, { useEffect, useState } from 'react';
import { Image, LayoutAnimation, Platform, StyleSheet, Text, TouchableOpacity, UIManager, View } from 'react-native';
import isEmpty from 'lodash/isEmpty';
import { holidayColors } from '../../../Styles/holidayColors';
import { fontStyles } from '../../../Styles/holidayFonts';

if (Platform.OS === 'android' && UIManager.setLayoutAnimationEnabledExperimental) {
  UIManager.setLayoutAnimationEnabledExperimental(true);
}

const arrowDown = require('@mmt/legacy-assets/src/ic_arrow_blue_down_3x.webp');
const arrowUp = require('@mmt/legacy-assets/src/ic_arrow_blue_up_3x.webp');
export const DEFAULT_HEADER_COLOR_CODE = '#000000';
export const DEFAULT_HEADER_COLOR_BACKGROUND_CODE = '#000000';
export const DEFAULT_MESSAGE_COLOR_CODE = '#000000';
export const DEFUALT_MESSAGE_BACKGROUND_CODE = '#ffffff';

const InformationStrip = (props) => {
  const [messageVisibility, setMessageVisibility] = useState(false);
  const { headerInfo = {}, sectionInfo = {}, shouldShow = false, trackClickEvent, change } = props;

  useEffect(() => {
    LayoutAnimation.configureNext(LayoutAnimation.Presets.easeInEaseOut);
  }, [messageVisibility]);

  const {
    text = '',
    colorCode = DEFAULT_HEADER_COLOR_CODE,
    colorCodeBg = DEFAULT_HEADER_COLOR_BACKGROUND_CODE,
  } = headerInfo;
  const {
    messages = [],
    colorCode: colorCodeSection = DEFAULT_MESSAGE_COLOR_CODE,
    colorCodeBg: colorCodeSectionBg = DEFUALT_MESSAGE_BACKGROUND_CODE,
  } = sectionInfo;

  const typeofChange = change > 0 ? 'Increase' : 'Others';

  useEffect(() => {
    if (shouldShow && text && !isEmpty(messages)) {
      trackClickEvent(`${typeofChange}_Seen`);
    }
  }, [shouldShow]);

  if (!shouldShow || !text || isEmpty(messages)) {
    return null;
  }

  const toggleMessageVisibility = () => {
    setMessageVisibility((prevProps) => {
      trackClickEvent(prevProps ? `${typeofChange}_Collapse` : `${typeofChange}_Expand`);
      return !prevProps;
    });
  };
  return (
    <View
      style={{
        backgroundColor: colorCodeSectionBg || DEFUALT_MESSAGE_BACKGROUND_CODE,
        borderTopLeftRadius: 16,
        borderTopRightRadius: 16,
      }}
    >
      <View style={styles.stripContainer}>
        <View style={[{ backgroundColor: colorCodeBg || DEFAULT_HEADER_COLOR_BACKGROUND_CODE }, styles.headingInfo]}>
          <Text style={[styles.headingText, { color: colorCode  || DEFAULT_HEADER_COLOR_CODE}]}>{text?.toUpperCase()}</Text>
        </View>
        <TouchableOpacity style={styles.imageContainer} onPress={toggleMessageVisibility}>
          <Image source={messageVisibility ? arrowDown : arrowUp} style={styles.image} />
        </TouchableOpacity>
      </View>
      {messageVisibility ? (
        <View style={styles.messageListContainer}>
          {messages?.map((message) => {
            return (
              <View style={styles.messageContainer}>
                <Text style={styles.bulletPoint}>{'\u2B24'} </Text>
                <Text style={[{ color: colorCodeSection || DEFAULT_MESSAGE_COLOR_CODE }, styles.messageText]}>{message}</Text>
              </View>
            );
          })}
        </View>
      ) : null}
    </View>
  );
};

const styles = StyleSheet.create({
  stripContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingHorizontal: 15,
    paddingTop: 12,
    paddingBottom: 10,
    backgroundColor:holidayColors.fadedRed,
  },
  headingInfo: {
    borderRadius: 4,
    paddingVertical: 4,
    paddingHorizontal: 10,
    backgroundColor:holidayColors.white,
  },
  headingText: {
    ...fontStyles.labelSmallBold,
    lineHeight: 16,
  },
  imageContainer: {},
  image: {
    width: 20,
    height: 20,
    display: 'flex',
  },
  messageListContainer: {
    display: 'flex',
    flexDirection: 'column',
    paddingHorizontal: 10,
    paddingBottom: 10,
    backgroundColor:holidayColors.fadedRed,
  },
  bulletPoint: {
    fontSize: 6,
    paddingHorizontal: 5,
    paddingTop: 10,
  },
  messageContainer: {
    display: 'flex',
    flexDirection: 'row',
    alignItems: 'flex-start',
    paddingHorizontal: 10,
  },
  messageText: {
    paddingVertical: 6,
    ...fontStyles.labelBaseRegular,
    lineHeight: 17,
    paddingRight:20,
  },
});

export default InformationStrip;
