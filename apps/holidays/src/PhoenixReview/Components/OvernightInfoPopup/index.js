import React from 'react';
import { StyleSheet, View, TouchableOpacity, Text, Image } from 'react-native';
import PropTypes from 'prop-types';
import fecha from 'fecha';
import FlightIcon from '@mmt/legacy-assets/src/holidays/flight.webp';
import { getFlightObject } from '../../../PhoenixDetail/Utils/PhoenixDetailUtils';
import { getFormattedDate } from '@mmt/legacy-commons/Helpers/dateHelpers';
import { DATES_FORMAT } from '../../../HolidayConstants';
import { getFlightObjectDetails } from '../../Utils/HolidayReviewUtils';
import { holidayColors } from 'mobile-holidays-react-native/src/Styles/holidayColors';
import PrimaryButton from 'mobile-holidays-react-native/src/Common/Components/Buttons/PrimaryButton';
import { paddingStyles } from 'mobile-holidays-react-native/src/Styles/Spacing';
import { fontStyles } from 'mobile-holidays-react-native/src/Styles/holidayFonts';
import { holidayBorderRadius } from 'mobile-holidays-react-native/src/Styles/holidayBorderRadius';
import { logHolidayReviewPDTClickEvents } from '../../Utils/HolidayReviewPDTTrackingUtils';
import { PDT_EVENT_TYPES } from '../../../utils/HolidayPDTConstants';

const { FULL_DAY_NAME, DATE_OF_MONTH, FULL_MONTH_NAME, FULL_YEAR, MONTH_NUMBER } = DATES_FORMAT;
const OvernightInfoPopup = ({ closeModal, continueBooking, reviewData }) => {
  const sellableFlightId =
    reviewData?.reviewDetail?.itineraryDetail?.dayItineraries?.[0]?.itineraryUnits?.[0]?.flight
      ?.sellableId;
  const flightObject = getFlightObjectDetails(
    reviewData?.reviewDetail?.flightDetail,
    sellableFlightId
  );

  const departureDate = getFormattedDate(
    flightObject?.departure || null,
    `${FULL_YEAR}-${MONTH_NUMBER}-${DATE_OF_MONTH}`,
    `${FULL_DAY_NAME}, ${DATE_OF_MONTH} ${FULL_MONTH_NAME} ${FULL_YEAR}`,
  );
  const arrivalDate = getFormattedDate(
    flightObject?.arrival || null,
    `${FULL_YEAR}-${MONTH_NUMBER}-${DATE_OF_MONTH}`,
    `${FULL_DAY_NAME}, ${DATE_OF_MONTH} ${FULL_MONTH_NAME} ${FULL_YEAR}`,
  );
  const src = flightObject?.fromAirport?.airportCity;
  const dest = flightObject?.toAirport?.airportCity;
  const overnightHeading = `You have selected an Overnight Flight from ${src} to ${dest} in your package.`;

  const handleContinueButton = () => {
    logHolidayReviewPDTClickEvents({
      actionType: PDT_EVENT_TYPES.buttonClicked,
      value: 'OvernightFlight_popup_Continue',
    })
    continueBooking();
  }

  if (!src || !dest) {
    return [];
  }

  return (
    <View style={styles.container}>
      <View style={styles.flightIconContainer}>
        <Image source={FlightIcon} style={styles.flightIcon} />
      </View>
      <View style={styles.contentWrap}>
        <Text style={styles.overnightHeading}>{overnightHeading}</Text>
        <View style={styles.flightDetailContainer}>
          <Text style={styles.flightDetailText}>
            Flight to {dest} : {departureDate}
          </Text>
          <Text style={styles.flightDetailText}>
            Arrival in {dest} : {arrivalDate}
          </Text>
        </View>
        <View style={styles.buttonsContainer}>
          <TouchableOpacity onPress={closeModal} style={styles.widthBtn}>
            <Text style={styles.goBackText}>GO BACK</Text>
          </TouchableOpacity>
          <PrimaryButton
            buttonText={'Continue'}
            handleClick={handleContinueButton}
            btnContainerStyles={styles.continueButton}
          />
        </View>
      </View>
    </View>
  );
};

OvernightInfoPopup.propTypes = {
  closeModal: PropTypes.func.isRequired,
  continueBooking: PropTypes.func.isRequired,
  reviewData: PropTypes.object.isRequired,
};

const styles = StyleSheet.create({
  container: {
    paddingTop: 10,
  },
  contentWrap: {
    paddingTop: 30,
    paddingBottom: 40,
    paddingHorizontal: 30,
  },
  flightIcon: {
    width: 50,
    height: 50,
    resizeMode: 'contain',
  },
  flightIconContainer: {
    borderRadius: 100,
    backgroundColor: '#EAF5FF',
    display: 'flex',
    position: 'absolute',
    width: 80,
    height: 80,
    top: -50,
    left: 15,
    justifyContent: 'center',
    alignItems: 'center',
  },
  overnightHeading: {
    ...fontStyles.headingMedium,
    color: holidayColors.black,
  },
  flightDetailContainer: {
    ...paddingStyles.pv20,
  },
  flightDetailText: {
    ...fontStyles.labelMediumRegular,
    color: holidayColors.lightGray,
    paddingVertical: 4,
  },
  goBackText: {
    color: holidayColors.primaryBlue,
    ...fontStyles.labelMediumBold,
  },
  buttonsContainer: {
    display: 'flex',
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    ...paddingStyles.pv10,
  },
  continueButton: {
    ...paddingStyles.ph20,
  },
});

export default OvernightInfoPopup;
