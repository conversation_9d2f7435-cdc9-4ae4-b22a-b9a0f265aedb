import React from 'react';
import { StyleSheet, View, Text, Image } from 'react-native';
import AtomicCss from '@mmt/legacy-commons/Styles/AtomicCss';
import iconSpoon from '@mmt/legacy-assets/src/holidays/ic_spoon.webp';
import iconfork from '@mmt/legacy-assets/src/holidays/ic_fork.webp';
import _ from 'lodash';
import { holidayColors } from '../../../../../Styles/holidayColors';
import { fontStyles } from '../../../../../Styles/holidayFonts';
import {isMobileClient} from "../../../../../utils/HolidayUtils";

const PhoenixDayMeals = (props) => {
    const HTMLView = isMobileClient() ? require('react-native-htmlview').default : require('../../../../../Common/Components/HTML').default;
    const { item } = props || {};
   const {mealSummary, mealCityId} = item || {};
   const getMealData = () => {
    let meal = mealSummary[mealCityId]?.text;
    return (
      <View style={[AtomicCss.flexColumn, AtomicCss.alignCenter]}>
          <View style={styles.daymealTitle}>
            {!_.isEmpty(meal) && (
              <HTMLView value={`<p>${meal}</p>`} stylesheet={HtmlStyle} />
            )}
          </View>
      </View>
    );
  };
    return (
        <View style={[styles.itineraryRowContent, styles.itineraryRowContentLast]}>
            <View style={[AtomicCss.flexRow, AtomicCss.alignCenter]}>
                <Text style={styles.daymealTitle}>Day Meals</Text>
                <View style={AtomicCss.paddingLeft10}><Image source={iconSpoon} style={styles.iconspoon} /></View>
                <View><Image source={iconfork} style={styles.iconfork} /></View>
            </View>
            <View style={AtomicCss.marginTop10}>
                {getMealData()}
            </View>
        </View>
    );
};

const styles = StyleSheet.create({
    itineraryRowContent: {
        width: '90%',
        marginLeft:10,
        paddingBottom: 15,
        borderBottomWidth: 1,
        borderColor: holidayColors.grayBorder,
    },
    itineraryRowContentLast: {
        borderBottomWidth: 0,
    },
    daymealTitle: {
        ...fontStyles.labelBaseBlack,
        color:holidayColors.black,
    },

    iconspoon: {
        width: 6,
        height: 14,
        resizeMode: 'cover',
    },
    iconfork: {
        width: 6,
        height: 14,
        resizeMode: 'cover',
    },
    crossIcon: {
        ...fontStyles.labelSmallBlack,
        color: holidayColors.white,
        marginTop: -2,
    },
    icontick: {
        width: 7,
        height: 5,
        borderBottomWidth: 1.5,
        borderLeftWidth: 1.5,
        transform: [{ rotate: '-45deg' }],
        borderColor: holidayColors.white,
        marginTop: -1,
    },
    iconGreyBg: {
        width: 5,
        height: 5,
        borderRadius: 5,
        backgroundColor: holidayColors.grayBorder,
        justifyContent: 'center',
        alignItems: 'center',
    },
    spacing:{
        paddingBottom:5,
    },
});
const HtmlStyle = {
  p: {
    ...fontStyles.labelBaseRegular,
    color:holidayColors.gray,
    marginTop:5,
  },
  b: {
    ...fontStyles.labelBaseBlack,
    color:holidayColors.gray,
  },
};
export default PhoenixDayMeals;
