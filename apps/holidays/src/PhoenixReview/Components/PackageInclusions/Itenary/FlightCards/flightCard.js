import React from 'react';
import {
    StyleSheet,
    View,
    Text,
} from 'react-native';
import { getFlightDuration, getFlightTime } from '../../../../../PhoenixDetail/Utils/PhoenixDetailUtils';
import { getFlightDate, parseFlightDate } from '../../../../../PhoenixDetail/Utils/FlightUtils';
import AtomicCss from '@mmt/legacy-commons/Styles/AtomicCss';
import HolidayImageHolder from '../../../../../Common/Components/HolidayImageHolder';
import { getAirlineIconUrl } from '../../../../../PhoenixDetail/Components/FlightDetailPage/FlightListing/FlightsUtils';
import { getAirlineCodes } from '../../../../../ChangeFlight/HolidayFlightUtils';
import { OvernightFlightPill } from '../../../../../Common/Components/OvernightFlightComponents';
import BaggageInfoDetails from '../BaggageInfoDetails';
import { ischeckinBaggageInfoAvailable } from '../../../../../utils/HolidayUtils';
import { holidayColors } from '../../../../../Styles/holidayColors';
import { fontStyles } from '../../../../../Styles/holidayFonts';
const FlightCard = (props) => {

    const { layovers, flightLegs, stops, overnightLabel = '', isOvernight = false, baggageInfo = {}, flightMetadataDetail = {} } = props || {};
    const flightData = flightLegs?.[0]; //need to show one flight only
    const { airlineCode, departure, fromAirport } = flightData || {};
    const duration = getFlightDuration(props?.duration);
    const airlineIconCodes = getAirlineCodes(props);
    const lastFlightLegIndex = flightLegs?.length - 1;
    const from = fromAirport?.airportCity;
   const airlineIcon =  airlineIconCodes?.length > 1 ? 'Multiple Flight Codes' : airlineCode;
    const lastFlight = flightLegs?.[lastFlightLegIndex];
    const to = lastFlight?.toAirport?.airportCity;

    const arrival = lastFlight?.arrival;
    const depTime = getFlightTime(parseFlightDate(departure));
    const depDate = getFlightDate(departure);
    const arrTime = getFlightTime(parseFlightDate(arrival));
    const arrivalDate = getFlightDate(arrival);
    const showBaggageInfo = ischeckinBaggageInfoAvailable(flightMetadataDetail);

    return (
        <View  style={styles.itineraryRowContent}>
            <View style={[AtomicCss.flexRow, AtomicCss.alignCenter,styles.wraptext]}>
                <Text style={styles.onwardText}>Onward  </Text>
                <Text style={styles.durationText}>| </Text>
                <Text style={styles.durationText}>
                    {flightLegs?.map((leg,index) => {
                        const { airlineName, flightId } = leg || {};
                        return <Text key={flightId}>
                            {airlineName} {flightId}
                            {index < lastFlightLegIndex && <Text>, </Text>}
                        </Text>;
                    })
                    }
                </Text>
                <Text style={styles.durationText}> | {duration}</Text>
                <Text style={styles.durationText}>
                {layovers?.length > 0 &&
                        layovers.map((layover, index) => {
                            return <Text >| Layover in {layover?.airport}
                                {index < layovers.length - 1 && <Text> | </Text>}
                            </Text>;
                        }
                        )}
                </Text>
            </View>
            <View style={[AtomicCss.flexRow, AtomicCss.alignCenter, AtomicCss.marginTop10]}>
                <View style={AtomicCss.marginRight16}>
                    <HolidayImageHolder
                        imageUrl={getAirlineIconUrl(airlineIcon)}
                        style={styles.imgFlight}
                        resizeMode={'contain'}
                    />
                </View>
                <View style={AtomicCss.flex1}>
                    <Text style={[styles.onwardText, AtomicCss.marginBottom2]}>{depTime}</Text>
                    <Text style={[styles.flightDate, AtomicCss.marginBottom2]}>{depDate}</Text>
                    <Text style={[styles.durationText, AtomicCss.marginBottom2]}>{from}</Text>
                </View>
                <View style={AtomicCss.flex1}>
                    <View style={[styles.layover]}>
                        <View style={styles.layoverInner}>
                        <View style={styles.layoverDot} />
                        {stops > 0 && <View style={styles.layoverMidDot} />}
                        <View style={styles.layoverDot} />

                        </View>
                        {stops == 1 && <View style={[AtomicCss.alignCenter, AtomicCss.paddingTop3]}>
                            <Text style={styles.stopText}>{stops} Stop</Text>
                        </View>
                        }
                        {stops > 1 && <View style={[AtomicCss.alignCenter, AtomicCss.paddingTop3]}>
                            <Text style={styles.stopText}>{stops} Stops</Text>
                        </View>
                        }

                    </View>
                </View>
                <View style={AtomicCss.flex1}>
                    <Text style={[styles.onwardText, AtomicCss.marginBottom2]}>{arrTime}</Text>
                    <Text style={[styles.flightDate, AtomicCss.marginBottom2]}>{arrivalDate}</Text>
                    <Text style={[styles.durationText, AtomicCss.marginBottom2]}>{to}</Text>
                </View>
            </View>
            {/* Assume baggage details is same for all flight legs */}
            {showBaggageInfo ? (
                <BaggageInfoDetails
                baggageInfo={baggageInfo}
                flight={flightLegs[0]}
                flightMetadataDetail={flightMetadataDetail}
                />
            ) : null}
        </View>
    );
};

const styles = StyleSheet.create({
    layoverDot: {
        backgroundColor: 'white',
        borderWidth: 1,
        width: 6,
        height:6,
        borderRadius: 3,
        borderColor: '#CBCBCB',
    },
    layoverMidDot: {
        width: 6,
        height:6,
        borderRadius: 3,
        backgroundColor: '#249995',
    },
    onwardText: {
        ...fontStyles.labelBaseBlack,
        color:holidayColors.gray,
    },
    durationText: {
        ...fontStyles.labelSmallRegular,
        color:holidayColors.black,
    },
    stopText: {
        ...fontStyles.labelSmallRegular,
        color:holidayColors.green,
    },
    imgFlight: {
        width: 26,
        height: 26,
        resizeMode: 'contain',
    },
    itineraryRowContent: {
        width: '100%',
        paddingBottom: 15,
        marginLeft:10,
    },
    borderStyle:{
        borderBottomWidth: 1,
        borderColor: holidayColors.grayBorder,
    },
    layover: {
        borderTopWidth: 1,
        borderColor: holidayColors.grayBorder,
        width: 50,

    },
    layoverInner: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        marginTop: -3,
    },
    bgWhite: {
        backgroundColor: holidayColors.white,
    },
    wraptext:{
        flex: 1,
        flexWrap: 'wrap',
    },
    flightDate: {
        ...fontStyles.labelSmallRegular,
        color:holidayColors.gray,
    },

});

export default FlightCard;
