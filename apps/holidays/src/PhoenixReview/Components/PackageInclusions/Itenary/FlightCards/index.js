import React from 'react';
import {
    StyleSheet,
    View,
} from 'react-native';
import {getFlightObject} from '../../../../../PhoenixDetail/Utils/PhoenixDetailUtils';
import FlightCard from './flightCard';
import AlertIcon from '@mmt/legacy-assets/src/alert_cream.webp';
import { getLayovers } from '../../../../../PhoenixDetail/Utils/FlightUtils';
import { OvernightFlightInfo } from '../../../../../Common/Components/OvernightFlightComponents';
import DummyFlightRow from '../../../../../PhoenixDetail/Components/DummyFlight/dummyFlightRow';
import { isEmpty } from 'lodash';
import HolidaysMessageStrip from '../../../../../Common/Components/HolidaysMessageStrip';
import { FLIGHT_MESSAGE_TYPES } from '../../../../../HolidayConstants';
import ItineraryUnitExtraInfoMessages from 'mobile-holidays-react-native/src/PhoenixDetail/Components/ItineraryUnitExtraInfoMessages';
import { marginStyles } from '../../../../../Styles/Spacing';

const PackageFlightCard = (props) => {

    const { flightDetail, itineraryUnit, baggageInfo = {} } = props || {};
    const sellableId = itineraryUnit?.flight?.sellableId;
    const flightObject = getFlightObject(flightDetail, sellableId);
    const layovers = getLayovers(flightObject);
    const { flightMetadataDetail = {} } = flightObject || {};
    const { isDummy = false, isOvernight = false } = flightMetadataDetail || {};

  const messages = {
    [FLIGHT_MESSAGE_TYPES.OVERNIGHT_MESSAGE]:
      flightMetadataDetail[FLIGHT_MESSAGE_TYPES.OVERNIGHT_MESSAGE] || '',
    [FLIGHT_MESSAGE_TYPES.TERMINAL_MESSAGE]:
      flightMetadataDetail[FLIGHT_MESSAGE_TYPES.TERMINAL_MESSAGE] || '',
  };
    if (!flightObject) {
        return null;
    }

    if (isDummy) {
      return (
        <DummyFlightRow flightObject={flightObject} itineraryUnit={itineraryUnit} isReviewPage />
      );
    }

    return (
      <View style={{ width: '100%' }}>
        <FlightCard
          duration={flightObject?.duration}
          layovers={layovers}
          flightLegs={flightObject?.flightLegs}
          stops={flightObject?.stops}
          overnightLabel={flightObject?.overnightLabel}
          isOvernight={flightObject?.isOvernight}
          flightMetadataDetail={flightMetadataDetail}
          baggageInfo={baggageInfo}
        />
        <ItineraryUnitExtraInfoMessages
          extraInfo={flightMetadataDetail.flightExtraInfo}
          containerStyles={marginStyles.mb10}
        />
      </View>
    );
};
export default PackageFlightCard;
