import React from 'react';
import { StyleSheet, View, Text, Image } from 'react-native';
import HTMLView from 'react-native-htmlview';
import AtomicCss from '@mmt/legacy-commons/Styles/AtomicCss';
import iconActivities from '@mmt/legacy-assets/src/ic_activityInclIcon.webp';
import { fonts, colors } from '@mmt/legacy-commons/Styles/globalStyles';
import _ from 'lodash';
import { holidayColors } from '../../../../../Styles/holidayColors';
import { fontStyles } from '../../../../../Styles/holidayFonts';

const PhoenixActivityCard = (props) => {
    const {itineraryUnit, activityMap} = props || {};
    const { text,activity} = itineraryUnit || {};
    const {sellableId} = activity || {};
    let description = activityMap?.[sellableId] || '';
    return (
            <View style={styles.itineraryRowContent}>
                <View style={[AtomicCss.flexRow, AtomicCss.alignCenter]}>
                    <Text style={styles.activityTitle}>Activity</Text>
                    <View style={AtomicCss.paddingLeft10}><Image source={iconActivities} style={styles.iconActivities} /></View>
                </View>
                <View style={AtomicCss.marginTop8}>
                    <Text style={styles.activityDesc}>
                      {!!text && <Text style={[ AtomicCss.defaultText]}>
                            {text}
                        </Text>}
                    </Text>
                </View>
            </View>
    );

};
const styles = StyleSheet.create({
    activityTitle: {
        ...fontStyles.labelBaseBlack,
        color:holidayColors.black,
    },
    activityDesc:{
        ...fontStyles.labelBaseRegular,
        color:holidayColors.gray,
    },
    itineraryRow: {
        flexDirection: 'row',
        flexWrap: 'wrap',
        marginLeft: -6,
        marginTop:10,
    },
    bulletBlueTxt: {
        color: holidayColors.midLightBlue,
    },
    bulletDot: {
        marginTop: -4,
    },
    itineraryRowContent: {
        width: '99%',
        paddingLeft: 10,
        paddingBottom:15,

    },
    borderStyle:{
        paddingBottom: 15,
        borderBottomWidth: 1,
        borderColor: holidayColors.grayBorder,
    },
    iconActivities: {
        width: 10,
        height: 16,
        resizeMode: 'cover',
    },
});
const HtmlStyle = {
    p: {
        ...fontStyles.labelBaseBlack,
        color:holidayColors.black,
        lineHeight: 13,
        marginTop:5,
    },
    b: {
        ...fontStyles.labelBaseBold,
        color:holidayColors.gray,
        lineHeight: 13,
    },
  };
export default PhoenixActivityCard;
