import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { fontStyles } from '../../../../Styles/holidayFonts';
import { marginStyles, paddingStyles } from '../../../../Styles/Spacing';
import { holidayColors } from '../../../../Styles/holidayColors';
import AtomicCss from '@mmt/legacy-commons/Styles/AtomicCss';
import { getShowNewActivityDetail } from '../../../../utils/HolidaysPokusUtils';

const ItineraryHeader = ({ itineraryCount }) => {
  const {
    flightCount = 0,
    hotelCount = 0,
    activityCount = 0,
    transferCount = 0,
    mealCount = 0,
  } = itineraryCount || {};
  const arr = [];
  const showNewActivityDetail = getShowNewActivityDetail();
  if (flightCount > 0) {
    arr.push({ title: flightCount > 2 ? 'Flights' : 'Flight', count: flightCount });
  }
  if (hotelCount > 0) {
    arr.push({ title: hotelCount > 1 ? 'Hotels' : 'Hotel', count: hotelCount });
  }
  if (activityCount > 0) {
    arr.push({ title: activityCount > 1 ? 'Activities' : 'Activity', count: activityCount });
  }
  if (transferCount > 0) {
    arr.push({ title: transferCount > 1 ? 'Transfers' : 'Transfer', count: transferCount });
  }
  if (mealCount > 0 && showNewActivityDetail) {
    arr.push({ title: mealCount > 1 ? 'Meals' : 'Meal', count: mealCount });
  }
  const totalCount = flightCount + hotelCount + activityCount + transferCount;
  return (
    <View style={[styles.itineraryContainer, showNewActivityDetail ? paddingStyles.pb16 : {}]}>
      <View style={styles.itineraryTitle}>
        <Text
          style={[fontStyles.labelMediumRegular, marginStyles.mb4, { color: holidayColors.gray }]}
        >
          <Text style={[fontStyles.labelMediumBlack, { color: holidayColors.black }]}>
            Itinerary :{' '}
          </Text>
          {arr?.length > 0 &&
            arr?.length < 3 &&
            arr?.map(
              (element, index) =>
                element?.count > 0 && (
                  <Text style={[fontStyles.labelMediumBlack]} key={element?.title}>
                    {' '}
                    {element?.count}
                    <Text style={AtomicCss.regularFont}> {element?.title}</Text>
                    {arr?.[index + 1]?.count > 0 && <Text> / </Text>}
                  </Text>
                ),
            )}
        </Text>
        {arr?.length > 2 && (
          <Text style={[fontStyles.labelBaseRegular]}>
            {arr?.map(
              (element, index) =>
                element?.count > 0 && (
                  <Text
                    style={[fontStyles.labelBaseRegular, { color: holidayColors.black }]}
                    key={element?.title}
                  >
                    {' '}
                    {element?.count}
                    <Text style={AtomicCss.regularFont}> {element?.title}</Text>
                    {arr?.[index + 1]?.count > 0 && <Text> / </Text>}
                  </Text>
                ),
            )}
          </Text>
        )}
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  itineraryContainer: {
    paddingHorizontal: 16,
  },

  itineraryTitle: {
    ...paddingStyles.pt12,
  },
});

export default ItineraryHeader;
