import React from 'react';
import {
    StyleSheet,
    View,
    Image,
    Text,
} from 'react-native';

import AtomicCss from '@mmt/legacy-commons/Styles/AtomicCss';
import iconCross from '@mmt/legacy-assets/src/exclusion.webp';
import { fonts, colors } from '@mmt/legacy-commons/Styles/globalStyles';
import { removeHTMLTags, unescapeHTML } from '../../../../utils/HolidayUtils';
export const TNC_SEPERATOR = '~';
import { holidayColors } from '../../../../Styles/holidayColors';
import { fontStyles } from '../../../../Styles/holidayFonts';

const PackageExclusions = (props) => {
    const { data: exclusions } = props || {};
    let exclusionsArr = [];
    if (exclusions && exclusions !== '') {
        exclusionsArr = exclusions.split(TNC_SEPERATOR);
    }
    return (
        <View style={styles.wrapper}>
            <View>
                <Text style={styles.header}>Package Exclusions</Text></View>
            <View style={styles.content}>
                {exclusionsArr?.map((item,index) => {
                    let element = removeHTMLTags(unescapeHTML(item))?.trim();
                    if (element) {
                        return <View style={[AtomicCss.flexRow, AtomicCss.marginTop8]} key={index} >
                            <View style={[AtomicCss.marginRight10, AtomicCss.marginTop5]}><Image source={iconCross} style={styles.iconCross} /></View>
                            <View style={styles.listContent}>
                                <Text style={styles.contentTxt}>
                                    {element}
                                </Text>
                            </View>
                        </View>;
                    }
                })
                }
            </View>
        </View>
    );
};

const styles = StyleSheet.create({
    wrapper: {
        paddingVertical: 15,
        paddingHorizontal:16,
    },
    header: {
        ...fontStyles.labelMediumBlack,
        color:holidayColors.black,
    },
    content: {
        paddingVertical: 5,
        flexDirection: 'column',
    },
    iconCross: {
        width: 8,
        height: 8,
        resizeMode: 'contain',
    },
    listContent: {
        width: '90%',
        flexWrap: 'wrap',
        flexDirection: 'row',
    },
    contentTxt: {
        ...fontStyles.labelBaseRegular,
        color:holidayColors.gray,
    },
});

export default PackageExclusions;
