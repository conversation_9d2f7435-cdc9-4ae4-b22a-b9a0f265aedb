import React, { useCallback, useEffect, useMemo } from 'react';
import { View, Text, StyleSheet } from 'react-native';
import isEmpty from 'lodash';
import { LEISURE_DAY } from '../Itenary';
import {
  itineraryUnitSubTypes,
  itineraryUnitTypes,
} from '../../../../PhoenixDetail/DetailConstants';
import { fontStyles } from '../../../../Styles/holidayFonts';
import { holidayColors } from '../../../../Styles/holidayColors';
import { ITINERARY_COLLAPSED_STATE } from 'mobile-holidays-react-native/src/PhoenixDetail/Components/ItineraryV2/constants';
import {
  optimizeActivityDetailsForReview,
  optimizeFlightDetailsForReview,
  optimizeFlightDetailsForReviewV2,
  optimizeHotelDetailsForReview,
} from '../../../Utils/HolidayReviewUtils';
import { marginStyles } from '../../../../Styles/Spacing';

/* Icons */
import iconBreakfast from '../../../../PhoenixDetail/Components/images/ic_breakfast.png';
import iconHotel from '@mmt/legacy-assets/src/ic_hotelInclIcon.webp';
import { getIconForItineraryUnitType } from 'mobile-holidays-react-native/src/PhoenixDetail/Utils/PhoenixDetailUtils';

/* Components */
import PhoenixHotelCard from '../Itenary/HotelCard';
import PhoenixTransfer from '../Itenary/TransferCard';
import PhoenixSightSeeing from '../Itenary/SightSeeingCard';
import PhoenixDayMeals from '../Itenary/DayMealsCard';
import {
  renderActivityRow,
  renderMealsRow,
  renderSightSeeing,
  renderTransferRow,
} from './ItineraryV2CardComponents';

import HotelRow from '../../../../PhoenixDetail/Components/ItineraryV2/Hotel/HotelRow';
import FlightRow from 'mobile-holidays-react-native/src/PhoenixDetail/Components/ItineraryV2/Flight/FlightRow';
import MealRow from 'mobile-holidays-react-native/src/PhoenixDetail/Components/ItineraryV2/Meals/MealRow';
import DayPlanRowHOC from 'mobile-holidays-react-native/src/PhoenixDetail/Components/ItineraryV2/Common/DayPlanRowHOC';
import { FUNNEL_PAGE_NAMES } from 'mobile-holidays-react-native/src/HolidayConstants';

export const accessRestriction = {
  changeFlightRestricted: true,
  removeFlightRestricted: true,
  changeHotelRestricted: true,
  removeHotelRestricted: true,
  addActivityRestricted: true,
  removeActivityRestricted: true,
  changeTransferRestricted: true,
  removeTransferRestricted: true,
  removeVisaRestricted: true,
};
export const viewDetailAccessRestriction = {
  hotelRestricted: true,
  flightRestricted: true,
  transferRestricted: true,
};
const ItineraryV2CardContainer = ({
  day,
  data,
  roomCount,
  carContents,
  baggageInfo,
  reviewDetail,
  getLeisureDay,
  getSummaryCard,
  activityItinary,
  optimizePackageReviewdetail,
}) => {
  const { flightDetail, departureDetail } = optimizePackageReviewdetail || {};
  const { hotelDetail, activityDetail, itineraryDetail, destinationDetail, metadataDetail } =
    reviewDetail || {};

  if (data.type === LEISURE_DAY) {
    return getLeisureDay();
  }

  // useEffect(() => {        // commented out because it is making changes on reviewDetail object itSelf
  //   optimizeFlightDetailsForReview(reviewDetail);
  // }, []);

  const renderItineraryCard = (item, index, length) => {
    const { itineraryUnitType, itineraryUnitSubType } = item;
    switch (itineraryUnitType) {
      case itineraryUnitTypes.HOTEL:
        switch (itineraryUnitSubType) {
          case itineraryUnitSubTypes.CHECKIN:
          case itineraryUnitSubTypes.CHECKOUT:
            return (
              <HotelRow
                day={day}
                hideBorder
                fromPage={FUNNEL_PAGE_NAMES.REVIEW}
                itineraryUnit={item}
                defaultCollapsedState={ITINERARY_COLLAPSED_STATE.NON_COLLAPSIBLE}
                viewDetailAccessRestriction={viewDetailAccessRestriction}
                accessRestriction={accessRestriction}
                hotelDetail={optimizeHotelDetailsForReview(reviewDetail)}
              />
            );
          default:
            return null;
        }

      case itineraryUnitTypes.MEALS:
        return renderMealsRow({ item, reviewDetail });
      case itineraryUnitTypes.FLIGHT:
        switch (itineraryUnitSubType) {
          case itineraryUnitSubTypes.FLIGHT_ARRIVE:
          case itineraryUnitSubTypes.FLIGHT_DEPART:
            return (
              <FlightRow
                day={day}
                itineraryUnit={item}
                flightDetail={optimizeFlightDetailsForReviewV2(reviewDetail)}
                defaultCollapsedState={ITINERARY_COLLAPSED_STATE.NON_COLLAPSIBLE}
                viewDetailAccessRestriction={viewDetailAccessRestriction}
                accessRestriction={accessRestriction}
              />
            );
          default:
            return null;
        }
      case itineraryUnitTypes.TRANSFERS:
      case itineraryUnitTypes.CAR:
        return renderTransferRow({ item, reviewDetail });
      case itineraryUnitTypes.ACTIVITY:
        return renderActivityRow({ item, reviewDetail });
      case itineraryUnitTypes.SIGHTSEEING:
        return renderSightSeeing({ item, reviewDetail });

      default:
        return null;
    }
  };
  const cards = data?.map((item, index) => {
    const lastItem = index < data?.length - 1 ? false : true;

    const renderCard = ({ index, item, data = [] }) => {
      return (
        <View style={[styles.itineraryInnerBlock, marginStyles.mt10]}>
          {renderItineraryCard(item, index, data.length)}
        </View>
      );
    };

    if (item.itineraryUnitType === itineraryUnitTypes.COMMUTE) {
      const cardsCommute = item?.commute?.map((itemCommute, indexCommute) => {
        return renderCard({ index: indexCommute, item: itemCommute });
      });
   
      return cardsCommute;
    }
    return renderCard({ item, index, data });
  });

  return cards;
};

const styles = StyleSheet.create({
  itineraryInnerBlock: {
  },
});

export default ItineraryV2CardContainer;
