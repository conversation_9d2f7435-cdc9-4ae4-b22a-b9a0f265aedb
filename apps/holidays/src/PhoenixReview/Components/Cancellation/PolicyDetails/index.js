import  React from 'react';
import {
View,
StyleSheet,
Text,
} from 'react-native';
import AtomicCss from '@mmt/legacy-commons/Styles/AtomicCss';
import VerticalStepInfo from '../../VerticalStepInfo';
import { CANCELLATION_POLICY, FLEXI_DESCRIPTION, ZC_DESCRIPTION } from '../../../Utils/HolidayReviewConstants';


const PolicyDetails = (props) => {
    const { selectedTab, penalties, zcOption } = props;
    const selectedTabDescription = props.selectedTab === CANCELLATION_POLICY ? ZC_DESCRIPTION : FLEXI_DESCRIPTION;
    const leftHeader = `${zcOption?.available ? 'Current' : 'Package'} ${selectedTab}`;
    const rightHeader = !zcOption?.selected ? `${selectedTab} with ${selectedTabDescription}` : `Package ${selectedTab}`;
    let stepperCount = 0;
    if (props?.penalties?.length > 0 && !zcOption?.selected) {stepperCount++;}
    if (props?.penalties?.length > 0 && zcOption?.available) {stepperCount++;}
    const stepperWidth = () => {
        let width = '100%';
        return width;
    };
    const calculatedWidth = stepperWidth();
    return (
        <View style={styles.flexColumn}>
            <View style={[styles.columnHeader,{width: calculatedWidth,flex:1}]}>
                     {props?.penalties?.length > 0 && !zcOption?.selected && <Text style={[AtomicCss.font11, AtomicCss.blackFont, AtomicCss.blackText,{flex:1} ]}>
                     {leftHeader}
                     </Text>
}
                     <Text numberOfLines={2} style={[AtomicCss.font11, AtomicCss.blackFont,AtomicCss.blackText, !zcOption?.selected ? styles.rightHeader : {},{flex:1}]}>
                         {props?.penalties?.length > 0 && zcOption?.available && rightHeader}
                    </Text>

              </View>
         <View style={[styles.wrapper]}>
            {/* left */}
            {props?.penalties?.length > 0 && !zcOption?.selected ?
                <VerticalStepInfo
                    mode={selectedTab}
                    penalties={penalties}
                    zcOption={zcOption}
                    zcSelected={false}
                    stepperCount={stepperCount}
                /> : []
            }
            {/* right */}
            {props?.penalties?.length > 0 && zcOption?.available ?
                <VerticalStepInfo
                    mode={selectedTab}
                    penalties={penalties}
                    zcOption={zcOption}
                    zcSelected={true}
                    stepperCount={stepperCount}
                /> : []
            }
        </View>
        </View>
    );
};

const styles = StyleSheet.create({
    flexColumn:{flexDirection:'column'},
    columnHeader: {
        backgroundColor: '#EAF5FF',
        display:'flex',
        flexDirection:'row',
        alignItems:'center',
        padding:5,
    },
    rightHeader:{
        marginLeft:10,
        display:'flex',
        flexWrap:'wrap',
        width:'50%',
    },
    wrapper:{
        backgroundColor: '#F9F9F9',
        display:'flex',
        flexDirection:'row',
        paddingBottom:15,
        flex:1,
    },

});
export default PolicyDetails;
