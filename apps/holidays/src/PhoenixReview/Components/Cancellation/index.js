import React, { useState } from 'react';
import {
  StyleSheet,
  View,
  TouchableOpacity,
  TouchableWithoutFeedback,
  Image,
  Text,
} from 'react-native';

import AtomicCss from '@mmt/legacy-commons/Styles/AtomicCss';
import CancellationPolicyList from './CancellationPolicyList';
import PolicyDetails from './PolicyDetails';
import PolicyDesc from './PolicyDesc';
import {
  CANCELLATION_POLICY,
  DATE_CHANGE_POLICY,
  FLEXI_DESCRIPTION,
  FLEXI_KEY,
  SECTIONS,
  ZC_FULL_DESCRIPTION,
  ZC_KEY,
} from '../../Utils/HolidayReviewConstants';
import iconArrowHead from '../../images/ic_arrowHead.webp';
import { addDays } from '@mmt/legacy-commons/Helpers/dateTimehelpers';
import { PDTConstants } from '../../Utils/HolidayReviewConstants';
import { trackReviewLocalClickEvent } from '../../../Review/Utils/HolidayReviewUtils';
import { fillDateAndTime } from '../../../Common/HolidaysCommonUtils';
import { DEFAULT_MESSAGE_COLOR_CODE, DEFUALT_MESSAGE_BACKGROUND_CODE } from '../InformationStrip';
import { CancellationPolicies } from '../../../Common/Components/CancellationPolicies';
import { holidayColors } from '../../../Styles/holidayColors';

const errorText = {
  Flexi: 'Date change not possible after booking',
  Cancellation: 'Cancellation not possible after booking',
};

export const PolicyContainer = ({ type, gotoAddon, penaltyList, zcOptions }) => {
  let penalties = [];
  let policies = [];
  let policyEndDate = '';
  let zcOption = zcOptions.find((e) =>
    type === CANCELLATION_POLICY ? e?.type === ZC_KEY : e?.type === FLEXI_KEY,
  );
  if (penaltyList[type]?.penalties) {penalties = penaltyList[type].penalties;}
  if (penaltyList[type]?.policies) {policies = penaltyList[type].policies.map((e) => e?.text);}
  if (penalties?.length > 0) {
    let date;
    for (let i = penalties?.length - 1; i >= 0; i--) {
      const item = penalties?.[i];
      if (!item?.nonRefundable) {
        date = item?.fromDate;
        break;
      }
    }
    let newDate = addDays(date, -1);
    const dateObj = fillDateAndTime(newDate, 'D MMMM');
    let day = dateObj?.split(' ')?.[0];
    policyEndDate = day + ' ' + dateObj?.split(' ')?.[1];
  }
  const policyTitle = `${
    type === CANCELLATION_POLICY ? 'Cancellation' : 'Date Change'
  } Possible till ${policyEndDate}*`;

  return (
    <View>
      <View style={{ marginBottom: 5 }}>
        <Text style={[AtomicCss.font18, AtomicCss.blackFont, AtomicCss.blackText]}>
          Package {type}
        </Text>
      </View>
      {penalties?.length > 0 &&
      !(penalties?.length === 1 && penalties[penalties.length - 1]?.nonRefundable) ? (
        <PolicyDesc
          policyTitle={policyTitle}
          content={
            <Text style={[AtomicCss.font12, AtomicCss.regularFont, AtomicCss.greyText]}>
              After that Package
              <Text> {type === CANCELLATION_POLICY ? 'is ' : []}</Text>
              <Text style={AtomicCss.blackFont}>
                {type === CANCELLATION_POLICY ? 'Non - Refundable ' : 'date cannot be changed'}
              </Text>
            </Text>
          }
        />
      ) : (
        <Text
          style={[AtomicCss.font12, AtomicCss.regularFont, { color: '#EB2026', marginBottom: 10 }]}
        >
          {type === DATE_CHANGE_POLICY ? errorText.Flexi : errorText.Cancellation}
        </Text>
      )}
      <PolicyDetails penalties={penalties} zcOption={zcOption} selectedTab={type} />
      {/* list */}
      <CancellationPolicyList policies={policies} />
      {zcOption.available && !zcOption.selected ? (
        <View style={[AtomicCss.flexRow, AtomicCss.alignCenter, AtomicCss.marginTop15]}>
          <Text style={[AtomicCss.font12, AtomicCss.blackFont, AtomicCss.blackText]}>
            ADD {type === CANCELLATION_POLICY ? ZC_FULL_DESCRIPTION : FLEXI_DESCRIPTION} from
          </Text>
          <TouchableOpacity onPress={gotoAddon}>
            <View style={[AtomicCss.flexRow, AtomicCss.alignCenter]}>
              <Text
                style={[
                  AtomicCss.font12,
                  AtomicCss.blackFont,
                  AtomicCss.azure,
                  AtomicCss.marginLeft3,
                ]}
              >
                Add-ons Section
              </Text>
              <Image source={iconArrowHead} style={styles.iconArrowHead} />
            </View>
          </TouchableOpacity>
        </View>
      ) : null}
    </View>
  );
};
const Cancellation = (props) => {
  const { reviewData, listData, gotoSection, penaltyDetail: penaltyDetails = {} } = props;

  const gotoAddon = () => {
    gotoSection(SECTIONS.CANCELLATION, SECTIONS.ADD_ONS);
    trackReviewLocalClickEvent(PDTConstants.PURCHASE_ADDON, '', reviewData);
  };

  const tabList = [];
  let penaltyList = {};
  let zcOptions = [];
  if (penaltyDetails?.cancellationPenalty) {
    penaltyList[CANCELLATION_POLICY] = penaltyDetails.cancellationPenalty;
    tabList.push(CANCELLATION_POLICY);
  }
  if (penaltyDetails?.dateChangePenalty) {
    penaltyList[DATE_CHANGE_POLICY] = penaltyDetails.dateChangePenalty;
    tabList.push(DATE_CHANGE_POLICY);
  }

  if (penaltyDetails?.zcOptions) {
    zcOptions = penaltyDetails.zcOptions;
  }

  const {
    text: title = '',
    colorCode = DEFAULT_MESSAGE_COLOR_CODE,
    colorCodeBg = DEFUALT_MESSAGE_BACKGROUND_CODE,
  } = penaltyDetails;

  return (
    <View style={styles.wrapper}>
      {title ? (
        <View style={[{ backgroundColor: colorCodeBg }, styles.headingInfo]}>
          <Text style={[styles.headingText, { color: colorCode }]}>{title?.toUpperCase()}</Text>
        </View>
      ) : null}
      {tabList.map((tab, index) => (
        <View>
          <CancellationPolicies
            type={tab}
            penaltyList={penaltyList}
            gotoAddon={gotoAddon}
            zcOptions={zcOptions}
            isReview
          />
          {index < (tabList.length - 1) ? <View style={styles.borderContainer}/> : null}
        </View>
      ))}
    </View>
  );
};

const styles = StyleSheet.create({
  wrapper: { padding: 16 },
  tabContent: {
    backgroundColor: 'white',
    borderTopWidth: 1,
    borderColor: '#e7e7e7',
    borderBottomLeftRadius: 4,
    borderBottomRightRadius: 4,
    padding: 15,
  },
  contentWrap: {
    paddingHorizontal: 25,
    paddingTop: 25,
    paddingBottom: 50,
  },
  headingInfo: {
    borderRadius: 4,
    paddingVertical: 4,
    paddingHorizontal: 10,
    marginBottom: 14,
    alignSelf: 'flex-start',
    display: 'flex',
  },
  headingText: {
    fontStyle: 'normal',
    fontWeight: '500',
    lineHeight: 13,
    fontSize: 11,
  },
  iconClose: {
    width: 24,
    height: 24,
  },
  borderContainer: {
    marginTop: 15,
    marginBottom: 10,
    borderWidth: 1,
    borderColor: '#e0e5e9',
    borderStyle: 'dashed',
    borderRadius: 1,
  },
  tabList: {
    backgroundColor: 'white',
    borderRadius: 4,
    borderWidth: 0.5,
    borderColor: '#f5f5f5',
    paddingHorizontal: 10,
    paddingVertical: 10,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,

    elevation: 5,
    width: '48%',
    marginRight: 2.5,
  },
  tabListActive: {
    backgroundColor: holidayColors.primaryBlue,
    borderRadius: 4,
  },
  tabListWrapper: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 15,
  },
  iconArrowHead: {
    width: 18,
    height: 14,
  },
});

export default Cancellation;
