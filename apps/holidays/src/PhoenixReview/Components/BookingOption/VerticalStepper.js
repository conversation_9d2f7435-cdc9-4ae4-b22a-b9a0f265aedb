import React from 'react';
import {
    StyleSheet,
    View,
    Text,
} from 'react-native';
import { rupeeFormatterUtils } from '../../../utils/HolidayUtils';
import { getFormattedDate } from '../../Utils/HolidayReviewUtils';
import { fontStyles } from 'mobile-holidays-react-native/src/Styles/holidayFonts';
import { holidayColors } from 'mobile-holidays-react-native/src/Styles/holidayColors';
import { marginStyles } from 'mobile-holidays-react-native/src/Styles/Spacing';

const VerticalStepper = (props) => {
    const installmentDetails = props.installmentDetails;

    return (
        <View style={styles.stepContainer}>
            <View style={styles.verticalLine} />
            {installmentDetails?.length > 0
            ? installmentDetails.map((installment, i) => (
                <View style={styles.installmentRow} key={i}>
                    <View style={styles.numberList}>
                    <Text style={styles.numberText}>{i + 1}</Text>
                    </View>
                    <View style={styles.payLaterRow}>
                    {i === 0 ? (
                        <View>
                        <Text style={styles.payHeading}>Pay to Book</Text>
                        <Text style={styles.payDesc}>Amount to pay now to reserve</Text>
                        </View>
                    ) : (
                        <Text style={styles.payHeading}>
                        Before {getFormattedDate(installment?.partPaymentDate, 0, 'D MMMM')}
                        </Text>
                    )}
                    {
                        <Text style={[i === 0 ? styles.reservePrice : styles.installmentPrice]}>
                        {rupeeFormatterUtils(installment?.partPaymentValue)}
                        </Text>
                    }
                    </View>
                </View>
                ))
            : []}
      </View>
    );
};

const styles = StyleSheet.create({
    stepContainer: {
        ...marginStyles.mv16,
        ...marginStyles.ml10,
        flex: 1,
    },
    installmentRow: {
        flexDirection: 'row',
        alignItems: 'center',
        ...marginStyles.mb16,
    },
    numberList: {
        width: 18,
        height: 18,
        borderRadius: 18,
        justifyContent: 'center',
        alignItems: 'center',
        marginRight: 20,
        backgroundColor: holidayColors.lightBlueBg,
    },
    numberText: {
        ...fontStyles.labelSmallBlack,
        color: holidayColors.gray,
    },
    payLaterRow: {
        width: '100%',
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-between',
        flex: 1,
    },
    payHeading: {
        ...fontStyles.labelMediumBlack,
        color: holidayColors.gray,
    },
    payDesc: {
        ...fontStyles.labelBaseRegular,
        color: holidayColors.gray,
    },
    verticalLine: {
        width: 1,
        height: '78%',
        backgroundColor: holidayColors.grayBorder,
        position: 'absolute',
        top: 10,
        left: 8,
    },
    reservePrice: {
        ...fontStyles.labelMediumBlack,
        color: holidayColors.gray,
    },
    installmentPrice: {
        ...fontStyles.labelMediumRegular,
        color: holidayColors.gray,
    },
});

export default VerticalStepper;
