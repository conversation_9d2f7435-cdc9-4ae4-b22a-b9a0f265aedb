import React from 'react';
import {Text,View,StyleSheet,TextInput} from 'react-native';

const InputElement = (props) => {

    const {
        error,
        label,
        placeholder,
        onChangeText,
        value,
        icon,
        iconPosition,
        keyboardType,
        isMandatory,
        editable,
    } = props;

    const getFlexDirection = () => {
        if (icon && iconPosition) {
          if (iconPosition === 'left') {
            return 'row';
          } else if (iconPosition === 'right') {
            return 'row-reverse';
          }
        }
    };

    const getBorderColor = () => {
        if (error) {
          return 'rgba(235, 32, 38, 0.2)';
        } else {
          return 'rgba(74, 74, 74, 0.2)';
        }
      };
    return (
        <View>
            {!!label && <Text style={styles.label}>{label} {isMandatory && <Text style={styles.mandatory}>*</Text>}</Text>}
            <View style={[styles.inputWrapper, {borderColor: getBorderColor(), flexDirection: getFlexDirection()} ]}>
                <View >{icon && icon}</View>
                <TextInput
                    style = {styles.input}
                    placeholder={placeholder}
                    onChangeText={onChangeText}
                    value = {value}
                    keyboardType = {keyboardType}
                    editable={editable}
                    onSubmitEditing={()=>{Keyboard.dismiss();}}
                    {...props}
                />
            </View>
            {error ? (<View style={styles.errorWrap}><Text style={styles.errorText}>{error}</Text></View>) : null}
        </View>

    );
};

const styles = StyleSheet.create({
    inputWrapper: {
        borderBottomWidth: 1,
        alignItems: 'center',
        borderColor: 'rgba(74, 74, 74, 0.2)',

    },
    labelWrapper: {
        flexDirection: 'row',
    },
    label: {
        fontSize:12,
        fontFamily:'Lato-Regular',
        color:'#000',
        marginBottom:8,

    },
    input:{
        borderRadius: 4,
        flex: 1,
        width: '100%',
        backgroundColor: '#fff',
        color: '#000',
        paddingVertical: 5,
        paddingHorizontal: 0,
        fontFamily: 'Lato-Bold',
        fontSize: 14,
        height: 25,
    },
    mandatory: {
        color: '#eb2026',

    },
    errorWrap: {
        marginTop: 5,
    },
    errorText: {
        color: '#eb2026',
        fontSize: 12,
    },
    errorTxtbox: {
        borderColor: '#eb2026',
    },

});

export default InputElement;
