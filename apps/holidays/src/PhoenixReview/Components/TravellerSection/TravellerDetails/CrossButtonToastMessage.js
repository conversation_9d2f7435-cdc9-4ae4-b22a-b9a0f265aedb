import React from 'react';
import { Image, Text, View, StyleSheet, TouchableOpacity } from 'react-native';
import { fonts } from '@mmt/legacy-commons/Styles/globalStyles';

const DESCRIPTION = "By Removing this user from this room, you are removing her/him from travellers on this trip. You can add her/him again from 'saved list'";
const CROSS_IMAGE = require('@mmt/legacy-assets/src/red_cross.webp');

const CrossButtonToastMessage = ({ closePopup }) => (
  <View style={styles.container}>
    <View style={styles.textContainer}>
      <Text style={styles.text}>{DESCRIPTION}</Text>
    </View>
    <TouchableOpacity onPress={closePopup}>
      <Image source={CROSS_IMAGE} style={styles.image} />
    </TouchableOpacity>
  </View>
);

const styles = StyleSheet.create({
  container: {
    paddingHorizontal: 16,
    paddingVertical: 10,
    backgroundColor: '#FFF2F2',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  textContainer: {
    flex: 1,
    marginRight: 9,
  },
  text: {
    fontFamily: fonts.italic,
    fontSize: 11,
    lineHeight: 13.2,
    fontWeight: '400',
    color: '#EB2026',
  },
  image: {
    marginHorizontal: 4,
    height: 12,
    width: 12,
  },
});

export default CrossButtonToastMessage;
