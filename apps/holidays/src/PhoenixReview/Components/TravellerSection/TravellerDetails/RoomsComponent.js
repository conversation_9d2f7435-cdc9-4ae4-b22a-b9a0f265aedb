import  React, {useState, useRef} from 'react';
import { Image, Text, View, StyleSheet, ScrollView, TouchableOpacity } from 'react-native';
import iconCloseBlue from '@mmt/legacy-assets/src/ic_close_blue.webp';
import AtomicCss from '@mmt/legacy-commons/Styles/AtomicCss';
import iconGuest from '../../../images/ic_guest.webp';
import { fonts } from '@mmt/legacy-commons/Styles/globalStyles';
import { FORM_FILL_STATUS, PDTConstants } from '../../../Utils/HolidayReviewConstants';
import { getAge } from '../../../Utils/ChooseTravellerUtil';
import ReviewPopup from '../../Popup/ReviewPopup';
import { trackReviewLocalClickEvent } from '../../../../Review/Utils/HolidayReviewUtils';
import PopupContent from '../../Popup/PopupContent';
import { holidayColors } from '../../../../Styles/holidayColors';
import { fontStyles } from '../../../../Styles/holidayFonts';
import { holidayBorderRadius, borderRadiusValues } from '../../../../Styles/holidayBorderRadius';

const RoomsComponent = (props) => {
  const {travellerList = [], onEmptyCardClick, currentFormIndex, onCrossClick} = props;
  return (travellerList && travellerList.length > 0) ? (
    <ScrollView
      horizontal
      showsHorizontalScrollIndicator={false}
      style={styles.roomCategory}
      contentContainerStyle={styles.roomContentContainer}
    >
      {travellerList.map((item, index) => (
        <View style={{flexDirection: 'row'}}>
          {item.showRoomTag ? <View style={styles.roomName}>
            <View style={styles.roomNameTxtWrap}>
              <Text style={styles.roomNameText}>{item.roomDisplayName}</Text>
            </View>
          </View> : []}
          <TouchableOpacity onPress={() => onEmptyCardClick(index)}>
            <TravellerComponent
              key={`TravellerComponent_${index}`}
              {...item}
              index={index}
              currentFormIndex={currentFormIndex}
              onCrossClick={onCrossClick}
            />
          </TouchableOpacity>
        </View>
      ))}
    </ScrollView>
  ) : [];
};

const TravellerComponent = ({status, index, displayName, age, fieldValues: {FIRST_NAME, LAST_NAME, DOB}, currentFormIndex, onCrossClick}) => {
  const [modalVisible, setModalVisible] = useState(false);
  const containerStyle = [styles.roomGuest];

  if (index === currentFormIndex) {
    containerStyle.push(styles.selectedRoomGuest);
  }
  const ageText = `${age} ${age == 1 ? 'year' : 'years'}`;
  if (status == FORM_FILL_STATUS.UNFILLED) {
    return (
      <View style={containerStyle}>
        <Image source={iconGuest} style={styles.iconGuest} />
        <View style={styles.displayNameContainer}>
          <Text style={styles.nameTitle}>{ displayName }</Text>
          {age > 0 && <Text style={styles.ageText}><Text style={styles.asterisk}>*</Text>{ageText}</Text>}
        </View>
      </View>
    );
  }
  const name = `${FIRST_NAME} ${LAST_NAME}`;
  const travellerAge = (DOB && (DOB.length > 0)) ? getAge(DOB[0], 'DD/MM/YYYY') : 0;

  const closeTraveller = ()=>{
   onCrossClick(index);
   setModalVisible(false);
   trackReviewLocalClickEvent(PDTConstants.CHANGE_PAX,`_${index}_form_confirm`);
  };
  const cancelTraveller = ()=>{
    trackReviewLocalClickEvent(PDTConstants.CHANGE_PAX,`_${index}_form`);
    setModalVisible(true);
  };
  let popupMessage =
    'By Removing this user from this room, you are removing her/him from travellers on this trip. ';
  if (status === FORM_FILL_STATUS.COMPLETE) {
    popupMessage = popupMessage.concat("You can add her/him again from 'saved list'");
  }
  return (
    <View style={containerStyle}>
      <TouchableOpacity style={styles.roomGuestCanel} onPress={cancelTraveller}>
        <Image source={iconCloseBlue} style={styles.iconCloseBlue}/>
      </TouchableOpacity>
      <View style={[AtomicCss.marginTop5, AtomicCss.paddingLeft5]}>
        <Text numberOfLines={2} style={styles.nameTitleGray}>{ name }</Text>
        {travellerAge > 0 ? <Text style={styles.nameTitleGray}>{ travellerAge }y</Text> : []}
        {status !== FORM_FILL_STATUS.FILLED && <Text style={[
          fontStyles.labelSmallRegular,
          (status == FORM_FILL_STATUS.INCOMPLETE) ? AtomicCss.redText : AtomicCss.greenText,
          AtomicCss.boldFont,
        ]}>{status}</Text>
        }
      </View>
      <ReviewPopup
        content={
          <PopupContent
          closeModal={closeTraveller}
          closePopup={() => setModalVisible(false)}
          heading = {'Are you sure you want to remove this user?'}
          desc={popupMessage}
          btnText={'REMOVE'}
          subBtnText={'Continue Editing'}
          />
        }
        closePopup={() => setModalVisible(false)}
        modalVisible={modalVisible}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  roomCategory:{
    flexDirection: 'row',
    paddingVertical: 16,
  },
  roomContentContainer: {
    alignItems: 'center',
    paddingHorizontal: 16,
  },
  nameTitle:{
    ...fontStyles.labelSmallBlack,
    color:holidayColors.primaryBlue,
  },
  nameTitleGray:{
    ...fontStyles.labelSmallBlack,
    color:holidayColors.gray,
  },
  roomGuest: {
    alignItems: 'center',
    justifyContent: 'center',
    width: 80,
    height: 80,
    borderWidth: 1,
    borderColor: holidayColors.primaryBlue,
    borderStyle: 'dashed',
    ...holidayBorderRadius.borderRadius8,
    marginRight: 10,
  },
  iconGuest: {
    width: 18,
    height: 18,
    resizeMode: 'contain',
  },
  selectedRoomGuest: {
    backgroundColor: holidayColors.lightBlueBg,
    borderStyle: 'solid',
  },
  roomName: {
    width: 22,
    height: 80,
    backgroundColor: holidayColors.black,
    borderTopLeftRadius: borderRadiusValues.br8,
    borderBottomLeftRadius: borderRadiusValues.br8,
    flexDirection: 'row',
    flexWrap: 'nowrap',
    position: 'relative',
  },
  roomNameText: {
    color: holidayColors.white,
    ...fontStyles.labelSmallRegular,
    transform: [{rotate: '-90deg'}],

  },
  roomNameTxtWrap: {
    position: 'absolute',
    left: -11,
    top: 32,
  },
  roomSelectionScroller: {
    paddingVertical: 10,
    paddingLeft: 15,

  },
  roomGuestCanel: {
    position: 'absolute',
    right: 6,
    top: 6,
    zIndex: 20,
  },
  iconCloseBlue: {
    width: 8,
    height: 8,
  },
  iconArrowDown: {
    width: 12,
    height: 8,
    resizeMode: 'contain',
  },
  displayNameContainer: {
    ...AtomicCss.marginTop5,
    alignItems: 'center',
  },
  ageText: {
    ...fontStyles.labelSmallRegular,
    lineHeight: 14,
    color: holidayColors.lightGray,
  },
  asterisk: {
    color: holidayColors.red,
  },
});

export default RoomsComponent;
