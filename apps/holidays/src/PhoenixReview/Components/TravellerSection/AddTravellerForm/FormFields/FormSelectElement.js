import React from 'react';
import {
  Text,
  View,
  StyleSheet,
  Platform,
  TouchableOpacity,
  Dimensions,
  ScrollView,
  FlatList
} from 'react-native';
import Dropdown from '@Frontend_Ui_Lib_App/Dropdown';
import {isAndroidClient} from '../../../../../utils/HolidayUtils';
import { SELECT_DEFAULT } from '../../../../../Review/HolidayReviewConstants';
import { holidayColors } from '../../../../../Styles/holidayColors';
import { fontStyles } from '../../../../../Styles/holidayFonts';
import { paddingStyles } from 'mobile-holidays-react-native/src/Styles/Spacing';
import { marginStyles } from 'mobile-holidays-react-native/src/Styles/Spacing';
import BottomSheetOverlay from 'mobile-holidays-react-native/src/Common/Components/BottomSheetOverlay';



const iconArrowDown = require('@mmt/legacy-assets/src/arrow_downGrey.webp');

export default class FormSelectElement extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      open: false,
      bottomsheetVisible: false,
    };
  }

  selected = (value) => {
    if (value === '' && !isAndroidClient()) {return;}
    this.setState({
      value,
    });
    this.props.handleChange(value);
    this.toggle();
  };
  static navigationOptions = {header: null};

  toggle = () => {
    this.setState({open: !this.state.open});
    this.props.toggleDropDown(this.state.open);
  };

  handleBottomsheet = () => {
    this.setState(prevState => ({
      bottomsheetVisible: !prevState.bottomsheetVisible,
    }));
  }

  render() {
    const {label, customLabelStyle, options, openDropDownName, dropDownName, errorState, errorText, isMandatory, value, selectedLabel} = this.props;
    const dropdownStyle = [styles.inputWrapperSelect];
    if (errorState) {
      dropdownStyle.push(styles.errorState);
    }
  const handleOtionSelect = (value) => {     
    this.selected(value); 
    this.setState({ bottomsheetVisible: false }); 
  }
  const dropDownCustomStyles = {
    labelStyle: customLabelStyle,
    endIconStyle: styles.iconArrowDown,
    dropdownValueStyle: styles.dropdownValueStyle
  }
    return (
      <View>
        <Dropdown 
          label={label}
          requiredText={isMandatory && <Text style={styles.mandatory}> *</Text>}
          isError = {errorState}
          errorMessage = {errorText}
          value={selectedLabel}
          onSelect={this.handleBottomsheet}
          isFloating={true}
          endIcon = {iconArrowDown}
          onEndIconPress={this.handleBottomsheet}
          customStyle={dropDownCustomStyles}
        />
        {this.state.bottomsheetVisible && <BottomSheetOverlay
          visible={this.state.bottomsheetVisible}
          toggleModal={this.handleBottomsheet}
          onDismiss={this.handleBottomsheet}
          containerStyles={styles.containerStyles}
          headingContainerStyles={styles.headingContainerStyles}
          title={SELECT_DEFAULT}
        >
          <FlatList
            data={options.slice(1)}
            keyExtractor={(item, index) => `${item.value}-${index}`}
            style={styles.scrollSection}
            contentContainerStyle={styles.dropDown}
            renderItem={({ item }) => (
              <TouchableOpacity onPress={() => handleOtionSelect(item.value)}>
                <Text style={styles.dropdownText}>{item.label}</Text>
              </TouchableOpacity>
            )}
          />
        </BottomSheetOverlay>}   
      </View>

    );
  }
}

const styles = StyleSheet.create({
  iconArrowDown: {
    width: 20,
    height: 20,
    tintColor: holidayColors.lightGray
  },
  dropDown: {
    ...marginStyles.mt10,
  },
  dropdownText: {
    color: holidayColors.black,
    ...fontStyles.labelMediumRegular,
    ...paddingStyles.pb30,
    ...paddingStyles.ph12,
  },
  errorState: {
    borderColor: holidayColors.red,
  },
  errorText: {
    color: holidayColors.red,
    ...fontStyles.labelSmallRegular,
  },
  mandatory: {
      color: holidayColors.red,
  },
  dropdownValueStyle: {
    color: holidayColors.black,
    ...fontStyles.labelMediumBlack,
    ...Platform.select({
      ios: {...marginStyles.mt8},
      android:{...marginStyles.mt0}
    }),
  },
  containerStyles: {
    ...paddingStyles.pa16, 
  },
  scrollSection: {
    maxHeight: Platform.select({
      ios: Dimensions.get('window').height - 250,  
      android: Dimensions.get('window').height - 270, 
      web: Dimensions.get('window').height - 270, 
    }),
    ...marginStyles.mb20,
  },
  headingContainerStyles: {
    ...paddingStyles.pb16
  }

});
