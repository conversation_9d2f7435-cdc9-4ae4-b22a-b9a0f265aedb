import React, { useState } from 'react';
import {
    StyleSheet,
    View,
    Image,
    Text,
    Platform
} from 'react-native';
import AtomicCss from '@mmt/legacy-commons/Styles/AtomicCss';
import iconPassportArrow from '../../../images/ic_passportArrow.webp';
import { FIELD_FIRSTNAME, FIELD_TYPE_TEXT, FIELD_TYPE_DATE, FIELD_TYPE_SINGLE_SELECT, FIELD_TYPE_RADIO, FIELD_TYPE_CHECKBOX } from '../../../Utils/HolidayReviewConstants';
import FormSelectElement from './FormFields/FormSelectElement';
import DatePickerComponent from './FormFields/DatePicker';
import { getDate } from '../../../Utils/HolidayReviewUtils';
import RadioOptions from './FormFields/RadioOptions';
import InputCheckboxGroup from './FormFields/InputCheckboxGroup';
import { isIosClient } from '../../../../utils/HolidayUtils';
import { holidayColors } from '../../../../Styles/holidayColors';
import { fontStyles } from '../../../../Styles/holidayFonts';
import { borderRadiusValues, holidayBorderRadius } from '../../../../Styles/holidayBorderRadius';
import FloatingInput from '@Frontend_Ui_Lib_App/FloatingInput';
import { marginStyles, paddingStyles } from '../../../../Styles/Spacing'

const  imgPassport = 'https://hldak.mmtcdn.com/prod-s3-hld-hpcmsadmin/holidays/images/phonixImages/tooltip1.png';

const AddTravellerForm = (props) => {
    const { values, errors, handleChange, fields, sectionFields, innerSections, description, options, layoutMap, onToggleField, currentFormIndex } = props;
    const [showPassportCard, setPassportCard] = useState(false);
    const togglePassportCard = (val) => {
        setPassportCard(val);
        setTimeout(()=> setPassportCard(false), 4000);
    };
    const getInputValue = (values, name) => {
        if (values[name] && values[name].length > 0) {
            return values[name].join('');
        }
        return '';
    };


    const getMultiSelectValues = (name)=>{
        return values[name];
    };

    const groupFieldsByRow = (fieldList) => {
        let fieldsByRow = {};
        let result = [];
        if (fieldList && fieldList.length > 0) {
            fieldList.forEach(e => {
                let fieldRow = fields[e]?.uiAttributes?.rowId;
                if (fieldRow) {
                    if (fieldsByRow[fieldRow]) {fieldsByRow[fieldRow] = [...fieldsByRow[fieldRow], fields[e]];}
                    else {fieldsByRow[fieldRow] = [fields[e]];}
                }
            });
        }
        if (fieldsByRow) {
            Object.keys(fieldsByRow).sort().forEach(e => {
                result.push(fieldsByRow[e]);
            });
        }
        return result;
    };

    let groupedFields = groupFieldsByRow(sectionFields);

    const renderField = (item, layoutMap, onToggleField, currentFormIndex) => {
        let widthPercentage = item?.uiAttributes?.width;
        if (widthPercentage) {
            if (widthPercentage <= 0 || widthPercentage > 100) {
                widthPercentage = 100;
            }
        } else {
            widthPercentage = 100;
        }

        switch (item?.inputType){
            case FIELD_TYPE_TEXT:
                return getFieldText(item,widthPercentage,layoutMap);
            case FIELD_TYPE_DATE:
                return getFieldDate(item,widthPercentage,layoutMap);
            case FIELD_TYPE_SINGLE_SELECT:
                return getFieldSelect(item,widthPercentage,layoutMap,onToggleField,currentFormIndex);
            case FIELD_TYPE_RADIO:
                return getRadioInput(item,widthPercentage,layoutMap);
            case FIELD_TYPE_CHECKBOX:
                return getCheckboxInput(item,widthPercentage,layoutMap);

        }
    }; 

    const [filledField, setFilledField] = useState([]);
    const handleLabelChange = (name, value) => {
        // Check if the value is not empty and if the field is not already in the array
        handleChange({ name, value: [value] });
        if (value && !filledField.includes(name)) {
          setFilledField(prevFields => [...prevFields, name]);
        } 
        // If the value is empty and the field exists in the array, remove it
        else if (!value && filledField.includes(name)) {
          setFilledField(prevFields => prevFields.filter(fieldName => fieldName !== name));
        }
      };
      
    const getFieldText = (item,widthPercentage,layoutMap) => {
        let fieldName = item?.name;
        const labelColorStyle = filledField.includes(item?.name) ? {color: holidayColors.lightGray} : {color: holidayColors.gray};
        const customStyles = {
            inputFieldStyle: [styles.inputFieldStyle],
            labelStyle:[styles.label, labelColorStyle],
        };
        return <>
            {item?.name === FIELD_FIRSTNAME && showPassportCard ? (    
                <View style={styles.demoCard}>
                    <View style={[AtomicCss.flexWrap, AtomicCss.flexRow, AtomicCss.paddingBottom8]}>
                        <Text style={[styles.cardDesc]}><Text style={styles.boldText}>Enter name</Text> exactly as mentioned in the passport</Text>
                    </View>
                    <Image source={{uri: imgPassport}} style={styles.picPassport} />
                    <View><Image source={iconPassportArrow} style={styles.iconPassportArrow} /></View>
                </View>
            ) : null}
            <View
                onLayout={event => layoutMap[fieldName] = event.nativeEvent.layout}
                style={[styles.formLeft, styles.inputContainer]}>
                <FloatingInput
                    customStyle={customStyles}
                    errorMessage="This field is required"
                    isError={errors[fieldName]}
                    label={item?.label?.toUpperCase()}
                    requiredText={item?.mandatory && <Text style={styles.mandatory}> *</Text>}
                    placeholder={item?.placeholder}
                    labelAnimationTopValue={12}
                    onBlur={() => item?.name === FIELD_FIRSTNAME ? togglePassportCard(false) : undefined}
                    onChangeText={(value) => {
                        handleLabelChange(item?.name, value); 
                    }}
                    onFocus={() => item?.name === FIELD_FIRSTNAME ? togglePassportCard(true) : undefined}
                    value={getInputValue(values, item?.name)}
                />
            </View>
        </>;
    };

    const getFieldDate = (item,widthPercentage,layoutMap) => {
        let name = item?.name;
        let valueRange = item?.valueRange;
        let dateProps = {};
          if (valueRange?.start) {
            dateProps.minDate = getDate(valueRange.start);
            dateProps.minDate.setDate(dateProps.minDate.getDate() + 1);
            dateProps.minYear = dateProps.minDate?.getFullYear();
          }
          if (valueRange?.end) {
            dateProps.maxDate = getDate(valueRange.end);
            dateProps.maxDate.setDate(dateProps.maxDate.getDate() - 1);
            dateProps.maxYear = dateProps.maxDate?.getFullYear();
          }
          const labelColorStyle = filledField.includes(item?.name) ? {color: holidayColors.lightGray} : {color: holidayColors.gray};
          return <View
              style={[
                styles.formLeft, styles.inputContainer,
                widthPercentage && { width: `${widthPercentage - 5}%` },
              ]}
              onLayout={event => layoutMap[name] = event.nativeEvent.layout}
            >
              <DatePickerComponent            
                label={item?.label?.toUpperCase()} 
                customLabelStyle={[styles.label, labelColorStyle]}
                selectedDate={getInputValue(values, name)}  
                handleChange={e => {
                    handleLabelChange(item?.name, e);  
                  }}
                errorState={!!errors[name]}
                errorText={errors[name]} 
                isMandatory={item?.mandatory}              
                {...dateProps} 
              />
            </View>;
    };

    const getFieldSelect = (item, widthPercentage, layoutMap, onToggleField, currentFormIndex) => {
        const itemName = item?.name;
        let selectOptions = [{label: item?.placeholder ? item.placeholder : null,value: ''}];
        if (options && options[item?.optionIdentifier]) {selectOptions.push(...options[item?.optionIdentifier]);}
        let value = getInputValue(values, itemName);
        let selectedOption = selectOptions.filter(option => option.value === value);
        let selectedLabel = (selectedOption && selectedOption.length > 0) ? selectedOption[0].label : value;
        const labelColorStyle = filledField.includes(item?.name) ? {color: holidayColors.lightGray} : {color: holidayColors.gray};

        return  <View
        style={[
          styles.formLeft,
          widthPercentage && { width: `${widthPercentage - 5}%` },
        ]}
        onLayout={event => layoutMap[itemName] = event.nativeEvent.layout}
      >
  
      <FormSelectElement
        selectedLabel={selectedLabel ? selectedLabel : ''}
        currentFormIndex={currentFormIndex}
        value={value ? value : ''}
        options={selectOptions}
        errorState={!!errors[itemName]}
        errorText={errors[itemName]}
        toggleDropDown={(isOpen) => {
            onToggleField(isOpen, itemName);
        }}
        openDropDownName={itemName}
        dropDownName={itemName}
        handleChange={val => {
            handleLabelChange(itemName, val);  
          }}
        label={item?.label?.toUpperCase()}
        customLabelStyle={[styles.label, labelColorStyle]}
        isMandatory={item?.mandatory}
      />
      </View>;
    };

    const getRadioInput = (item,widthPercentage,layoutMap) => {
        const itemName = item?.name;
        let optionList = options[itemName];
        let value = getInputValue(values, itemName);
        return  <View
        style={[
          styles.formLeft,
        ]}
        onLayout={event => layoutMap[itemName] = event.nativeEvent.layout}
        >
        <RadioOptions
        value={value ? value : ''}
        options={optionList}
        errorState={!!errors[itemName]}
        errorText={errors[itemName]}
        handleChange={val => handleChange({name: itemName, value:[val]})}
        label={item?.label}
        widthPercentage={widthPercentage}
        isMandatory={item?.mandatory}
      />
      </View>;
    };

    const getCheckboxInput = (item,widthPercentage,layoutMap) => {
        const itemName = item?.name;
        let optionList = options[itemName];
        let values = getMultiSelectValues(itemName);
        return  <View
        style={[
          styles.formLeft,
        ]}
        onLayout={event => layoutMap[itemName] = event.nativeEvent.layout}
        >
        <InputCheckboxGroup
        values={values ? values : []}
        options={optionList}
        errorState={!!errors[itemName]}
        errorText={errors[itemName]}
        handleChange={val => handleChange({name: itemName, value: val})}
        label={item?.label}
        widthPercentage={widthPercentage}
        isMandatory={item?.mandatory}
      />
      </View>;
    };
    return (
        <View style={[styles.formWrapper, (isIosClient() ? {paddingBottom: 180} : {})]}>
            <View>
                {
                    groupedFields && groupedFields.length > 0 && groupedFields.map((section, index1) =>
                        <View key={section?.title} style={[{position: 'relative',zIndex: 100 - index1},AtomicCss.flexRow, AtomicCss.flexWrap, AtomicCss.alignCenter]}>
                            {
                                section && section.length > 0 && section.map((fieldElement, index2) => {
                                    let fieldComponent = renderField(fieldElement, layoutMap, onToggleField, currentFormIndex);
                                    return <View style={{width: "100%",position: 'relative',zIndex: 100 - index2}}>{fieldComponent}</View>;
                                })
                            }
                        </View>
                    )
                }

                {
                    innerSections && innerSections.length > 0 && innerSections.map(section => {
                        let groupedInnerFields = groupFieldsByRow(section?.fields);
                        return <>
                            <View style={AtomicCss.marginTop5}>
                                <View style={AtomicCss.marginBottom10}><Text style={[styles.titleHead]}>{section?.title}</Text></View>
                                <Text style={[styles.descText, styles.lineHeight18, AtomicCss.marginBottom10]}>{section?.description}</Text>
                            </View>
                            {
                                groupedInnerFields && groupedInnerFields.length > 0 && groupedInnerFields.map(sectionField =>

                                    <View key={section?.title} style={[AtomicCss.flexRow, AtomicCss.flexWrap]}>
                                        {
                                            sectionField && sectionField.length > 0 && sectionField.map(fieldElement => {
                                                let fieldComponent = renderField(fieldElement, layoutMap, onToggleField, currentFormIndex);
                                                return fieldComponent;
                                            }
                                            )
                                        }
                                    </View>
                                )
                            }
                        </>;
                    })
                }
            </View>
        </View>
    );
};

const styles = StyleSheet.create({
    wrapper: {
        flex: 1,
        backgroundColor: holidayColors.white,
    },
    inputFieldStyle: {
    ...fontStyles.labelMediumBlack,
    ...paddingStyles.pt20
    },
    addTravellerFooterBtn: {
        backgroundColor: holidayColors.primaryBlue,
        width: '100%',
        ...paddingStyles.pa20,
        alignItems: 'center',
        justifyContent: 'center',
    },
    cardDesc:{
        ...fontStyles.labelBaseRegular,
        color:holidayColors.white,
    },
    titleHead:{
        ...fontStyles.headingBase,
    },
    descText: {
        ...fontStyles.labelBaseRegular,
        color:holidayColors.gray,
    },
    btnTxt: {
        color: holidayColors.white,
        ...fontStyles.labelBaseBlack,
    },
    scanWrapper: {
        backgroundColor: holidayColors.fadedGreen,
        flexDirection: 'row',
        alignItems: 'center',
        borderBottomWidth: 1,
        borderColor: holidayColors.green,
        ...paddingStyles.pa20,

    },
    scanContent: {
        width: '80%',
        flexDirection: 'row',
        alignItems: 'center',
    },
    scanImgHolder: {
        width: 40,
        height: 40,
        ...holidayBorderRadius.borderRadius4,
        overflow: 'hidden',
        backgroundColor: holidayColors.lightGray,
        ...marginStyles.mr10
    },
    formDropdownList: {
        borderWidth: 1,
        backgroundColor: holidayColors.white,
        borderColor: holidayColors.primaryBlue,
        ...holidayBorderRadius.borderRadius4,
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-between',
        shadowColor: holidayColors.black,
        shadowOffset: {
            width: 0,
            height: 1,
        },
        shadowOpacity: 0.22,
        shadowRadius: 2.22,
        elevation: 3,
        width: 190,
        ...paddingStyles.pa10,
    },
    travellerFormHdr: {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-between',
        ...paddingStyles.pa16,
        borderBottomWidth: 1,
        borderColor: holidayColors.grayBorder,
    },
    selectFormListWrapper: {
        flexDirection: 'row',
        alignItems: 'center',
        ...paddingStyles.pv10,
        ...paddingStyles.ph16,
        borderBottomWidth: 1,
        borderColor: holidayColors.grayBorder,
    },
    formWrapper: {
        ...paddingStyles.pa16,
        ...paddingStyles.pr0,
    },
    lineHeight18: {
        lineHeight: 18,
    },
    formLeft: {
        marginRight: '5%',
        ...marginStyles.mt6,
        ...marginStyles.mb10
    },
    formRight: {
        marginLeft: '5%',
    },
    iconArrowDown: {
        width: 12,
        height: 8,
        resizeMode: 'contain',
    },
    demoCard: {
        backgroundColor: holidayColors.gray,
        ...holidayBorderRadius.borderRadius4,
        ...paddingStyles.pa16,
        width: 300,
        position: 'absolute',
        zIndex: 5,
        flexWrap: 'wrap',
        top: -150,
    },
    iconPassportArrow: {
        width: 32,
        height: 10,
        position: 'absolute',
        bottom: -24,
    },
    picPassport: {
        width: 270,
        height: 66,
        resizeMode: 'cover',
    },
    roomCategory: {
        flexDirection: 'row',
        alignItems: 'center',
    },
    roomGuest: {
        alignItems: 'center',
        justifyContent: 'center',
        width: 80,
        height: 80,
        borderWidth: 1,
        borderColor: holidayColors.primaryBlue,
        borderStyle: 'dashed',
        ...holidayBorderRadius.borderRadius4,
        ...marginStyles.mr10
    },
    iconGuest: {
        width: 18,
        height: 18,
        resizeMode: 'contain',
    },
    selectedRoomGuest: {
        backgroundColor: holidayColors.lightBlueBg,
        borderStyle: 'solid',
    },
    roomName: {
        width: 22,
        height: 80,
        backgroundColor: holidayColors.black,
        borderTopLeftRadius: borderRadiusValues.br4,
        borderBottomLeftRadius: borderRadiusValues.br4,
        flexDirection: 'row',
        flexWrap: 'nowrap',
        position: 'relative',
    },
    roomNameText: {
        color: holidayColors.white,
        ...fontStyles.labelSmallBold,
        transform: [{ rotate: '-90deg' }],
    },
    roomNameTxtWrap: {
        position: 'absolute',
        left: -11,
        top: 32,
    },
    roomSelectionScroller: {
        ...paddingStyles.pl16,
        ...paddingStyles.pv10
    },
    roomGuestCanel: {
        position: 'absolute',
        right: 6,
        top: 6,
    },
    iconCloseBlue: {
        width: 8,
        height: 8,
    },
    mealPreferenceWrap: {
        flexDirection: 'row',
        alignItems: 'center',
        flexWrap: 'wrap',
    },
    accordianWrap: {
        borderTopWidth: 5,
        borderColor: holidayColors.lightGray2,
    },
      boldText: {
        ...fontStyles.labelBaseBlack,
      },
    label: {
        ...Platform.select({
            ios: {
                ...fontStyles.labelSmallBold,
            },
            android: {
                ...fontStyles.labelBaseBold,
            },
        }),
    },
    mandatory: {
        color: holidayColors.red,
    },
});

export default AddTravellerForm;
