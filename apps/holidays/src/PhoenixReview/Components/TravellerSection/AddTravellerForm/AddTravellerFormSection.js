import React from 'react';
import {
  StyleSheet,
  View,
  TouchableOpacity,
  Image,
  Text,
} from 'react-native';
import AtomicCss from '@mmt/legacy-commons/Styles/AtomicCss';
import { fonts, colors } from '@mmt/legacy-commons/Styles/globalStyles';
import iconArrowDown from '../../../images/ic_arrowDownBlue.webp';
import AddTravellerForm from './';
import { getDate } from '../../../Utils/HolidayReviewUtils';
import { FORM_FILL_STATUS } from '../../../Utils/HolidayReviewConstants';
import { holidayColors } from '../../../../Styles/holidayColors';
import { borderRadiusValues } from '../../../../Styles/holidayBorderRadius';
import { fontStyles } from '../../../../Styles/holidayFonts';
export const validateForm = (formData, setErrors, fieldSectionWise, fieldObj, layoutMap, scrollTo) => {
    const error = {};
    let isError = false;
    let isCommonError = false;
    const fieldList = Object.keys(fieldSectionWise);
    fieldList.map(fieldStr => {
      isError = false;
      let field = fieldObj[fieldStr];
      if (!field?.inputType) {
        return null;
      }
      const fieldName = field?.name;
      if (field?.mandatory && (!formData[fieldName] || formData[fieldName].length === 0 || formData[fieldName][0].length === 0)) {
        error[fieldName] = `${field?.label} is required`;
        isError = true;
        isCommonError = true;
      }
      if (!isError && formData[fieldName] && field?.regex?.length > 0) {
        try {
          field.regex.forEach(regexStr => {
            const regexObj = eval(`/${regexStr}/`);
            if (!regexObj.test(formData[fieldName])) {
              error[fieldName] = `${field?.label} is invalid`;
              isError = true;
              isCommonError = true;
            }
          });
        } catch (e) {
        }
      }
      if (!isError && formData[fieldName] && field.valueRange) {
        const {start, end} = field.valueRange;
        if (field.inputType === 'DATE') {
          const startDate = getDate(start);
          const endDate = getDate(end);
          const currDate = getDate(formData[fieldName].join(''));
          if (currDate.getTime() > endDate.getTime() || currDate.getTime() < startDate.getTime()) {
            error[fieldName] = `Invalid ${field?.label} range`;
            isError = true;
            isCommonError = true;
          }
        } else {
          if (formData[fieldName] > end && formData[fieldName] < start) {
            error[fieldName] = `Invalid ${field?.label} range`;
            isError = true;
            isCommonError = true;
          }
        }
      }
    });
    if (isCommonError) {
      const keys = Object.keys(error);
      const gotoField = keys?.length > 0 ? keys[0] : null;
      if (layoutMap[gotoField]?.y || layoutMap[gotoField]?.y === 0) {
        scrollTo({y:70 * (fieldList.findIndex(field => field === gotoField) + 1), animated: true});
      }
      setErrors(error);
      return false;
    } else {
      return formData;
    }
  };

const AddTravellerFormSection = (props) => {
  const { currentTravellerList, currentFormIndex, fillCurrentFormIndex, options, dynamicFormList, errors, setErrors, showAccordian, handleAccordian, layoutMap, onToggleField } = props;

  const values = currentTravellerList[currentFormIndex]?.fieldValues;

  const addTravellerFormData = dynamicFormList[currentFormIndex];

  const handleChange = ({ name, value }) => {
    fillCurrentFormIndex({status: FORM_FILL_STATUS.INCOMPLETE, fields: {[name]: value}});
    if (errors[name]) {
      setErrors(prevState => {
        let error = {...prevState};
        delete error[name];
        return {
          ...error,
        };
      });
    }
  };
  const isSingleSection = addTravellerFormData?.sections?.length === 1;
  return (<>
        {addTravellerFormData?.sections ? addTravellerFormData.sections.map((item, i) =>
          <View key={item?.sectionId} style={styles.accordianWrap}>
            {/* header */}
            <TouchableOpacity disabled={isSingleSection} onPress={() => handleAccordian(i)}>
              <View style={styles.travellerFormHdr} key={i}>
                <View style={[AtomicCss.flexRow, AtomicCss.alignCenter]}>
                  <Text style={[styles.titleHead]}>
                    {!isSingleSection ? `${i + 1}.` : ''} {item?.title}
                  </Text>
                </View>
                {!isSingleSection ?
                  <Image source={iconArrowDown} style={[styles.iconArrowDown, showAccordian.includes(i) ? { transform: [{ rotate: '180deg' }] } : '']} />
                : []}
              </View>
            </TouchableOpacity>
            {isSingleSection || showAccordian.includes(i) ?
              (<AddTravellerForm
                values={values}
                errors={errors}
                handleChange={handleChange}
                fields={addTravellerFormData?.fields}
                sectionFields={item?.fields}
                innerSections={item?.sections}
                description={item?.description}
                options={options}
                layoutMap={layoutMap}
                onToggleField={onToggleField}
                currentFormIndex={currentFormIndex}
                />)
              : null}
          </View>
        ) : []}
      </>
  );
};

const styles = StyleSheet.create({
  wrapper: {
    paddingTop: 20,
    height: '100%',
  },
  addTravellerFooterBtn: {
    backgroundColor: holidayColors.primaryBlue,
    width: '100%',
    padding: 20,
    alignItems: 'center',
    justifyContent: 'center',
  },
  btnTxt: {
    color: holidayColors.white,
    ...fontStyles.labelBaseBlack,
  },
  titleHead: {
    ...fontStyles.labelLargeBlack,
    color:holidayColors.black,
  },
  scanWrapper: {
    backgroundColor: holidayColors.fadedGreen,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    borderBottomWidth: 1,
    borderColor: holidayColors.green,
    paddingVertical: 10,
    paddingHorizontal: 20,
  },
  travellerFormHdr: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: 16,
    borderBottomWidth: 1,
    borderColor: holidayColors.grayBorder,
  },
  footerWrapper:{
    position:'absolute',
    bottom:0,
    paddingLeft: 16,
    height:100,
    width:'100%',
    },
  roomCategory: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  iconGuest: {
    width: 18,
    height: 18,
    resizeMode: 'contain',
  },
  iconArrowDown: {
    width: 12,
    height: 8,
    resizeMode: 'contain',
  },
  accordianWrap: {
    borderTopWidth: borderRadiusValues.br8,
    borderColor: holidayColors.grayBorder,
  },
});

export default AddTravellerFormSection;
