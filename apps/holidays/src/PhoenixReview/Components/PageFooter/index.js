import React, { useEffect, useState } from 'react';
import {
    StyleSheet,
    ScrollView,
    View,
    TouchableOpacity,
    Image,
    Text,
    Animated,
} from 'react-native';
import AtomicCss from '@mmt/legacy-commons/Styles/AtomicCss';

import iconArrowUp from '../../images/ic_arrow_up_white.webp';
import BottomSheetOverlay from '../../../Common/Components/BottomSheetOverlay';
import ReviewFareBreakup from '../FareBreakup';
import { rupeeFormatter } from '@mmt/legacy-commons/Helpers/currencyUtils';
import { rupeeFormatterUtils } from '../../../utils/HolidayUtils';
import BookingOption from '../BookingOption';
import { PART_PAYMENT, REVIEW_FOOTER_BTN_TEXT, REVIEW_FOOTER_COMPONENTS } from '../../Utils/HolidayReviewConstants';
import { PDTConstants } from '../../Utils/HolidayReviewConstants';
import { getPackagePrice } from '../../../Review/Utils/HolidayReviewUtils';
import { colors } from '@mmt/legacy-commons/Styles/globalStyles';
import OvernightInfoPopup from '../OvernightInfoPopup';
import { cloneDeep } from 'lodash';
import { sectionCodes } from '../../../LandingNew/LandingConstants';
import { fontStyles } from '../../../Styles/holidayFonts';
import { holidayColors } from '../../../Styles/holidayColors';
import PrimaryButton from '../../../Common/Components/Buttons/PrimaryButton';
import { paddingStyles, marginStyles } from '../../../Styles/Spacing/index';
import { borderRadiusValues, holidayBorderRadius } from '../../../Styles/holidayBorderRadius';
import { REVIEW_PAGE_NAME } from "../../../Review/HolidayReviewConstants";
import { showInsuranceSection } from 'mobile-holidays-react-native/src/utils/HolidaysPokusUtils';
import BottomBar from '@Frontend_Ui_Lib_App/BottomBar';
import { logHolidayReviewPDTClickEvents } from '../../Utils/HolidayReviewPDTTrackingUtils';
import { PDT_EVENT_TYPES } from 'mobile-holidays-react-native/src/utils/HolidayPDTConstants';
import { sendMMTGtmEvent } from 'mobile-holidays-react-native/src/utils/ThirdPartyUtils';

const footerComponents = {
    fareBreakUp: 'fareBreakup',
    bookingOption: 'bookingOption',
    OvernightInfoPopup: 'OvernightInfoPopup',
};


const ReviewFooter = (props) => {
    const [comp, setComp] = useState('');
    const [showPopup, setShowPopup] = useState(false);
    const {
        paymentScheduleOptions,
        btnTxt,
        reviewData,
        dealDetail,
        validateCoupon,
        couponData,
        timerStarted,
        optionSelected,
        disableBookingButton,
        countdownEndTime,
        listData,
        gotoSection,
        validateZC,
        trackReviewLocalClickEvent,
        selectPaymentOption,
        showOvernightPopup = false,
        goToPaymentPage = () => {},
        setShowOvernightPopupState = () => {},
        callbackHandler = () => {},
        updateReviewFooter = () => {},
        fromAmendment = false,
        holidayReviewData = {},
        pageName,
        insuranceAddonDetail = {},
        fareBreakUpCardData={}
    } = props;

    useEffect(() => {
        if (!props.reviewPopUp) {
            return;
        }
        if (pageName === REVIEW_PAGE_NAME) {
            if (btnTxt === REVIEW_FOOTER_BTN_TEXT.CONTINUE) {
                props.startPayment()
            } else {
                handlePopupOpen(REVIEW_FOOTER_COMPONENTS.bookingOption)
            }
        }
    }, [props.reviewPopUp])

    useEffect(() => {
        if (showOvernightPopup) {
            logHolidayReviewPDTClickEvents({
                actionType: PDT_EVENT_TYPES.contentSeen,
                value: 'OvernightFlight_popup_seen',
                shouldTrackToAdobe:false
            })
            setComp(footerComponents.OvernightInfoPopup);
            setShowPopup(true);
        }
    },[showOvernightPopup]);

    const captureClickEvents = ({ eventName = '', suffix = '' }) => {
        logHolidayReviewPDTClickEvents({
            actionType: PDT_EVENT_TYPES.buttonClicked,
            value: eventName + suffix,
        })
        props.trackReviewLocalClickEvent(eventName,suffix);

    };
    const handlePopupOpen = (compName) => {
        setShowPopup(true);
        props.updateReviewFooter(true);
        setComp(compName);
        if (compName === REVIEW_FOOTER_COMPONENTS.fareBreakUp) {captureClickEvents({ eventName: PDTConstants.FARE_BREAKUP, suffix: PDTConstants.EXPAND});}
        else if (compName === REVIEW_FOOTER_COMPONENTS.bookingOption){
          captureClickEvents({ eventName: PDTConstants.BOOKING_OPTIONS });
        }
    };
    const handlePopupClose = () => {
        if (comp === REVIEW_FOOTER_COMPONENTS.fareBreakUp) {captureClickEvents({ eventName: PDTConstants.FARE_BREAKUP, suffix: PDTConstants.COLLAPSE });}
        if (showOvernightPopup) {setShowOvernightPopupState(false);}
        setShowPopup(false);
        props.updateReviewFooter(false);
    };
    const continueBooking = ({openOvernightComp = false} = {}) =>{
        sendMMTGtmEvent({
            eventName: 'PROCEED_TO_PAYMENT',
            data: {
                pageName: 'holidayReview',
            },
        })
        captureClickEvents({ eventName: PDTConstants.INITIATE_PAYMENT, suffix: PDTConstants.COLLAPSE });
        props.updateReviewFooter(false);
        props.startPayment();
        setShowPopup(false);
    };

    const evaluateInsuranceAddons = () => {
        const hasInsuranceAddons = showInsuranceSection() && insuranceAddonDetail?.addons?.length > 0;
        if (hasInsuranceAddons) {
            props.validateAddOns();
        } else {
            handleDefaultActions();
        }
    };

    const handleDefaultActions = () => {
        if (btnTxt === REVIEW_FOOTER_BTN_TEXT.CONTINUE) {
            captureClickEvents({ eventName: PDTConstants.INITIATE_PAYMENT });
            props.startPayment();
        } else {
            handlePopupOpen(REVIEW_FOOTER_COMPONENTS.bookingOption);
        }
    };

    const onContinueClick = () => {
        if (pageName === REVIEW_PAGE_NAME) {
            evaluateInsuranceAddons();
        } else {
            props.startPayment();
        }
    };

    const openPaymentPage = () => {
        setShowPopup(false);
        goToPaymentPage();
    };
    const pricingDetails = props?.pricingDetail;
    const fareBreakup = pricingDetails?.fareBreakUp ? cloneDeep(pricingDetails?.fareBreakUp) : [];

    if (pricingDetails?.dateChangePenalty && pricingDetails?.dateChangePenalty > 0) {
        fareBreakup.push({ '@id': 12, 'title': 'Date Change Panelty', 'value': pricingDetails?.dateChangePenalty, 'sign': 'NA' });
    }
    if (pricingDetails?.amountPaid && pricingDetails?.amountPaid > 0) {
        fareBreakup.push({ '@id': 13, 'title': 'Amount Already Paid', 'value': pricingDetails?.amountPaid, 'sign': 'SUBTRACT' });
    }
    const paymentSchedules = paymentScheduleOptions?.paymentSchedules;
    const partPayment = paymentSchedules?.length > 0 ? paymentSchedules?.find(e => e.paymentType === PART_PAYMENT) : [];
    const reservePrice = partPayment?.installmentDetails?.find(e => !e.partPaymentDate)?.partPaymentValue;
    let discountedPrice = getPackagePrice(pricingDetails);
    if (fromAmendment && pricingDetails?.amountDue && pricingDetails.amountDue > 0) {
        discountedPrice = pricingDetails.amountDue;
    }

    const TitleComponent = () => {
      return (
        <View>
          {reservePrice ? (
            <Text style={styles.upperHeading}>
              Reserve for{' '}
              <Text style={AtomicCss.blackFont}>₹ {`${rupeeFormatter(reservePrice)}`}*</Text>
            </Text>
          ) : (
            []
          )}
          <View style={styles.netPriceContainer}>
            <Text style={styles.netPrice}>{rupeeFormatterUtils(discountedPrice)}</Text>
            <TouchableOpacity onPress={() => handlePopupOpen(REVIEW_FOOTER_COMPONENTS.fareBreakUp)}>
              <View style={[styles.fareDetailsWrap]}>
                <Text style={styles.fareDetailsText}>Fare Details</Text>
                <View style={AtomicCss.paddingLeft5}>
                  <Image source={iconArrowUp} style={styles.iconArrowUp} />
                </View>
              </View>
            </TouchableOpacity>
          </View>
        </View>
      );
    };

    const RenderDescription = () => {
      return (
        <Text style={styles.grandTotal}>
          Grand Total - {props.totalTravellers}{' '}
          {props.totalTravellers > 1 ? 'Travellers' : 'Traveller'}
        </Text>
      );
    };

    return (
      <>
        <BottomBar
          titleRightComponent={<TitleComponent />}
          description1={<RenderDescription /> }
          customStyles={{
            containerStyle: [styles.bottomBarContainer],
            description1Style: [styles.grandTotal],
          }}
          rightComponent={
            <PrimaryButton
              isDisable={disableBookingButton}
              buttonText={btnTxt?.toUpperCase()}
              handleClick={onContinueClick}
              btnContainerStyles={styles.footerButton}
            />
          }
        />

{showPopup &&
            <BottomSheetOverlay
                visible={showPopup}
                toggleModal={handlePopupClose}
                showCross={false}
                containerStyles={styles.containerStyles}
            >
                {(comp === REVIEW_FOOTER_COMPONENTS.fareBreakUp) &&
                    <ReviewFareBreakup
                        closeModal={() => handlePopupClose()}
                        fareBreakUp={fareBreakup}
                        listData={listData}
                        gotoSection={gotoSection}
                        validateZC={validateZC}
                        emiDetail={reviewData?.reviewDetail?.emiDetail}
                        holidayReviewData={holidayReviewData}
                        fareBreakUpCardData={fareBreakUpCardData}
                        trackReviewLocalClickEvent={trackReviewLocalClickEvent}
                    />
                }
                {comp === footerComponents.OvernightInfoPopup && (
                    <OvernightInfoPopup
                        closeModal={() => handlePopupClose()}
                        continueBooking={openPaymentPage}
                        reviewData={reviewData}
                    />
                )}
                {(comp === REVIEW_FOOTER_COMPONENTS.bookingOption) &&
                    <BookingOption
                        closeModal={() => handlePopupClose()}
                        gotoSection={gotoSection}
                        paymentScheduleOptions={paymentScheduleOptions}
                        dealDetail={dealDetail}
                        validateCoupon={validateCoupon}
                        couponData={couponData}
                        timerStarted={timerStarted}
                        reviewData={reviewData}
                        continueBooking={continueBooking}
                        countdownEndTime={countdownEndTime}
                        trackReviewLocalClickEvent={trackReviewLocalClickEvent}
                        pricingDetail={props?.pricingDetail}
                        listData={listData}
                        validateZC={validateZC}
                        selectPaymentOption={selectPaymentOption}
                        callbackHandler={callbackHandler}
                        updateReviewFooter={updateReviewFooter}
                        fromAmendment={fromAmendment}
                        fromPresalesDetail={holidayReviewData.reviewType === sectionCodes.PRESALES}
                    />
                }
            </BottomSheetOverlay>
      }
        </>
    );
};

const styles = StyleSheet.create({
    bottomBarContainer: {
        backgroundColor: holidayColors.black,
        paddingVertical: 10,
      },
    footerButton: {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'center',
        ...paddingStyles.ph16,
    },
    upperHeading: {
        ...fontStyles.labelSmallRegular,
        color: holidayColors.white,
        opacity: 0.7,
    },
    netPriceContainer: {
        ...AtomicCss.flexRow,
        ...AtomicCss.alignCenter,
        ...AtomicCss.flexWrap,
        ...paddingStyles.pt4,
        ...paddingStyles.pb2,
    },
    netPrice: {
        ...fontStyles.headingBase,
        color: holidayColors.white,
    },
    fareDetailsWrap: {
        borderWidth: 1,
        ...paddingStyles.pv2,
        ...paddingStyles.ph8,
        flexDirection: 'row',
        alignItems: 'center',
        borderColor: holidayColors.lightGray2,
        ...holidayBorderRadius.borderRadius8,
        ...marginStyles.ml16,
    },
    fareDetailsText: {
        color: holidayColors.white,
        ...fontStyles.labelSmallRegular,
    },
    grandTotal: {
        ...fontStyles.labelSmallRegular,
        color: 'rgba(203, 203, 203, 0.7)',
   },
    iconArrowUp: {
        width: 8.5,
        height: 5,
        resizeMode: 'cover',
    },
    grossPrice: {
        ...fontStyles.labelSmallRegular,
        color: 'rgba(256,256,256,0.7)',
        textDecorationLine: 'line-through',
        ...marginStyles.ml4,
    },
    containerStyles: {
        paddingBottom: 0,
    }
});

export default ReviewFooter;
