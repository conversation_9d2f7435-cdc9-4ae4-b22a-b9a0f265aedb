import React, { useEffect, useRef, useState, useMemo } from 'react';
import {
    StyleSheet,
    View,
    TouchableOpacity,
    Image,
    Text,
    Dimensions,
    ScrollView,
} from 'react-native';
import { NO_COST_EMI } from '../../../HolidayConstants';
import AtomicCss from '@mmt/legacy-commons/Styles/AtomicCss';
import { rupeeFormatterUtils } from '../../../utils/HolidayUtils';
import { fonts } from '@mmt/legacy-commons/Styles/globalStyles';
import { fontStyles } from 'mobile-holidays-react-native/src/Styles/holidayFonts';
import { holidayColors } from 'mobile-holidays-react-native/src/Styles/holidayColors';
import { paddingStyles } from 'mobile-holidays-react-native/src/Styles/Spacing';
const windowWidth = Dimensions.get('window').width;
const planCategory = ['Plan','EMI','Annual Interest', 'Interest Payable','Total Cost'];
const BANK_ROW_HEIGHT = 60;

const EMIOption = (props) => {
    const { closeModal, emiOptions, getEmiOptions, fromDetails, defaultBankName } = props || {};
    const {bankDetails} = emiOptions || {};
    const createBankDetailEmi = (bankDetails, selectedBank) => {
        let banksList = [];
        let defaultSelectedBank = [];
        bankDetails?.forEach((bankDetail) => {
            const noCostEmiVal = bankDetail?.emiDetails?.filter((val) => (val.interestRate === '0' || val.emiType === NO_COST_EMI))?.length > 0 || false;
            if (bankDetail.name === selectedBank) {
                defaultSelectedBank.push({ ...bankDetail, isNoCostEmi: noCostEmiVal });
            } else {
                banksList.push({ ...bankDetail, isNoCostEmi: noCostEmiVal });            }
        });
        return [...defaultSelectedBank, ...banksList];
    };

    const bankDetailEmi = useMemo(() => createBankDetailEmi(bankDetails, defaultBankName), [bankDetails?.[0]?.emiDetails]);

    const [selectPlan, setSelectedPlan] = useState(-1); //active plan
    const [showSelectedPlan, setShowSelectedPlan] = useState(-1); //selected plan

    const [showPlan, setShowPlan] = useState([]);

    const [selectedBankData, setSelectedBankData] = useState(0); //selected bank
    const [activeData, setActiveData] = useState(0);  //active bank
    const scrollViewRef = useRef();
    useEffect(()=>{
        setShowPlan(bankDetailEmi?.[0]?.emiDetails);
    }, [bankDetailEmi?.[0]?.emiDetails]);
    useEffect(()=>{
        !fromDetails && getEmiOptions();
    },[]);
    const handleselectPlan = (index) => {
        setSelectedPlan(index);
        setSelectedBankData(activeData);
        setShowSelectedPlan(index);
    };


    const handleDataPlan = (index) => {
        setActiveData(index);
        setShowPlan(bankDetailEmi?.[index]?.emiDetails);
        setSelectedPlan(-1);
        scrollViewRef.current.scrollTo({x:0,y:0});
    };
    const confirm = ()=>{
        closeModal();
    };

    return (
        <View>
            <ScrollView>
                <View style={styles.dataContent}>
                {fromDetails && (
                    <Text style={styles.subHeading}>
                    The EMI amount shown below is per person only. The final EMI amount will depend on
                    number of travellers
                    </Text>
                )}
                    <View style={styles.scrollWrap}>
                        <ScrollView horizontal showsHorizontalScrollIndicator={false}>
                            {bankDetailEmi?.map((item, index) => {
                                return <View>
                                    {item.isNoCostEmi && (
                                      <View style={styles.noCostView}>
                                        <Text style={styles.noCostEmiText}>No Cost EMI</Text>
                                      </View>
                                    )}
                                    <TouchableOpacity style={[styles.bankItemList, activeData === index ? styles.bankItemListActive : '']} key={index} onPress={() => handleDataPlan(index)}>
                                        <Image source={{uri:item.logoUrl}} style={styles.bankIcon} />
                                        <View style={{width: '100%' }}><Text numberOfLines={1} style={styles.bankItemName}>{item.name}</Text></View>
                                    </TouchableOpacity>
                                </View>;
                            }
                            )}
                        </ScrollView>
                    </View>
                    <View style={[AtomicCss.flexRow]}>
                    <View>
                        {planCategory?.map((item, index) => (
                            <View style={styles.bankItemBox}>
                            <Text style={styles.bankItemBoxName}>{item}</Text>
                            </View>
                        ))}
                        </View>
                        <View style={[AtomicCss.flexRow,{width:(windowWidth - 30) * 3 / 4}]}>
                            <ScrollView horizontal showsHorizontalScrollIndicator={false} ref={scrollViewRef}>
                                {showPlan && showPlan?.map((list,i) => {

                                    const checkSelected = showSelectedPlan === i  && selectedBankData === activeData;
                                    const emiAmount = list.emiAmount ? parseFloat(list.emiAmount) : 0;
                                    const totalInterestPayable = list.totalInterestPayable ? parseFloat(list.totalInterestPayable) : 0;
                                    const totalCost = list.totalCost ? parseFloat(list.totalCost) : 0;
                                    return <View style={{width: (windowWidth - 30) / 4}} key={i}>
                                        <TouchableOpacity>
                                          <View
                                            style={checkSelected ? styles.selectPlan : styles.plan}
                                          >
                                            <View style={styles.bankContentBox}>
                                              <Text style={styles.bankItemBoxHeading}>
                                                {list.tenure} Months
                                              </Text>
                                            </View>
                                            <View
                                              style={[
                                                styles.bankContentBox,
                                                checkSelected
                                                  ? styles.alternateRowActive
                                                  : styles.alternateRow,
                                              ]}
                                            >
                                              <Text style={styles.amountText}>
                                                {rupeeFormatterUtils(emiAmount)}{' '}
                                              </Text>
                                            </View>
                                            <View style={styles.bankContentBox}>
                                              <Text style={styles.amountText}>
                                                {parseFloat(list.interestRate)}%
                                              </Text>
                                            </View>
                                            <View
                                              style={[
                                                styles.bankContentBox,
                                                checkSelected
                                                  ? styles.alternateRowActive
                                                  : styles.alternateRow,
                                              ]}
                                            >
                                              <Text style={styles.amountText}>
                                                {rupeeFormatterUtils(totalInterestPayable)}{' '}
                                              </Text>
                                            </View>
                                            <View style={styles.bankContentBox}>
                                              <Text style={styles.amountText}>
                                                {rupeeFormatterUtils(totalCost)}
                                              </Text>
                                            </View>
                                          </View>
                                        </TouchableOpacity>
                                    </View>;
                                }
                                )}
                            </ScrollView>
                        </View>



                    </View>
                </View>
            </ScrollView>
        </View>
    );
};

const styles = StyleSheet.create({
    subHeading: {
        ...fontStyles.labelBaseRegular,
        color: holidayColors.gray,
    },
    scrollWrap: {
        ...paddingStyles.pt16,
    },
    bankItemList: {
        width: 64,
        height: 64,
        borderWidth: 1,
        borderRadius: 2,
        borderColor: holidayColors.grayBorder,
        marginRight: 10,
        alignItems: 'center',
        justifyContent: 'center',
        paddingHorizontal: 5,
        position: 'relative',
        top: 0,
        marginTop: 5,
        zIndex: -1,
    },
    noCostView: {
        borderRadius: 14,
        width: 60,
        alignItems: 'center',
        justifyContent: 'center',
        marginLeft: 2,
        position: 'absolute',
        bottom: 58,
        backgroundColor: holidayColors.primaryBlue,
    },
    noCostEmiText: {
        width: 54,
        fontFamily: fonts.bold,
        fontSize: 9,
        textAlign: 'center',
        color: holidayColors.white,
    },
    bankItemName: {
        ...fontStyles.labelSmallBold,
        color: holidayColors.black,
    },
    bankItemListActive: {
        borderWidth: 2,
        borderColor: holidayColors.primaryBlue,
    },
    bankIcon: {
        width: 24,
        height: 24,
        resizeMode: 'cover',
        backgroundColor: holidayColors.lightGray2,
        marginBottom: 5,
    },
    bankItemBox: {
        height: BANK_ROW_HEIGHT,
        width: (windowWidth - 30) / 4,
        alignItems: 'center',
        justifyContent: 'center',
        paddingHorizontal: 10,
        flexWrap: 'wrap',
    },
    bankItemBoxName: {
        ...fontStyles.labelSmallBold,
        color: holidayColors.gray,
    },
    bankItemBoxHeading: {
        ...fontStyles.labelSmallBold,
        color: holidayColors.black,
    },
    bankContentBox: {
        height: BANK_ROW_HEIGHT,
        alignItems: 'center',
        justifyContent: 'center',
        paddingHorizontal: 10,
    },
    alternateRow: {
        backgroundColor: holidayColors.lightGray2,
    },
    alternateRowActive: {
        backgroundColor: '#F4FAFF',
    },
    selectPlan: {
        borderWidth: 2,
        borderColor: '#008CFF',
        borderTopLeftRadius: 15,
        borderTopRightRadius: 15,
        backgroundColor: '#F4FAFF',
        shadowColor: '#000',
        shadowOffset: {
            width: 0,
            height: 1,
        },
        flex:1,
        shadowOpacity: 0.22,
        shadowRadius: 2.22,

        elevation: 3,
    },
    plan:{
        width: (windowWidth) / 4,
        borderWidth: 2,
        borderColor: 'transparent',

    },
    dataContent: {
        paddingBottom: 30,
    },
    iconTick: {
        width: 13,
        height: 13,
        marginRight: 5,
    },
    amountText:{
        ...fontStyles.labelBaseBlack,
        color: holidayColors.black,
    },
    amountBlackText: {
        ...fontStyles.labelBaseBlack,
        color: holidayColors.black,
    },

});

export default EMIOption;
