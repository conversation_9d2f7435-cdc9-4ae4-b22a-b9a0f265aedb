import React from 'react';
import { StyleSheet, View, TouchableOpacity, Image, Text } from 'react-native';

import AtomicCss from '@mmt/legacy-commons/Styles/AtomicCss';
import iconBalloon from '../../images/ic_balloon.webp';
import { fontStyles } from '../../../Styles/holidayFonts';
import { holidayColors } from '../../../Styles/holidayColors';
import { holidayBorderRadius } from '../../../Styles/holidayBorderRadius';

const PopupContent = ({ closeModal, closePopup, heading, desc, btnText, subBtnText }) => {
  return (
    <View style={styles.popupContainer}>
      <View style={styles.popContent}>
        <View style={[AtomicCss.alignCenter]}>
          <Image source={iconBalloon} style={styles.iconImg} />
        </View>
        <View style={AtomicCss.marginTop15}>
          <Text style={styles.popupHeading}>{heading}</Text>
        </View>
        <View style={AtomicCss.marginTop15}>
          <Text style={styles.popupDesc}>{desc}</Text>
        </View>
      </View>
      <View style={styles.btnWrap}>
        <TouchableOpacity onPress={closeModal} style={styles.popupBtnContainer}>
          <View style={styles.popupBtn}>
            <Text style={styles.btnText}>{btnText?.toUpperCase()}</Text>
          </View>
        </TouchableOpacity>
        <TouchableOpacity onPress={closePopup}>
          <Text style={styles.continueEditing}>{subBtnText?.toUpperCase()}</Text>
        </TouchableOpacity>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  popupContainer: {
    paddingHorizontal: 30,
  },
  popContent: {
    paddingTop: 30,
    paddingBottom: 15,
    alignItems: 'center',
  },
  popupHeading: {
    ...fontStyles.headingMedium,
    color: holidayColors.black,
    textAlign: 'center',
  },
  popupDesc: {
    ...fontStyles.labelBaseRegular,
    color: holidayColors.gray,
    textAlign: 'center',
  },
  btnWrap: {
    width: '100%',
    justifyContent: 'space-between',
    alignItems: 'center',
    borderBottomLeftRadius: 4,
    borderBottomRightRadius: 4,
    flexDirection: 'column',
    justifyContent: 'flex-end',
    paddingBottom: 30,
  },
  popupBtnContainer: {
    width: '100%',
  },
  popupBtn: {
    marginVertical: 15,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.22,
    shadowRadius: 2.22,
    elevation: 3,
    ...holidayBorderRadius.borderRadius4,
    paddingVertical: 15,
    paddingHorizontal: 50,
    backgroundColor: '#008CFF',
  },
  btnText: {
    ...fontStyles.labelMediumBlack,
    color: holidayColors.white,
    textAlign: 'center',
  },
  iconImg: {
    width: 64,
    height: 64,
    resizeMode: 'cover',
  },
  continueEditing: {
    ...fontStyles.labelMediumBlack,
    color: holidayColors.primaryBlue,
  },
});

export default PopupContent;
