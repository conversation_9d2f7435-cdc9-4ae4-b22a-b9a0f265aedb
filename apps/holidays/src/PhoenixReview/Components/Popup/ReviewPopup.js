import React from 'react';
import {Modal, StyleSheet, View, TouchableOpacity, Image, Dimensions } from 'react-native';
import { holidayBorderRadius } from '../../../Styles/holidayBorderRadius';
import iconClose from '../../images/ic_closeGrey.png';

const windowWidth = Dimensions.get('window').width;
const ReviewModal = ({content, modalVisible, closePopup}) => {

  return (
    <Modal
        animationType="slide"
        transparent={true}
        visible={modalVisible}
        onRequestClose={()=>closePopup()}
      >
                <View style={styles.centeredView}>
                    <View style={styles.modalView} onStartShouldSetResponder={() => true}>
                        <TouchableOpacity onPress={closePopup} style={styles.closeWrap}><View ><Image source={iconClose} style={styles.iconClose} /></View></TouchableOpacity>
                        {content}
                    </View>
                </View>
      </Modal>


  );
};

const styles = StyleSheet.create({
  centeredView: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.8)',
  },
  modalView: {
    backgroundColor: 'white',
    ...holidayBorderRadius.borderRadius16,
    width: windowWidth - 40,
    maxWidth: 330,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 4,
    elevation: 5,

  },
  iconClose: {
      width: 20,
      height: 20,
  },
  closeWrap: {
      position: 'absolute',
      right: 0,
      top: 0,
      padding: 15,
      zIndex: 1,
  },
  button: {
    borderRadius: 20,
    padding: 10,
    elevation: 2,
  },
  buttonOpen: {
    backgroundColor: '#F194FF',
  },
  buttonClose: {
    backgroundColor: '#2196F3',
  },
  textStyle: {
    color: 'white',
    fontWeight: 'bold',
    textAlign: 'center',
  },
  modalText: {
    marginBottom: 15,
    textAlign: 'center',
  },
});

export default ReviewModal;
