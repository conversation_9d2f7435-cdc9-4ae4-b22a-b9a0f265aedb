import { DeviceEventEmitter, requireNativeComponent, StyleSheet, View } from 'react-native';
import React, { useEffect } from 'react';
import {
  PAN_TCS_WIDGET_DETAILS,
  PAN_TCS_WIDGET_TRACKING,
  TCS_PAN_WIDGET_TAG,
} from './TcsPanConstants';
import { holidayColors } from '../../../Styles/holidayColors';
import { TAX_COLLECT_TCS_TYPES } from '../../../HolidayConstants';

// Define a global variable to track if the component is already registered.
global.PanViewWidget = global.PanViewWidget || requireNativeComponent('PanView');
const TcsPanWidget = (props) => {
  const {handleWidgetDetails = () => {}, handleTrackingData = () => {}, type = TAX_COLLECT_TCS_TYPES.NOT_APPLICABLE, containerStyle = {}, pricingDetail } = props || {};
  const {tcsMetadata} = pricingDetail || {};
  const {searchKey, tcsApplicableEnum, tcsApplicableAmount, extraInfo} = tcsMetadata || {};

  useEffect(() => {
    const widgetDetailsListener = DeviceEventEmitter.addListener(PAN_TCS_WIDGET_DETAILS, handleWidgetDetails);
    const trackingDataListener = DeviceEventEmitter.addListener(PAN_TCS_WIDGET_TRACKING, handleTrackingData);


    // Clean up listeners on un mount
    return () => {
      widgetDetailsListener.remove();
      trackingDataListener.remove();

    };
  }, []);

  const getParams = () => {
    const requestParams = JSON.stringify({
      key: searchKey,
      productType: 'HOLIDAYS',
      type,
      amount: tcsApplicableAmount,
      currency: 'INR',
      extraInfo,
    });

    return {
      request: requestParams,
      tag: TCS_PAN_WIDGET_TAG,
    };
  };

  return (
    <View style={[styles.container, containerStyle]}>
      <View>
      <global.PanViewWidget
        style={[styles.panView]}
        params={getParams()}
      />
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container:{
    borderWidth: 1,
    borderColor: holidayColors.grayBorder,
    borderRadius: 16,
    backgroundColor: holidayColors.white,
  },
  panView: {
  },
});

export default TcsPanWidget;
