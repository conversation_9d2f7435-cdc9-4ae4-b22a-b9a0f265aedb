import React, { useEffect, useRef } from 'react';
import { DeviceEventEmitter, requireNativeComponent, StyleSheet, View } from 'react-native';
import {
  PAN_TCS_WIDGET_DETAILS,
  PAN_TCS_WIDGET_TRACKING,
  TCS_PAN_WIDGET_TAG,
} from './TcsPanConstants';
import { holidayColors } from '../../../Styles/holidayColors';
import { TAX_COLLECT_TCS_TYPES } from '../../../HolidayConstants';
import { isRawClient } from '../../Utils/HolidayReviewUtils';
import PanCard from 'PAY-UI/PanCard';

// Define a global variable to track if the component is already registered.
const PanView = isRawClient()  ? PanCard  : global.PanViewWidget = global.PanViewWidget || requireNativeComponent('PanView');
const TcsPanWidget = (props) => {
  const {
    handleWidgetDetails = () => {},
    handleTrackingData = () => {},
    type = TAX_COLLECT_TCS_TYPES.NOT_APPLICABLE,
    containerStyle = {},
    pricingDetail,
  } = props || {};
  const { tcsMetadata } = pricingDetail || {};
  const { searchKey, tcsApplicableEnum, tcsApplicableAmount, extraInfo } = tcsMetadata || {};
  const panRef = useRef();

  useEffect(() => {
    const widgetDetailsListener = DeviceEventEmitter.addListener(
      PAN_TCS_WIDGET_DETAILS,
      handleWidgetDetails,
    );
    const trackingDataListener = DeviceEventEmitter.addListener(
      PAN_TCS_WIDGET_TRACKING,
      handleTrackingData,
    );

    // Clean up listeners on un mount
    return () => {
      widgetDetailsListener.remove();
      trackingDataListener.remove();
    };
  }, []);

  const getParams = () => {
    const requestParams = JSON.stringify({
      key: searchKey,
      productType: 'HOLIDAYS',
      type,
      amount: tcsApplicableAmount,
      currency: 'INR',
      extraInfo,
    });

    return {
      request: requestParams,
      tag: TCS_PAN_WIDGET_TAG,
    };
  };

  const webParams  = {
    key: searchKey,
    lob: 'HOLIDAYS',
    type,
    payableAmount: tcsApplicableAmount,
  }
  return (
    <View style={[styles.container, containerStyle]}>
      <View>
      {isRawClient() ? (
          <PanView
            panInitialParams={webParams}
            isDesktop={false}
            updatePanDetails={handleWidgetDetails}
            extraInfo={extraInfo}
            ref={panRef}
            lang="eng"
            brand="MMT"
            currency="INR"
          />
        ) : (
          <global.PanViewWidget style={[styles.panView]} params={getParams()} />
        )}
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    borderWidth: 1,
    borderColor: holidayColors.grayBorder,
    borderRadius: 16,
    flex: 1,
    backgroundColor: holidayColors.white,
  },
  panView: {},
});

export default TcsPanWidget;
