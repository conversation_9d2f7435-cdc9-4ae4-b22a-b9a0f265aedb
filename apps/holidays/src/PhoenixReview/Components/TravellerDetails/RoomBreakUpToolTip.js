import React from 'react';
import {
    StyleSheet,
    View,
    Image,
    Text,
} from 'react-native';

import AtomicCss from '@mmt/legacy-commons/Styles/AtomicCss';
import iconTick from '@mmt/legacy-assets/src/tick_trans_background.webp';
import { holidayColors } from 'mobile-holidays-react-native/src/Styles/holidayColors';
import { fontStyles } from 'mobile-holidays-react-native/src/Styles/holidayFonts';
import { marginStyles } from 'mobile-holidays-react-native/src/Styles/Spacing';

const RoomBreakUpToolTip = ({ roomDetail }) => {
    let total = 0;
    let rows = [];
    roomDetail.rooms.forEach((room, index) => {
        let str;
        if (roomDetail.rooms.length === 1) {
            str = 'Room -';
        } else {
            str = `Room ${(index + 1)} -`;
        }

        if (room.noOfAdults) {
            str = `${str} ${room.noOfAdults} ${room.noOfAdults > 1 ? 'Adults' : 'Adult'}`;
        }
        let child = 0;
        if (room.noOfChildrenWOB) {
            child += room.noOfChildrenWOB;
        }
        if (room.noOfChildrenWB) {
            child += room.noOfChildrenWB;
        }
        if (child > 0) {
            str = `${str}, ${child} ${child > 1 ? 'Children' : 'Child'}`;
        }
        if (room.noOfInfants) {
            str = `${str} ${room.noOfInfants} ${room.noOfInfants > 1 ? 'Infants' : 'Infant'}`;
        }
        total += room.noOfAdults + child + room.noOfInfants;
        rows.push(str);
    });
    return (
    <View style={styles.roomBreakup}>
        <View>
          <Text style={styles.heading}>
            <Text style={[AtomicCss.blackFont]}>Room Breakup - </Text>
            {`${roomDetail.rooms.length} ${
              roomDetail.rooms.length > 1 ? 'Rooms' : 'Room'
            }, ${total} Travellers`}
          </Text>
        </View>
        <View style={AtomicCss.marginTop10}>
          {rows.map((item) => (
            <View
              key={item}
              style={[AtomicCss.flexRow, AtomicCss.alignCenter, marginStyles.mb6]}
            >
              <Image source={iconTick} style={[styles.iconTickWhite, marginStyles.mr10]} />
              <Text style={styles.item}>{item}</Text>
            </View>
          ))}
        </View>
    </View>

    );
};
const styles = StyleSheet.create({
    roomBreakup: {
        backgroundColor: 'black',
        borderRadius: 4,
        paddingVertical: 15,
        paddingHorizontal: 10,
        position: 'absolute',
        zIndex: 3,
        elevation: 3,
        left: 15,
        top: 40,
        width: 300,
    },
    heading: {
        ...fontStyles.labelBaseRegular,
        color: holidayColors.white,
    },
    item: {
        ...fontStyles.labelSmallRegular,
        color: holidayColors.white,
    },
    iconTickWhite: {
        width: 12,
        height: 12,
        tintColor: '#fff',
        resizeMode: 'cover',
    },
});
export default RoomBreakUpToolTip;
