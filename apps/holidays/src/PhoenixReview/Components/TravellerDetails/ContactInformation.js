import  React, {useState} from 'react';
import {
    StyleSheet,
    View,
    TouchableOpacity,
    Image,
    Text,
  } from 'react-native';
  import AtomicCss from '@mmt/legacy-commons/Styles/AtomicCss';
  import iconArrowDown from '@mmt/legacy-assets/src/down_arrow_blue.webp';
  import { fonts } from '@mmt/legacy-commons/Styles/globalStyles';
  import InputElement from '../FormViews/InputElement';
  import InputCheckbox from '../FormViews/InputCheckbox';

  const ContactInformation = ({userDetails}) => {
    const [values, setValues] = useState({
        email: '',
        phone: '',
        city: '',
        state: '',
    });

    const [errors, setErrors] = useState({
      email: '',
      phone: '',
      city: '',
      state: '',
    });
    const handleChange = ({name, value}) => {
            setValues({
            ...values,
            [name]: value,
        });
        if (value) {
            if (name === 'email'){
                if (!/\S+@\S+\.\S+/.test(value)){
                    setErrors((prev) => {
                        return {...prev, [name]: 'Email address is invalid'};
                    });
                } else {
                    setErrors((prev) => {
                        return {...prev, [name]: ''};
                    });
                }
            }
            if (name === 'phone'){
                if (!/^\d{10}$/.test(value)){
                  setErrors((prev) => {
                    return {...prev, [name]: 'Phone number is invalid'};
                });
                } else {
                    setErrors((prev) => {
                        return {...prev, [name]: ''};
                    });
                }
            }
            if (name === 'city' || name === 'state'){
                if (!value){
                  setErrors((prev) => {
                    return {...prev, [name]: 'Enter city/state'};
                  });
                } else {
                  setErrors((prev) => {
                      return {...prev, [name]: ''};
                  });
                }
            }
        }
    };
    const [countryCode, setCountryCode] = useState('91');
    const updateCountryCode = (code) => {
        setCountryCode(code);
        values.countryCode = code;
    };

    const searchCountryCode = () => {
        // navigation.navigate('CountryCode', {
        //     updateCode:updateCountryCode
        // })
    };

    const [cityValue, setCityValue] = useState();
    const updateState = (value) => {
        setCityValue(value);
        values.state = value;
    };
    const updateCity = (value) => {
        setCityValue(value);
        values.city = value;
    };

    const searchLocation = (showSearch) => {

        if (showSearch === 'state') {
            // navigation.navigate('SearchLocation', {
            //     updateLocation:updateState,
            //     showSearch: showSearch,
            // })
        } else {
            // navigation.navigate('SearchLocation', {
            //     updateLocation:updateCity,
            //     showSearch: showSearch,
            // })
        }
    };


      return (

        <View style={styles.contactInfoWrap}>
            <View>
                <Text style={[AtomicCss.font18, AtomicCss.blackFont, AtomicCss.blackText]}>Contact Information</Text>
                <Text style={[AtomicCss.font11, AtomicCss.italicFont, AtomicCss.defaultText, AtomicCss.paddingTop3]}>Booking details & communication will be sent to - </Text>
            </View>
            <View style={[AtomicCss.flexRow, AtomicCss.alignCenter, AtomicCss.marginTop15]}>
                <InputCheckbox list="I’m the primary point of contact. I am above 18 years" />
            </View>
            <View style={AtomicCss.marginTop15}>
                <View style={AtomicCss.marginBottom15}>
                    <InputElement
                        keyboardType={'email-address'}
                        label="Email Id"
                        editable={true}
                        isMandatory={true}
                        value={values.email}
                        placeholder="eg:<EMAIL>"
                        onChangeText={(value) => {
                            handleChange({name: 'email', value});
                        }}
                        error={errors.email}
                    />
                </View>
                <View style={[AtomicCss.marginBottom15, AtomicCss.marginTop5]}>
                    <InputElement
                        keyboardType={'numeric'}
                        label="Mobile Number"
                        isMandatory={true}
                        editable={true}
                        value={values.phone}
                        placeholder="Enter phone no"
                        onChangeText={(value) => {
                            handleChange({name: 'phone', value});
                        }}
                        error={errors.phone}
                        icon={
                            <TouchableOpacity onPress={() => searchCountryCode()}>
                                <View style={[styles.selectBox, AtomicCss.marginRight15]}>
                                    <View style={AtomicCss.marginRight5}><Text style={styles.selectText}>+ {countryCode}</Text></View>
                                    <View style={AtomicCss.marginLeft5}><Image source={iconArrowDown} style={styles.iconArrowDown} /></View>
                                </View>
                            </TouchableOpacity>
                        }
                        iconPosition="left"
                    />
                </View>
                <View style={[AtomicCss.marginBottom15, AtomicCss.marginTop5]}>
                    <View><Text style={[AtomicCss.font12, AtomicCss.defaultText, AtomicCss.regularFont]}>GST City & State<Text style={AtomicCss.red2Text}>*</Text></Text></View>
                    <View style={[AtomicCss.flexRow, AtomicCss.alignCenter, AtomicCss.marginTop10]}>
                        <View style={[AtomicCss.flex1, AtomicCss.marginRight15]}>
                            <TouchableOpacity onPress={() => searchLocation('state')}>
                                <InputElement
                                    keyboardType={'default'}
                                    editable={false}
                                    isMandatory={true}
                                    value={values.state}
                                    error={errors.state}
                                    iconPosition="right"
                                    placeholder="Select State"
                                    icon={
                                        <View style={AtomicCss.marginRight15}>
                                            <Image source={iconArrowDown} style={styles.iconArrowDown} />
                                        </View>
                                    }
                                    iconPosition="right"
                                />
                            </TouchableOpacity>
                        </View>
                        <View style={AtomicCss.flex1}>
                            <TouchableOpacity onPress={() => searchLocation('city')}>
                                <InputElement
                                    keyboardType={'default'}
                                    editable={false}
                                    isMandatory={true}
                                    value={values.city}
                                    error={errors.city}
                                    iconPosition="right"
                                    placeholder="Select City"
                                    icon={
                                        <View style={AtomicCss.marginRight15}>
                                            <Image source={iconArrowDown} style={styles.iconArrowDown} />
                                        </View>
                                    }
                                    iconPosition="right"
                                />
                            </TouchableOpacity>
                        </View>
                    </View>
                </View>
                <View style={AtomicCss.marginBottom15}>
                    <InputElement
                        keyboardType={'default'}
                        label="GST Address"
                        editable={true}
                        value={values.address}
                        onChangeText={(value) => {
                            handleChange({name: 'address', value});
                        }}
                        error={errors.address}
                        placeholder="Enter Address"
                    />
                </View>
            </View>
        </View>


      );
  };

  const styles = StyleSheet.create({


    contactInfoWrap: {
        paddingVertical: 15,
        paddingHorizontal: 16,
    },
    iconArrowDown: {
        width: 10,
        height: 6,
        resizeMode: 'contain',
    },
    selectBox: {
        alignItems: 'center',
        flexDirection: 'row',
    },
    selectText: {
        fontSize: 12,
        fontFamily: fonts.black,
        color: 'black',
    },



  });

  export default ContactInformation;
