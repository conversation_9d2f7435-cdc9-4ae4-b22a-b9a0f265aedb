import React from 'react';
import { Text, View, StyleSheet, TextInput, Platform } from 'react-native';
import { holidayColors } from '../../../../Styles/holidayColors';
import { fontStyles } from '../../../../Styles/holidayFonts';

const MAX_LENGTH_FOR_PLACE_HOLDER = 30;

export default class FormInputElement extends React.Component {
  render() {
    const {label, value, errorState, errorText, placeholder, handleChange, toggleDropDown,marginBottom, maxLength} = this.props;
    const inputStyle = [styles.input];
    let marginBottomInput = marginBottom || 25;
    if (errorState) {
      inputStyle.push(styles.errorState);
      marginBottomInput += 10;
    }
    return (
      <View style={[styles.inputContainer, { marginBottom: marginBottomInput }]}>
        {!!label && <Text style={styles.label}>{label}</Text>}
        <TextInput
          placeholder={
            placeholder?.length > MAX_LENGTH_FOR_PLACE_HOLDER
              ? placeholder.slice(0, MAX_LENGTH_FOR_PLACE_HOLDER) + '...'
              : placeholder
          }
          placeholderTextColor="#ccc"
          style={inputStyle}
          maxLength={maxLength}
          onFocus={() => {
            toggleDropDown();
          }}
          onChangeText={handleChange}
          value={value}
        />
        {errorText ? <Text style={styles.errorText}>{errorText}</Text> : []}
      </View>
    );
  }
}

const styles = StyleSheet.create({
  label: {
    ...fontStyles.labelBaseRegular,
    color: holidayColors.lightGray,
    marginBottom: 6,
    lineHeight: 14,
    letterSpacing: 1,
  },
  input: {
    borderColor: 'rgba(155,155,155,0.5)',
    width: '100%',
    paddingHorizontal: 2,
    backgroundColor: holidayColors.white,
    color: holidayColors.black,
    borderWidth: 0,
    borderBottomWidth: 1,
    ...Platform.select({
      ios: {
        paddingBottom: 10,
      },
      android: {
        paddingBottom: 5,
      },
    }),
    ...fontStyles.labelMediumBold,
  },
  inputContainer: {
    ...Platform.select({
      android: {
        height: 35,
      },
    }),
    width: '100%',
    ...Platform.select({
      ios: {
        zIndex: -1,
      },
      android: {
        position: 'relative',
      },
      web: {
        position: 'relative',
      },
    }),

  },
  errorState: {
    borderColor: holidayColors.red,
  },
  errorText: {
    ...fontStyles.labelSmallRegular,
    color: holidayColors.red,
    ...Platform.select({
      ios: {
        marginLeft: 2,
        position: 'absolute',
        bottom: -20,

      },
      android: {
        marginLeft: 2,
        marginTop: 3,
        marginBottom: 5,
      },
    }),
  },
});
