import React from 'react';
import _ from 'lodash';

import {
  View,
  StyleSheet,
} from 'react-native';

import Form from './ContactForm';
import isEmpty from 'lodash/isEmpty';

const DEFAULT_VALUE_MAPPING = {
  MOBILE_NUMBER: 'mobileNumber',
  MOBILE_CODE: 'countryCode',
  EMAIL: 'email',
  CITY: 'city',
  ADDRESS: 'address',
  GST_STATE: 'state',
};

class BookerInfo extends React.Component {

  constructor(props) {
    super(props);
    const {userDetails = {}, formIntialValue = {}} = props || {};
    this.state = {
      formIntialValue:this.setFormValue(formIntialValue.activeRadio === 0 ? userDetails : {}),
      userDetails: userDetails,
    };
    this.form = React.createRef();
  }
  componentDidMount() {
    this.notifyFormChanged(this.state.formIntialValue);
  }

  resetForm=()=>{
    const formData = {
      ...this.state.formIntialValue,
    };
    Object.keys(formData)?.map((key)=>{
      if (key.includes('#')) {
        // when resetting form its needed to set default value to mobile code i.e. 91
        formData[key] = key.includes('MOBILE_CODE') ? '91' : undefined;
      }
    });
    this.setState({
      formIntialValue: {...formData},
    }, () => {
      this.notifyFormChanged(this.state.formIntialValue);
    });

}
componentDidUpdate(prevProps,prevState){
  if (!_.isEqual(prevProps?.formIntialValue.activeRadio, this.props?.formIntialValue.activeRadio)) {
    this.resetForm();
  }
  else if (!_.isEqual(prevProps?.userDetails, this.props?.userDetails)) {
    this.setState({
      formIntialValue:this.setFormValue(this.props.userDetails || {}),
    });
  }
}
 setFormValue(userDetails){
  const {formSections, options} = this.props || {};
  const formIntialValue = {};
  formSections?.map(form => {
    const {sectionId, fields} = form;
    for (let name in fields) {
      const {optionIdentifier} = fields[name];
      const key = `${sectionId}#${name}`;
      if (optionIdentifier) {
        const valuesList = options[optionIdentifier];
        let fieldValue = userDetails && userDetails[DEFAULT_VALUE_MAPPING[name]];
        if (userDetails && isEmpty(fieldValue) && userDetails.mobile) {
          const mobile =  userDetails.mobile;
          fieldValue = mobile[DEFAULT_VALUE_MAPPING[name]];
        }
        valuesList?.map(value => {
          if (value.label === fieldValue || value.value === fieldValue) {
            formIntialValue[key] = value.value;
          }
        });
        if (isEmpty(fieldValue) && name === 'MOBILE_CODE') {
          formIntialValue[key] = '91';
        }
        if (!formIntialValue[key]) {
          formIntialValue[key] = this.props.formIntialValue[key];
        }
      } else {
        let fieldValue = userDetails && userDetails[DEFAULT_VALUE_MAPPING[name]];
        if (key in this.props.formIntialValue) {
          fieldValue = this.props.formIntialValue[key];
        }
        else if (userDetails && isEmpty(fieldValue) && userDetails.mobile) {
         const mobile =  userDetails.mobile;
         fieldValue = mobile[DEFAULT_VALUE_MAPPING[name]];
        }
        if (isEmpty(fieldValue) && name == 'MOBILE_CODE') {
          formIntialValue[key] = '91';
        }
         formIntialValue[key] = fieldValue;
      }
    }
  });
  // copy other for values
  Object.keys(this.props.formIntialValue).forEach(key => {
    if (!key.includes('#')) {
      formIntialValue[key] = this.props.formIntialValue[key];
    }
  });
  return formIntialValue;
}
notifyFormChanged = (values) => {
  const {userDetails = {}, updateUserDetailsMaster} = this.props || {};
  if (updateUserDetailsMaster) {
    updateUserDetailsMaster(userDetails, values);
  }
}
  validateBookerInfo =  () => {
    const formData = this.form.current.validateForm();
    const {updateUserDetailsMaster, formSections, options} = this.props;
    if (formData) {
      const userDetailsObj = {};
      formSections?.map(form => {
        const {sectionId, fields} = form;
        for (let name in fields) {
          const {optionIdentifier} = fields[name];
          if (optionIdentifier) {
            const valuesList = options[optionIdentifier];
            let fieldValue = formData[`${sectionId}#${name}`];
            valuesList?.map(value => {
              if (value.label === fieldValue || value.value === fieldValue) {
                userDetailsObj[DEFAULT_VALUE_MAPPING[name]] = value.label;
              }
            });
          } else {
            userDetailsObj[DEFAULT_VALUE_MAPPING[name]] = formData[`${sectionId}#${name}`];
          }
        }
      });
      updateUserDetailsMaster(userDetailsObj, formData);
      return true;
    } else {
      return false;
    }
  };

  render() {
    const {options, formSections, setBookerInfoRef} = this.props;
    return (
      <View style={styles.bookerInfo} ref={setBookerInfoRef}>
        <Form
            ref={this.form}
            formSections={formSections}
            options={options}
            initialValues={this.state.formIntialValue}
            onChange={this.notifyFormChanged}
            onContactFormError={this.props.onContactFormError}
          />
      </View>
    );
  }
}

const styles = StyleSheet.create({
  bookerInfo: {
    zIndex: 2,
    position: 'relative',
  },
});

export default BookerInfo;
