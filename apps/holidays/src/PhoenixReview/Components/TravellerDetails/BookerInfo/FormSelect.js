import React from 'react';
import {
  Text,
  View,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  Platform,
  Dimensions
} from 'react-native';
import Dropdown from '@Frontend_Ui_Lib_App/Dropdown';
import PropTypes from 'prop-types';
import {SELECT_DEFAULT} from '../../../../Review/HolidayReviewConstants';
import isEqual from 'lodash/isEqual';
import { fontStyles } from '../../../../Styles/holidayFonts';
import { holidayColors } from '../../../../Styles/holidayColors';
import BottomSheetOverlay from 'mobile-holidays-react-native/src/Common/Components/BottomSheetOverlay';
import { paddingStyles } from '../../../../Styles/Spacing';
import { marginStyles } from '../../../../Styles/Spacing';
const iconArrowDown = require('@mmt/legacy-assets/src/iconArrowDown.webp');

export default class FormSelectElement extends React.Component {

  ref = React.createRef()

  constructor(props) {
    super(props);
    this.state = this.getInitialState(props);
  }
  componentDidUpdate(prevProps){
    if (!isEqual(this.props.value, prevProps.value)) {
        this.setState({...this.getInitialState(this.props)});
    }
  }
  getInitialState = (props) => {
    let label = '';
    if (props.value) {
      for (let i = 0; i < props.options.length; i++ ){
        if (props.value === props.options[i].value) {
          label = props.options[i].label;
          break;
        }
      }
    }
    const state = {
      value: props.value,
      label: props?.placeholder ? props.placeholder : null,
      open: false,
    };
    if (props.value){
      this.props.handleChange(props.value);
    }
    return state;
  }

  focus = () => {
    this.toggle();
  }

  selected = (value, index) => {
    if (value === SELECT_DEFAULT) {
      return;
    }
    const {options} = this.props;
    this.setState({
      value,
      label: options[index].label,
    });
    this.props.handleChange(value);
    this.toggle();
  };
  static navigationOptions = {header: null};

  toggle = () => {
    this.setState({open: !this.state.open});
  };

  render() {
    const {label,customLabelStyle, options, errorState, errorText, formStyle,isPhoenixReview,heightStyle, isMandatory} = this.props;
    const {open} = this.state;
    let containerStyle = formStyle;
    const customStyles = {
      dropdownFieldWrapperStyle: styles.inputWrapperDropDown,
      dropdownWrapperStyle: styles.dropdownWrapperStyle,
      labelStyle: customLabelStyle,
      endIconStyle: styles.iconArrowDown,
      dropdownValueStyle: styles.dropdownValueStyle,
    };
    
    return (
      <View style={containerStyle}>
          <Dropdown 
            label={label}
            requiredText={isMandatory && <Text style={styles.mandatory}> *</Text>}
            isError = {errorState}
            errorMessage = {errorText}
            value= {
              isPhoenixReview 
                ? (this.state.value ? `+${this.state.value}` : this.props?.placeholder)
                : this.state.label
            }
            onSelect={this.toggle}
            isFloating={true}
            endIcon = {iconArrowDown}
            onEndIconPress={this.toggle}
            customStyle={customStyles}
        />
        { (open) && <BottomSheetOverlay
          visible={this.state.open}
          toggleModal={this.toggle}
          onDismiss={this.toggle}
          containerStyles={styles.containerStyles}
          headingContainerStyles={styles.headingContainerStyles}
          title={SELECT_DEFAULT}
        >
          <ScrollView style={styles.scrollSection}>
            <View style={styles.dropDownList}>
              {options?.map((option, index) => (
                <TouchableOpacity key={index} onPress={() => this.selected(option.value, index)}>
                  <Text style={styles.dropdownText}>{option.label}</Text>
                </TouchableOpacity>
              ))}
            </View>
          </ScrollView>
        </BottomSheetOverlay>}   
      </View>
    );
  }
}

FormSelectElement.propTypes = {
  label: PropTypes.string.isRequired,
  value: PropTypes.string,
  errorState: PropTypes.bool,
  errorText: PropTypes.string,
  options: PropTypes.array.isRequired,
  handleChange: PropTypes.func.isRequired,
};

const styles = StyleSheet.create({
  iconArrowDown: {
    width: 12,
    height: 12,
    tintColor: holidayColors.lightGray
  },
  dropdownText: {
    color: holidayColors.black,
    ...fontStyles.labelMediumRegular,
    ...paddingStyles.pb30,
    ...paddingStyles.ph12
  },
  mandatory: {
    color: holidayColors.red,
  },
  inputWrapperDropDown:{
    color: holidayColors.black,
    ...fontStyles.labelMediumBold,
  },
  dropdownWrapperStyle:{
    ...Platform.select({
      ios: {
        ...paddingStyles.pt20,
      },
      android: {
        ...paddingStyles.pt24,
      }
    })
  },
  dropDownList: {
      ...marginStyles.mt10,
  },
  dropdownValueStyle: {
    color: holidayColors.black,
    ...fontStyles.labelMediumBlack,
  },
  containerStyles:{
    ...paddingStyles.pa16,
  },
  scrollSection: {
    maxHeight: Platform.select({
      ios: Dimensions.get('window').height - 250,  
      android: Dimensions.get('window').height - 270, 
      web: Dimensions.get('window').height - 270, 
    }),
    ...marginStyles.mb20,
  },
  headingContainerStyles: {
    ...paddingStyles.pb16
  }
});
