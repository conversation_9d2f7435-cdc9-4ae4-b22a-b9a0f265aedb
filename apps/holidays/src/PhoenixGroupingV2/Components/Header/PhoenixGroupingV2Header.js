import React from 'react';
import { View, Animated, StyleSheet } from 'react-native';
import { connect } from 'react-redux';
import { marginStyles } from '../../../Styles/Spacing';
import { HEADER_HEIGHT } from '../../Contants';
import { fontStyles } from '../../../Styles/holidayFonts';
import { holidayColors } from '../../../Styles/holidayColors';
import BackIconWhite from '@mmt/legacy-assets/src/back-white.webp';
import placeHolderImage from '@mmt/legacy-assets/src/genericDarkTextureImage.webp';
import { convertUrlToHttps } from 'mobile-holidays-react-native/src/utils/HolidayNetworkUtils';
import { shapeTypes } from 'packages/legacy-commons/Common/Components/CoachMarks';

/* Components */
import CollectionGroups from '../CollectionGroups';
import PackageHeaderDetails from './PackageHeaderDetails';
import QuickFilterView from '../quickFilterView';
import { PageHeaderBackButton } from '../../../Common/Components/PageHeader';
import HoldiaysMessageStrip from 'mobile-holidays-react-native/src/Common/Components/HolidaysMessageStrip';
import HolidayImageHolder from 'mobile-holidays-react-native/src/Common/Components/HolidayImageHolder';
import DynamicCoachMark from '@mmt/legacy-commons/Common/Components/CoachMarks/DynamicCoachMark';
import { getTripIdeasSection } from '../../Utils/PhoenixGroupingV2Utils';
import { isRawClient } from '../../../utils/HolidayUtils';
import { logHolidaysGroupingPDTEvents } from '../../Utils/PhoenixGroupingV2PDTTrackingUtils';
import { PDT_EVENT_TYPES } from 'mobile-holidays-react-native/src/utils/HolidayPDTConstants';


const PhoenixGroupingV2Header = ({
  holidayLandingGroupDto,
  metaDataState = {},
  onBackPress,
  scrollY,
  togglePopup = null,
  getQuickFilterViewProps = null,
  groupSections = [],
  closeIntervention = null,
  trackClickEvent = null,
  handleScrollToTop,
  trackViewedSectionClickEvent = () => {},
  ...rest
}) => {
  const { metaDataDetail = {}, resetMetaIfNoPackage = false } = metaDataState || {};
  const { headerDetail = {}, message = '' } = metaDataDetail || {};
  const imageUrl = convertUrlToHttps(headerDetail?.image);
  const tripIdeasSection = getTripIdeasSection(groupSections);

  const headerImageStyleTransform = {
    transform: [
      {
        translateY: scrollY.interpolate({
          inputRange: [-HEADER_HEIGHT, 0, HEADER_HEIGHT],
          outputRange: [-HEADER_HEIGHT / 2, 0, HEADER_HEIGHT * 0.82],
        }),
      },
      {
        scale: scrollY.interpolate({
          inputRange: [-HEADER_HEIGHT, 0, HEADER_HEIGHT],
          outputRange: [2, 1, 0.82],
        }),
      },
    ],
  };

  const headerImageTextStyleTransform = {
    transform: [
      {
        translateY: scrollY.interpolate({
          inputRange: [0, HEADER_HEIGHT - 190, HEADER_HEIGHT - 90],
          outputRange: [0, 0, 180],
          extrapolate: 'clamp',
        }),
      },
    ],
  };

  const trackNoPckFoundMessageShown = () => {
    const eventName = 'enhancement_no_pkg_found';
    logHolidaysGroupingPDTEvents({
      actionType : PDT_EVENT_TYPES.contentSeen,
      value : eventName,
      shouldTrackToAdobe:false
    })
    trackClickEvent({ eventName });
  }

  const hideMarginIfMessage = resetMetaIfNoPackage || message;
  return (
    <>
      <View style={styles.arrowIconContainer}>
        <PageHeaderBackButton
          onBackPressed={onBackPress}
          iconSource={BackIconWhite}
          iconStyles={styles.iconStyle}
        />
      </View>
      <View style={[styles.headerContainer, hideMarginIfMessage ? {} : marginStyles.mb16]}>
        <Animated.View style={[styles.headerImage, headerImageStyleTransform]}>
          <HolidayImageHolder
            imageUrl={imageUrl}
            defaultImage={placeHolderImage}
            resizeMode="cover"
            style={styles.headerImage}
            containerStyles={styles.headerImage}
          />
        </Animated.View>
        <Animated.View style={[styles.headerBottomStyles, headerImageTextStyleTransform]}>
          <View>
            <PackageHeaderDetails
              holidayLandingGroupDto={holidayLandingGroupDto}
              metaDataDetail={metaDataDetail}
              togglePopup={togglePopup}
              trackClickEvent={trackClickEvent}
              tiSectionDetails={isRawClient() ? {} : tripIdeasSection}
              closeIntervention={closeIntervention}
            />
            <DynamicCoachMark
              isSetCustomShape
              cueStepKey={'sortAndFilter'}
              offsetHeight={15}
              shapeObject={{
                type: shapeTypes.rect,
                height: 80,
                borderRadius: 16,
              }}
            >
              <View style={styles.filterContainers}>
                <QuickFilterView
                  {...getQuickFilterViewProps()}
                  holidayLandingGroupDto={holidayLandingGroupDto}
                  trackViewedSectionClickEvent={trackViewedSectionClickEvent}
                />
              </View>
            </DynamicCoachMark>
          </View>
        </Animated.View>
      </View>
      <HoldiaysMessageStrip
        message={message}
        shouldShow={!!message}
        containerStyles={marginStyles.mt0}
      />
      <HoldiaysMessageStrip
        shouldShow={resetMetaIfNoPackage}
        message={
          'We had to remove your Filter selection as no matching results were found. Please re-apply the required Filters.'
        }
        trackEvent={trackNoPckFoundMessageShown}
        containerStyles={{ ...(message ? {} : marginStyles.mt0), ...marginStyles.mb10 }} // remove margin if there is error message above this
      />
      <CollectionGroups
        trackClickEvent={trackClickEvent}
        trackViewedSectionClickEvent={trackViewedSectionClickEvent}
      />
    </>
  );
};

const styles = StyleSheet.create({
  arrowIconContainer: {
    position: 'absolute',
    zIndex: 1,
    left: 20,
    top: 20,
  },
  headerContainer: {
    alignItems: 'center',
    overflow: 'hidden',
    marginTop: -1000,
    paddingTop: 1000,
  },
  headerImage: {
    height: HEADER_HEIGHT,
    width: '120%',
  },
  headerBottomStyles: {
    position: 'absolute',
    bottom: -5,
    left: 0,
    width: '100%',
  },
  filterContainers: {
    backgroundColor: holidayColors.transparent,
  },
  headingPreTitle: {
    ...fontStyles.labelBaseRegular,
    ...marginStyles.mt8,
    color: holidayColors.white,
  },
  headingTitle: {
    ...fontStyles.headingMedium,
    color: holidayColors.white,
  },
  headingPostTitle: {
    ...fontStyles.labelBaseRegular,
    color: holidayColors.white,
    ...marginStyles.mt4,
  },
  iconStyle: {
    tintColor: holidayColors.white,
    width: 16,
    height: 16,
  },
});

const mapStateToProps = (state) => {
  return {
    groupSections: state.holidaysPhoenixGroupingV2.holidayLandingGroupReducer.groupSections,
    metaDataState: state.holidaysPhoenixGroupingV2.metaDataDetail,
  };
};

export default connect(mapStateToProps)(PhoenixGroupingV2Header);
