import React, { useEffect, useState } from 'react';
import { View, Animated, StyleSheet, TouchableOpacity, Image } from 'react-native';
import { connect } from 'react-redux';
import * as DateUtil from '../../../utils/HolidayDateUtils';
import { HEADER_HEIGHT, PHOENIX_GROUPING_V2_POPUPS } from '../../Contants';
import { holidayColors } from '../../../Styles/holidayColors';
import { marginStyles, paddingStyles } from '../../../Styles/Spacing';
import { getPaxText } from 'mobile-holidays-react-native/src/utils/HolidayUtils';
import { getCityCampaignDisplayName } from 'mobile-holidays-react-native/src/LandingNew/Utils/DestinationDepartureCityUtils';
import { holidayBorderRadius } from 'mobile-holidays-react-native/src/Styles/holidayBorderRadius';
import { createTravellerObj } from 'mobile-holidays-react-native/src/utils/RoomPaxUtils';
import EditIcon from '@mmt/legacy-assets/src/holidays/EditIcon.webp';
import AtomicCss from '@mmt/legacy-commons/Styles/AtomicCss';

/* Components */
import QuickFilterView from '../quickFilterView';
import CollectionGroups from '../CollectionGroups';
import PageHeader from 'mobile-holidays-react-native/src/Common/Components/PageHeader';
import { logHolidaysGroupingPDTEvents } from '../../Utils/PhoenixGroupingV2PDTTrackingUtils';
import { PDT_EVENT_TYPES } from 'mobile-holidays-react-native/src/utils/HolidayPDTConstants';

const PhoenixGroupingV2StickyHeader = ({
  scrollY,
  togglePopup,
  holidayLandingGroupDto,
  onBackPressed,
  getQuickFilterViewProps = null,
  handleScrollToTop = null,
  trackClickEvent,
  barAnim,
  userDepCityState,
  trackViewedSectionClickEvent = () => {}
}) => {
  const { userDepCity: userDepartureCity, selectedDate, packageDate, destinationCityData, rooms } =
    holidayLandingGroupDto || {};
  const destinationCity = getCityCampaignDisplayName(destinationCityData);
  const travellerObject = createTravellerObj(rooms);
  const isSearchFilter = true;
  const [pointerEvents, setPointerEvents] = useState('none');

  const trackEventsWithPDTV3 = () => {
    const eventName = 'edit_intent';
    logHolidaysGroupingPDTEvents({
      actionType: PDT_EVENT_TYPES.buttonClicked,
      value : eventName
    });
    trackClickEvent({ eventName });
  };

  const handleOpenEditOverlay = () => {
    togglePopup({ popup: PHOENIX_GROUPING_V2_POPUPS.EDIT_OVERLAY });
    trackEventsWithPDTV3();
  };

  const userDepartureCityValue = typeof userDepartureCity === 'object' ? userDepCityState : userDepartureCity
 
  const overlayAnimationStyle = {
    opacity: scrollY.interpolate({
      inputRange: [HEADER_HEIGHT - 110, HEADER_HEIGHT - 40],
      outputRange: [0, 1],
    }),
  };
  // Listen to scrollY changes
  useEffect(() => {
    const id = scrollY.addListener(({ value }) => {
      const inputRange = [HEADER_HEIGHT - 90, HEADER_HEIGHT - 40];
      const opacity =
        value <= inputRange[0]
          ? 0
          : value >= inputRange[1]
          ? 1
          : (value - inputRange[0]) / (inputRange[1] - inputRange[0]);

      if (opacity === 0) {
        setPointerEvents('none');
      } else {
        setPointerEvents('auto');
      }
    });

    // Clean up after unmount
    return () => {
      scrollY.removeListener(id);
    };
  }, []);

  const containerAnimationStyle = {
    opacity: scrollY.interpolate({
      inputRange: [HEADER_HEIGHT - 90, HEADER_HEIGHT - 50],
      outputRange: [0, 1],
    }),
    transform: [
      {
        translateY: scrollY.interpolate({
          inputRange: [HEADER_HEIGHT - 90, HEADER_HEIGHT - 60],
          outputRange: [-50, 0],
          extrapolate: 'clamp',
        }),
      },
    ],
  };

  return (
    <View style={styles.headerOverlay} pointerEvents={pointerEvents}>
      <Animated.View style={[styles.overlayContainer, overlayAnimationStyle]} />
      <Animated.View style={[styles.container, containerAnimationStyle]}>
        <View style={styles.widgetPageWrapperRoot}>
          <View style={[styles.widgetPageWrapper, AtomicCss.flexRow, AtomicCss.alignCenter]}>
            <View style={styles.headerLeftWrapper}>
            <PageHeader
              title={`${userDepartureCityValue} to ${destinationCity || 'Select Destination'}`}
              subTitle={`${DateUtil.getDateText({ selectedDate, packageDate })}${
                isSearchFilter ? getPaxText({ trvInfo: travellerObject, seprator: ' | ' }) : ''
              }`}
              onBackPressed={onBackPressed}
              showBackBtn
              subTitleStyles={paddingStyles.pt2}
              containerStyles={{width: '90%'}}
              headerWrapperStyle={styles.headerWrapperStyle}
            />
            </View>
            <TouchableOpacity style={[AtomicCss.pushRight, styles.editIconWrapper]} onPress={handleOpenEditOverlay}>
              <Image style={styles.editIcon} source={EditIcon} />
            </TouchableOpacity>
          </View>
        </View>
        <View style={{ zIndex: 10 }}>
          <QuickFilterView
            isStickyHeader={true}
            {...getQuickFilterViewProps()}
            holidayLandingGroupDto={holidayLandingGroupDto}
          />
        </View>
        <Animated.View
          style={[
            {
              transform: [
                {
                  translateY: barAnim,
                },
              ],
            },
          ]}
        >
          <CollectionGroups
            isStickyHeader={true}
            handleScrollToTop={handleScrollToTop}
            trackClickEvent={trackClickEvent}
            trackViewedSectionClickEvent={trackViewedSectionClickEvent}
          />
        </Animated.View>
      </Animated.View>
    </View>
  );
};

const styles = StyleSheet.create({
  headerOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    zIndex: 100,
  },
  overlayContainer: {
    position: 'absolute',
    ...paddingStyles.pa20,
    backgroundColor: holidayColors.white,
    shadowColor: holidayColors.black,
    shadowOffset: { width: -2, height: 4 },
    shadowOpacity: 0.2,
    shadowRadius: 3,
    height: 100,
    width: '100%',
    zIndex: 1,
  },
  container: {
    position: 'relative',
    ...marginStyles.mb10,
    zIndex: 2,
  },
  widgetPageWrapperRoot: {
    backgroundColor: holidayColors.white,
    ...paddingStyles.ph16,
    ...paddingStyles.pt20,
    zIndex: 1,
  },
  widgetPageWrapper: {
    borderWidth: 1,
    borderColor: holidayColors.grayBorder,
    ...holidayBorderRadius.borderRadius8,
    ...paddingStyles.pr10,
    backgroundColor: holidayColors.lightGray2,
  },
  editIconWrapper: {
    ...marginStyles.mr8,
    alignSelf: 'center'
  },
  editIcon: { width: 20, height: 29, ...marginStyles.ml10 },
  headerWrapperStyle: {
    backgroundColor: holidayColors.transparent,
  },
  headerLeftWrapper:{
    flex:1
  }
});
const mapStateToProps = (state) => {
  return {
    userDepCityState: state.holidaysSearchWidget.userDepCity,
    holidayLandingGroupDto:
      state.holidaysPhoenixGroupingV2.holidayLandingGroupReducer.holidayLandingGroupDto,
  };
};
export default connect(mapStateToProps)(PhoenixGroupingV2StickyHeader);
