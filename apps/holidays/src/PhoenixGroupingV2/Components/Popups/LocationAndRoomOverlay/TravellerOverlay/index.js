import React from 'react';
import { Modal, View, StyleSheet, Platform } from 'react-native';
import TravellerPage from 'mobile-holidays-react-native/src/PhoenixDetail/Components/EditOverlay/TravellerPage/TravellerPage';

import {
  createChildAgeArrayFromApi,
  createRoomDetailsFromRoomDataForPhoenix,
} from 'mobile-holidays-react-native/src/utils/RoomPaxUtils';
import { getPaxDetails } from '../../../../../utils/HolidayUtils';
import { getRoomDataForPdt } from 'mobile-holidays-react-native/src/PhoenixDetail/Utils/HolidayDetailUtils';
import { marginStyles, paddingStyles } from '../../../../../Styles/Spacing';
import { logHolidaysGroupingPDTEvents } from 'mobile-holidays-react-native/src/PhoenixGroupingV2/Utils/PhoenixGroupingV2PDTTrackingUtils';
import { PDT_EVENT_TYPES } from 'mobile-holidays-react-native/src/utils/HolidayPDTConstants';

interface TravellerOverlayProps {
  setShowTravellerPopup: Function;
  paxDetails: {
    roomData: Object;
  };
  paxData: Object;
  dateObj: Object;
  trackClickEvent: Function;
  setPaxDetails: Function;
  setUpdatedPaxDetails: Function;
  setChildAgeArray: Function;
}
const TravellerPageOverlay = ({
  setShowTravellerPopup,
  paxDetails,
  paxData,
  dateObj,
  trackClickEvent = () => {},
  setPaxDetails,
  setIsPaxDetailsUpdated,
  // setChildAgeArray,
  setRoomDetails,
}: TravellerOverlayProps) => {
  const trackLocalClickEvent = (event, suffix) => {
    trackClickEvent({ eventName: event });
  };

  const handleClose = () => {
    setShowTravellerPopup(false);
  };
  // Tracking
  const trackTravellerChangeEvents = (paxDetails, roomDetails, isBack) => {
    const { adult, child, infantCount, noOfRooms } = paxDetails || {};
    const eventSuffix = `adult_${adult}_child_${child}_infant_${infantCount}_noOfRooms_${noOfRooms}`;
    const eventName = `changed_pax_${eventSuffix}`;
    const prop1 = `changed_adult:${adult}_children:${child}_infant:${infantCount}`;
    const pdtExtraData = {
      pax_selected: `${adult}|${child}|${infantCount}|${adult + child + infantCount}`,
      pax_room_selected: getRoomDataForPdt(roomDetails),
    };
    const value = isBack ? 'back_pax' : `changed_pax|${eventSuffix}`;
    logHolidaysGroupingPDTEvents({
      actionType: PDT_EVENT_TYPES.buttonClicked,
      value,
    });
    trackClickEvent({ eventName, prop1, pdtExtraData });
  };
  const handleTravellerChange = (roomData, packagePaxDetail, isBack = false) => {
    const roomDetails = createRoomDetailsFromRoomDataForPhoenix(roomData, packagePaxDetail);
    const paxDetails = getPaxDetails({ roomDetails });

    setPaxDetails({
      roomData,
      ...paxDetails,
    });
    setRoomDetails(roomDetails);
    handleClose();
    setIsPaxDetailsUpdated(true);
    trackTravellerChangeEvents(paxDetails, roomDetails, isBack);
    // setChildAgeArray({ data: createChildAgeArrayFromApi(roomDetails) });
  };
  return (
    <Modal animationType="slide" onRequestClose={handleClose}>
      <View style={styles.paxModal}>
        <TravellerPage
          roomData={paxDetails?.roomData}
          handleTravellerChange={handleTravellerChange}
          selectedDate={dateObj?.selectedDate}
          packagePaxDetail={paxData}
          fromLanding={true}
          handlePDT={() => {}}
          trackClickEvent={trackLocalClickEvent}
          trackPDTV3Event={logHolidaysGroupingPDTEvents}
        />
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  paxModal: {
    height: '95%',
    ...Platform.select({
      ios: {
        ...marginStyles.mt30,
        ...paddingStyles.pv20,
      }
    }),
  },
});
export default TravellerPageOverlay;
