import React, { useCallback, useEffect, useState } from 'react';
import { connect } from 'react-redux';
import { PHOENIX_GROUPING_V2_POPUPS } from '../../../Contants';
import {
  createChildAgeArrayFromApi,
  createRoomDataFromRoomDetailsPhoenix,
} from '../../../../utils/RoomPaxUtils';
import { getPaxDetails } from '../../../../utils/HolidayUtils';

/* Components */
import PhoenixGroupingV2EditOverlay from './EditOverlay';
import TravellerPageOverlay from './TravellerOverlay';
import { logHolidaysGroupingPDTEvents } from '../../../Utils/PhoenixGroupingV2PDTTrackingUtils';
import { PDT_EVENT_TYPES } from 'mobile-holidays-react-native/src/utils/HolidayPDTConstants';

const LocationAndRoomOverlay = ({
  popup,
  togglePopup,
  roomDetails,
  setRoomDetails,
  setSelectedCityType,
  setCitySelectionType,
  holidayLandingGroupDto,
  citySelectionType,
  selectedCityType,
  trackClickEvent = () => {},
  trackViewedSectionClickEvent = () => {},
  setPdtObj,
}) => {
  const { rooms = [] } = holidayLandingGroupDto || {};
  const childAgeArray = rooms?.length ? createChildAgeArrayFromApi(rooms) : [];
  const [showTravellerPopup, setShowTravellerPopup] = useState(false);
  const [paxDetails, setPaxDetails] = useState({
    roomData: createRoomDataFromRoomDetailsPhoenix(roomDetails, childAgeArray),
    ...getPaxDetails({ roomDetails }),
  });
  const [paxData, setPaxData] = useState({});
  const [isPaxDetailsUpdated, setIsPaxDetailsUpdated] = useState(false);
  const captureClickEvents = ({ eventName }) => {
    logHolidaysGroupingPDTEvents({
      actionType: PDT_EVENT_TYPES.buttonClicked,
      value : eventName,
    });
    trackClickEvent({ eventName });
  };
  const togglePax = useCallback((updatedPaxData) => {
    captureClickEvents({eventName:'change_pax'});
    setShowTravellerPopup(true);
    setPaxData(updatedPaxData);
  }, []);

  return (
    <>
      {popup === PHOENIX_GROUPING_V2_POPUPS.EDIT_OVERLAY && (
        <PhoenixGroupingV2EditOverlay
          paxDetails={paxDetails}
          roomDetails={roomDetails}
          togglePopup={togglePopup}
          togglePax={togglePax}
          holidayLandingGroupDto={holidayLandingGroupDto}
          updatedPaxDetails={isPaxDetailsUpdated}
          selectedCityType={selectedCityType}
          setSelectedCityType={setSelectedCityType}
          citySelectionType={citySelectionType}
          setCitySelectionType={setCitySelectionType} // use useState funciton
          setShowTravellerPopup={setShowTravellerPopup}
          setIsPaxDetailsUpdated={setIsPaxDetailsUpdated}
          trackClickEvent={trackClickEvent}
          trackViewedSectionClickEvent={trackViewedSectionClickEvent}
        />
      )}
      {showTravellerPopup && (
        <TravellerPageOverlay
          paxDetails={paxDetails}
          paxData={paxData}
          setShowTravellerPopup={setShowTravellerPopup}
          setPaxDetails={setPaxDetails}
          setRoomDetails={setRoomDetails}
          dateObj={{ selectedDate: holidayLandingGroupDto.selectedDate }}
          setIsPaxDetailsUpdated={setIsPaxDetailsUpdated}
          trackClickEvent={trackClickEvent}
        />
      )}
    </>
  );
};

const mapStateToProps = (state) => {
  return {
    holidayLandingGroupDto:
      state.holidaysPhoenixGroupingV2.holidayLandingGroupReducer.holidayLandingGroupDto,
  };
};

export default connect(mapStateToProps)(LocationAndRoomOverlay);
