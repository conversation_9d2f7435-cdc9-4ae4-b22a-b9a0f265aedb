import React from 'react';
import { connect } from 'react-redux';
import { cloneDeep, isEmpty } from 'lodash';
import { Dimensions, Modal, SafeAreaView, StyleSheet } from 'react-native';
import { LISTING_TRACKING_PAGE_NAME } from '../../../../Listing/ListingConstants';
import { GROUPING_PAGE_NAME } from '../../../../SearchWidget/SearchWidgetConstants';

import {
  fetchSearchWidgetData,
  fetchSearchWidgetDataWithOldData,
  toggleLocationAutoComplete,
  updateDepCity,
  setSearchWidgetRefreshRequired,
} from '../../../../SearchWidget/Actions/HolidaySearchWidgetActions';
import { loadPhoenixSearchPageData } from '../../../Actions/metaDataActions';
import BottomSheetOverlay from '../../../../Common/Components/BottomSheetOverlay';
import { logHolidaysGroupingPDTEvents } from '../../../Utils/PhoenixGroupingV2PDTTrackingUtils';


interface PhoenixSearchPageProps {
  onDone: Function;
  onSWClose: Function;
  unmountIntervention: Function;
  stateValues: {
    visibleOn: String;
    filterThreshold: String;
    showCoachMarks: Boolean;
    quickFilter: Array;
  };
  filterHeader: String;
  isBottomSheet: Boolean;
  filterSubHeader: String;
  masterMetaResponse: Object;
  holidayLandingGroupDto: {
    aff: String;
    cmp: String;
    rooms: Array;
    toDate: Date;
    branch: String;
    filters: Array;
    campaign: String;
    fromDate: Date;
    packageDate: Date;
    selectedDate: Date;
    sorterCriterias: Array;
    destinationCity: String;
  };
  getSearchResults: Function;
  trackClickEvent: Function;
  trackErrorLocalClickEvent: Function;
  updateShowFilterBottomsheet: Function;
  updateShowSearchWidget: Function;
}
const PhoenixGroupingV2SearchPageContainer = (props) => {
  const {
    onDone,
    onSWClose,
    isSWSuccess,
    isSWLoading,
    isSWError,
    stateValues,
    filterHeader,
    isBottomSheet,
    filterSubHeader,
    masterMetaResponse,
    updateMasterMetaRepsonse,
    holidayLandingGroupDto,
    getSearchResults,
    trackClickEvent,
    trackErrorLocalClickEvent,
    updateShowFilterBottomsheet,
    updateShowSearchWidget,
    unmountIntervention,
    groupingData = {},
    trackViewedSectionClickEvent = () => {},
  }: PhoenixSearchPageProps = props || {};
  const {
    aff,
    cmp,
    rooms,
    toDate,
    branch,
    filters,
    campaign,
    fromDate,
    packageDate,
    selectedDate,
    sorterCriterias,
    destinationCity,
  } = holidayLandingGroupDto || {};

  const dateObj = {
    fromDate,
    toDate,
    packageDate,
    selectedDate,
  };

  const { visibleOn, filterThreshold, showCoachMarks, quickFilter } = stateValues || {};
  const SearchPage = require('../../../../SearchWidget/Components/PhoenixSearchPage').default;

  if (isEmpty(masterMetaResponse)) {
    // only one time assignment is required
    updateMasterMetaRepsonse(groupingData?.metaResponse);
  }
  const extraProps = {
    aff,
    rooms,
    branch,
    campaign,
    quickFilter,
    destinationCity,
    cmpChannel: cmp,
    criterias: filters || [],
    sorterCriterias: sorterCriterias,
    masterMetaResponse: masterMetaResponse,
    holidayLandingGroupDto: cloneDeep(holidayLandingGroupDto),
  };

  const searchPage = (
    <SearchPage
      {...props}
      {...extraProps}
      pageName={GROUPING_PAGE_NAME}
      trackingPageName={LISTING_TRACKING_PAGE_NAME}
      dateObj={dateObj}
      onSWClose={onSWClose}
      isSuccess={isSWSuccess}
      isLoading={isSWLoading}
      isError={isSWError}
      onSWDone={onDone}
      getSearchResults={getSearchResults}
      trackClickEvent={trackClickEvent}
      trackPDTV3Event={logHolidaysGroupingPDTEvents}           //this is passed in props as common component used in other places
      trackViewedSectionClickEvent={trackViewedSectionClickEvent}
      trackErrorClickEvent={trackErrorLocalClickEvent}
      isBottomSheet={isBottomSheet}
      visibleOn={(isBottomSheet && isEmpty(quickFilter) )? props.visibleOn : undefined}
      isFilterQuickView={!isEmpty(quickFilter)}
      bottomsheetHeader={{ header: filterHeader, subHeader: filterSubHeader }}
      requestParams = {{
        apiVersion: 'v21',
      }}
    />
  );
  const totalPackageCount = masterMetaResponse?.headerDetail?.packagesCount || 0;
  unmountIntervention();
  return !isBottomSheet ? (
    <Modal animationType="slide" onRequestClose={() => updateShowSearchWidget(false)}>
      <SafeAreaView style={styles.oldSearchPage}>{searchPage}</SafeAreaView>
    </Modal>
  ) : totalPackageCount > filterThreshold && !showCoachMarks ? (
    <BottomSheetOverlay
      containerStyles={styles.bottomSheetContainer}
      toggleModal={() => updateShowFilterBottomsheet(false)}
      visible={isBottomSheet}
      showCross={false}
    >
      {searchPage}
      {/* <View style={styles.searchPage}>
        {searchPage}
      </View> */}
    </BottomSheetOverlay>
  ) : null;
};

const styles = StyleSheet.create({
  oldSearchPage: {
    flex: 1,
  },
  searchPage: {
    // height: '100%',
    width: '100%',
  },
  bottomSheetContainer: {
    maxHeight: Dimensions.get('window').height / 2,
  },
});

const mapStateToProps = (state) => ({
  userDepCity: state.holidaysGrouping.userDepCity,
  searchWidgetRefreshRequired: state.holidaysGrouping.searchWidgetRefreshRequired,
  searchWidgetData: state.holidaysGrouping.searchWidgetData,
  isSWSuccess: state.holidaysGrouping.isSWSuccess,
  isSWLoading: state.holidaysGrouping.isSWLoading,
  isSWError: state.holidaysGrouping.isSWError,
  groupingData: state.holidaysGrouping.groupingData,
});

const mapDispatchToProps = (dispatch) => ({
  fetchSearchWidgetData: (searchWidgetDataMaster) =>
    dispatch(fetchSearchWidgetData(searchWidgetDataMaster)),
  fetchSearchWidgetDataWithOldData: (searchWidgetDataObj) =>
    dispatch(fetchSearchWidgetDataWithOldData(searchWidgetDataObj)),
  toggleLocationAutoComplete: (showAutoComplete) =>
    dispatch(toggleLocationAutoComplete(showAutoComplete)),
  updateDepCity: (depCity) => dispatch(updateDepCity(depCity)),
  loadPhoenixSearchPageData: () => dispatch(loadPhoenixSearchPageData()),
  setSearchWidgetRefreshRequired: (searchWidgetRefreshRequired) =>
    dispatch(setSearchWidgetRefreshRequired(searchWidgetRefreshRequired)),
});

export default connect(mapStateToProps, mapDispatchToProps)(PhoenixGroupingV2SearchPageContainer);
