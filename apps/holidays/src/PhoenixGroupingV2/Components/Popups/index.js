import React, { useState, useEffect } from 'react';
import { StyleSheet } from 'react-native';
import { connect } from 'react-redux';
import { GROUPING_LOCAL_NOTIFICATION_PAGE_NAME } from 'mobile-holidays-react-native/src/Grouping/HolidayGroupingConstants';
import { entryLocalNotification, openGenericDeeplink } from 'mobile-holidays-react-native/src/utils/HolidayUtils';
import HolidayDataHolder from 'mobile-holidays-react-native/src/utils/HolidayDataHolder';
import { PHOENIX_GROUPING_V2_POPUPS } from '../../Contants';
import { saveRecentSearch } from 'mobile-holidays-react-native/src/utils/HolidayNetworkUtils';
import {
  clearPageVisitResponses,
  sectionTrackingPageNames,
} from 'mobile-holidays-react-native/src/utils/SectionVisitTracking';
import { SELECT_CITY_POPUP_PAGES } from '../../../SearchWidget/utils/SelectCityPopUpUtil';
import {
  getDataFromStorage,
  KEY_USER_CITY_SEARCH_TYPE,
  KEY_USER_CITY_SELECTION_TYPE,
} from 'packages/legacy-commons/AppState/LocalStorage';
import {
  getSelectedGroup,
  openPhoenixPackagePageFromGroupingV2,
} from '../../Utils/PhoenixGroupingV2Utils';
import { GUIDED } from 'mobile-holidays-react-native/src/HolidayConstants';
/* Actions */
import {
  updateDepCity,
  setSearchWidgetRemove,
} from '../../../SearchWidget/Actions/HolidaySearchWidgetActions';
import {
  holidayLandingGroupDtoUpdate,
  loadGroupAndListingAction,
  fetchAvailableHubs,
  updateGroupDtoByMeta,
} from '../../Actions/groupingV2Actions';
import { resetListingState } from '../../Actions/listingPackagesActions';
import {
  getCardCategoryForPdt,
  getCardClickEventName,
  getCardClickPDTEventName,
  getCardProp1Value,
} from '../../Utils/PhoenixGroupingV2TrackingUtils';
import { isEmpty } from 'lodash';

/* Components  */
import BottomSheetOverlay from '../../../Common/Components/BottomSheetOverlay';
import PhoenixVariantModal from 'mobile-holidays-react-native/src/Grouping/Components/PhoenixVariantModal/PhoenixVariantModal';
import SelectCityPopupContainer from '../../../SearchWidget/Components/SelectCityPopupComponent';
import PhoenixGroupingV2SearchPageContainer from './SearchPageContanier';
import LocationAndRoomOverlay from './LocationAndRoomOverlay';
import { logHolidaysGroupingPDTEvents } from '../../Utils/PhoenixGroupingV2PDTTrackingUtils';
import { PDT_EVENT_TYPES } from 'mobile-holidays-react-native/src/utils/HolidayPDTConstants';
import BottomSheet from 'mobile-holidays-react-native/src/Common/Components/BottomSheet';
import MMTBlackBottomSheet from 'mobile-holidays-react-native/src/Common/Components/Membership/BottomSheet';
import { bottomSheetCtaType } from 'mobile-holidays-react-native/src/Common/Components/Membership/utils/constants';
import GenericModule from 'packages/legacy-commons/Native/GenericModule';
import { TRACKING_EVENTS } from 'mobile-holidays-react-native/src/HolidayTrackingConstants';
import HolidayPDTMetaIDHolder from '../../../utils/HolidayPDTMetaIDHolder';
const PhoenixV2Popups = ({
  /* dispatch to props */
  holidayLandingGroupDtoUpdate,
  loadGroupAndListingAction,
  updateDepCity,
  fetchAvailableHubs,
  resetListingState,
  /* dispatch to props */

  /* state to prop */
  metaDataState,
  groupMetaState,
  holidayLandingGroupDto,
  availableHubs = [],
  /* state to prop */

  /* parent stateValues */
  showFilterBottomsheet,
  quickFilter,
  popup,
  packageVariantData,
  /* parent stateValues */

  updateStateFunctions = {},
  togglePopup = null,
  setRoomDetails = null,
  roomDetails = [],
  variantTypeFromPokus = false,
  mmtBlackBottomSheetDetails = {},
  setMmtBlackBottomSheetDetails = () => {},
  selectCityPopupProps = {},
  unmountIntervention = () => {},
  trackClickEvent = () => {},
  trackViewedSectionClickEvent = () => {},
  trackErrorLocalClickEvent = () => {},
  mmtBlackBucketDetail = {},
}) => {
  const { setShowFilterBottomsheet, setQuickFilter } = updateStateFunctions || {};
  const {
    userDepCity: userDepartureCity,
    rooms,
    interventionFilterData = {},
  } = holidayLandingGroupDto || {};
  const { selectedGroupCollectionKey, groupMetaDetail } = groupMetaState || {};
  const currentSelectedGroupCollection = getSelectedGroup({
    selectedGroupKey: selectedGroupCollectionKey,
    groupMetaDetail: groupMetaDetail,
  });
  const [filterThreshold, setFilterThreshold] = useState(0);
  const [showCoachMarks, setShowCoachMarks] = useState(false);
  // const [selectCityPopupData, setSelectCityPopupData] = useState({});
  // const [showSelectCityPopUp, setShowSelectCityPopUp] = useState(false);
  const [selectedCityType, setSelectedCityType] = useState('');
  const [citySelectionType, setCitySelectionType] = useState('');
  const [pdtObj, setPdtObj] = useState({}); // Not needed now, only being used landing
  const bottomSheetFilterHeader = !isEmpty(quickFilter)
    ? quickFilter?.name
    : interventionFilterData?.header;
  const bottomSheetFilterSubHeader = !isEmpty(quickFilter) ? '' : interventionFilterData?.subHeader;
  useEffect(() => {
    getDataFromStorage(KEY_USER_CITY_SEARCH_TYPE).then((cityType) =>
      setSelectedCityType(cityType ? cityType : 'Airport'),
    );
    getDataFromStorage(KEY_USER_CITY_SELECTION_TYPE).then((selectionType) =>
      setCitySelectionType(selectionType ? selectionType : 'Auto-Detect'),
    );

    /* Call to get cities to see departure city selection */
    async function fetchAvailableHubsData() {
      await fetchAvailableHubs();
    }
    if (availableHubs.length <= 0) {
      fetchAvailableHubsData();
    }
  }, []);

  const handlePopupClose = () => {
    togglePopup({ popup: '' });
  };

  const refreshPage = (updatedHolidayLandingGroupDto) => {
    if (holidayLandingGroupDto.fromSeo) {
      window.location.href = `https://holidayz.makemytrip.com/holidays/india/group?dest=${
        holidayLandingGroupDto.destinationCity
      }&filters=${JSON.stringify(holidayLandingGroupDto.filters)}`;
    } else {
      clearPageVisitResponses({
        pages: [
          sectionTrackingPageNames.PHOENIX_GROUPING_V2_PAGE,
          sectionTrackingPageNames.PHOENIX_GROUPING_PAGE_V2_RVS,
          sectionTrackingPageNames.PHOENIX_GROUPING_PAGE_V2_RVS_V2,
        ],
      });
      loadGroupAndListingAction({
        holidayLandingGroupDto: updatedHolidayLandingGroupDto,
        metaDataResponse: metaDataState?.metaDataDetail,
        resetMetaIfNoPackageValue: metaDataState?.resetMetaIfNoPackage,
        selectedGroup: currentSelectedGroupCollection,
      }).then(() => {
        saveRecentSearch(holidayLandingGroupDto);
      });
    }
  };

  const onDone = (nextProps) => {
    if (
      (nextProps.backFromSW || nextProps.backFromInline) &&
      nextProps.holidayGroupingNewDto !== holidayLandingGroupDto
    ) {
      if (
        nextProps.holidayGroupingNewDto.destinationCity !== holidayLandingGroupDto.destinationCity
      ) {
        entryLocalNotification(
          nextProps.holidayGroupingNewDto.destinationCity,
          GROUPING_LOCAL_NOTIFICATION_PAGE_NAME,
        );
      }
      nextProps.holidayGroupingNewDto.fromSeo = holidayLandingGroupDto.fromSeo;
      const nextHolidayLandingGroupDto = {
        ...holidayLandingGroupDto,
        ...nextProps.holidayGroupingNewDto,
      };

      const updatedHolidayLandingGroupDto = !isEmpty(nextProps.newMetaResponse)
        ? updateGroupDtoByMeta({
            holidayLandingGroupDto: nextHolidayLandingGroupDto,
            metaDataResponse: nextProps.newMetaResponse,
          })
        : nextHolidayLandingGroupDto;
      HolidayPDTMetaIDHolder.getInstance().setPdtId();
      holidayLandingGroupDtoUpdate(updatedHolidayLandingGroupDto); // call action to update holidayLandingDto
      resetListingState();
      refreshPage(updatedHolidayLandingGroupDto); // need to refersh only groupMeta and Listing packages
    }
    setShowFilterBottomsheet(false);
    if (!holidayLandingGroupDto.fromSeo) {
      handlePopupClose();
      setQuickFilter(null);
      HolidayDataHolder.getInstance().setCurrentPage('holidaysGrouping');
    }
  };

  const onSWClose = () => {
    handlePopupClose();
    setQuickFilter(null);
    setShowFilterBottomsheet(false);
    HolidayDataHolder.getInstance().setCurrentPage('holidaysGrouping');
  };

  const updateShowSearchWidget = (value) => {
    handlePopupClose();
  };
  const updateShowFilterBottomsheet = (value) => setShowFilterBottomsheet(value);

  const updateHolidayLandingGroupDto = (params) => {
    holidayLandingGroupDto = {
      ...holidayLandingGroupDto,
      ...params,
    };
    holidayLandingGroupDtoUpdate(holidayLandingGroupDto);
  };

  const trackVariantModalEvents = ({ eventName = '', value = '', prop1 = '', isPremium = false }) => {
    logHolidaysGroupingPDTEvents({
      actionType: PDT_EVENT_TYPES.buttonClicked,
      value: value || eventName,
      category: getCardCategoryForPdt({ isPremium }),
    });
    trackClickEvent({ eventName, prop1 });
  };

  const onPackageClickedFromVariantModal = ({
    variant,
    data = null,
    packageDetail,
    categoryId,
    cardNo,
    length,
  }) => {
    togglePopup({ popup: '' });
    const eventName = getCardClickEventName({
      cardItem: packageDetail,
      cardIndex: packageDetail?.cardIndex,
      groupCollection: currentSelectedGroupCollection,
      sourceInfo: {
        name: 'Variant',
        key: `${variant?.variantType}_${length}_pos_${cardNo}`,
      },
    });
    const value = getCardClickPDTEventName({
      cardItem: packageDetail,
      cardIndex: packageDetail?.cardIndex,
      groupCollection: currentSelectedGroupCollection,
      sourceInfo: {
        name: 'Variant',
        key: `${variant?.variantType}_${length}_pos_${cardNo}`,
      },
    });
    const prop1 = getCardProp1Value({ cardItem: packageDetail });
    trackVariantModalEvents({ eventName, value, prop1, isPremium: packageDetail?.isPremium });
    openPhoenixPackagePageFromGroupingV2({
      holidayLandingGroupDto,
      packageCard: packageDetail,
      categoryId,
      params: data,
      rooms,
    });
  };

  const handleCloseMmtBlackPopup = () => {
    togglePopup({ popup: {} });
    setMmtBlackBottomSheetDetails({});
  };

  const handleMmtBlackTogglePopup = () => {
    const eventName = 'GC_Popop_click_close';
    handleCloseMmtBlackPopup();
    const {gcBucket = null, myCashBucket = null, effectivePriceBucket = null} = mmtBlackBucketDetail || {};
    const evar46 = `${gcBucket}|${myCashBucket}|${effectivePriceBucket}`;
    trackClickEvent({eventName, omniData: {[TRACKING_EVENTS.M_V46]: evar46}});
    logHolidaysGroupingPDTEvents({
      actionType: PDT_EVENT_TYPES.buttonClicked,
      value: eventName,
    })
  };

  const handleTermConditionClick = (url) => {
    const eventName = 'GC_Popup_Click_t&c';
    const {gcBucket = null, myCashBucket = null, effectivePriceBucket = null} = mmtBlackBucketDetail || {};
    const evar46 = `${gcBucket}|${myCashBucket}|${effectivePriceBucket}`;
    handleCloseMmtBlackPopup();
    if (url) {
      if (isMobileClient()) {
        GenericModule.openDeepLink(url);
      } else {
        openGenericDeeplink({ url });
      }
    }
    trackClickEvent({eventName, omniData: {[TRACKING_EVENTS.M_V46]: evar46}}, mmtBlackBucketDetail);
    logHolidaysGroupingPDTEvents({
      actionType: PDT_EVENT_TYPES.buttonClicked,
      value: eventName,
    })
  }

  const handleMmtBlackCtaButtonClicked = () => {
    const eventName = 'GC_Popup_Click_got_it';
    handleCloseMmtBlackPopup();
    const {gcBucket = null, myCashBucket = null, effectivePriceBucket = null} = mmtBlackBucketDetail || {};
    const evar46 = `${gcBucket}|${myCashBucket}|${effectivePriceBucket}`;
    trackClickEvent({eventName, omniData: {[TRACKING_EVENTS.M_V46]: evar46}}, mmtBlackBucketDetail);
    logHolidaysGroupingPDTEvents({
      actionType: PDT_EVENT_TYPES.buttonClicked,
      value: eventName,
    })
  };

  const handleVariantModalClose = () => {
    handlePopupClose();
    trackVariantModalEvents({ eventName: 'back_variantcard' });
  };
  return (
    <>
      {popup === PHOENIX_GROUPING_V2_POPUPS.MMT_BLACK_BOTTOMSHEET &&
        Object.keys(mmtBlackBottomSheetDetails).length > 0 && (
          <BottomSheet onBackPressed={handleMmtBlackTogglePopup} containerStyle={{ padding: 0 }}>
            <MMTBlackBottomSheet
              togglePopup={handleMmtBlackTogglePopup}
              bottomSheetDetail={mmtBlackBottomSheetDetails}
              ctaButtonClick={handleMmtBlackCtaButtonClicked}
              mmtBlackPdtEvents={logHolidaysGroupingPDTEvents}
              {...{handleTermConditionClick, trackClickEvent, mmtBlackBucketDetail}}
            />
          </BottomSheet>
        )}
      {(showFilterBottomsheet || popup === PHOENIX_GROUPING_V2_POPUPS.FILTER_PAGE) && (
        <PhoenixGroupingV2SearchPageContainer
          onDone={onDone}
          onSWClose={onSWClose}
          unmountIntervention={unmountIntervention}
          stateValues={{
            filterThreshold,
            showCoachMarks,
            quickFilter,
            showFilterBottomsheet,
          }}
          visibleOn={
            showFilterBottomsheet
              ? interventionFilterData?.metaData?.visibleOn || GUIDED
              : undefined
          }
          isBottomSheet={showFilterBottomsheet}
          filterHeader={bottomSheetFilterHeader} // needed to show on bottomsheet filter view
          filterSubHeader={bottomSheetFilterSubHeader} // needed to show on bottomsheet filter view
          masterMetaResponse={metaDataState.metaDataDetail}
          holidayLandingGroupDto={holidayLandingGroupDto}
          trackClickEvent={trackClickEvent}
          trackViewedSectionClickEvent={trackViewedSectionClickEvent}
          trackErrorLocalClickEvent={trackErrorLocalClickEvent}
          updateShowFilterBottomsheet={updateShowFilterBottomsheet}
          updateShowSearchWidget={updateShowSearchWidget}
          // updateMasterMetaRepsonse={() => {}} not needed
        />
      )}
      <LocationAndRoomOverlay
        togglePopup={togglePopup}
        popup={popup}
        setPdtObj={setPdtObj}
        roomDetails={roomDetails}
        setRoomDetails={setRoomDetails}
        citySelectionType={citySelectionType}
        selectedCityType={selectedCityType}
        setSelectedCityType={setSelectedCityType}
        setCitySelectionType={setCitySelectionType}
        trackClickEvent={trackClickEvent}
        trackViewedSectionClickEvent={trackViewedSectionClickEvent}
      />
      {popup === PHOENIX_GROUPING_V2_POPUPS.VARIANT && (
        <BottomSheetOverlay
          visible={popup === PHOENIX_GROUPING_V2_POPUPS.VARIANT}
          toggleModal={handleVariantModalClose}
          containerStyles={styles.bottomsheetContainer}
          headingTextStyle={{ flex: 1 }}
          title={packageVariantData?.packageDetail?.name}
        >
          <PhoenixVariantModal
            packageVariantData={packageVariantData}
            close={handleVariantModalClose}
            variantTypeFromPokus={variantTypeFromPokus}
            onPackageClickedFromVariantModal={onPackageClickedFromVariantModal}
            departureCity={userDepartureCity}
            rooms={rooms}
          />
        </BottomSheetOverlay>
      )}
      <SelectCityPopupContainer
        pdtObj={pdtObj}
        setPdtObj={setPdtObj}
        setCitySelectionType={setCitySelectionType}
        setSelectedCityType={setSelectedCityType}
        setSearchWidgetRemove={setSearchWidgetRemove}
        holidayLandingGroupDto={holidayLandingGroupDto}
        onDone={onDone}
        updateHoldidayLandingGroupDto={updateHolidayLandingGroupDto}
        updateDepCity={updateDepCity}
        trackLocalClickEvent={trackClickEvent}
        trackPDTV3Event={logHolidaysGroupingPDTEvents}
        pageName={SELECT_CITY_POPUP_PAGES.GROUPING}
      />
    </>
  );
};

const styles = StyleSheet.create({
  bottomsheetContainer: {
    padding: 20,
    paddingBottom: 40,
  },
});

const mapStateToProps = (state) => {
  return {
    metaDataState: state.holidaysPhoenixGroupingV2.metaDataDetail,
    holidayLandingGroupDto:
      state.holidaysPhoenixGroupingV2.holidayLandingGroupReducer.holidayLandingGroupDto,
    availableHubs: state.holidaysSearchWidget?.availableHubs,
    groupMetaState: state.holidaysPhoenixGroupingV2.groupMetaDetail,
  };
};

const mapDispatchToProps = {
  holidayLandingGroupDtoUpdate,
  loadGroupAndListingAction,
  updateDepCity,
  setSearchWidgetRemove,
  fetchAvailableHubs,
  resetListingState,
};
export default connect(mapStateToProps, mapDispatchToProps)(PhoenixV2Popups);
