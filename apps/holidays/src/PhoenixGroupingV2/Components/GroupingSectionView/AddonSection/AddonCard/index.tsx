import React from 'react';
import { marginStyles } from 'mobile-holidays-react-native/src/Styles/Spacing';
import { Dimensions, StyleSheet, View, ViewStyle } from 'react-native';
import TagComponent from './TagComponent';
import CardContent from './CardContent';
import { AddonCardTypes } from '../AddonSectionType';

const getMobileWidth = () => Dimensions.get('window').width - 32; // margin both sides 16*2

const getCardStyle = (isSingleCard?: boolean): ViewStyle => ({
  width: isSingleCard ? getMobileWidth() : getMobileWidth() - 40,
  ...styles.cardContainer,
});


const AddonCard = (props: AddonCardTypes) => {
  return (
    <View style={getCardStyle(props?.isSingleCard)}>
      <TagComponent tag={props.tag} />
      <CardContent {...props} />
    </View>
  );
};
const styles = StyleSheet.create({
  cardContainer: {
    ...marginStyles.mr16,
  },
});
export default AddonCard;
