import { PDT_EVENT_TYPES } from "mobile-holidays-react-native/src/utils/HolidayPDTConstants";
import { HOLIDAY_ROUTE_KEYS } from "../../../../Navigation";
import { logHolidaysGroupingPDTEvents } from "../../../Utils/PhoenixGroupingV2PDTTrackingUtils";
import { EventDetailsType, TrackAddonClickEventType } from "./AddonSectionType";
import { COMMON_OVERLAYS } from "@mmt/holidays/src/Common/Components/CommonOverlay";

export const ADDON_TYPE = {
    VPP: 'VISA_PROTECTION_PLAN',
}
 
const noop = () => {};
const defaultEventDetails = { eventName: '', suffix: '' };

export const trackAddonClickEvent = (props: TrackAddonClickEventType) => {
    const { trackClickEvent =  noop, eventDetails = defaultEventDetails } = props;
    const { eventName = '', suffix = '' } = eventDetails as EventDetailsType;
    logHolidaysGroupingPDTEvents({
        value: eventName + suffix,
        actionType: PDT_EVENT_TYPES.buttonClicked,
    });
    trackClickEvent(eventDetails);
}

const PAGE_ROOT_KEYS = {
    [ADDON_TYPE.VPP]: HOLIDAY_ROUTE_KEYS.VISA_PROTECTION_DETAILS
};

const OVERLAY_TYPE_KEYS = {
    [ADDON_TYPE.VPP]: COMMON_OVERLAYS.VPP_OVERLAY
}

export const getPageRootKey = (type: string) => {
    return PAGE_ROOT_KEYS[type] || '';
};

export const getOverLayeKey = (type: string) => {
    return OVERLAY_TYPE_KEYS[type] || '';
};