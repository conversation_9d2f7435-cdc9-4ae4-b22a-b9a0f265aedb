export type AddonCardTypes = {
    header: string;
    id: string;
    subHeader: string;
    isSingleCard?: boolean;
    tag: string;
    iconUrl: string;
    showOverlay?: Function;
    hideOverlay?: Function;
trackClickEvent: (eventDetails: EventDetailsType) => void;
    cta: {
        onclick: string;
        key: string;
        type: string;
    };
    description: {
        text: string;
        colour: string;
        bold: boolean;
    }[];
}

export type RenderAddonCardTypes = {
    item: AddonCardTypes;
    index: number;
}

export type TagProps = {
    tag: string;
}

export type AddonSectionProps = {
    cards: AddonCardTypes[];
trackClickEvent: (eventDetails: EventDetailsType) => void;
    type: string;
    tag: string;
    header: string;
    cardIndex: number;
    showOverlay?: Function;
    hideOverlay?: Function;
}

export type CardDescriptionProps = {
    header: string;
    subHeader: string;
    description: {
        text: string;
        colour: string;
        bold: boolean;
    }[];
}

export type ActionComponentProps = {
    cta: {
        onclick: string;
        key: string;
        type: string;
    };
}

export type EventDetailsType = {
    eventName: string;
    suffix: string;
}
export type DescriptionProps = {
    description: DescriptionItemProps[];
}

export type DescriptionItemProps = {
    text: string;
    colour: string;
    bold: boolean;
}

export type HandlePressType = {
    type: string;
    trackClickEvent: (eventDetails: EventDetailsType) => void;
    showOverlay?: Function;
    hideOverlay?: Function;
}


export type TrackAddonClickEventType = {
trackClickEvent: (eventDetails: EventDetailsType) => void;
    eventDetails: EventDetailsType,
}
