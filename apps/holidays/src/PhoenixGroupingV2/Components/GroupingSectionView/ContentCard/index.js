import React, { useState } from 'react';
import { TouchableOpacity ,NativeModules } from 'react-native';
import { connect } from 'react-redux';
import { handleDeeplink } from '../../../Utils/PhoenixGroupingSectionCardClickHandler';
import { getSelectedGroup } from '../../../Utils/PhoenixGroupingV2Utils';
import { isValidURL } from '@mmt/legacy-commons/Helpers/validationHelpers';
import { convertUrlToHttps } from 'mobile-holidays-react-native/src/utils/HolidayNetworkUtils';
import { marginStyles } from 'mobile-holidays-react-native/src/Styles/Spacing';
import { getScreenWidth } from 'mobile-holidays-react-native/src/utils/HolidayUtils';
import { isEmpty } from 'lodash';
import queryString from 'query-string';

/* Components */
import ContentCardModalContainer from 'mobile-holidays-react-native/src/Common/Components/ContentCardModal/ContentCardModalContainer';
import HolidayImageHolder from '../../../../Common/Components/HolidayImageHolder';


function getCardDimensions() {
  const CONTENT_CARD_WIDTH = 328;
  const CONTENT_CARD_HEIGHT = 198;
  const MARGIN_HORIZONTAL_SPACE = 38;
  const aspectRatio = CONTENT_CARD_WIDTH / CONTENT_CARD_HEIGHT;

  const width = getScreenWidth() - MARGIN_HORIZONTAL_SPACE;
  const height = width / aspectRatio;

  return { width, height };
}
const ContentCard = ({
  data,
  trackClickEvent = () => {},
  fabCta,
  index = '',
  groupMetaState,
  trackViewedSectionClickEvent = () => {},
}) => {
  const [openContentCardModal, setOpenContentCardModal] = useState(false);
  const {
    imageUrl = '',
    deepLink = '',
    id = '',
  } = data || {};

  const imageUrlEmpty = isEmpty(imageUrl);
  const deepLinkEmpty = isEmpty(deepLink);
  if (imageUrlEmpty || deepLinkEmpty) {
    return null;
  }

  const { groupMetaDetail = {}, selectedGroupCollectionKey = '' } = groupMetaState || {};

  const { query } = queryString.parseUrl(deepLink);
  const openPopUp = !isEmpty(query?.profileId) && !isEmpty(query?.pop);

  const closeModal = () => {
    setOpenContentCardModal(false);
  };
  const group = getSelectedGroup({ selectedGroupKey: selectedGroupCollectionKey, groupMetaDetail });
  const handleContentCardDeeplink = (deepLinkUrl, destination) => {
    let card = {
      deeplink: deepLinkUrl,
      dynamic: false,
      destination: destination,
      disableTracking: true,
    };
    handleDeeplink(card, group);
  };

  const onContentCardClick = () => {
    const { index: groupIndex, name, id: groupId = '' } = group || {};
    if (isValidURL(deepLink)) {
      if (openPopUp) {
        setOpenContentCardModal();
      } else if (deepLink?.includes('.pdf')) {
        const eventName = `CardClick|Group_${groupIndex}_${groupId}_${name}|Content_${index}_${id}`;
        trackViewedSectionClickEvent();
        trackClickEvent({ eventName });
        const {HolidayModule} = NativeModules;
        HolidayModule.openWebView({
          url: deepLink,
        });
      } else {
        const eventName = `CardClick|Group_${groupIndex}_${groupId}_${name}|Content_${index}_${id}`;
        trackViewedSectionClickEvent();
        trackClickEvent({ eventName });
        handleContentCardDeeplink(deepLink, eventName);
      }
    }
  };

  return (
    <>
      <TouchableOpacity onPress={onContentCardClick} activeOpacity={1} style={marginStyles.mb16}>
        <HolidayImageHolder
          imageUrl={convertUrlToHttps(imageUrl)}
          style={getCardDimensions()}
          resizeMode={'cover'}
        />
      </TouchableOpacity>
      <ContentCardModalContainer
        modalVisible={openContentCardModal}
        closeModal={closeModal}
        profileId={query?.profileId}
      />
    </>
  );
};

const mapStateToProps = (state) => {
  return {
    fabCta: state.holidaysPhoenixGroupingV2.fabCtaDetail?.fabCtaDetail,
    holidayLandingGroupDto:
      state.holidaysPhoenixGroupingV2.holidayLandingGroupReducer.holidayLandingGroupDto,
    groupMetaState: state.holidaysPhoenixGroupingV2.groupMetaDetail,
  };
};

export default connect(mapStateToProps)(ContentCard);
