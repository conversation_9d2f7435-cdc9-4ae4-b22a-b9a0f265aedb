import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { holidayColors } from 'mobile-holidays-react-native/src/Styles/holidayColors';
import { fontStyles } from 'mobile-holidays-react-native/src/Styles/holidayFonts';
import { marginStyles, paddingStyles } from 'mobile-holidays-react-native/src/Styles/Spacing';

const PackageDuration = ({ destinationList = [] }) => {
  const renderBulletPoints = () => {
    let renderedBulletPoints = [];
    let totalChars = 0;
    let remainingBulletPoints = destinationList.length;
    let displayMore = false;

    for (let i = 0; i < destinationList.length; i++) {
      const bulletPoint = destinationList[i];
      const bulletPointLength = bulletPoint.length + 2;

      if (totalChars + bulletPointLength <= 80 && remainingBulletPoints > 0) {
        const bulletStyle = styles.bulletText;

        renderedBulletPoints.push(
          <View key={`${bulletPoint}-${i}`} style={styles.durationItem}>
            <Text style={styles.bulletImage}>{'\u2022'}</Text>
            <Text style={bulletStyle}>{bulletPoint}</Text>
          </View>,
        );
        totalChars += bulletPointLength;
        remainingBulletPoints--;
      } else {
        displayMore = true;
        break;
      }
    }

    if (displayMore) {
      renderedBulletPoints.push(
        <View key={`${remainingBulletPoints}-more`} style={styles.durationItem}>
          <Text style={[styles.bulletImage]}>{'\u2022'}</Text>
          <Text style={styles.moreText}>+ {remainingBulletPoints} More</Text>
        </View>,
      );
    }
    return renderedBulletPoints;
  };

  return (
    <View style={styles.container}>
      <View style={styles.durationItemsContainer}>{renderBulletPoints()}</View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    borderColor: holidayColors.grayBorder,
    borderBottomWidth: 1,
    ...paddingStyles.pb10,
  },
  durationItemsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    alignItems: 'center',
  },
  durationItem: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  bulletImage: {
    ...fontStyles.labelSmallRegular,
    color: holidayColors.gray,
    marginRight: 6,
  },
  bulletText: {
    ...fontStyles.labelSmallRegular,
    color: holidayColors.gray,
    marginRight: 6,
  },
  listingText: {
    ...fontStyles.labelSmallRegular,
    color: holidayColors.gray,
    ...marginStyles.mr10,
  },
  moreText: {
    ...fontStyles.labelSmallRegular,
    color: holidayColors.gray,
  },
});

export default PackageDuration;
