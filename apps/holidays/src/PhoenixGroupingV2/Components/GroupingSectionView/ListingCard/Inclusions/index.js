import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { holidayBorderRadius } from 'mobile-holidays-react-native/src/Styles/holidayBorderRadius';
import { holidayColors } from 'mobile-holidays-react-native/src/Styles/holidayColors';
import { fontStyles } from 'mobile-holidays-react-native/src/Styles/holidayFonts';
import { marginStyles } from 'mobile-holidays-react-native/src/Styles/Spacing';

const Inclusions = ({ inclusions }) => {
  const middleIndex = Math.ceil(inclusions.length / 2);
  const column1 = inclusions.slice(0, middleIndex);
  const column2 = inclusions.slice(middleIndex);

  const renderColumn = (columnData) => {
    return columnData.map((item, index) => (
      <View key={index} style={styles.inclusionContainer}>
        <View style={styles.bulletImage} />
        <Text style={styles.inclusionText}>{item}</Text>
      </View>
    ));
  };

  return (
    <View style={styles.container}>
      <View style={styles.column}>{renderColumn(column1)}</View>
      <View style={styles.column}>{renderColumn(column2)}</View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    ...marginStyles.mt8,
  },
  inclusionContainer: {
    flexDirection: 'row',
    alignSelf: 'flex-start',
  },
  column: { flex: 1 },
  bulletImage: {
    backgroundColor: holidayColors.lightGray,
    ...holidayBorderRadius.borderRadius2,
    ...marginStyles.mr8,
    height: 4,
    width: 4,
    alignSelf: 'flex-start',
    marginTop: 7,
  },
  inclusionText: {
    color: holidayColors.gray,
    ...fontStyles.labelSmallRegular,
    lineHeight: 17,
    flex: 1,
    ...marginStyles.mr6,
    alignSelf: 'flex-start',
  },
});
export default Inclusions;
