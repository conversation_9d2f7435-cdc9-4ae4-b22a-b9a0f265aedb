import React from 'react';
import { View, StyleSheet } from 'react-native';
import { holidayColors } from 'mobile-holidays-react-native/src/Styles/holidayColors';
import { marginStyles, paddingStyles } from 'mobile-holidays-react-native/src/Styles/Spacing';
import {
  borderRadiusValues,
  holidayBorderRadius,
} from 'mobile-holidays-react-native/src/Styles/holidayBorderRadius';
import ShimmerCommon from '@Frontend_Ui_Lib_App/Shimmer';
const DestinationLoader = () => {
  const widths = [
    styles.width100,
    styles.width80,
    styles.width90,
    styles.width60,
    styles.width100,
    styles.width50,
  ];

  return widths.map((width,index) => (
    <View style={[styles.animatedOuter, styles.inclusionStyle, width]} key={`index-${index}-destination`}>
        <ShimmerCommon animatedInnerColor={holidayColors.lightGray4} animationRange={holidayColors.lightGray5} />
    </View>
  ));
};

const InclusionLoader = () => {
  const inclusionRows = [0, 1, 2];
  const widths = {
    0: 'widthFull',
    1: 'widthHalf',
    2: 'widthHalf',
  };
  return inclusionRows.map((item) => (
    <View style={styles.bulletRow} key={`index-${item}`}>
      <View
        style={[
          styles.animatedOuter,
          styles[widths[item]],
          styles.flexOne,
          styles.height14,
          marginStyles.mr20,
        ]}
      >
        <ShimmerCommon animatedInnerColor={holidayColors.lightGray4} animationRange={holidayColors.lightGray5} />
      </View>
      {item !== 2 && (
        <View style={[styles.animatedOuter, styles.widthFull, styles.flexOne, styles.height14]}>
            <ShimmerCommon animatedInnerColor={holidayColors.lightGray4} animationRange={holidayColors.lightGray5} />
        </View>
      )}
    </View>
  ));
};

const ActiveInclusion = () => (
  <View style={styles.activeInclusion}>
    <View
      style={[
        styles.animatedOuter,
        styles.width75,
        styles.height12,
        styles.height12,
        marginStyles.mb6,
      ]}
    >
        <ShimmerCommon animatedInnerColor={holidayColors.lightGray4} animationRange={holidayColors.lightGray5} />
    </View>
    <View style={[styles.animatedOuter, styles.widthHalf, styles.height12]}>
      <ShimmerCommon animatedInnerColor={holidayColors.lightGray4} animationRange={holidayColors.lightGray5} />
    </View>
  </View>
);
const PriceSection = () => (
  <View style={styles.priceBox}>
    <View style={styles.priceContainer}>
      <View style={styles.flexOne}>
        <View style={[styles.animatedOuter, styles.widthFull, styles.height12, marginStyles.mb4]}>
          <ShimmerCommon animatedInnerColor={holidayColors.lightGray4} animationRange={holidayColors.lightGray5} />
        </View>
        <View style={[styles.animatedOuter, styles.widthHalf, styles.height12]}>
          <ShimmerCommon animatedInnerColor={holidayColors.lightGray4} animationRange={holidayColors.lightGray5} />
        </View>
      </View>
      <View style={[styles.flexOne, marginStyles.ml10, styles.alignEnd]}>
        <View style={[styles.animatedOuter, styles.widthFull, styles.height12, marginStyles.mb4]}>
          <ShimmerCommon animatedInnerColor={holidayColors.lightGray4} animationRange={holidayColors.lightGray5} />
        </View>
        <View style={[styles.animatedOuter, styles.width75, styles.height12]}>
          <ShimmerCommon animatedInnerColor={holidayColors.lightGray4} animationRange={holidayColors.lightGray5} />
        </View>
      </View>
    </View>
  </View>
);
const CardLoader = () => {
  return (
    <View style={styles.listingCard}>
      <View style={[styles.animatedOuter, styles.imageStyle]}>
        <ShimmerCommon animatedInnerColor={holidayColors.lightGray4} animationRange={holidayColors.lightGray5} />
      </View>
      <View style={styles.contentContainer}>
        <View style={styles.packageNameContainer}>
          <View style={[styles.animatedOuter, styles.width75, styles.height20, marginStyles.mr10]}>
            <ShimmerCommon animatedInnerColor={holidayColors.lightGray4} animationRange={holidayColors.lightGray5} />
          </View>
          <View style={[styles.animatedOuter, styles.width70, styles.height20]}>
            <ShimmerCommon animatedInnerColor={holidayColors.lightGray4} animationRange={holidayColors.lightGray5} />
          </View>
        </View>
      </View>
      <View style={styles.inclusion}>
        <DestinationLoader />
      </View>
      <View style={styles.bulletPoints}>
        <InclusionLoader />
      </View>
      <ActiveInclusion />
      <PriceSection />
    </View>
  );
};

const ListingCardsLoader = () => {
  return [0, 1, 2].map((item) => <CardLoader key={`index-${item}`} />);
};
const styles = StyleSheet.create({
  width100: { width: 100 },
  width90: { width: 90 },
  width80: { width: 80 },
  width70: { width: 70 },
  width60: { width: 60 },
  width50: { width: 50 },
  height12: { height: 14 },
  height13: { height: 13 },
  height14: { height: 14 },
  height20: { height: 20 },
  animatedOuter: {
    backgroundColor: '#e6e6e6',
    position: 'relative',
    overflow: 'hidden',
    ...holidayBorderRadius.borderRadius2,
  },
  widthFull: {
    width: '100%',
  },
  width75: {
    width: '75%',
  },
  widthHalf: {
    width: '50%',
  },
  contentTab: {
    ...paddingStyles.ph14,
    flex: 1,
    ...marginStyles.mb12,
  },
  listingCard: {
    ...holidayBorderRadius.borderRadius16,
    backgroundColor: holidayColors.white,
    borderWidth: 1,
    borderColor: holidayColors.grayBorder,
    ...marginStyles.mh12,
    ...marginStyles.mv10,
  },
  imageStyle: {
    width: '100%',
    height: 152,
    borderTopLeftRadius: borderRadiusValues.br16,
    borderTopRightRadius: borderRadiusValues.br16,
  },
  contentContainer: {
    ...paddingStyles.ph16,
    ...marginStyles.mt14,
    flex: 1,
  },
  packageNameContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    ...marginStyles.mb2,
  },
  durationBox: {
    ...paddingStyles.ph4,
    backgroundColor: holidayColors.lightGray,
    ...holidayBorderRadius.borderRadius4,
    borderWidth: 1,
    borderColor: holidayColors.darkBlue,
  },

  inclusion: {
    ...paddingStyles.ph16,
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    flexWrap: 'wrap',
    ...paddingStyles.pb10,
    borderBottomWidth: 1,
    borderBottomColor: '#d8d8d8',
  },
  inclusionStyle: {
    ...marginStyles.mr10,
    ...marginStyles.mb6,
    height: 13,
  },
  bulletPoints: {
    ...paddingStyles.ph16,
    ...paddingStyles.pt10,
  },
  bulletRow: {
    justifyContent: 'space-between',
    flexDirection: 'row',
    alignItems: 'center',
    ...marginStyles.mb8,
  },
  activeInclusionLong: {},
  activeInclusion: {
    ...marginStyles.mb12,
    ...paddingStyles.ph16,
    ...paddingStyles.pt6,
  },
  priceBox: {
    ...marginStyles.mh14,
    ...marginStyles.mb14,
  },
  priceContainer: {
    ...paddingStyles.ph16,
    ...paddingStyles.pv10,
    ...holidayBorderRadius.borderRadius8,
    backgroundColor: holidayColors.lightGray2,
    borderColor: holidayColors.disableGrayBg,
    borderWidth: 1,
    width: '100%',
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  flexOne: { flex: 1 },
  alignEnd: {
    alignItems: 'flex-end',
  },
});

export default ListingCardsLoader;
