import ExpiryTimer, { TIMER_DESIGN_TYPES } from 'mobile-holidays-react-native/src/ExpiryTimer';
import { holidayBorderRadius } from 'mobile-holidays-react-native/src/Styles/holidayBorderRadius';
import { holidayColors } from 'mobile-holidays-react-native/src/Styles/holidayColors';
import { fontStyles } from 'mobile-holidays-react-native/src/Styles/holidayFonts';
import { marginStyles, paddingStyles } from 'mobile-holidays-react-native/src/Styles/Spacing';
import React from 'react';
import { View, Text, StyleSheet } from 'react-native';

const PriceDetails = ({ listingPricingDetails }) => {
  const { pricePersuasion = [], priceInfo = [], urgencyPersuasion = {} } =
    listingPricingDetails || {};

  const { backgroundColor = '', borderColor = '', fontColor = '', info = [], timer = {} } =
    urgencyPersuasion || {};

  const { info: timerInfo, timelines = {} } = timer || {};
  const isTimerAvailable =
    timer?.info?.length > 0 && Object.keys(timer?.timelines || {})?.length > 0;
  const showUrgencyPersuasions = info.length > 0 || isTimerAvailable;
  const heightStyle = showUrgencyPersuasions ? 'marginHeight' : '';
  const paddingStyle = showUrgencyPersuasions ? 'padding24' : 'padding12';
  const renderUrgencyPersuasions = () => {
    if (!showUrgencyPersuasions) {
      return null;
    }
    return (
      <View
        style={[
          styles.messageContainer,
          {
            backgroundColor: backgroundColor || holidayColors.fadedGreen,
            borderColor: borderColor || holidayColors.grayBorder,
          },
        ]}
      >
        <Text style={styles.infoContainer} numberOfLines={1}>
          {info.length > 0 && (
            <Text numberOfLines={1}>
              {info?.map((infoItem, index) => (
                <Text
                  style={[
                    infoItem.isEmphasized ? styles.infoTextBold : styles.infoText,
                    { color: fontColor || holidayColors.gray },
                  ]}
                  key={`info-item-${index}`}
                >
                  {infoItem?.text}{' '}
                </Text>
              ))}
            </Text>
          )}
        </Text>
        <ExpiryTimer
          {...timelines}
          startText={timerInfo?.[0]?.startText}
          endText={timerInfo?.[0]?.endText}
          type={TIMER_DESIGN_TYPES.CHIP_DESIGN}
          showTimerWithoutPokus
        />
      </View>
    );
  };
  return (
    <View style={styles.container}>
      {renderUrgencyPersuasions()}
      <View style={[styles.priceContainer, styles[heightStyle], styles[paddingStyle]]}>
        <View style={styles.leftContainer}>
          <Text style={styles.priceText}>
            {pricePersuasion?.map((item, index) => (
              <Text
                style={item.isEmphasized && styles.messageTextBlack}
                key={`id-persusasion-${index}`}
              >
                {item.text}{' '}
              </Text>
            ))}
          </Text>
        </View>

        <View style={styles.rightContainer}>
          {priceInfo.map((priceItem, index) => (
            <Text style={styles.price} key={`id-price-${index}`}>
              {priceItem.map((item, index) => (
                <Text style={item.isEmphasized && styles.priceBlack} key={`id-priceItem-${index}`}>
                  {item?.text}{' '}
                </Text>
              ))}
            </Text>
          ))}
        </View>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    alignItems: 'center',
    justifyContent: 'center',
    ...marginStyles.mt10,
  },
  priceContainer: {
    ...paddingStyles.ph16,
    ...paddingStyles.pb10,
    ...holidayBorderRadius.borderRadius8,
    backgroundColor: holidayColors.lightGray2,
    borderColor: holidayColors.disableGrayBg,
    borderWidth: 1,
    width: '100%',
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  padding12: {
    ...paddingStyles.pt12,
  },
  padding24: {
    ...paddingStyles.pt24,
  },
  messageContainer: {
    top: 0,
    backgroundColor: holidayColors.fadedGreen,
    borderWidth: 1,
    borderColor: holidayColors.grayBorder,
    borderRadius: 38,
    ...paddingStyles.pt4,
    ...paddingStyles.pb6,
    ...paddingStyles.ph20,
    alignItems: 'center',
    zIndex: 2,
    flexDirection: 'row',
    maxWidth: '100%',
    overflow: 'hidden',
  },
  infoContainer: {
    alignItems: 'center',
    flexShrink: 1,
  },
  expiryContainer: {
    ...marginStyles.ml6,
  },
  infoText: {
    ...fontStyles.labelSmallRegular,
  },
  infoTextBlack: {
    ...fontStyles.labelSmallBlack,
  },
  infoTextBold: {
    ...fontStyles.labelSmallBold,
  },
  messageText: {
    ...fontStyles.labelSmallRegular,
    color: holidayColors.gray,
  },
  messageTextBlack: {
    ...fontStyles.labelSmallBlack,
    color: holidayColors.gray,
  },
  leftContainer: {
    flex: 1,
    ...marginStyles.mr10,
  },
  priceText: {
    ...fontStyles.labelSmallRegular,
    color: holidayColors.black,
    lineHeight: 16,
  },
  boldFont: {
    fontWeight: '700',
  },
  rightContainer: {
    flex: 1,
  },
  price: {
    textAlign: 'right',
    ...fontStyles.labelSmallRegular,
    color: holidayColors.lightGray,
  },
  priceBlack: {
    ...fontStyles.labelLargeBlack,
    lineHeight: 22,
    color: holidayColors.black,
  },
  marginHeight: {
    marginTop: -15,
  },
});
export default PriceDetails;
