import React, { Fragment, useEffect } from 'react';
import { connect } from 'react-redux';
import { View, Text, StyleSheet, TouchableOpacity, Image,Platform } from 'react-native';
import {
  borderRadiusValues,
  holidayBorderRadius,
} from 'mobile-holidays-react-native/src/Styles/holidayBorderRadius';
import { holidayColors } from 'mobile-holidays-react-native/src/Styles/holidayColors';
import { fontStyles } from 'mobile-holidays-react-native/src/Styles/holidayFonts';
import { marginStyles, paddingStyles } from 'mobile-holidays-react-native/src/Styles/Spacing';
import {
  createDestinationList,
  createPackageHighlightList,
  getDestinationCityName,
  getSelectedGroup,
} from '../../../Utils/PhoenixGroupingV2Utils';
import { PHOENIX_GROUPING_V2_CARD_SECTION_LIMIT } from '../../../Contants';
import { convertUrlToHttps } from 'mobile-holidays-react-native/src/utils/HolidayNetworkUtils';

/* Components */
import PackageDuration from './PackageDuration';
import ImageCarousal from 'mobile-holidays-react-native/src/Common/Components/ImageCarousal';
import ActiveInclusions from './ActiveInclusion';
import Inclusions from './Inclusions';
import PriceDetails from './PriceDetails';
import HolidayImageHolder from 'mobile-holidays-react-native/src/Common/Components/HolidayImageHolder';
import OtherPromos from './OtherPromos';
import { variantEnum } from 'mobile-holidays-react-native/src/HolidayConstants';
import { groupingCollectionVisits, viewedListingCardLastIndex } from '../../../Utils/SectionTrackingUtils';
import { logHolidaysGroupingPDTEvents } from '../../../Utils/PhoenixGroupingV2PDTTrackingUtils';
import { PDT_EVENT_TYPES } from 'mobile-holidays-react-native/src/utils/HolidayPDTConstants';
import ListingCardHeader from './ListingCardHeader';
import { getCardCategoryForPdt, getCardProp1Value } from '../../../Utils/PhoenixGroupingV2TrackingUtils';

const OPTIONS_AVAILABLE = 'Options';
const OPTION_AVAILABLE = 'Option';

const checkHotelsOthers = (val)=>{
  return variantEnum.HOTELS === val || variantEnum.OTHERS === val;
};
const ListingCard = ({
  item,
  setPackageVariant,
  variantTypeFromPokus,
  handleOpenPackageClick,
  index,
  holidayLandingGroupDto = {},
  trackClickEvent = () => {},
  groupMetaState = {},
}) => {
  const {
    packageDetails = {},
    noPackageFoundResetFilter = false,
    selectedGroupCollectionIndex = 0,
  } = item || {};
  const {
    cardImages = [],
    name = '',
    id = '',
    inclusions = [],
    highlightsData = [],
    noOfNights = 0,
    destinationDetails,
    listingPricingDetails = {},
    tags = [],
    listingPackageVariantsDetails,
  } = packageDetails || {};
  const tagImage = convertUrlToHttps(tags?.[0]?.imageUrl) || '';
  const highlights = createPackageHighlightList(highlightsData) || [];
  const destinationList = createDestinationList(destinationDetails) || {};
  const isVariantAvailable = Boolean(
    variantTypeFromPokus &&
      (listingPackageVariantsDetails?.packageVariants?.length || 0) > 1 &&
      checkHotelsOthers(variantTypeFromPokus),
  );

  const destinationCityName = getDestinationCityName(holidayLandingGroupDto?.destinationCityData)

  const trackFirstCardRenederedEvents = () => {
    const collectionVisit = `Viewed_Collection_${selectedGroupCollectionIndex}`;
    if (index === 0 && !groupingCollectionVisits[collectionVisit]) {
      const eventName = 'Viewed_Package_0';
      groupingCollectionVisits[collectionVisit] = 1;
      const eventDetails = {
        eventName,
        prop1: collectionVisit,
      };
      logHolidaysGroupingPDTEvents({
        actionType : PDT_EVENT_TYPES.contentSeen,
        value : eventName + '|' + collectionVisit, 
        shouldTrackToAdobe:false
      })
      trackClickEvent(eventDetails);
    }
  }
 // This useEffect is to show that first card of collection is rendered 
  useEffect(() => {
    trackFirstCardRenederedEvents();
  }, []);
  const handlePackageClick = () => {
    if (isVariantAvailable) {
      viewPackages({ index });
    } else {
      handleOpenPackageClick({ item: packageDetails , sourceInfo: { noPackageFoundResetFilter } });
    }
  };

  const trackVarientClickEvents = ({eventName = '',suffix = '', prop1 = '', isPremium = false}) => {
    logHolidaysGroupingPDTEvents({
      actionType : PDT_EVENT_TYPES.contentClicked,
      value : `${suffix}|${eventName}_selection`,
      category: getCardCategoryForPdt({ isPremium })
    })
    trackClickEvent({ eventName, prop1 });
  }
  const viewPackages = ({ groupName, groupIndex, index } = {}) => {
    const { variantType = '', packageVariants = [] } = listingPackageVariantsDetails || {};
    const eventName = `Variant_${variantType}_${packageVariants?.length}`
    const pdtEvent = `CardClick|package_${packageDetails?.cardIndex}_collection_${selectedGroupCollectionIndex}_${packageDetails?.id}`
    const prop1 = getCardProp1Value({cardItem: packageDetails});
    trackVarientClickEvents({
      eventName,
      suffix: pdtEvent,
      prop1,
      isPremium: packageDetails?.isPremium,
    });
    setPackageVariant({
      data: listingPackageVariantsDetails,
      packageDetail: packageDetails,
      groupName,
      groupIndex,
      index,
      variantTypeFromPokus: variantTypeFromPokus,
    });
  };

  const renderVariant = () => {
    if (!isVariantAvailable) {
      return [];
    }
    const variantLength = listingPackageVariantsDetails?.packageVariants?.length || 0;
    const text = `${variantLength - 1} More ${
      variantLength - 1 > 1 ? OPTIONS_AVAILABLE : OPTION_AVAILABLE
    }`;
    return (
      <View style={styles.similarPackagesContainer}>
        <TouchableOpacity style={styles.spButton} onPress={viewPackages} activeOpacity={1}>
          <Text style={styles.textLink}>{text}</Text>
        </TouchableOpacity>
        <View style={styles.containerWhite} />
        <View style={styles.containerGray} />
      </View>
    );
  };

  return (
    <>
    {item.headerType ? <ListingCardHeader type={item.headerType} cityName={destinationCityName}/> : null}
    <Fragment>
      {renderVariant()}
      <View style={styles.container}>
        <View>
          <ImageCarousal
            cardImages={cardImages}
            trackClickEvent={trackClickEvent}
            packageId={id}
            handlePackageClick={handlePackageClick}
          />
          {!!tagImage && (
            <HolidayImageHolder
              imageUrl={tagImage}
              style={styles.tagStyle}
              containerStyles={styles.tagStyleContainer}
            />
          )}
        </View>
        <TouchableOpacity onPress={handlePackageClick} activeOpacity={1}>
          <View style={styles.contentContainer}>
            <View style={styles.packageNameContainer}>
              <Text numberOfLines={1} style={styles.packageName}>
                {name}
              </Text>
              <View style={styles.durationBox}>
                <Text style={styles.durationText}>
                  {noOfNights}N/{noOfNights + 1}D
                </Text>
              </View>
            </View>
            <PackageDuration destinationList={destinationList} />
            <Inclusions
              inclusions={inclusions.slice(0, PHOENIX_GROUPING_V2_CARD_SECTION_LIMIT.INCLUSIONS)}
            />
            <ActiveInclusions
              highlights={highlights.slice(0, PHOENIX_GROUPING_V2_CARD_SECTION_LIMIT.HIGHLIGHTS)}
            />
            <PriceDetails listingPricingDetails={listingPricingDetails} />
            <OtherPromos otherPromo={listingPricingDetails?.otherPromo?.[0] || {}} />
          </View>
        </TouchableOpacity>
      </View>
    </Fragment>
    </>
  );
};

const styles = StyleSheet.create({
  container: {
    ...holidayBorderRadius.borderRadius16,
    backgroundColor: holidayColors.white,
    borderWidth: 1,
    borderColor: holidayColors.grayBorder,
    ...marginStyles.mh16,
    position: 'relative',
    overflow: 'hidden',
    zIndex: 10,
  },
  contentContainer: {
    ...paddingStyles.ph16,
    ...paddingStyles.pt8,
    ...paddingStyles.pb16,
  },
  imageStyle: {
    width: '100%',
    height: 152,
    borderTopLeftRadius: borderRadiusValues.br16,
    borderTopRightRadius: borderRadiusValues.br16,
  },
  packageNameContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    ...marginStyles.mb2,
  },
  packageName: {
    ...fontStyles.labelMediumBlack,
    color: holidayColors.black,
    lineHeight: 20,
    flex: 1,
    ...marginStyles.mr10,
  },
  durationBox: {
    ...paddingStyles.ph4,
    backgroundColor: holidayColors.white,
    ...holidayBorderRadius.borderRadius4,
    borderWidth: 1,
    borderColor: holidayColors.darkBlue,
  },
  durationText: {
    color: holidayColors.darkBlue,
    ...fontStyles.labelSmallBold,
    lineHeight: 16,
  },

  similarPackagesContainer: {
    ...marginStyles.mt30,
  },
  containerWhite: {
    ...holidayBorderRadius.borderRadius16,
    ...marginStyles.mh30,
    borderWidth: 1,
    borderColor: holidayColors.lightGrayBorder,
    top: -26,
    position: 'absolute',
    zIndex: 1,
    backgroundColor: holidayColors.white,
    width: '73%',
    height: 50,
    top: '50%',
    left: 62,
    transform: [{ translateX: -50 }, { translateY: -25 }],
  },
  containerGray: {
    ...holidayBorderRadius.borderRadius16,
    ...marginStyles.mh16,
    borderWidth: 1,
    borderColor: holidayColors.lightGrayBorder,
    top: -14,
    position: 'absolute',
    zIndex: 2,
    backgroundColor: holidayColors.lightGray2,
    width: '83%',
    height: 50,
    top: '50%',
    left: 62,
    transform: [{ translateX: -50 }, { translateY: -15 }],
  },
  spButton: {
    backgroundColor: holidayColors.midLightBlue,
    ...Platform.select({
      ios: {
        justifyContent: 'center'
      },
      android: {
       justifyContent: 'center'
      }
    }),
    ...paddingStyles.ph16,
    borderRadius: 100,
    position: 'absolute',
    right: 37,
    height: 44,
    top: -25,
    zIndex: 3,
  },
  textLink: {
    ...fontStyles.labelSmallRegular,
    color: holidayColors.primaryBlue,
    ...Platform.select({
      ios: {
        top:-8,
      },
      android: {
        top:-8,
      },
      web:{
        // top: -8,
    paddingTop: 5,
      },
    }),
  },
  tagStyleContainer: {
    position: 'absolute',
    top: 16,
    left: 0,
  },
  tagStyle: {
    width: 99,
    height: 21,
  },
});

const mapStateToProps = (state) => {
  return {
    groupMetaState: state.holidaysPhoenixGroupingV2.groupMetaDetail,
    holidayLandingGroupDto:
      state.holidaysPhoenixGroupingV2.holidayLandingGroupReducer.holidayLandingGroupDto,
  };
};
export default connect(mapStateToProps)(ListingCard);
