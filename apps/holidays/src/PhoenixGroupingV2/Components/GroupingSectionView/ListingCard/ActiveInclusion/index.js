import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { holidayColors } from 'mobile-holidays-react-native/src/Styles/holidayColors';
import { fontStyles } from 'mobile-holidays-react-native/src/Styles/holidayFonts';
import { marginStyles, paddingStyles } from 'mobile-holidays-react-native/src/Styles/Spacing';
import { isEmpty } from 'lodash';
import HolidayImageHolder from 'mobile-holidays-react-native/src/Common/Components/HolidayImageHolder';

const GREEN_TICK_URL =
  'https://hldak.mmtcdn.com/prod-s3-hld-hpcmsadmin/holidays/images/phonixImages/highlights_tick3x.png';

const ActiveInclusions = ({ highlights }) => {
  return (
      <View style={styles.container}>
        {highlights.map((item, index) => {
          const { colorCode = holidayColors.green, iconUrl = GREEN_TICK_URL, highlight = '' } = item || {};
          return (
              <View key={`id=${index}`} style={styles.activeInclusionRow}>
                <HolidayImageHolder imageUrl={iconUrl} style={styles.greenTickImgStyle} resizeMode={'contain'} />
                <Text style={[styles.activeInclusionText, { color: colorCode }]}>{highlight}</Text>
              </View>
          );
        })}
      </View>
  );
};

const styles = StyleSheet.create({
  container: {
    alignItems: 'flex-start',
    width: '100%',
    ...marginStyles.mt6,
  },
  activeInclusionRow: {
    flexDirection: 'row',
    ...marginStyles.mt2,
    ...marginStyles.mb2,
  },
  greenTickImgStyle: {
    width: 10,
    height: 10,
    ...marginStyles.mr6,
    ...marginStyles.mt4,
  },
  activeInclusionText: {
    ...fontStyles.labelSmallRegular,
    lineHeight: 17,
    ...paddingStyles.pr6,
    flex:1,
  },
});

export default ActiveInclusions;
