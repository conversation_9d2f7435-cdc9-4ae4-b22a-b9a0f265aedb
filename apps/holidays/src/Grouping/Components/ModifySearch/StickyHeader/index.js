import React, { Fragment } from 'react';
import {UIManager, LayoutAnimation, Platform} from 'react-native';
import WidgetPage from '../WidgetPage';
import {isAndroidClient} from "../../../../utils/HolidayUtils";

if (isAndroidClient()) {
  UIManager.setLayoutAnimationEnabledExperimental(true);
}
const StickyHeader = props => {
  LayoutAnimation.configureNext(LayoutAnimation.Presets.easeInEaseOut);
  return (
    <Fragment>
      {props.fixed && <WidgetPage {...props} />}
    </Fragment>
  );
};

export default StickyHeader;
