import React, { useCallback } from 'react';
import { TouchableOpacity, StyleSheet, Text, View } from 'react-native';
import * as DateUtil from '../DateUtil';
import { colors, fonts } from '@mmt/legacy-commons/Styles/globalStyles';
import { getPaxText } from '../../../../utils/HolidayUtils';

export default (props) => {
    const {
        togglePopup, enableInlineEditSearchView = false,
    } = props;
    let { userDepCity, destinationCity, isStickyHeaderVisible, travellerObject, isSearchFilter} = props;
    if (typeof userDepCity === 'object') {
        userDepCity = undefined;
    }
    if (typeof destinationCity === 'object') {
        destinationCity = undefined;
    }
    const handleEditClick = useCallback(() => togglePopup('EditOverlay'), [togglePopup]);
    return enableInlineEditSearchView && !isStickyHeaderVisible ? (
        <View style={[styles.container]}>
            <View style={styles.widgetPageWrapper}>
                <View style={styles.widgetTextView}>
                {(userDepCity || destinationCity) ? <Text numberOfLines={1} style={styles.sourceCity}>{userDepCity} - <Text style={styles.destinationCity}>{destinationCity || 'Select Destination'} </Text></Text> : null}
                <Text style={styles.date}>{DateUtil.getDateText({ ...props, removeDayName: true })}{isSearchFilter ? getPaxText({trvInfo : travellerObject, seprator : ' | '}) : ''}</Text>
                </View>
                    <TouchableOpacity onPress={handleEditClick} >
                    <Text style={styles.editText}>Edit</Text>
                </TouchableOpacity>
            </View>
        </View>
    ) : null;
};
const styles = StyleSheet.create({

    container: {
        paddingVertical: 12,
        paddingHorizontal: 16,
        backgroundColor: '#fff',
    },
    widgetPageWrapper: {
        flexDirection: 'row',
        alignItems: 'center',
        borderWidth: 1,
        borderColor: '#eae8e8',
        borderRadius: 6,
        paddingHorizontal: 10,
        paddingVertical: 4,
        backgroundColor: colors.grey13,
    },
    widgetTextView: {
        flex:1,
        flexDirection: 'column',
    },
    sourceCity: {
        flex: 2,
        fontSize: 14,
        fontFamily: fonts.regular,
        color: colors.black,
    },
    destinationCity: {
        fontFamily: fonts.bold,
    },
    date: {
        flex:1,
        textAlign: 'left',
        marginHorizontal: 0,
        fontSize: 12,
        marginTop: 3,
        fontFamily: fonts.regular,
        color: '#ACACAC',
    },
    editText: {
        fontSize: 12,
        fontFamily: fonts.regular,
        fontWeight: '700',
        color: '#008CFF',
    },
});
