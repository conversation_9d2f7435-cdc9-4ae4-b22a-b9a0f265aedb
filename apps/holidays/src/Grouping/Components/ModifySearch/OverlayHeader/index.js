import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity ,Image} from 'react-native';

const OverlayHeader = ({title,handleBack}) => {
    return (
        <View style={styles.header}>
            <TouchableOpacity onPress={handleBack} style={styles.crossWrapper} >
                <Image style={styles.iconBack} source={require('@mmt/legacy-assets/src/holidays/iconCross.webp')}/>
             </TouchableOpacity>
            <Text style={styles.title}>{title}</Text>
      </View>
    );
};
const styles = StyleSheet.create({
    header: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        backgroundColor: '#fff',
        color: '#4a4a4a',
        paddingTop: 20,
        paddingBottom: 30,

    },
    crossWrapper:{
      paddingRight:10,
      paddingLeft:10,

    },
    title: {
        flex:1,
        color: '#484848',
        fontFamily: 'Lato-Bold',
        letterSpacing: 0.3,
        fontSize: 16,
    },
    iconBack:{
      height:24,
      width:24,

    },
});
export default OverlayHeader;
